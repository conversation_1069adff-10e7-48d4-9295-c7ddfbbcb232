/**
 * Part Data Preservation Utilities
 * 
 * This module provides utilities for intelligently merging part data to preserve
 * populated Part objects while updating form fields from hierarchical form data.
 * 
 * Key Features:
 * - Preserves populated Part objects with inventory data
 * - Updates form-specific fields (quantities, etc.)
 * - Handles both string partIds and populated Part objects
 * - Supports recursive merging for nested children parts
 * - Maintains data consistency across form submissions
 */

export interface PartRequirement {
  _id?: string;
  partId: string | any; // Can be string ID or populated Part object
  quantityRequired: number;
  unitOfMeasure?: string;
  name?: string;
  description?: string;
  currentStock?: number;
  children?: PartRequirement[];
  isExpanded?: boolean;
  [key: string]: any;
}

/**
 * Extracts the actual part ID from either a string or populated Part object
 */
export function extractPartId(partId: string | any): string | null {
  if (typeof partId === 'string') {
    return partId;
  }
  if (typeof partId === 'object' && partId?._id) {
    return partId._id;
  }
  return null;
}

/**
 * Checks if a part object contains populated Part data
 */
export function isPopulatedPart(partId: string | any): boolean {
  return typeof partId === 'object' && 
         partId !== null && 
         partId._id && 
         (partId.name || partId.description || partId.inventory);
}

/**
 * Extracts current stock using canonical schema field access patterns
 */
export function extractCurrentStock(part: any): number {
  // Use canonical schema field access patterns only
  if (part.inventory?.currentStock !== undefined) {
    return part.inventory.currentStock;
  }
  if (typeof part.partId === 'object' && part.partId?.inventory?.currentStock !== undefined) {
    return part.partId.inventory.currentStock;
  }
  return 0;
}

/**
 * Extracts part name from various possible locations
 */
export function extractPartName(part: any): string {
  if (part.name) return part.name;
  if (typeof part.partId === 'object' && part.partId?.name) {
    return part.partId.name;
  }
  return 'Unknown Part';
}

/**
 * Extracts part description from various possible locations
 */
export function extractPartDescription(part: any): string {
  if (part.description) return part.description;
  if (typeof part.partId === 'object' && part.partId?.description) {
    return part.partId.description;
  }
  return '';
}

/**
 * Creates a map of parts indexed by their part ID for efficient lookup
 */
export function createPartLookupMap(parts: PartRequirement[]): Map<string, PartRequirement & { originalIndex: number }> {
  const map = new Map();
  
  parts.forEach((part, index) => {
    const partId = extractPartId(part.partId);
    if (partId) {
      map.set(partId, { ...part, originalIndex: index });
    }
  });
  
  return map;
}

/**
 * Intelligently merges part data to preserve populated Part objects while updating form fields
 * 
 * @param currentParts - Parts from context with potentially populated Part objects
 * @param hierarchicalParts - Parts from hierarchical form with updated form data
 * @param options - Configuration options for merging behavior
 */
export function mergePartsRequiredData(
  currentParts: PartRequirement[] = [],
  hierarchicalParts: PartRequirement[] = [],
  options: {
    preservePopulatedData?: boolean;
    updateStockFromHierarchical?: boolean;
    logMergeProcess?: boolean;
  } = {}
): PartRequirement[] {
  const {
    preservePopulatedData = true,
    updateStockFromHierarchical = false,
    logMergeProcess = false
  } = options;

  if (logMergeProcess) {
    console.log('[mergePartsRequiredData] Starting merge');
    console.log('[mergePartsRequiredData] currentParts:', currentParts.length);
    console.log('[mergePartsRequiredData] hierarchicalParts:', hierarchicalParts.length);
  }

  // Create a map of current parts by their partId for quick lookup
  const currentPartsMap = createPartLookupMap(currentParts);

  // Merge hierarchical parts with current parts, preserving populated Part objects
  const mergedParts = hierarchicalParts.map((hierarchicalPart, index) => {
    const hierarchicalPartId = extractPartId(hierarchicalPart.partId);
    
    if (!hierarchicalPartId) {
      if (logMergeProcess) {
        console.warn(`[mergePartsRequiredData] No valid partId found for hierarchical part at index ${index}`);
      }
      return hierarchicalPart;
    }

    const currentPart = currentPartsMap.get(hierarchicalPartId);

    if (currentPart && preservePopulatedData) {
      // Part exists in current data - preserve populated Part object, update form fields
      if (logMergeProcess) {
        console.log(`[mergePartsRequiredData] Preserving populated data for part: ${extractPartName(hierarchicalPart)}`);
      }

      const { originalIndex, ...currentPartWithoutIndex } = currentPart;
      const mergedPart: PartRequirement = {
        ...currentPartWithoutIndex,
        // Update form-specific fields from hierarchical data
        quantityRequired: hierarchicalPart.quantityRequired,
        unitOfMeasure: hierarchicalPart.unitOfMeasure || 'ea',
        isExpanded: hierarchicalPart.isExpanded || false,
        
        // Preserve the populated partId object from current data if it exists
        partId: isPopulatedPart(currentPart.partId) ? currentPart.partId : hierarchicalPart.partId,
        
        // Update display fields but keep populated data as source of truth
        name: isPopulatedPart(currentPart.partId) ? extractPartName(currentPart) : extractPartName(hierarchicalPart),
        description: isPopulatedPart(currentPart.partId) ? extractPartDescription(currentPart) : extractPartDescription(hierarchicalPart),
        
        // Handle stock data based on options
        currentStock: updateStockFromHierarchical 
          ? extractCurrentStock(hierarchicalPart)
          : extractCurrentStock(currentPart),
        
        // Handle children recursively if they exist
        children: hierarchicalPart.children && hierarchicalPart.children.length > 0
          ? mergePartsRequiredData(currentPart.children || [], hierarchicalPart.children, options)
          : hierarchicalPart.children || []
      };

      return mergedPart;
    } else {
      // New part from hierarchical form - use as-is but may need to fetch full data later
      if (logMergeProcess) {
        console.log(`[mergePartsRequiredData] New part detected: ${extractPartName(hierarchicalPart)}`);
      }
      return {
        ...hierarchicalPart,
        // For new parts, partId might be a string from search - this is expected
        // The saveAssembly function will handle converting this properly
        name: extractPartName(hierarchicalPart),
        description: extractPartDescription(hierarchicalPart),
        currentStock: extractCurrentStock(hierarchicalPart)
      };
    }
  });

  if (logMergeProcess) {
    console.log('[mergePartsRequiredData] Merged parts count:', mergedParts.length);
  }

  return mergedParts;
}

/**
 * Validates that part data structure is consistent
 */
export function validatePartDataStructure(parts: PartRequirement[]): {
  isValid: boolean;
  issues: string[];
} {
  const issues: string[] = [];
  
  parts.forEach((part, index) => {
    if (!part.partId) {
      issues.push(`Part at index ${index} is missing partId`);
    }
    
    if (typeof part.quantityRequired !== 'number' || part.quantityRequired <= 0) {
      issues.push(`Part at index ${index} has invalid quantityRequired: ${part.quantityRequired}`);
    }
    
    if (part.children && Array.isArray(part.children)) {
      const childValidation = validatePartDataStructure(part.children);
      if (!childValidation.isValid) {
        issues.push(`Part at index ${index} has invalid children: ${childValidation.issues.join(', ')}`);
      }
    }
  });
  
  return {
    isValid: issues.length === 0,
    issues
  };
}

/**
 * Refreshes stock data for parts by fetching latest information from the API
 *
 * @param parts - Array of parts to refresh stock data for
 * @param options - Configuration options for stock refresh
 */
export async function refreshPartStockData(
  parts: PartRequirement[],
  options: {
    batchSize?: number;
    logProgress?: boolean;
    skipInvalidParts?: boolean;
  } = {}
): Promise<PartRequirement[]> {
  const {
    batchSize = 10,
    logProgress = false,
    skipInvalidParts = true
  } = options;

  if (!Array.isArray(parts) || parts.length === 0) {
    if (logProgress) {
      console.log('[refreshPartStockData] No parts to refresh');
    }
    return parts;
  }

  if (logProgress) {
    console.log(`[refreshPartStockData] Refreshing stock data for ${parts.length} parts`);
  }

  // Process parts in batches to avoid overwhelming the API
  const refreshedParts: PartRequirement[] = [];

  for (let i = 0; i < parts.length; i += batchSize) {
    const batch = parts.slice(i, i + batchSize);

    if (logProgress) {
      console.log(`[refreshPartStockData] Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(parts.length / batchSize)}`);
    }

    const batchPromises = batch.map(async (part) => {
      const partId = extractPartId(part.partId);

      if (!partId) {
        if (logProgress) {
          console.warn(`[refreshPartStockData] Part has no valid partId, skipping:`, part.name || 'Unknown');
        }
        return skipInvalidParts ? part : null;
      }

      try {
        const response = await fetch(`/api/parts/${partId}`);

        if (!response.ok) {
          if (logProgress) {
            console.warn(`[refreshPartStockData] Failed to fetch data for part ${partId}: ${response.status}`);
          }
          return part; // Return unchanged if fetch fails
        }

        const freshPartData = await response.json();

        if (!freshPartData.data) {
          if (logProgress) {
            console.warn(`[refreshPartStockData] No data returned for part ${partId}`);
          }
          return part; // Return unchanged if no data
        }

        const oldStock = extractCurrentStock(part);
        const newStock = extractCurrentStock(freshPartData.data);

        if (logProgress && oldStock !== newStock) {
          console.log(`[refreshPartStockData] Stock updated for ${part.name || partId}: ${oldStock} → ${newStock}`);
        }

        // Update the part with fresh data while preserving form-specific fields
        return {
          ...part,
          partId: isPopulatedPart(part.partId) ? freshPartData.data : partId, // Update populated object or keep string ID
          name: freshPartData.data.name || part.name,
          description: freshPartData.data.description || part.description,
          currentStock: newStock,
          // Update any other fields that might have changed
          cost: freshPartData.data.cost || part.cost,
          reorderLevel: freshPartData.data.reorderLevel || part.reorderLevel,
          category: freshPartData.data.category || part.category,
          // Preserve form-specific fields
          quantityRequired: part.quantityRequired,
          unitOfMeasure: part.unitOfMeasure || 'ea',
          isExpanded: part.isExpanded || false,
          children: part.children || []
        };

      } catch (error) {
        if (logProgress) {
          console.error(`[refreshPartStockData] Error refreshing part ${partId}:`, error);
        }
        return part; // Return unchanged if error occurs
      }
    });

    const batchResults = await Promise.all(batchPromises);
    refreshedParts.push(...batchResults.filter(part => part !== null));

    // Small delay between batches to be nice to the API
    if (i + batchSize < parts.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  if (logProgress) {
    console.log(`[refreshPartStockData] Stock refresh completed for ${refreshedParts.length} parts`);
  }

  return refreshedParts;
}

/**
 * Detects potentially stale stock data by comparing timestamps or other indicators
 */
export function detectStaleStockData(parts: PartRequirement[]): {
  hasStaleData: boolean;
  staleParts: string[];
  recommendations: string[];
} {
  const staleParts: string[] = [];
  const recommendations: string[] = [];

  parts.forEach((part) => {
    const partId = extractPartId(part.partId);
    const partName = extractPartName(part);

    // Check if part has populated data with timestamp
    if (isPopulatedPart(part.partId)) {
      const partData = part.partId;
      const lastUpdated = partData.updatedAt || partData.lastModified;

      if (lastUpdated) {
        const lastUpdatedTime = new Date(lastUpdated).getTime();
        const now = Date.now();
        const hoursSinceUpdate = (now - lastUpdatedTime) / (1000 * 60 * 60);

        // Consider data stale if it's older than 1 hour
        if (hoursSinceUpdate > 1) {
          staleParts.push(partName);
        }
      } else {
        // No timestamp available - potentially stale
        staleParts.push(partName);
      }
    } else {
      // Part data is not populated - likely from search results
      staleParts.push(partName);
    }
  });

  const hasStaleData = staleParts.length > 0;

  if (hasStaleData) {
    recommendations.push('Consider refreshing stock data before saving');
    if (staleParts.length > 5) {
      recommendations.push('Multiple parts may have stale data - use bulk refresh');
    }
  }

  return {
    hasStaleData,
    staleParts,
    recommendations
  };
}

/**
 * Compares stock data between two part arrays to detect discrepancies
 */
export function compareStockData(
  currentParts: PartRequirement[],
  referenceParts: PartRequirement[]
): {
  hasDiscrepancies: boolean;
  discrepancies: Array<{
    partId: string;
    partName: string;
    currentStock: number;
    referenceStock: number;
    difference: number;
  }>;
} {
  const discrepancies: Array<{
    partId: string;
    partName: string;
    currentStock: number;
    referenceStock: number;
    difference: number;
  }> = [];

  const referenceMap = createPartLookupMap(referenceParts);

  currentParts.forEach((currentPart) => {
    const partId = extractPartId(currentPart.partId);
    if (!partId) return;

    const referencePart = referenceMap.get(partId);
    if (!referencePart) return;

    const currentStock = extractCurrentStock(currentPart);
    const referenceStock = extractCurrentStock(referencePart);

    if (currentStock !== referenceStock) {
      discrepancies.push({
        partId,
        partName: extractPartName(currentPart),
        currentStock,
        referenceStock,
        difference: currentStock - referenceStock
      });
    }
  });

  return {
    hasDiscrepancies: discrepancies.length > 0,
    discrepancies
  };
}

/**
 * Automatically determines if stock refresh is needed based on various criteria
 */
export function shouldRefreshStock(
  parts: PartRequirement[],
  options: {
    maxAgeHours?: number;
    forceRefreshOnSave?: boolean;
    checkForDiscrepancies?: boolean;
  } = {}
): {
  shouldRefresh: boolean;
  reasons: string[];
  priority: 'low' | 'medium' | 'high';
} {
  const {
    maxAgeHours = 1,
    forceRefreshOnSave = false,
    checkForDiscrepancies = true
  } = options;

  const reasons: string[] = [];
  let priority: 'low' | 'medium' | 'high' = 'low';

  // Check for stale data
  const staleDataCheck = detectStaleStockData(parts);
  if (staleDataCheck.hasStaleData) {
    reasons.push(`${staleDataCheck.staleParts.length} parts have potentially stale stock data`);
    priority = 'medium';
  }

  // Force refresh on save if configured
  if (forceRefreshOnSave) {
    reasons.push('Automatic refresh before save is enabled');
    priority = 'high';
  }

  // Check for parts without populated data
  const unPopulatedParts = parts.filter(part => !isPopulatedPart(part.partId));
  if (unPopulatedParts.length > 0) {
    reasons.push(`${unPopulatedParts.length} parts lack detailed inventory data`);
    if (priority === 'low') priority = 'medium';
  }

  const shouldRefresh = reasons.length > 0;

  return {
    shouldRefresh,
    reasons,
    priority
  };
}
