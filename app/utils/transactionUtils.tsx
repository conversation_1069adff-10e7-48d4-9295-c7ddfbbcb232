import { 
  ArrowDownLeft, 
  ArrowUpRight, 
  Factory, 
  Package, 
  RefreshCw, 
  Truck 
} from 'lucide-react';

/**
 * Transaction type definitions and mappings
 */
export const TRANSACTION_TYPES = {
  STOCK_IN_PURCHASE: 'stock_in_purchase',
  STOCK_OUT_PRODUCTION: 'stock_out_production',
  ADJUSTMENT_CYCLE_COUNT: 'adjustment_cycle_count',
  STOCK_IN_PRODUCTION: 'stock_in_production',
  TRANSFER_OUT: 'transfer_out',
  TRANSFER_IN: 'transfer_in',
  SALES_SHIPMENT: 'sales_shipment'
} as const;

/**
 * Transaction type display names
 */
export const TRANSACTION_TYPE_LABELS: Record<string, string> = {
  'stock_in_purchase': 'Stock In Purchase',
  'stock_out_production': 'Stock Out Production',
  'stock_in_production': 'Stock In Production',
  'adjustment_cycle_count': 'Adjustment Cycle Count',
  'adjustment_manual': 'Adjustment Manual',
  'transfer_in': 'Transfer In',
  'transfer_out': 'Transfer Out',
  'sales_shipment': 'Sales Shipment',
  'return_from_production': 'Return From Production',
  'return_to_supplier': 'Return To Supplier',
  // Legacy support
  'purchase_receipt': 'Purchase Receipt',
  'production_consumption': 'Production Consumption',
  'stock_adjustment': 'Stock Adjustment',
  'production_output': 'Production Output',
  'internal_transfer_out': 'Internal Transfer Out',
  'internal_transfer_in': 'Internal Transfer In',
  'stock_in': 'Stock In',
  'stock_out': 'Stock Out'
};

/**
 * Get the appropriate icon for a transaction type
 */
export function getTransactionTypeIcon(transactionType: string, size: 'sm' | 'md' | 'lg' = 'md') {
  const sizeClass = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  }[size];

  const type = transactionType.toLowerCase();
  
  if (type.includes('stock in') || type.includes('purchase') || type.includes('receipt')) {
    return <ArrowDownLeft className={`${sizeClass} text-green-600`} />;
  }
  if (type.includes('stock out') || type.includes('consumption') || type.includes('sales') || type.includes('shipment')) {
    return <ArrowUpRight className={`${sizeClass} text-red-600`} />;
  }
  if (type.includes('adjustment') || type.includes('cycle count') || type.includes('manual')) {
    return <RefreshCw className={`${sizeClass} text-blue-600`} />;
  }
  if (type.includes('transfer')) {
    return <Truck className={`${sizeClass} text-amber-600`} />;
  }
  if (type.includes('production') && (type.includes('stock in') || type.includes('output'))) {
    return <Factory className={`${sizeClass} text-purple-600`} />;
  }
  
  return <Package className={`${sizeClass} text-gray-600`} />;
}

/**
 * Get the appropriate color class for transaction type
 */
export function getTransactionTypeColor(transactionType: string): string {
  const type = transactionType.toLowerCase();
  
  if (type.includes('stock in') || type.includes('purchase') || type.includes('receipt')) {
    return 'text-green-600';
  }
  if (type.includes('stock out') || type.includes('consumption') || type.includes('sales') || type.includes('shipment')) {
    return 'text-red-600';
  }
  if (type.includes('adjustment') || type.includes('cycle count') || type.includes('manual')) {
    return 'text-blue-600';
  }
  if (type.includes('transfer')) {
    return 'text-amber-600';
  }
  if (type.includes('production')) {
    return 'text-purple-600';
  }
  
  return 'text-gray-600';
}

/**
 * Get the appropriate badge variant for transaction type
 */
export function getTransactionTypeBadgeVariant(transactionType: string): "default" | "secondary" | "destructive" | "outline" {
  const type = transactionType.toLowerCase();
  
  if (type.includes('stock in') || type.includes('purchase') || type.includes('receipt')) {
    return 'default'; // Green-ish
  }
  if (type.includes('stock out') || type.includes('consumption') || type.includes('sales') || type.includes('shipment')) {
    return 'destructive'; // Red-ish
  }
  if (type.includes('adjustment') || type.includes('cycle count') || type.includes('manual')) {
    return 'secondary'; // Blue-ish
  }
  
  return 'outline';
}

/**
 * Format transaction type for display
 */
export function formatTransactionType(type: string): string {
  return TRANSACTION_TYPE_LABELS[type] || type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

/**
 * Get quantity change display information
 */
export function getQuantityChangeDisplay(quantity: number) {
  if (quantity > 0) {
    return {
      icon: <ArrowDownLeft className="h-4 w-4 text-green-600" />,
      color: 'text-green-600',
      bgColor: 'bg-green-50 dark:bg-green-900/20',
      prefix: '+',
      label: 'Stock Increase'
    };
  } else if (quantity < 0) {
    return {
      icon: <ArrowUpRight className="h-4 w-4 text-red-600" />,
      color: 'text-red-600',
      bgColor: 'bg-red-50 dark:bg-red-900/20',
      prefix: '',
      label: 'Stock Decrease'
    };
  } else {
    return {
      icon: <RefreshCw className="h-4 w-4 text-gray-600" />,
      color: 'text-gray-600',
      bgColor: 'bg-gray-50 dark:bg-gray-900/20',
      prefix: '',
      label: 'No Change'
    };
  }
}

/**
 * Get reference type display information
 */
export function getReferenceTypeInfo(referenceType?: string) {
  const typeMap: Record<string, { label: string; color: string }> = {
    'PurchaseOrder': { label: 'Purchase Order', color: 'text-blue-600' },
    'WorkOrder': { label: 'Work Order', color: 'text-purple-600' },
    'SalesOrder': { label: 'Sales Order', color: 'text-green-600' },
    'StockAdjustment': { label: 'Stock Adjustment', color: 'text-amber-600' }
  };

  return typeMap[referenceType || ''] || { label: referenceType || 'Unknown', color: 'text-gray-600' };
}

/**
 * Format transaction date for display
 */
export function formatTransactionDate(date: string | Date): string {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return 'Invalid Date';
  }
}

/**
 * Get stock level status and color
 */
export function getStockLevelStatus(stock: number) {
  if (stock <= 0) {
    return {
      status: 'Out of Stock',
      color: 'text-red-600',
      bgColor: 'bg-red-50 dark:bg-red-900/20'
    };
  } else if (stock < 10) {
    return {
      status: 'Low Stock',
      color: 'text-amber-600',
      bgColor: 'bg-amber-50 dark:bg-amber-900/20'
    };
  } else if (stock < 50) {
    return {
      status: 'Medium Stock',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 dark:bg-blue-900/20'
    };
  } else {
    return {
      status: 'Good Stock',
      color: 'text-green-600',
      bgColor: 'bg-green-50 dark:bg-green-900/20'
    };
  }
}
