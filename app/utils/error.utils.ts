/**
 * Error Handling Utilities
 * Standardized utilities for error handling, formatting, and logging
 */

import { v4 as uuidv4 } from 'uuid';
import { 
  StandardError, 
  ErrorSeverity, 
  ErrorCategory, 
  ERROR_MESSAGES,
  ErrorMessageCategory,
  ErrorMessageKey
} from '@/app/types/error.types';

/**
 * Create a standardized error object
 */
export function createStandardError(
  message: string,
  options: {
    code?: string;
    details?: string;
    severity?: ErrorSeverity;
    category?: ErrorCategory;
    context?: Record<string, any>;
    recoverable?: boolean;
    retryable?: boolean;
  } = {}
): StandardError {
  return {
    id: uuidv4(),
    code: options.code || 'UNKNOWN_ERROR',
    message,
    ...(options.details !== undefined ? { details: options.details } : {}),
    severity: options.severity || ErrorSeverity.ERROR,
    category: options.category || ErrorCategory.SYSTEM,
    ...(options.context !== undefined ? { context: options.context } : {}),
    timestamp: new Date(),
    recoverable: options.recoverable ?? true,
    retryable: options.retryable ?? false,
  };
}

/**
 * Convert any error to a StandardError
 */
export function normalizeError(error: unknown): StandardError {
  if (isStandardError(error)) {
    return error;
  }

  if (error instanceof Error) {
    return createStandardError(error.message, {
      code: error.name,
      ...(error.stack !== undefined ? { details: error.stack } : {}),
      context: { originalError: error },
    });
  }

  if (typeof error === 'string') {
    return createStandardError(error);
  }

  return createStandardError('An unknown error occurred', {
    code: 'UNKNOWN_ERROR',
    details: String(error),
    context: { originalError: error },
  });
}

/**
 * Check if an error is a StandardError
 */
export function isStandardError(error: unknown): error is StandardError {
  return (
    typeof error === 'object' &&
    error !== null &&
    'id' in error &&
    'code' in error &&
    'message' in error &&
    'severity' in error &&
    'category' in error &&
    'timestamp' in error
  );
}

/**
 * Format error message using templates
 */
export function formatErrorMessage(
  template: string,
  variables?: Record<string, string>
): string {
  if (!variables) {
    return template;
  }

  return template.replace(/\{(\w+)\}/g, (match, key) => {
    return variables[key] || match;
  });
}

/**
 * Get error message from template
 */
export function getErrorMessage<T extends ErrorMessageCategory>(
  category: T,
  key: ErrorMessageKey<T>,
  variables?: Record<string, string>
): string {
  const template = ERROR_MESSAGES[category][key] as string;
  return formatErrorMessage(template, variables);
}

/**
 * Extract error message from various error types
 */
export function extractErrorMessage(error: unknown): string {
  if (isStandardError(error)) {
    return error.message;
  }

  if (error instanceof Error) {
    return error.message;
  }

  if (typeof error === 'string') {
    return error;
  }

  return 'An unknown error occurred';
}

/**
 * Check if error is recoverable
 */
export function isRecoverableError(error: unknown): boolean {
  if (isStandardError(error)) {
    return error.recoverable;
  }

  // Default to recoverable for unknown errors
  return true;
}

/**
 * Check if error is retryable
 */
export function isRetryableError(error: unknown): boolean {
  if (isStandardError(error)) {
    return error.retryable;
  }

  // Check for common retryable error patterns
  const message = extractErrorMessage(error).toLowerCase();
  const retryablePatterns = [
    'timeout',
    'network',
    'connection',
    'server error',
    'service unavailable',
    'try again',
  ];

  return retryablePatterns.some(pattern => message.includes(pattern));
}

/**
 * Get error severity level
 */
export function getErrorSeverity(error: unknown): ErrorSeverity {
  if (isStandardError(error)) {
    return error.severity;
  }

  // Determine severity based on error type
  if (error instanceof TypeError || error instanceof ReferenceError) {
    return ErrorSeverity.CRITICAL;
  }

  return ErrorSeverity.ERROR;
}

/**
 * Get error category
 */
export function getErrorCategory(error: unknown): ErrorCategory {
  if (isStandardError(error)) {
    return error.category;
  }

  const message = extractErrorMessage(error).toLowerCase();

  // Network errors
  if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
    return ErrorCategory.NETWORK;
  }

  // Validation errors
  if (message.includes('validation') || message.includes('invalid') || message.includes('required')) {
    return ErrorCategory.VALIDATION;
  }

  // Permission errors
  if (message.includes('permission') || message.includes('unauthorized') || message.includes('forbidden')) {
    return ErrorCategory.PERMISSION;
  }

  return ErrorCategory.SYSTEM;
}

/**
 * Create error context for logging
 */
export function createErrorContext(
  component?: string,
  action?: string,
  additionalContext?: Record<string, any>
): Record<string, any> {
  const context: Record<string, any> = {
    timestamp: new Date().toISOString(),
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
    url: typeof window !== 'undefined' ? window.location.href : 'unknown',
    ...additionalContext,
  };

  if (component) {
    context.component = component;
  }

  if (action) {
    context.action = action;
  }

  return context;
}

/**
 * Log error with standardized format
 */
export function logError(
  error: unknown,
  context?: Record<string, any>
): void {
  const standardError = normalizeError(error);
  const logContext = createErrorContext(undefined, undefined, context);

  const logEntry = {
    id: standardError.id,
    timestamp: standardError.timestamp,
    severity: standardError.severity,
    category: standardError.category,
    code: standardError.code,
    message: standardError.message,
    details: standardError.details,
    context: { ...standardError.context, ...logContext },
  };

  // Log based on severity
  switch (standardError.severity) {
    case ErrorSeverity.CRITICAL:
      console.error('[CRITICAL ERROR]', logEntry);
      break;
    case ErrorSeverity.ERROR:
      console.error('[ERROR]', logEntry);
      break;
    case ErrorSeverity.WARNING:
      console.warn('[WARNING]', logEntry);
      break;
    case ErrorSeverity.INFO:
      console.info('[INFO]', logEntry);
      break;
    default:
      console.error('[ERROR]', logEntry);
  }
}

/**
 * Check if error is a test error
 */
export function isTestError(error: unknown): boolean {
  const message = extractErrorMessage(error).toLowerCase();
  return (
    message.includes('[test error]') ||
    message.includes('test error') ||
    (typeof error === 'object' && error !== null && 'isTestError' in error)
  );
}
