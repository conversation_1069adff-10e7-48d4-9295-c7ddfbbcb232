/**
 * Utility functions for API requests.
 *
 * This module provides safe API URL generation that works correctly in both
 * client and server environments, with proper handling for deployment scenarios.
 */

import { getApiBaseUrl as getApiBaseUrlFromEnv, getApiUrl as getApiUrlFromEnv } from './env';

/**
 * Determines the base URL for API calls, ensuring absolute paths in production.
 *
 * This function is critical for Vercel deployments where serverless functions
 * need to know the full URL to make API requests to themselves.
 *
 * @returns {string} The absolute base URL for the API.
 * @deprecated Use getApiBaseUrl from env.ts instead
 */
export const getApiBaseUrl = (): string => {
  console.warn('getApiBaseUrl from apiUtils.ts is deprecated. Use getApiBaseUrl from env.ts instead.');
  return getApiBaseUrlFromEnv();
};

/**
 * Creates a full, absolute API URL by combining the base URL with a given endpoint.
 *
 * @param {string} endpoint - The API endpoint (e.g., '/api/parts').
 * @returns {string} The full, absolute API URL.
 * @deprecated Use getApiUrl from env.ts instead
 */
export const getApiUrl = (endpoint: string = ''): string => {
  console.warn('getApiUrl from apiUtils.ts is deprecated. Use getApiUrl from env.ts instead.');
  return getApiUrlFromEnv(endpoint);
};