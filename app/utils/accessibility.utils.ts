/**
 * Accessibility Utilities
 * Functions to test and validate theme accessibility and contrast ratios
 */

/**
 * Convert HSL to RGB
 */
function hslToRgb(h: number, s: number, l: number): [number, number, number] {
  h /= 360;
  s /= 100;
  l /= 100;

  const c = (1 - Math.abs(2 * l - 1)) * s;
  const x = c * (1 - Math.abs((h * 6) % 2 - 1));
  const m = l - c / 2;

  let r = 0, g = 0, b = 0;

  if (0 <= h && h < 1/6) {
    r = c; g = x; b = 0;
  } else if (1/6 <= h && h < 2/6) {
    r = x; g = c; b = 0;
  } else if (2/6 <= h && h < 3/6) {
    r = 0; g = c; b = x;
  } else if (3/6 <= h && h < 4/6) {
    r = 0; g = x; b = c;
  } else if (4/6 <= h && h < 5/6) {
    r = x; g = 0; b = c;
  } else if (5/6 <= h && h < 1) {
    r = c; g = 0; b = x;
  }

  return [
    Math.round((r + m) * 255),
    Math.round((g + m) * 255),
    Math.round((b + m) * 255)
  ];
}

/**
 * Calculate relative luminance of a color
 */
function getLuminance(r: number, g: number, b: number): number {
  const [rs, gs, bs] = [r, g, b].map(c => {
    c = c / 255;
    return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
  });
  // Add null safety checks for the mapped values
  if (rs === undefined || gs === undefined || bs === undefined) {
    return 0;
  }
  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
}

/**
 * Calculate contrast ratio between two colors
 */
function getContrastRatio(color1: [number, number, number], color2: [number, number, number]): number {
  const lum1 = getLuminance(...color1);
  const lum2 = getLuminance(...color2);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  return (brightest + 0.05) / (darkest + 0.05);
}

/**
 * Parse HSL string to HSL values
 */
function parseHSL(hslString: string): [number, number, number] | null {
  const match = hslString.match(/(\d+(?:\.\d+)?)\s+(\d+(?:\.\d+)?)%\s+(\d+(?:\.\d+)?)%/);
  if (!match || !match[1] || !match[2] || !match[3]) return null;
  return [parseFloat(match[1]), parseFloat(match[2]), parseFloat(match[3])];
}

/**
 * Get computed CSS variable value
 */
function getCSSVariable(variableName: string): string {
  if (typeof window === 'undefined') return '';
  return getComputedStyle(document.documentElement).getPropertyValue(variableName).trim();
}

/**
 * Test contrast ratio for a color combination
 */
export function testContrastRatio(
  foregroundVar: string, 
  backgroundVar: string
): { ratio: number; passes: { aa: boolean; aaa: boolean } } | null {
  try {
    const foregroundHSL = getCSSVariable(foregroundVar);
    const backgroundHSL = getCSSVariable(backgroundVar);

    const fgHSL = parseHSL(foregroundHSL);
    const bgHSL = parseHSL(backgroundHSL);

    if (!fgHSL || !bgHSL) return null;

    const fgRGB = hslToRgb(...fgHSL);
    const bgRGB = hslToRgb(...bgHSL);

    const ratio = getContrastRatio(fgRGB, bgRGB);

    return {
      ratio,
      passes: {
        aa: ratio >= 4.5,   // WCAG AA standard
        aaa: ratio >= 7     // WCAG AAA standard
      }
    };
  } catch (error) {
    console.warn('Error testing contrast ratio:', error);
    return null;
  }
}

/**
 * Test all critical color combinations for accessibility
 */
export function testThemeAccessibility(): {
  results: Array<{
    name: string;
    foreground: string;
    background: string;
    ratio: number;
    passes: { aa: boolean; aaa: boolean };
  }>;
  overallScore: number;
} {
  const tests = [
    { name: 'Primary Text', foreground: '--foreground', background: '--background' },
    { name: 'Primary Button', foreground: '--primary-foreground', background: '--primary' },
    { name: 'Secondary Button', foreground: '--secondary-foreground', background: '--secondary' },
    { name: 'Muted Text', foreground: '--muted-foreground', background: '--background' },
    { name: 'Card Text', foreground: '--card-foreground', background: '--card' },
    { name: 'Destructive Button', foreground: '--destructive-foreground', background: '--destructive' },
    { name: 'Success Text', foreground: '--success-foreground', background: '--success' },
    { name: 'Warning Text', foreground: '--warning-foreground', background: '--warning' },
    { name: 'Info Text', foreground: '--info-foreground', background: '--info' },
  ];

  const results = tests.map(test => {
    const result = testContrastRatio(test.foreground, test.background);
    return {
      name: test.name,
      foreground: test.foreground,
      background: test.background,
      ratio: result?.ratio || 0,
      passes: result?.passes || { aa: false, aaa: false }
    };
  }).filter(result => result.ratio > 0);

  const passedAA = results.filter(r => r.passes.aa).length;
  const overallScore = (passedAA / results.length) * 100;

  return { results, overallScore };
}

/**
 * Get accessibility recommendations for a theme
 */
export function getAccessibilityRecommendations(
  results: ReturnType<typeof testThemeAccessibility>['results']
): string[] {
  const recommendations: string[] = [];

  const failedAA = results.filter(r => !r.passes.aa);
  const failedAAA = results.filter(r => !r.passes.aaa);

  if (failedAA.length > 0) {
    recommendations.push(
      `${failedAA.length} color combinations fail WCAG AA standards (4.5:1 ratio). Consider adjusting: ${failedAA.map(r => r.name).join(', ')}`
    );
  }

  if (failedAAA.length > 0) {
    recommendations.push(
      `${failedAAA.length} color combinations fail WCAG AAA standards (7:1 ratio). For enhanced accessibility, consider improving: ${failedAAA.map(r => r.name).join(', ')}`
    );
  }

  if (failedAA.length === 0) {
    recommendations.push('✅ All color combinations meet WCAG AA accessibility standards!');
  }

  return recommendations;
}

/**
 * Format contrast ratio for display
 */
export function formatContrastRatio(ratio: number): string {
  return `${ratio.toFixed(2)}:1`;
}

/**
 * Get accessibility grade for a contrast ratio
 */
export function getAccessibilityGrade(ratio: number): 'AAA' | 'AA' | 'Fail' {
  if (ratio >= 7) return 'AAA';
  if (ratio >= 4.5) return 'AA';
  return 'Fail';
}
