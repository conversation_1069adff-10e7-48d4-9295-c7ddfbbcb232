import {
  transformBomDataToTree,
  buildHierarchicalBomData,
  searchBomTree,
  calculateTotalQuantity,
  getItemPath,
  ModalComponentItem,
} from '../bomDataTransform';

// Test data
const mockBomData: ModalComponentItem[] = [
  {
    part: {
      _id: 'part1',
      name: 'Main Assembly',
      partNumber: 'MA-001',
      description: 'Main assembly component',
      category: 'Assembly',
      is_assembly: true,
      current_stock: 10,
    },
    quantity: 1,
    level: 0,
  },
  {
    part: {
      _id: 'part2',
      name: 'Sub Assembly A',
      partNumber: 'SA-001',
      description: 'Sub assembly A',
      category: 'Assembly',
      is_assembly: true,
      current_stock: 5,
    },
    quantity: 2,
    level: 1,
    parentId: 'part1',
  },
  {
    part: {
      _id: 'part3',
      name: 'Component X',
      partNumber: 'CX-001',
      description: 'Component X',
      category: 'Component',
      is_assembly: false,
      current_stock: 100,
    },
    quantity: 4,
    level: 2,
    parentId: 'part2',
  },
  {
    part: {
      _id: 'part4',
      name: 'Component Y',
      partNumber: 'CY-001',
      description: 'Component Y',
      category: 'Component',
      is_assembly: false,
      current_stock: 50,
    },
    quantity: 2,
    level: 1,
    parentId: 'part1',
  },
];

describe('BOM Data Transformation Utilities', () => {
  describe('transformBomDataToTree', () => {
    it('should transform BOM data to tree structure', () => {
      const treeData = transformBomDataToTree(mockBomData);
      
      // Check root item exists
      expect(treeData['root']).toBeDefined();
      expect(treeData['root']!.children).toContain('part1');
      
      // Check main assembly
      expect(treeData['part1']).toBeDefined();
      expect(treeData['part1']!.isFolder).toBe(true);
      expect(treeData['part1']!.children).toEqual(['part2', 'part4']);
      
      // Check sub assembly
      expect(treeData['part2']).toBeDefined();
      expect(treeData['part2']!.children).toEqual(['part3']);
      
      // Check leaf component
      expect(treeData['part3']).toBeDefined();
      expect(treeData['part3']!.isFolder).toBe(false);
      expect(treeData['part3']!.children).toEqual([]);
    });

    it('should create proper display names and details', () => {
      const treeData = transformBomDataToTree(mockBomData);
      
      const part1 = treeData['part1']!;
      expect(part1.data.displayName).toBe('Main Assembly');
      expect(part1.data.displayDetails).toContain('PN: MA-001');
      expect(part1.data.displayDetails).toContain('Qty: 1');
      expect(part1.data.displayDetails).toContain('Stock: 10');
      expect(part1.data.displayDetails).toContain('Assembly');
    });
  });

  describe('buildHierarchicalBomData', () => {
    it('should build hierarchical structure from flat data', () => {
      // Create flat data (unsorted)
      const flatData = [...mockBomData].reverse();
      
      const hierarchical = buildHierarchicalBomData(flatData);
      
      // Should be sorted by level
      expect(hierarchical[0]!.level).toBe(0);
      expect(hierarchical[1]!.level).toBe(1);
      expect(hierarchical[2]!.level).toBe(1);
      expect(hierarchical[3]!.level).toBe(2);
    });
  });

  describe('searchBomTree', () => {
    it('should find items by name', () => {
      const treeData = transformBomDataToTree(mockBomData);
      
      const results = searchBomTree(treeData, 'Component');
      expect(results).toContain('part3');
      expect(results).toContain('part4');
    });

    it('should find items by part number', () => {
      const treeData = transformBomDataToTree(mockBomData);
      
      const results = searchBomTree(treeData, 'CX-001');
      expect(results).toContain('part3');
      expect(results).toHaveLength(1);
    });

    it('should be case insensitive', () => {
      const treeData = transformBomDataToTree(mockBomData);
      
      const results = searchBomTree(treeData, 'component');
      expect(results.length).toBeGreaterThan(0);
    });
  });

  describe('calculateTotalQuantity', () => {
    it('should calculate total quantity including children', () => {
      const treeData = transformBomDataToTree(mockBomData);
      
      // part1 has quantity 1, part2 has quantity 2, part4 has quantity 2
      // part3 has quantity 4 (child of part2)
      // Total for part1 should be 1 + 2 + 2 + 4 = 9
      const total = calculateTotalQuantity(treeData, 'part1');
      expect(total).toBe(9);
    });

    it('should return item quantity for leaf nodes', () => {
      const treeData = transformBomDataToTree(mockBomData);
      
      const total = calculateTotalQuantity(treeData, 'part3');
      expect(total).toBe(4);
    });
  });

  describe('getItemPath', () => {
    it('should return path from root to item', () => {
      const treeData = transformBomDataToTree(mockBomData);
      
      const path = getItemPath(treeData, 'part3');
      expect(path).toHaveLength(3);
      expect(path[0]!.index).toBe('part1');
      expect(path[1]!.index).toBe('part2');
      expect(path[2]!.index).toBe('part3');
    });

    it('should return single item for root level items', () => {
      const treeData = transformBomDataToTree(mockBomData);
      
      const path = getItemPath(treeData, 'part1');
      expect(path).toHaveLength(1);
      expect(path[0]!.index).toBe('part1');
    });
  });
});

// Integration test
describe('BOM Data Integration', () => {
  it('should handle complete workflow', () => {
    // 1. Build hierarchical data
    const hierarchical = buildHierarchicalBomData(mockBomData);
    
    // 2. Transform to tree
    const treeData = transformBomDataToTree(hierarchical);
    
    // 3. Search
    const searchResults = searchBomTree(treeData, 'Assembly');
    expect(searchResults.length).toBeGreaterThan(0);
    
    // 4. Calculate totals
    const total = calculateTotalQuantity(treeData, 'root');
    expect(total).toBeGreaterThan(0);
    
    // 5. Get paths
    const path = getItemPath(treeData, 'part3');
    expect(path.length).toBeGreaterThan(0);
  });
});
