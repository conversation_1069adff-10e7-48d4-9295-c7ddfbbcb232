/**
 * Build-safe utilities to prevent API calls during SSG/build time
 * These utilities help avoid the "Unexpected token '<'" error
 */

import { env } from './env';

/**
 * Check if we're currently in build mode
 */
export function isBuildTime(): boolean {
  return (
    env.NEXT_PHASE === 'phase-production-build' ||
    env.NODE_ENV === 'production' && typeof window === 'undefined'
  );
}

/**
 * Check if we're in a server environment
 */
export function isServer(): boolean {
  return typeof window === 'undefined';
}

/**
 * Check if we're in a client environment
 */
export function isClient(): boolean {
  return typeof window !== 'undefined';
}

/**
 * Safe wrapper for API calls that might happen during build
 */
export async function buildSafeApiCall<T>(
  apiCall: () => Promise<T>,
  fallbackData: T
): Promise<T> {
  if (isBuildTime()) {
    console.log('[BuildSafe] Skipping API call during build time');
    return fallbackData;
  }
  
  try {
    return await apiCall();
  } catch (error) {
    console.error('[BuildSafe] API call failed, using fallback:', error);
    return fallbackData;
  }
}

/**
 * Safe wrapper for fetch calls with build-time protection
 */
export async function buildSafeFetch(
  url: string,
  options?: RequestInit
): Promise<Response | null> {
  if (isBuildTime()) {
    console.log('[BuildSafe] Skipping fetch during build time:', url);
    return null;
  }
  
  try {
    const response = await fetch(url, options);
    
    // Check if response is HTML (error page)
    const contentType = response.headers.get('content-type') || '';
    if (!contentType.includes('application/json') && !response.ok) {
      const body = await response.text();
      console.error('[BuildSafe] Received HTML instead of JSON:', body.substring(0, 200));
      throw new Error('Server returned HTML error page instead of JSON');
    }
    
    return response;
  } catch (error) {
    console.error('[BuildSafe] Fetch failed:', error);
    return null;
  }
}

/**
 * Environment-aware console logging
 */
export function buildSafeLog(message: string, data?: any) {
  if (env.NODE_ENV === 'development' || !isBuildTime()) {
    console.log(`[BuildSafe] ${message}`, data);
  }
}

/**
 * Get safe API URL that works in all environments
 */
export function getSafeApiUrl(endpoint: string): string {
  if (isBuildTime()) {
    return `http://localhost:3000${endpoint}`;
  }
  
  if (isClient()) {
    // Client-side: use relative URLs
    return endpoint;
  }
  
  // Server-side: use absolute URLs
  const baseUrl = env.VERCEL_URL
    ? `https://${env.VERCEL_URL}`
    : env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    
  return `${baseUrl}${endpoint}`;
}
