/**
 * Theme Utility Functions
 * Helper functions for theme management and manipulation
 */

import { DEFAULT_THEME_MODE, DEFAULT_THEME_VARIANT, getThemeConfig } from '@/app/config/themes.config';
import { CurrentTheme, ThemeMode, ThemeVariant } from '@/app/types/theme.types';

/**
 * Storage keys for theme persistence
 */
export const THEME_STORAGE_KEYS = {
  MODE: 'theme-mode',
  VARIANT: 'theme-variant',
  LEGACY: 'theme' // For backward compatibility
} as const;

/**
 * Get stored theme mode from localStorage
 */
export function getStoredThemeMode(): ThemeMode {
  if (typeof window === 'undefined') return DEFAULT_THEME_MODE;
  
  try {
    // Check new storage key first
    const storedMode = localStorage.getItem(THEME_STORAGE_KEYS.MODE) as ThemeMode;
    if (storedMode && (storedMode === 'light' || storedMode === 'dark')) {
      return storedMode;
    }
    
    // Fallback to legacy storage for backward compatibility
    const legacyTheme = localStorage.getItem(THEME_STORAGE_KEYS.LEGACY) as ThemeMode;
    if (legacyTheme && (legacyTheme === 'light' || legacyTheme === 'dark')) {
      return legacyTheme;
    }
  } catch (error) {
    console.warn('Failed to read theme mode from localStorage:', error);
  }
  
  return DEFAULT_THEME_MODE;
}

/**
 * Get stored theme variant from localStorage
 */
export function getStoredThemeVariant(): ThemeVariant {
  if (typeof window === 'undefined') return DEFAULT_THEME_VARIANT;
  
  try {
    const storedVariant = localStorage.getItem(THEME_STORAGE_KEYS.VARIANT) as ThemeVariant;
    if (storedVariant) {
      return storedVariant;
    }
  } catch (error) {
    console.warn('Failed to read theme variant from localStorage:', error);
  }
  
  return DEFAULT_THEME_VARIANT;
}

/**
 * Store theme mode in localStorage
 */
export function storeThemeMode(mode: ThemeMode): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem(THEME_STORAGE_KEYS.MODE, mode);
    // Also update legacy key for backward compatibility
    localStorage.setItem(THEME_STORAGE_KEYS.LEGACY, mode);
  } catch (error) {
    console.warn('Failed to store theme mode in localStorage:', error);
  }
}

/**
 * Store theme variant in localStorage
 */
export function storeThemeVariant(variant: ThemeVariant): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem(THEME_STORAGE_KEYS.VARIANT, variant);
  } catch (error) {
    console.warn('Failed to store theme variant in localStorage:', error);
  }
}

/**
 * Get system theme preference
 */
export function getSystemThemePreference(): ThemeMode {
  if (typeof window === 'undefined') return DEFAULT_THEME_MODE;
  
  try {
    const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
    return prefersDark ? 'dark' : 'light';
  } catch (error) {
    console.warn('Failed to detect system theme preference:', error);
    return DEFAULT_THEME_MODE;
  }
}

/**
 * Initialize theme from stored preferences or system preference
 */
export function initializeTheme(): CurrentTheme {
  const storedMode = getStoredThemeMode();
  const storedVariant = getStoredThemeVariant();

  // If no stored mode, use system preference
  const mode = storedMode || getSystemThemePreference();
  const variant = storedVariant;

  const resolvedMode = mode === 'system' ? 'dark' : mode; // Default system to dark for now
  const config = getThemeConfig(variant, resolvedMode as 'light' | 'dark');

  if (!config) {
    // Fallback to default theme
    const fallbackConfig = getThemeConfig(DEFAULT_THEME_VARIANT, DEFAULT_THEME_MODE);
    const fallbackTheme: CurrentTheme = {
      mode: DEFAULT_THEME_MODE,
      variant: DEFAULT_THEME_VARIANT,
      resolvedMode: DEFAULT_THEME_MODE as 'light' | 'dark',
      isDark: (DEFAULT_THEME_MODE as string) === 'dark',
      isLight: DEFAULT_THEME_MODE === 'light',
      isSystem: false,
      config: fallbackConfig!
    };

    // Immediately store the fallback theme for persistence
    storeThemeMode(DEFAULT_THEME_MODE);
    storeThemeVariant(DEFAULT_THEME_VARIANT);

    return fallbackTheme;
  }

  // Ensure the theme is stored for persistence
  storeThemeMode(mode);
  storeThemeVariant(variant);

  return {
    mode,
    variant,
    resolvedMode: resolvedMode,
    isDark: resolvedMode === 'dark',
    isLight: resolvedMode === 'light',
    isSystem: mode === 'system',
    config
  };
}

/**
 * Apply theme to document
 */
export function applyThemeToDocument(theme: CurrentTheme): void {
  if (typeof document === 'undefined') return;
  
  try {
    console.group('Theme Application Debug');
    console.log('Theme object:', JSON.parse(JSON.stringify(theme)));
    
    const { mode, variant, config } = theme;
    const root = document.documentElement;
    const resolvedMode = mode === 'system' ? 'dark' : mode;
    
    console.log(`Applying theme - Variant: ${variant}, Mode: ${mode}, Resolved Mode: ${resolvedMode}`);
    
    // Apply mode class (for backward compatibility)
    if (resolvedMode === 'dark') {
      console.log('Adding dark mode class');
      root.classList.add('dark');
    } else {
      console.log('Removing dark mode class');
      root.classList.remove('dark');
    }
    
    // Log current classes before removal
    console.log('Current classes before cleanup:', Array.from(root.classList));
    
    // Remove all existing theme classes
    const existingThemeClasses = Array.from(root.classList)
      .filter((className: string) => className.startsWith('theme-'));
    
    console.log('Removing theme classes:', existingThemeClasses);
    existingThemeClasses.forEach((className: string) => {
      root.classList.remove(className);
    });
    
    // Add new theme class
    const themeClass = `theme-${variant}-${resolvedMode}`;
    console.log('Adding theme class:', themeClass);
    root.classList.add(themeClass);
    
    // Set data attributes for CSS targeting
    console.log('Setting data attributes:', { 
      'data-theme': resolvedMode, 
      'data-theme-variant': variant 
    });
    root.setAttribute('data-theme', resolvedMode);
    root.setAttribute('data-theme-variant', variant);
    
    // Theme application complete - CSS variables are handled by globals.css
    // The .dark class and theme-{variant}-{mode} classes trigger the appropriate CSS rules
    console.log('Theme classes applied. CSS variables will be handled by stylesheet rules.');
    
    console.log("Applied theme. New class list:", root.classList);
    console.groupEnd();
  } catch (error) {
    console.error('Failed to apply theme to document:', error);
    console.groupEnd();
    
    // Fallback to default theme on error
    try {
      document.documentElement.classList.add('dark');
      document.documentElement.setAttribute('data-theme', 'dark');
      document.documentElement.setAttribute('data-theme-variant', 'default');
      console.warn('Falling back to default dark theme due to error');
    } catch (fallbackError) {
      console.error('Failed to apply fallback theme:', fallbackError);
    }
  }
}

/**
 * Create a current theme object
 */
export function createCurrentTheme(variant: ThemeVariant, mode: ThemeMode): CurrentTheme | null {
  const resolvedMode = mode === 'system' ? 'dark' : mode; // Default system to dark for now
  const config = getThemeConfig(variant, resolvedMode as 'light' | 'dark');
  
  if (!config) {
    return null;
  }
  
  return {
    mode,
    variant,
    resolvedMode: resolvedMode,
    isDark: resolvedMode === 'dark',
    isLight: resolvedMode === 'light',
    isSystem: mode === 'system',
    config
  };
}

/**
 * Toggle theme mode (light/dark) while preserving variant
 */
export function toggleThemeMode(currentTheme: CurrentTheme): CurrentTheme | null {
  const newMode = currentTheme.mode === 'light' ? 'dark' : 'light';
  return createCurrentTheme(currentTheme.variant, newMode);
}

/**
 * Check if a theme variant is valid
 */
export function isValidThemeVariant(variant: string): variant is ThemeVariant {
  const validVariants: ThemeVariant[] = ['default', 'blue', 'green', 'purple', 'orange', 'rose', 'slate', 'modern'];
  return validVariants.includes(variant as ThemeVariant);
}

/**
 * Check if a theme mode is valid
 */
export function isValidThemeMode(mode: string): mode is ThemeMode {
  return mode === 'light' || mode === 'dark';
}

