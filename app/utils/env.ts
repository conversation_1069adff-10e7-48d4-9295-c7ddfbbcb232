/**
 * OPTIMIZED ENVIRONMENT VARIABLE HANDLER WITH CONDITIONAL LOADING
 *
 * This module provides conditional environment variable loading based on NODE_ENV
 * with enhanced logging, validation, and fallback strategies.
 *
 * Key Features:
 * - Conditional environment loading (dev/prod/test)
 * - Enhanced MongoDB connection configuration per environment
 * - Comprehensive logging and debugging
 * - Fallback strategies for connection failures
 * - Type safety and validation
 * - Environment variable change tracking
 *
 * @version 2.0.0 - Optimized for conditional loading
 */

// Environment detection
const isServer = typeof window === 'undefined';
const isBuildTime = typeof process !== 'undefined' && process.env.NEXT_PHASE === 'phase-production-build';

// Environment variable change tracking
const envChangeLog: Array<{
  timestamp: string;
  variable: string;
  oldValue?: string;
  newValue?: string;
  environment: string;
}> = [];

// Enhanced environment variable accessor with logging
export function getEnvVar(key: string, fallback: string = ''): string {
  if (typeof process === 'undefined') {
    return fallback;
  }

  const value = process.env[key] || fallback;

  // Log environment variable access if debugging is enabled
  // Use direct process.env check to avoid circular dependency
  if (process.env.ENABLE_ENV_LOGGING === 'true' && isServer && !isBuildTime) {
    logEnvAccess(key, value, 'string');
  }

  return value;
}

// Enhanced boolean environment variable accessor with logging
export function getBooleanEnvVar(key: string, fallback: boolean = false): boolean {
  if (typeof process === 'undefined') {
    return fallback;
  }

  const value = process.env[key];
  const result = value === 'true' || value === '1' || (value === undefined ? fallback : false);

  // Log environment variable access if debugging is enabled (avoid recursion for ENABLE_ENV_LOGGING)
  // Use direct process.env check to avoid circular dependency
  if (key !== 'ENABLE_ENV_LOGGING' && process.env.ENABLE_ENV_LOGGING === 'true' && isServer && !isBuildTime) {
    logEnvAccess(key, String(result), 'boolean');
  }

  return result;
}

// Environment variable access logging
function logEnvAccess(key: string, value: string, type: string): void {
  const logEntry = {
    timestamp: new Date().toISOString(),
    variable: key,
    newValue: value === '' ? 'EMPTY' : (value.length > 50 ? `${value.substring(0, 50)}...` : value),
    environment: getEnvVar('NODE_ENV', 'unknown'),
    type
  };

  envChangeLog.push(logEntry);

  // Console log for immediate debugging
  console.log(`[ENV] ${logEntry.timestamp} - ${key}=${logEntry.newValue} (${type}) [${logEntry.environment}]`);
}

/**
 * Centralized environment configuration object
 * 
 * Server-only variables: Only accessible on server-side, return undefined on client
 * Client-accessible variables: NEXT_PUBLIC_* prefixed variables accessible everywhere
 */
export const env = {
  // === NODE ENVIRONMENT ===
  NODE_ENV: (process.env.NODE_ENV || 'development') as 'development' | 'production' | 'test',

  // === BUILD AND DEPLOYMENT ===
  NEXT_PHASE: isServer ? process.env.NEXT_PHASE : undefined,
  CI: isServer ? (process.env.CI === 'true' || process.env.CI === '1') : false,
  VERCEL_URL: isServer ? process.env.VERCEL_URL : undefined,

  // === DATABASE CONFIGURATION (Server-only) ===
  MONGODB_URI: isServer ? process.env.MONGODB_URI : undefined,
  MONGODB_URI_PROD: isServer ? process.env.MONGODB_URI_PROD : undefined,
  MONGODB_URI_DEV: isServer ? process.env.MONGODB_URI_DEV : undefined,
  MONGODB_DB_NAME: isServer ? (process.env.MONGODB_DB_NAME || 'IMS') : undefined,

  // === DATABASE MONITORING (Server-only) ===
  ENABLE_DB_MONITORING: isServer ? (process.env.ENABLE_DB_MONITORING === 'true' || process.env.ENABLE_DB_MONITORING === '1') : false,
  ENABLE_DB_PROFILER: isServer ? (process.env.ENABLE_DB_PROFILER === 'true' || process.env.ENABLE_DB_PROFILER === '1') : false,
  SKIP_MONGODB_CONNECTION: isServer ? (process.env.SKIP_MONGODB_CONNECTION === 'true' || process.env.SKIP_MONGODB_CONNECTION === '1') : false,

  // === AUTHENTICATION (Server-only) - OPTIONAL ===
  // Note: This application uses MockAuthContext, so NextAuth is not required
  NEXTAUTH_SECRET: isServer ? process.env.NEXTAUTH_SECRET : undefined,
  NEXTAUTH_URL: isServer ? process.env.NEXTAUTH_URL : undefined,

  // === CLIENT-ACCESSIBLE VARIABLES (NEXT_PUBLIC_*) ===
  NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000',

  // === OPTIONAL MONITORING (Disabled by default) ===
  NEXT_PUBLIC_SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN,
  NEXT_PUBLIC_ENABLE_SENTRY_DEV: process.env.NEXT_PUBLIC_ENABLE_SENTRY_DEV === 'true' || process.env.NEXT_PUBLIC_ENABLE_SENTRY_DEV === '1',

  // === DEVELOPMENT TOOLS ===
  MCP_POSTMAN_PORT: isServer ? (process.env.MCP_POSTMAN_PORT || '3366') : undefined,

  // === SERVER CONFIGURATION ===
  PORT: isServer ? (process.env.PORT || '3000') : undefined,
  HOST: isServer ? (process.env.HOST || 'localhost') : undefined,
} as const;

/**
 * Get the appropriate MongoDB URI based on environment
 * Handles the logic for selecting between production, development, and fallback URIs
 */
export function getMongoDBUri(): string | undefined {
  if (!isServer) {
    return undefined;
  }

  // During build time, return a safe fallback to prevent build errors
  if (isBuildTime) {
    return process.env.MONGODB_URI || 'mongodb://localhost:27017/trend_ims_build';
  }

  const nodeEnv = process.env.NODE_ENV || 'development';

  // In production, prefer MONGODB_URI_PROD, fallback to MONGODB_URI
  if (nodeEnv === 'production') {
    return process.env.MONGODB_URI_PROD || process.env.MONGODB_URI;
  }

  // In development, prefer MONGODB_URI_DEV, fallback to MONGODB_URI
  return process.env.MONGODB_URI_DEV || process.env.MONGODB_URI;
}

/**
 * Get the base URL for API calls with proper fallbacks
 * Handles different deployment environments (Vercel, local, etc.)
 */
export function getApiBaseUrl(): string {
  // During build time, return a safe fallback
  if (isBuildTime) {
    return 'http://localhost:3000';
  }

  // On server side, check for Vercel URL first
  if (isServer && process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }

  // Use the configured API base URL (not APP_URL)
  return process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';
}

/**
 * Create a full API URL by combining base URL with endpoint
 */
export function getApiUrl(endpoint: string = ''): string {
  const baseUrl = getApiBaseUrl();
  return `${baseUrl.replace(/\/$/, '')}/${endpoint.replace(/^\//, '')}`;
}

/**
 * Validate that required environment variables are set
 * Call this during application startup to catch configuration issues early
 */
export function validateEnvironment(): { isValid: boolean; errors: string[]; warnings: string[] } {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Only validate server-side variables when on server
  if (isServer && !isBuildTime) {
    // Required variables
    if (!getMongoDBUri()) {
      errors.push('MongoDB URI is not configured. Set MONGODB_URI or environment-specific variants.');
    }

    // Optional variables (warnings only)
    if (!env.NEXTAUTH_SECRET) {
      warnings.push('NEXTAUTH_SECRET is not set. Authentication features will use mock implementation.');
    }

    if (!env.NEXT_PUBLIC_SENTRY_DSN) {
      warnings.push('NEXT_PUBLIC_SENTRY_DSN is not set. Error monitoring is disabled.');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Debug helper to log current environment configuration
 * Only logs in development mode and on server side
 */
export function logEnvironmentInfo(): void {
  const nodeEnv = process.env.NODE_ENV || 'development';
  if (nodeEnv === 'development' && isServer) {
    console.log('🔧 Environment Configuration:');
    console.log(`  - Node Environment: ${nodeEnv}`);
    console.log(`  - Server Side: ${isServer}`);
    console.log(`  - Build Time: ${isBuildTime}`);
    console.log(`  - MongoDB URI: ${getMongoDBUri() ? 'SET' : 'NOT SET'}`);
    console.log(`  - API Base URL: ${getApiBaseUrl()}`);
    console.log(`  - Authentication: Mock (NextAuth disabled)`);
    console.log(`  - Monitoring: ${process.env.NEXT_PUBLIC_SENTRY_DSN ? 'Sentry (disabled)' : 'Disabled'}`);
  }
}

/**
 * ENHANCED CONDITIONAL ENVIRONMENT CONFIGURATION
 *
 * These functions provide environment-specific configuration with enhanced MongoDB settings
 * and conditional URL handling based on NODE_ENV.
 */

// Environment-specific MongoDB configuration with enhanced settings
export function getEnhancedMongoDBConfig() {
  const nodeEnv = process.env.NODE_ENV || 'development';
  // Use direct process.env access to avoid circular dependencies
  const mongoUri = nodeEnv === 'production'
    ? (process.env.MONGODB_URI_PROD || process.env.MONGODB_URI || '')
    : (process.env.MONGODB_URI_DEV || process.env.MONGODB_URI || '');

  const baseConfig = {
    uri: mongoUri,
    dbName: process.env.MONGODB_DB_NAME || 'IMS',
  };

  if (nodeEnv === 'production') {
    return {
      ...baseConfig,
      connectTimeoutMS: parseInt(process.env.MONGODB_PROD_CONNECT_TIMEOUT || '15000'),
      serverSelectionTimeoutMS: parseInt(process.env.MONGODB_PROD_SERVER_SELECTION_TIMEOUT || '15000'),
      socketTimeoutMS: parseInt(process.env.MONGODB_PROD_SOCKET_TIMEOUT || '30000'),
      maxPoolSize: parseInt(process.env.MONGODB_PROD_MAX_POOL_SIZE || '10'),
      minPoolSize: parseInt(process.env.MONGODB_PROD_MIN_POOL_SIZE || '2'),
      retryWrites: true,
      retryReads: true,
      // Production-specific options for better reliability
      heartbeatFrequencyMS: 30000,
      maxIdleTimeMS: 300000,
    };
  } else {
    // Development settings - more aggressive timeouts for faster feedback
    return {
      ...baseConfig,
      connectTimeoutMS: parseInt(process.env.MONGODB_DEV_CONNECT_TIMEOUT || '5000'),
      serverSelectionTimeoutMS: parseInt(process.env.MONGODB_DEV_SERVER_SELECTION_TIMEOUT || '5000'),
      socketTimeoutMS: parseInt(process.env.MONGODB_DEV_SOCKET_TIMEOUT || '8000'),
      maxPoolSize: parseInt(process.env.MONGODB_DEV_MAX_POOL_SIZE || '5'),
      minPoolSize: parseInt(process.env.MONGODB_DEV_MIN_POOL_SIZE || '1'),
      retryWrites: true,
      retryReads: true,
      // Development-specific options for faster iteration
      heartbeatFrequencyMS: 10000,
      maxIdleTimeMS: 60000,
    };
  }
}

// Environment-specific server configuration
export function getEnhancedServerConfig() {
  const nodeEnv = process.env.NODE_ENV || 'development';
  if (nodeEnv === 'production') {
    return {
      port: process.env.PORT || '3000',
      host: process.env.HOST || 'localhost',
      appUrl: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
      apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000',
      environment: 'production' as const,
    };
  } else {
    // Development settings
    return {
      port: process.env.PORT || '5174',
      host: process.env.HOST || 'localhost',
      appUrl: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:5174',
      apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5174',
      environment: 'development' as const,
    };
  }
}

// Enhanced MongoDB URI selection with better logging and error handling
export function getEnhancedMongoDBUri(): string {
  // Skip database connections during build phase
  if (isBuildTime) {
    if (process.env.ENABLE_DB_CONNECTION_DEBUG === 'true') {
      console.log('[MongoDB][BUILD] Skipping MongoDB URI selection during build phase');
    }
    return '';
  }

  const nodeEnv = process.env.NODE_ENV || 'development';

  // Log the environment for debugging
  if (process.env.ENABLE_DB_CONNECTION_DEBUG === 'true') {
    console.log(`[MongoDB][ENHANCED] Environment: ${nodeEnv}`);
  }

  // Use the single MONGODB_URI for all environments (user's optimization)
  const selectedUri = getMongoDBUri() || 'mongodb://localhost:27017/trend_ims';

  if (process.env.ENABLE_DB_CONNECTION_DEBUG === 'true') {
    console.log(`[MongoDB][ENHANCED] Using ${nodeEnv} configuration`);

    // Log the connection string (safely, without exposing credentials)
    const safeUri = selectedUri.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@');
    console.log(`[MongoDB][ENHANCED] Connection string loaded: ${safeUri}`);

    // Log basic configuration info without calling getEnhancedMongoDBConfig to avoid circular dependency
    console.log(`[MongoDB][ENHANCED] Environment: ${nodeEnv}, URI configured: ${selectedUri ? 'YES' : 'NO'}`);
  }

  return selectedUri;
}

// Environment variable change logging utility
export function getEnvironmentChangeLog() {
  return [...envChangeLog];
}

// Clear environment change log
export function clearEnvironmentChangeLog() {
  envChangeLog.length = 0;
}

// Export type for TypeScript consumers
export type Environment = typeof env;
export type EnhancedMongoDBConfig = ReturnType<typeof getEnhancedMongoDBConfig>;
export type EnhancedServerConfig = ReturnType<typeof getEnhancedServerConfig>;
