import { 
  WarehouseApiResponse, 
  WarehouseApiData, 
  CreateWarehouseRequest, 
  UpdateWarehouseRequest,
  transformFormDataToApiRequest 
} from '@/app/components/forms/WarehouseForm/types';
import { getApiUrl } from './env';

/**
 * API utility functions for warehouse operations
 * Provides a consistent interface for all warehouse-related API calls
 */

/**
 * Fetch all warehouses with optional pagination and filtering
 */
export async function fetchWarehouses(options: {
  page?: number;
  limit?: number;
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
} = {}): Promise<WarehouseApiResponse> {
  const {
    page = 1,
    limit = 20,
    sortField = 'name',
    sortOrder = 'asc',
    search = ''
  } = options;

  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    sortField,
    sortOrder,
    ...(search && { search })
  });

  const url = getApiUrl(`/api/warehouses?${params.toString()}`);
  
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch warehouses: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Fetch a single warehouse by location ID
 */
export async function fetchWarehouse(locationId: string): Promise<WarehouseApiResponse> {
  const url = getApiUrl(`/api/warehouses/${encodeURIComponent(locationId)}`);
  
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch warehouse: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Create a new warehouse
 */
export async function createWarehouse(warehouseData: CreateWarehouseRequest): Promise<WarehouseApiResponse> {
  const url = getApiUrl('/api/warehouses');
  
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(warehouseData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `Failed to create warehouse: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Update an existing warehouse
 */
export async function updateWarehouse(
  locationId: string, 
  warehouseData: UpdateWarehouseRequest
): Promise<WarehouseApiResponse> {
  const url = getApiUrl(`/api/warehouses/${encodeURIComponent(locationId)}`);
  
  const response = await fetch(url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(warehouseData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `Failed to update warehouse: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Delete a warehouse
 */
export async function deleteWarehouse(locationId: string): Promise<WarehouseApiResponse> {
  const url = getApiUrl(`/api/warehouses/${encodeURIComponent(locationId)}`);
  
  const response = await fetch(url, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `Failed to delete warehouse: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Warehouse API service class for more organized usage
 */
export class WarehouseApiService {
  /**
   * Get all warehouses
   */
  static async getAll(options?: Parameters<typeof fetchWarehouses>[0]) {
    return fetchWarehouses(options);
  }

  /**
   * Get warehouse by location ID
   */
  static async getById(locationId: string) {
    return fetchWarehouse(locationId);
  }

  /**
   * Create new warehouse
   */
  static async create(warehouseData: CreateWarehouseRequest) {
    return createWarehouse(warehouseData);
  }

  /**
   * Update existing warehouse
   */
  static async update(locationId: string, warehouseData: UpdateWarehouseRequest) {
    return updateWarehouse(locationId, warehouseData);
  }

  /**
   * Delete warehouse
   */
  static async delete(locationId: string) {
    return deleteWarehouse(locationId);
  }
}

/**
 * Hook-like function for warehouse operations with error handling
 */
export function useWarehouseApi() {
  const handleApiCall = async <T>(
    apiCall: () => Promise<T>,
    errorMessage: string
  ): Promise<T> => {
    try {
      return await apiCall();
    } catch (error) {
      console.error(errorMessage, error);
      throw error instanceof Error ? error : new Error(errorMessage);
    }
  };

  return {
    fetchWarehouses: (options?: Parameters<typeof fetchWarehouses>[0]) =>
      handleApiCall(() => fetchWarehouses(options), 'Failed to fetch warehouses'),
    
    fetchWarehouse: (locationId: string) =>
      handleApiCall(() => fetchWarehouse(locationId), 'Failed to fetch warehouse'),
    
    createWarehouse: (data: CreateWarehouseRequest) =>
      handleApiCall(() => createWarehouse(data), 'Failed to create warehouse'),
    
    updateWarehouse: (locationId: string, data: UpdateWarehouseRequest) =>
      handleApiCall(() => updateWarehouse(locationId, data), 'Failed to update warehouse'),
    
    deleteWarehouse: (locationId: string) =>
      handleApiCall(() => deleteWarehouse(locationId), 'Failed to delete warehouse'),
  };
}
