import { uiEnhancementConfig } from '../config/ui-enhancement-plan';

/**
 * Check if accessibility mode is enabled in the UI enhancement configuration
 * @returns Boolean indicating if accessibility mode is enabled
 */
export function isAccessibilityModeEnabled(): boolean {
  return uiEnhancementConfig.globalSettings.accessibilityMode;
}

/**
 * Get the appropriate ARIA attributes for a component based on its type and state
 * @param componentType The type of component (e.g., 'button', 'dialog', 'menu')
 * @param state The current state of the component
 * @returns Object containing appropriate ARIA attributes
 */
export function getAriaAttributes(
  componentType: 'button' | 'dialog' | 'menu' | 'form' | 'table' | 'alert' | 'status' | 'tab',
  state: Record<string, any> = {}
): Record<string, string | boolean> {
  const baseAttributes: Record<string, string | boolean> = {};
  
  switch (componentType) {
    case 'button':
      if (state.isExpanded !== undefined) {
        baseAttributes['aria-expanded'] = state.isExpanded;
      }
      if (state.isPressed !== undefined) {
        baseAttributes['aria-pressed'] = state.isPressed;
      }
      if (state.controls) {
        baseAttributes['aria-controls'] = state.controls;
      }
      if (state.label) {
        baseAttributes['aria-label'] = state.label;
      }
      if (state.isDisabled) {
        baseAttributes['aria-disabled'] = true;
      }
      break;
      
    case 'dialog':
      baseAttributes['role'] = 'dialog';
      baseAttributes['aria-modal'] = true;
      if (state.title) {
        baseAttributes['aria-labelledby'] = state.title;
      }
      if (state.description) {
        baseAttributes['aria-describedby'] = state.description;
      }
      break;
      
    case 'menu':
      baseAttributes['role'] = 'menu';
      if (state.label) {
        baseAttributes['aria-label'] = state.label;
      }
      if (state.expanded !== undefined) {
        baseAttributes['aria-expanded'] = state.expanded;
      }
      break;
      
    case 'form':
      if (state.hasError) {
        baseAttributes['aria-invalid'] = true;
      }
      if (state.errorMessage) {
        baseAttributes['aria-errormessage'] = state.errorMessage;
      }
      if (state.isRequired) {
        baseAttributes['aria-required'] = true;
      }
      break;
      
    case 'table':
      baseAttributes['role'] = 'table';
      if (state.label) {
        baseAttributes['aria-label'] = state.label;
      }
      if (state.sortColumn) {
        baseAttributes['aria-sort'] = state.sortDirection || 'none';
      }
      break;
      
    case 'alert':
      baseAttributes['role'] = 'alert';
      baseAttributes['aria-live'] = 'assertive';
      baseAttributes['aria-atomic'] = true;
      break;
      
    case 'status':
      baseAttributes['role'] = 'status';
      baseAttributes['aria-live'] = 'polite';
      baseAttributes['aria-atomic'] = true;
      break;
      
    case 'tab':
      baseAttributes['role'] = 'tab';
      if (state.selected !== undefined) {
        baseAttributes['aria-selected'] = state.selected;
      }
      if (state.controls) {
        baseAttributes['aria-controls'] = state.controls;
      }
      break;
  }
  
  return baseAttributes;
}

/**
 * Generate a unique ID for accessibility purposes
 * @param prefix Prefix for the ID
 * @returns Unique ID string
 */
export function generateAccessibleId(prefix: string): string {
  return `${prefix}-${Math.random().toString(36).substring(2, 9)}`;
}

/**
 * Check if animations should be reduced based on user preferences and accessibility settings
 * @returns Boolean indicating if animations should be reduced
 */
export function shouldReduceAnimations(): boolean {
  // Check if we're in a browser environment
  if (typeof window === 'undefined') return false;
  
  // Check if the user has requested reduced motion
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  
  // If accessibility mode is enabled or user prefers reduced motion, reduce animations
  return isAccessibilityModeEnabled() || prefersReducedMotion;
}

/**
 * Check if high contrast mode should be enabled based on user preferences
 * @returns Boolean indicating if high contrast mode should be enabled
 */
export function shouldUseHighContrast(): boolean {
  // Check if we're in a browser environment
  if (typeof window === 'undefined') return false;

  // Check if the user has requested high contrast
  return window.matchMedia('(prefers-contrast: more)').matches;
}

/**
 * Get comprehensive accessibility attributes for a component
 * @param options Configuration options for accessibility attributes
 * @returns Object containing accessibility attributes
 */
export function getAccessibilityAttributes(options: {
  role?: string;
  label?: string | undefined;
  labelledBy?: string | undefined;
  describedBy?: string | undefined;
  expanded?: boolean;
  selected?: boolean;
  disabled?: boolean;
  required?: boolean;
  invalid?: boolean;
  live?: 'polite' | 'assertive' | 'off';
  atomic?: boolean;
} = {}): Record<string, string | boolean> {
  const attributes: Record<string, string | boolean> = {};

  if (options.role) attributes.role = options.role;
  if (options.label) attributes['aria-label'] = options.label;
  if (options.labelledBy) attributes['aria-labelledby'] = options.labelledBy;
  if (options.describedBy) attributes['aria-describedby'] = options.describedBy;
  if (options.expanded !== undefined) attributes['aria-expanded'] = options.expanded;
  if (options.selected !== undefined) attributes['aria-selected'] = options.selected;
  if (options.disabled !== undefined) attributes['aria-disabled'] = options.disabled;
  if (options.required !== undefined) attributes['aria-required'] = options.required;
  if (options.invalid !== undefined) attributes['aria-invalid'] = options.invalid;
  if (options.live) attributes['aria-live'] = options.live;
  if (options.atomic !== undefined) attributes['aria-atomic'] = options.atomic;

  return attributes;
}
