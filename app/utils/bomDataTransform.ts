import { TreeItem, TreeItemIndex } from 'react-complex-tree';

/**
 * Component item interface for BOM viewer
 */
export interface ModalComponentItem {
  part: {
    _id: string;
    name: string;
    businessName?: string | null; // NEW FIELD: Human-readable business name for the part
    partNumber?: string;
    description?: string;
    category?: string;
    type?: string;
    current_stock?: number;
    is_assembly?: boolean;
  };
  quantity: number;
  level?: number;
  parentId?: string | null;
  partId?: string;
}

/**
 * Tree data structure for React Complex Tree
 */
export interface BomTreeItem extends TreeItem {
  index: TreeItemIndex;
  isFolder: boolean;
  children: TreeItemIndex[];
  data: {
    part: ModalComponentItem['part'];
    quantity: number;
    level: number;
    parentId?: string | null;
    partId?: string;
    displayName: string;
    displayDetails: string;
  };
}

/**
 * Tree data provider structure
 */
export type BomTreeData = Record<TreeItemIndex, BomTreeItem>;

/**
 * Transform ModalComponentItem[] to React Complex Tree format
 */
export function transformBomDataToTree(
  components: ModalComponentItem[],
  rootId: string = 'root'
): BomTreeData {
  const treeData: BomTreeData = {};
  const childrenMap = new Map<string, TreeItemIndex[]>();

  // First pass: Create all tree items and build children map
  components.forEach((component, index) => {
    const itemId = component.part._id || `item-${index}`;
    const parentKey = component.parentId || rootId;

    // Create tree item (isFolder will be determined later based on actual children)
    const treeItem: BomTreeItem = {
      index: itemId,
      isFolder: false, // Will be updated later based on actual children
      children: [],
      data: {
        part: component.part,
        quantity: component.quantity,
        level: component.level || 0,
        parentId: component.parentId ?? null,
        partId: component.partId ?? '',
        displayName: component.part.name,
        displayDetails: createDisplayDetails(component),
      },
    };

    treeData[itemId] = treeItem;

    // Build children map
    if (!childrenMap.has(parentKey)) {
      childrenMap.set(parentKey, []);
    }
    childrenMap.get(parentKey)!.push(itemId);
  });

  // Second pass: Assign children to their parents and mark items with children as folders
  Object.keys(treeData).forEach((itemId) => {
    const children = childrenMap.get(itemId) || [];
    treeData[itemId]!.children = children;
    // Mark as folder if it has children OR if it's an assembly
    treeData[itemId]!.isFolder = children.length > 0 || treeData[itemId]!.data.part.is_assembly || false;
  });

  // Create root item if it doesn't exist
  if (!treeData[rootId]) {
    const rootChildren = childrenMap.get(rootId) || [];
    treeData[rootId] = {
      index: rootId,
      isFolder: true,
      children: rootChildren,
      data: {
        part: {
          _id: rootId,
          name: 'Bill of Materials',
          is_assembly: true,
        },
        quantity: 1,
        level: 0,
        displayName: 'Bill of Materials',
        displayDetails: `${rootChildren.length} components`,
      },
    };
  }

  return treeData;
}

/**
 * Create display details string for a component
 */
function createDisplayDetails(component: ModalComponentItem): string {
  const details: string[] = [];
  
  if (component.part.partNumber) {
    details.push(`PN: ${component.part.partNumber}`);
  }
  
  details.push(`Qty: ${component.quantity}`);
  
  if (component.part.current_stock !== undefined) {
    details.push(`Stock: ${component.part.current_stock}`);
  }
  
  if (component.part.category) {
    details.push(component.part.category);
  }

  return details.join(' • ');
}

/**
 * Transform flat BOM data to hierarchical structure
 * This handles cases where BOM data comes as a flat array but needs hierarchy
 */
export function buildHierarchicalBomData(
  components: ModalComponentItem[]
): ModalComponentItem[] {
  // Sort by level to ensure proper hierarchy
  const sortedComponents = [...components].sort((a, b) => {
    const levelA = a.level || 0;
    const levelB = b.level || 0;
    return levelA - levelB;
  });

  // Group by parent ID
  const parentGroups = new Map<string | null, ModalComponentItem[]>();
  
  sortedComponents.forEach((component) => {
    const parentKey = component.parentId || null;
    if (!parentGroups.has(parentKey)) {
      parentGroups.set(parentKey, []);
    }
    parentGroups.get(parentKey)!.push(component);
  });

  // Build hierarchical structure
  const result: ModalComponentItem[] = [];
  
  function addComponentsRecursively(parentId: string | null, level: number = 0) {
    const children = parentGroups.get(parentId) || [];
    children.forEach((component) => {
      // Ensure level is set correctly
      component.level = level;
      result.push(component);

      // Recursively add children if this component has children (based on parentId references)
      const hasChildren = parentGroups.has(component.part._id);
      if (hasChildren) {
        addComponentsRecursively(component.part._id, level + 1);
      }
    });
  }

  // Start with root level components (parentId is null or "root-0")
  // Check if we have components with parentId "root-0" (from BOM integration helper)
  const hasRootComponents = sortedComponents.some(c => c.parentId === "root-0");
  const rootParentId = hasRootComponents ? "root-0" : null;

  addComponentsRecursively(rootParentId, 0);

  return result;
}

/**
 * Get tree item title for React Complex Tree
 */
export function getTreeItemTitle(item: BomTreeItem): string {
  return item.data.displayName;
}

/**
 * Get tree item subtitle/details for React Complex Tree
 */
export function getTreeItemDetails(item: BomTreeItem): string {
  return item.data.displayDetails;
}

/**
 * Check if a tree item can have children (is expandable)
 */
export function canHaveChildren(item: BomTreeItem): boolean {
  return item.isFolder && item.data.part.is_assembly === true;
}

/**
 * Lazy loading support: Get children for a specific item
 * This would be used for assemblies that need to load their components on demand
 */
export async function loadChildrenForItem(
  itemId: TreeItemIndex,
  assemblyId: string,
  loadAssemblyComponents: (id: string) => Promise<ModalComponentItem[]>
): Promise<ModalComponentItem[]> {
  try {
    const components = await loadAssemblyComponents(assemblyId);
    return components.map((component) => ({
      ...component,
      parentId: itemId.toString(),
    }));
  } catch (error) {
    console.error('Failed to load children for item:', itemId, error);
    return [];
  }
}

/**
 * Search functionality for BOM tree
 */
export function searchBomTree(
  treeData: BomTreeData,
  searchTerm: string
): TreeItemIndex[] {
  const results: TreeItemIndex[] = [];
  const lowerSearchTerm = searchTerm.toLowerCase();

  Object.values(treeData).forEach((item) => {
    const { part } = item.data;
    const searchableText = [
      part.name,
      part.partNumber,
      part.description,
      part.category,
    ]
      .filter(Boolean)
      .join(' ')
      .toLowerCase();

    if (searchableText.includes(lowerSearchTerm)) {
      results.push(item.index);
    }
  });

  return results;
}

/**
 * Get all parent items for a given item (breadcrumb functionality)
 */
export function getItemPath(
  treeData: BomTreeData,
  itemId: TreeItemIndex
): BomTreeItem[] {
  const path: BomTreeItem[] = [];
  let currentItem = treeData[itemId];

  while (currentItem) {
    path.unshift(currentItem);
    const parentId = currentItem.data.parentId;
    if (!parentId || parentId === 'root') break;
    currentItem = treeData[parentId];
  }

  return path;
}

/**
 * Calculate total quantity for an item including all its children
 */
export function calculateTotalQuantity(
  treeData: BomTreeData,
  itemId: TreeItemIndex
): number {
  const item = treeData[itemId];
  if (!item) return 0;

  let total = item.data.quantity;

  // Add quantities from all children recursively
  item.children.forEach((childId) => {
    total += calculateTotalQuantity(treeData, childId);
  });

  return total;
}
