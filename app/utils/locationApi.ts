import { getApiUrl } from './env';

/**
 * API utility functions for location operations
 * Provides a consistent interface for all location-related API calls
 */

export interface LocationApiResponse {
  data: any;
  error: string | null;
  meta?: any;
}

export interface CreateLocationRequest {
  warehouseId: string;
  name: string;
  description?: string | null;
  locationType: 'Bin' | 'Shelf' | 'Floor Area' | 'Staging' | 'Production Area' | 'Quality Control' | 'Shipping' | 'Receiving';
  capacity?: {
    maxWeightKg?: number;
    volumeM3?: number;
  } | null;
  isActive?: boolean;
}

export interface UpdateLocationRequest extends Partial<CreateLocationRequest> {
  // All fields are optional for updates
}

/**
 * Fetch all locations with optional filtering
 */
export async function fetchLocations(options?: {
  warehouseId?: string;
  activeOnly?: boolean;
  locationType?: string;
  page?: number;
  limit?: number;
}): Promise<LocationApiResponse> {
  const params = new URLSearchParams();
  
  if (options?.warehouseId) params.append('warehouseId', options.warehouseId);
  if (options?.activeOnly !== undefined) params.append('activeOnly', options.activeOnly.toString());
  if (options?.locationType) params.append('locationType', options.locationType);
  if (options?.page) params.append('page', options.page.toString());
  if (options?.limit) params.append('limit', options.limit.toString());

  const url = getApiUrl(`/api/locations?${params.toString()}`);
  
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch locations: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Fetch locations for a specific warehouse
 */
export async function fetchWarehouseLocations(
  warehouseId: string,
  options?: {
    activeOnly?: boolean;
    locationType?: string;
  }
): Promise<LocationApiResponse> {
  const params = new URLSearchParams();
  
  if (options?.activeOnly !== undefined) params.append('activeOnly', options.activeOnly.toString());
  if (options?.locationType) params.append('locationType', options.locationType);

  const url = getApiUrl(`/api/warehouses/${encodeURIComponent(warehouseId)}/locations?${params.toString()}`);
  
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch warehouse locations: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Fetch a single location by ID
 */
export async function fetchLocation(locationId: string): Promise<LocationApiResponse> {
  const url = getApiUrl(`/api/locations/${encodeURIComponent(locationId)}`);
  
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch location: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Create a new location
 */
export async function createLocation(locationData: CreateLocationRequest): Promise<LocationApiResponse> {
  const url = getApiUrl('/api/locations');
  
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(locationData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `Failed to create location: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Update an existing location
 */
export async function updateLocation(
  locationId: string, 
  locationData: UpdateLocationRequest
): Promise<LocationApiResponse> {
  const url = getApiUrl(`/api/locations/${encodeURIComponent(locationId)}`);
  
  const response = await fetch(url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(locationData),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `Failed to update location: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Delete (deactivate) a location
 */
export async function deleteLocation(locationId: string): Promise<LocationApiResponse> {
  const url = getApiUrl(`/api/locations/${encodeURIComponent(locationId)}`);
  
  const response = await fetch(url, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `Failed to delete location: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

/**
 * Location API service class for more organized usage
 */
export class LocationApiService {
  /**
   * Get all locations
   */
  static async getAll(options?: Parameters<typeof fetchLocations>[0]) {
    return fetchLocations(options);
  }

  /**
   * Get locations for a specific warehouse
   */
  static async getByWarehouse(warehouseId: string, options?: Parameters<typeof fetchWarehouseLocations>[1]) {
    return fetchWarehouseLocations(warehouseId, options);
  }

  /**
   * Get location by ID
   */
  static async getById(locationId: string) {
    return fetchLocation(locationId);
  }

  /**
   * Create new location
   */
  static async create(locationData: CreateLocationRequest) {
    return createLocation(locationData);
  }

  /**
   * Update existing location
   */
  static async update(locationId: string, locationData: UpdateLocationRequest) {
    return updateLocation(locationId, locationData);
  }

  /**
   * Delete location
   */
  static async delete(locationId: string) {
    return deleteLocation(locationId);
  }
}

export default LocationApiService;
