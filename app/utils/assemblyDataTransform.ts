/**
 * Assembly Data Transformation Utilities
 * 
 * This module provides utilities for transforming assembly data between frontend
 * and backend formats, with special handling for hierarchical parts structures.
 */

export interface HierarchicalPartData {
  partId: string | { _id: string; [key: string]: any }; // Support both string and populated object
  partDetails?: { _id: string; [key: string]: any }; // Support populated part details
  quantityRequired: number;
  unitOfMeasure?: string;
  children?: HierarchicalPartData[];
}

export interface TransformedPartData {
  partId: string; // Always string for API submission
  quantityRequired: number;
  unitOfMeasure: string;
  children?: TransformedPartData[];
}

/**
 * Recursively transforms hierarchical parts data for API submission
 * Preserves the children structure while ensuring proper field mapping
 * 
 * @param parts - Array of hierarchical part data from the form
 * @returns Transformed parts data ready for API submission
 */
export function transformHierarchicalPartsForApi(parts: HierarchicalPartData[]): TransformedPartData[] {
  if (!Array.isArray(parts)) {
    console.warn('[assemblyDataTransform] transformHierarchicalPartsForApi: parts is not an array, returning empty array');
    return [];
  }

  return parts.map((part, index) => {
    // Extract string partId from either string or populated object
    let partIdString: string;
    if (typeof part.partId === 'string') {
      partIdString = part.partId;
    } else if (typeof part.partId === 'object' && part.partId !== null && part.partId._id) {
      partIdString = part.partId._id;
    } else {
      console.warn(`[assemblyDataTransform] Part at index ${index} missing valid partId, skipping`);
      return null;
    }

    const transformedPart: TransformedPartData = {
      partId: partIdString,
      quantityRequired: part.quantityRequired || 1,
      unitOfMeasure: String(part.unitOfMeasure || 'PCS')
    };

    // Recursively transform children if they exist
    if (part.children && Array.isArray(part.children) && part.children.length > 0) {
      console.log(`[assemblyDataTransform] Processing ${part.children.length} children for part ${part.partId}`);
      transformedPart.children = transformHierarchicalPartsForApi(part.children);
      
      // Only include children if the transformation resulted in valid children
      if (transformedPart.children.length === 0) {
        delete transformedPart.children;
      }
    }

    return transformedPart;
  }).filter((part): part is TransformedPartData => part !== null); // Remove any null entries
}

/**
 * Recursively counts all parts in a hierarchical structure
 * Includes both parent and child parts in the count
 * Handles both populated and unpopulated partId structures from API
 *
 * @param parts - Array of hierarchical part data
 * @returns Total count of all parts including children
 */
export function countHierarchicalParts(parts: HierarchicalPartData[]): number {
  if (!Array.isArray(parts)) {
    return 0;
  }

  let totalCount = 0;

  for (const part of parts) {
    // Validate that the part has a valid partId (either string or populated object)
    const hasValidPartId = part && (
      (typeof part.partId === 'string' && part.partId.length > 0) ||
      (typeof part.partId === 'object' && part.partId !== null && part.partId._id)
    );

    if (hasValidPartId) {
      // Count the current part only if it has a valid partId
      totalCount += 1;

      // Recursively count children
      if (part.children && Array.isArray(part.children)) {
        totalCount += countHierarchicalParts(part.children);
      }
    } else {
      console.warn('[countHierarchicalParts] Skipping part with invalid partId:', part);
    }
  }

  return totalCount;
}

/**
 * Validates hierarchical parts data structure
 * Ensures all required fields are present and properly formatted
 * 
 * @param parts - Array of hierarchical part data to validate
 * @returns Object with validation result and any error messages
 */
export function validateHierarchicalParts(parts: HierarchicalPartData[]): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!Array.isArray(parts)) {
    errors.push('Parts data must be an array');
    return { isValid: false, errors };
  }

  function validatePartRecursively(part: HierarchicalPartData, path: string): void {
    // Validate partId
    if (!part.partId || typeof part.partId !== 'string') {
      errors.push(`${path}: partId is required and must be a string`);
    }

    // Validate quantityRequired
    if (typeof part.quantityRequired !== 'number' || part.quantityRequired <= 0) {
      errors.push(`${path}: quantityRequired must be a positive number`);
    }

    // Validate unitOfMeasure if provided
    if (part.unitOfMeasure !== undefined && typeof part.unitOfMeasure !== 'string') {
      errors.push(`${path}: unitOfMeasure must be a string if provided`);
    }

    // Recursively validate children
    if (part.children) {
      if (!Array.isArray(part.children)) {
        errors.push(`${path}: children must be an array if provided`);
      } else {
        part.children.forEach((child, index) => {
          validatePartRecursively(child, `${path}.children[${index}]`);
        });
      }
    }
  }

  parts.forEach((part, index) => {
    validatePartRecursively(part, `parts[${index}]`);
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Flattens hierarchical parts data into a flat array
 * Useful for operations that need to work with all parts regardless of hierarchy
 * 
 * @param parts - Array of hierarchical part data
 * @returns Flat array of all parts including children
 */
export function flattenHierarchicalParts(parts: HierarchicalPartData[]): HierarchicalPartData[] {
  if (!Array.isArray(parts)) {
    return [];
  }

  const flattened: HierarchicalPartData[] = [];

  function flattenRecursively(partsArray: HierarchicalPartData[]): void {
    for (const part of partsArray) {
      // Add the current part (without children to avoid circular references in flat structure)
      flattened.push({
        partId: part.partId,
        quantityRequired: part.quantityRequired,
        unitOfMeasure: part.unitOfMeasure || 'PCS'
      });

      // Recursively flatten children
      if (part.children && Array.isArray(part.children)) {
        flattenRecursively(part.children);
      }
    }
  }

  flattenRecursively(parts);
  return flattened;
}

/**
 * Debug utility to log hierarchical parts structure
 * Useful for troubleshooting data transformation issues
 * 
 * @param parts - Array of hierarchical part data
 * @param label - Label for the log output
 */
export function debugLogHierarchicalParts(parts: HierarchicalPartData[], label: string = 'Parts'): void {
  console.group(`[assemblyDataTransform] ${label}`);
  
  function logPartRecursively(part: HierarchicalPartData, level: number = 0): void {
    const indent = '  '.repeat(level);

    // Handle both string and populated object partId
    const partIdDisplay = typeof part.partId === 'string'
      ? part.partId
      : (typeof part.partId === 'object' && part.partId?._id ? part.partId._id : 'INVALID');

    console.log(`${indent}Part: ${partIdDisplay} (qty: ${part.quantityRequired}, uom: ${part.unitOfMeasure || 'N/A'})`);

    if (part.children && part.children.length > 0) {
      console.log(`${indent}Children (${part.children.length}):`);
      part.children.forEach(child => logPartRecursively(child, level + 1));
    }
  }

  if (Array.isArray(parts) && parts.length > 0) {
    console.log(`Total root parts: ${parts.length}`);
    console.log(`Total parts (including children): ${countHierarchicalParts(parts)}`);
    parts.forEach(part => logPartRecursively(part));
  } else {
    console.log('No parts data or empty array');
  }
  
  console.groupEnd();
}
