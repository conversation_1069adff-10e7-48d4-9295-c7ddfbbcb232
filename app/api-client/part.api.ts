'use client';

import { getApiUrl } from '@/app/utils/env';
import { safeFetch } from '@/app/utils/safeFetch';

/**
 * Client-side API wrapper for part service
 * This avoids importing server-side mongoose models in client components
 */

/**
 * Get part by ID
 * @param partId - The ID of the part to retrieve
 * @returns The part data or null if not found
 */
export async function getPartById(partId: string) {
  try {
    const response = await safeFetch(`${getApiUrl('/api/parts')}/${partId}`);
    
    if (!response.success || !response.data) {
      console.error('Failed to fetch part:', response.error);
      return null;
    }
    
    return response.data;
  } catch (error) {
    console.error('Error fetching part:', error);
    return null;
  }
}

/**
 * Search parts with optional filters
 * @param query - Search query
 * @param options - Search options
 * @returns Search results
 */
export async function searchParts(query: string, options: any = {}) {
  try {
    const queryParams = new URLSearchParams({
      q: query,
      ...options
    });
    
    const response = await safeFetch(`${getApiUrl('/api/parts/search')}?${queryParams}`);
    
    if (!response.success) {
      console.error('Failed to search parts:', response.error);
      return { parts: [], total: 0 };
    }
    
    return response.data;
  } catch (error) {
    console.error('Error searching parts:', error);
    return { parts: [], total: 0 };
  }
}
