"use client";

/**
 * Home Page Component
 * Redirects users to the dashboard page
 */
import { useEffect } from 'react';
import { useRouter, redirect } from 'next/navigation';

/**
 * HomePage component that redirects to the dashboard
 * Displays a loading spinner during redirection
 */
export default function HomePage() {
  const router = useRouter();
  
  useEffect(() => {
    // Redirect to the dashboard page
    router.push('/dashboard');
  }, [router]);

  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-24">
      <div className="animate-pulse">
        <h1 className="text-2xl font-bold mb-4">Redirecting to Dashboard...</h1>
        <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
      </div>
    </main>
  );
} 