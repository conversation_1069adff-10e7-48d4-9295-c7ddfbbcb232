"use client";

import { Alert, AlertDescription, AlertTitle } from "@/app/components/data-display/alert";
import { Button } from "@/app/components/forms/Button";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/forms/Select";
import Header from '@/app/components/layout/Header';
import { StandardizedTable, type ViewMode } from '@/app/components/tables/StandardizedTable';
import { createPurchaseOrdersComplexColumns, type PurchaseOrderColumnData, type PurchaseOrdersTableActions } from "@/app/components/data-display/data-table";

import { motion } from 'framer-motion';
import {
    AlertTriangle,
    Plus
} from 'lucide-react';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';

// Define purchase order interface based on database schema
interface PurchaseOrder {
  _id: string;
  poNumber: string;
  supplierId?: string | null;
  orderDate: Date | string;
  expectedDeliveryDate?: Date | string | null;
  items: Array<{
    partId: string;
    description: string;
    quantity: number;
    unitPrice: number;
    lineTotal: number;
    receivedQuantity: number;
  }>;
  totalAmount: number;
  status: 'draft' | 'pending_approval' | 'ordered' | 'partially_received' | 'fully_received' | 'cancelled';
  notes?: string | null;
  shippingAddress?: string | null;
  billingAddress?: string | null;
  termsAndConditions?: string | null;
  createdBy?: string | null;
  approvedBy?: string | null;
  approvalDate?: Date | string | null;
  createdAt: Date | string;
  updatedAt: Date | string;
  // Populated fields
  supplier?: {
    _id: string;
    name: string;
    supplier_id?: string;
    email?: string;
  };
  createdByUser?: {
    _id: string;
    username: string;
    first_name?: string;
    last_name?: string;
    email?: string;
  };
  approvedByUser?: {
    _id: string;
    username: string;
    first_name?: string;
    last_name?: string;
    email?: string;
  };
}

// Define API response types
interface PurchaseOrdersApiResponse {
  data: PurchaseOrder[] | null;
  error: string | null;
  pagination?: any;
  meta?: any;
}

/**
 * Purchase Orders page component
 * Displays a list of purchase orders and allows users to create, edit, and delete purchase orders
 * Includes filtering and search functionality
 */
const PurchaseOrders: React.FC = () => {

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // StandardizedTable state
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>('table');

  // Fetch purchase orders data
  const fetchPurchaseOrders = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/purchase-orders');
      if (!response.ok) {
        throw new Error(`Failed to fetch purchase orders: ${response.statusText}`);
      }

      const data = await response.json() as PurchaseOrdersApiResponse;
      if (data.error) {
        throw new Error(data.error);
      }

      setPurchaseOrders(data.data || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch purchase orders';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Load data on component mount and refresh trigger
  useEffect(() => {
    fetchPurchaseOrders();
  }, [fetchPurchaseOrders, refreshTrigger]);



  // Transform PurchaseOrder to PurchaseOrderColumnData
  const transformPurchaseOrderData = useCallback((purchaseOrder: PurchaseOrder): PurchaseOrderColumnData => ({
    _id: purchaseOrder._id,
    poNumber: purchaseOrder.poNumber,
    supplierId: purchaseOrder.supplierId ?? null,
    orderDate: purchaseOrder.orderDate,
    expectedDeliveryDate: purchaseOrder.expectedDeliveryDate ?? null,
    items: purchaseOrder.items || [],
    totalAmount: purchaseOrder.totalAmount,
    status: purchaseOrder.status,
    notes: purchaseOrder.notes ?? null,
    shippingAddress: purchaseOrder.shippingAddress ?? null,
    billingAddress: purchaseOrder.billingAddress ?? null,
    termsAndConditions: purchaseOrder.termsAndConditions ?? null,
    createdBy: purchaseOrder.createdBy ?? null,
    approvedBy: purchaseOrder.approvedBy ?? null,
    approvalDate: purchaseOrder.approvalDate ?? null,
    createdAt: purchaseOrder.createdAt,
    updatedAt: purchaseOrder.updatedAt,
    supplier: purchaseOrder.supplier ?? null,
    createdByUser: purchaseOrder.createdByUser ?? null,
    approvedByUser: purchaseOrder.approvedByUser ?? null,
  }), []);

  // Table actions
  const tableActions: PurchaseOrdersTableActions = useMemo(() => ({
    onView: (purchaseOrder: PurchaseOrderColumnData) => {
      const originalPO = purchaseOrders.find(po => po._id === purchaseOrder._id);
      if (originalPO) handleViewPurchaseOrder(originalPO);
    },
    onEdit: (purchaseOrder: PurchaseOrderColumnData) => {
      const originalPO = purchaseOrders.find(po => po._id === purchaseOrder._id);
      if (originalPO) handleEditPurchaseOrder(originalPO);
    },
    onDelete: (purchaseOrder: PurchaseOrderColumnData) => {
      const originalPO = purchaseOrders.find(po => po._id === purchaseOrder._id);
      if (originalPO) handleDeletePurchaseOrder(originalPO);
    },
  }), [purchaseOrders]);

  // Table columns
  const columns = useMemo(() =>
    createPurchaseOrdersComplexColumns(tableActions),
    [tableActions]
  );

  // Filtered and transformed data
  const filteredPurchaseOrders = useMemo(() => {
    const filtered = purchaseOrders.filter(purchaseOrder => {
      const matchesSearch = !searchTerm ||
        purchaseOrder.poNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        purchaseOrder.supplier?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        purchaseOrder.notes?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = statusFilter === 'all' || purchaseOrder.status === statusFilter;

      return matchesSearch && matchesStatus;
    });

    return filtered.map(transformPurchaseOrderData);
  }, [purchaseOrders, searchTerm, statusFilter, transformPurchaseOrderData]);

  // Handle status filter change
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
  };

  // Open create modal
  const openCreateModal = () => {
    // TODO: Implement create modal
    toast.info('Create purchase order functionality will be implemented soon');
  };

  // Handle purchase order actions
  const handleViewPurchaseOrder = (purchaseOrder: PurchaseOrder) => {
    // TODO: Implement view modal
    toast.info('View purchase order functionality will be implemented soon');
  };

  const handleEditPurchaseOrder = (purchaseOrder: PurchaseOrder) => {
    // TODO: Implement edit modal
    toast.info('Edit purchase order functionality will be implemented soon');
  };

  const handleDeletePurchaseOrder = (purchaseOrder: PurchaseOrder) => {
    // TODO: Implement delete functionality
    toast.info('Delete purchase order functionality will be implemented soon');
  };

  return (
    <div className="flex-1 overflow-y-auto bg-background">
      <Header title="Purchase Orders" />

      <div className="px-8 pb-8">


        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <StandardizedTable
          data={filteredPurchaseOrders}
          columns={columns}
          searchPlaceholder="Search purchase orders..."
          defaultViewMode={viewMode}
          enableSearch={true}
          enableViewToggle={true}
          onViewModeChange={setViewMode}
          onSearchChange={setSearchTerm}
          renderFilters={() => (
            <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="pending_approval">Pending Approval</SelectItem>
                <SelectItem value="ordered">Ordered</SelectItem>
                <SelectItem value="partially_received">Partially Received</SelectItem>
                <SelectItem value="fully_received">Fully Received</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          )}
          renderActions={() => (
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button variant="default" onClick={openCreateModal}>
                <Plus size={18} className="mr-1" />
                <span>New Purchase Order</span>
              </Button>
            </motion.div>
          )}
          tableProps={{
            isLoading: isLoading,
            error: error ? new Error(error) : null,
            enableSorting: true,
            enableFiltering: true,
            enablePagination: true,
            enableGlobalSearch: false, // Using StandardizedTable's search instead
            enableColumnVisibility: false, // Disabled - StandardizedTable handles column visibility
            mobileDisplayMode: "cards",
            density: "normal",
            initialPagination: { pageIndex: 0, pageSize: 20 },
            pageSizeOptions: [10, 20, 50, 100]
          }}
          key={`purchase-orders-table-${refreshTrigger}`}
        />
      </div>
    </div>
  );
};

export default PurchaseOrders;