"use client";

import { LazyProductionCapacity } from '@/app/components/charts/LazyProductionCapacity';
import { LazyProductionPlanning } from '@/app/components/charts/LazyProductionPlanning';
import { AssemblyStatus } from '@/app/components/features';
import { MemoizedProductCard } from '@/app/components/features/MemoizedProductCard';
import { AccessibleUnifiedCard } from '@/app/components/layout/cards/accessibility/AccessibleUnifiedCard';
import ErrorDisplay from '@/app/components/feedback/ErrorDisplay';
import { LoadingCard, LoadingSkeleton } from '@/app/components/data-display/loading';
import { UnifiedCard } from '@/app/components/layout';
import Header from '@/app/components/layout/Header';
import { ProductsTable } from '@/app/components/tables/ProductsTable/';
import { useAppContext } from '@/app/context/AppContext';
import { useTheme } from '@/app/context/ThemeContext';
import { Product } from '@/app/types';
import { AnimatePresence, motion } from 'framer-motion';
import {
    AlertCircle,
    AlertTriangle,
    BarChart3, Bell,
    ChevronRight,
    FileText,
    Filter,
    Package,
    Plus,
    RefreshCw,
    ShoppingCart
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useCallback, useEffect, useRef, useState } from 'react';

// Extended Product type for frontend display
type ProductWithFrontendProps = Product & {
  id: string;
  onHandValue: number;
  productCode: string;
  sellingPrice: number;
};
// Create real data structures to replace mock data
// Import WorkOrder type to ensure type safety
import { WorkOrder } from '@/app/types';

const workOrders: WorkOrder[] = [
  {
    _id: 'WO-2023-001', // Changed from id to _id
    woNumber: 'WO-2023-001', // Added woNumber
    quantity: 10, // Example quantity
    status: 'in_progress',
    priority: 'high',
    assignedTo: 'user-002',
    createdAt: new Date('2023-03-15T00:00:00Z'), // Added createdAt
    updatedAt: new Date('2023-03-16T00:00:00Z'), // Added updatedAt
    dueDate: new Date('2023-04-15T00:00:00Z'),
    notes: 'Need to complete before the next shipment'
  }
];

const productionCapacityData = {
  capacityData: [
    {
      department: 'Assembly',
      currentCapacity: 85,
      maxCapacity: 100,
      bottleneck: false
    },
    {
      department: 'Machining',
      currentCapacity: 95,
      maxCapacity: 100,
      bottleneck: true
    },
    {
      department: 'Welding',
      currentCapacity: 70,
      maxCapacity: 100,
      bottleneck: false
    }
  ],
  forecastAccuracy: 92,
  productionTrend: 'increasing' as const,
  bottleneckImpact: 15
};

const DashboardPage: React.FC = () => {
  const { mode } = useTheme();
  const router = useRouter();
  const [showAlerts, setShowAlerts] = useState(false);
  const [filterOpen, setFilterOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const alertsButtonRef = useRef<HTMLButtonElement>(null);
  const alertsPopupRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const {
    products: rawProducts,
    stockStatus: rawStockStatus,
    orderStatus: rawOrderStatus,
    highDemandItems: rawHighDemandItems,
    assemblies: rawAssemblies,
    // logisticsInfo is derived from orderStatus now
    isLoading,
    error,
    refreshData
  } = useAppContext();

  // Memoize data processing for performance optimization
  const products = React.useMemo(() => Array.isArray(rawProducts) ? rawProducts : [], [rawProducts]);
  const stockStatus = React.useMemo(() => rawStockStatus || { total: 0, lowStock: 0, outOfStock: 0, overstock: 0 }, [rawStockStatus]);
  const orderStatus = React.useMemo(() => rawOrderStatus || { pending: 0, processing: 0, shipped: 0, delivered: 0, total: 0 }, [rawOrderStatus]);
  const highDemandItems = React.useMemo(() => Array.isArray(rawHighDemandItems) ? rawHighDemandItems : [], [rawHighDemandItems]);
  const assemblies = React.useMemo(() => Array.isArray(rawAssemblies) ? rawAssemblies : [], [rawAssemblies]);

  const extendedAssemblies = React.useMemo(() => {
    // Add additional safety check before mapping
    if (!Array.isArray(assemblies)) {
      console.error('[DASHBOARD] Assemblies is not an array:', assemblies);
      return [];
    }

    return assemblies.map(assembly => ({
      ...assembly,
      stage: assembly.stage || 'active',
      parts: Array.isArray(assembly.parts) ? assembly.parts : []
    }));
  }, [assemblies]);

  const handleRefreshData = useCallback(async () => {
    setRefreshing(true);
    try {
      await refreshData();
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setTimeout(() => setRefreshing(false), 600);
    }
  }, [refreshData]);

  const mockHighDemandProduct = {
    id: 'P0001', // Ensure id is a string
    name: 'Featured Product',
    description: 'This is a featured product with high demand',
    currentStock: 5,
    reorderLevel: 10,
    demand: 'High' as const,
    category: 'Electronics',
    imageUrl: null,
    onHandValue: 450 // Added for compatibility
  };

  const lowStockPercentage = stockStatus.total > 0
    ? Math.round((stockStatus.lowStock / stockStatus.total) * 100) : 0;
  const outOfStockPercentage = stockStatus.total > 0
    ? Math.round((stockStatus.outOfStock / stockStatus.total) * 100) : 0;

  const hasLowStockAlert = stockStatus.lowStock > 0;
  const hasOutOfStockAlert = stockStatus.outOfStock > 0;
  const alertCount = (hasLowStockAlert ? 1 : 0) + (hasOutOfStockAlert ? 1 : 0);

  const handleAddStock = () => router.push('/hierarchical-part-entry');
  const handleCreateOrder = () => router.push('/purchase-orders');
  const handleGenerateReport = () => router.push('/reports');
  const handleViewCriticalItems = () => router.push('/inventory?filter=critical');
  const handleViewDeliveries = () => router.push('/logistics?tab=deliveries');
  const handleViewAllAlerts = () => { router.push('/inventory?filter=alerts'); setShowAlerts(false); };
  const handleViewAllActivity = () => router.push('/inventory-transactions');

  // Memoize expensive product data processing for performance
  const productsWithFrontendProps = React.useMemo(() =>
    products.map(p => ({
      ...p,
      id: p._id, // Map _id to id
      onHandValue: ((p as any).cost || (p as any).costPrice || 0) * (p.inventory?.currentStock || 0), // Calculate onHandValue using available price fields
      productCode: (p as any).partNumber || p._id, // Add missing productCode
      sellingPrice: (p as any).cost || (p as any).costPrice || 0, // Add missing sellingPrice
      status: (p.status || 'active') as 'active' | 'discontinued' | 'in_development', // Ensure valid status
      description: p.description || '', // Ensure description is not null
      name: p.name || 'Unknown Product', // Ensure name exists
      createdAt: new Date(p.createdAt),
      updatedAt: new Date(p.updatedAt),
    })) as ProductWithFrontendProps[], [products]);

  // Memoize top items calculation with search filtering for performance
  const topItemsByValue = React.useMemo(() => {
    if (!Array.isArray(productsWithFrontendProps)) return [];

    return [...productsWithFrontendProps]
      .sort((a, b) => (b.onHandValue || 0) - (a.onHandValue || 0))
      .slice(0, 5)
      .filter(product => searchQuery ?
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) : true
      );
  }, [productsWithFrontendProps, searchQuery]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showAlerts && alertsPopupRef.current && alertsButtonRef.current &&
          !alertsPopupRef.current.contains(event.target as Node) &&
          !alertsButtonRef.current.contains(event.target as Node)) {
        setShowAlerts(false);
      }
    };
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        if (showAlerts) setShowAlerts(false);
        if (filterOpen) setFilterOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [showAlerts, filterOpen]);

  useEffect(() => {
    if (filterOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [filterOpen]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
        duration: 0.5
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4, ease: "easeOut" }
    }
  };

  const shimmerVariants = {
    initial: { x: '-100%' },
    animate: {
      x: '100%',
      transition: {
        repeat: Infinity,
        duration: 1.5,
        ease: "easeInOut"
      }
    }
  };

  return (
    <div className="flex-1 overflow-y-auto bg-background text-foreground">
      {/* Live region for accessibility announcements */}
      <div
        aria-live="polite"
        aria-atomic="true"
        className="sr-only"
        id="dashboard-announcements"
      />
      <div
        aria-live="assertive"
        aria-atomic="true"
        className="sr-only"
        id="dashboard-alerts"
      />
      <Header title="Inventory Dashboard" role="banner" aria-labelledby="dashboard-title">
        <div className="flex items-center space-x-2" role="toolbar" aria-label="Dashboard controls">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setFilterOpen(!filterOpen)}
            className={`p-2 rounded-full transition-colors duration-200 ${
              filterOpen
                ? 'bg-muted text-foreground'
                : 'text-muted-foreground hover:text-foreground'
            }`}
            aria-expanded={filterOpen}
            aria-label="Filter and search"
          >
            <Filter size={20} />
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleRefreshData}
            className={`p-2 rounded-full transition-colors duration-200 ${
              refreshing
                ? 'animate-spin text-primary'
                : 'text-muted-foreground hover:text-foreground'
            }`}
            disabled={refreshing}
            aria-label="Refresh dashboard data"
          >
            <RefreshCw size={20} />
          </motion.button>
          <div className="relative">
            <motion.button
              ref={alertsButtonRef}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              className={`p-2 relative rounded-full transition-colors duration-200 ${
                showAlerts
                  ? 'bg-muted text-foreground'
                  : 'text-muted-foreground hover:text-foreground'
              }`}
              onClick={() => setShowAlerts(!showAlerts)}
              aria-expanded={showAlerts}
              aria-haspopup="true"
              aria-label={`Critical alerts ${alertCount > 0 ? `(${alertCount} alerts)` : ''}`}
            >
              <Bell size={20} />
              {alertCount > 0 && (
                <motion.span
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute top-0 right-0 bg-destructive text-destructive-foreground text-xs rounded-full h-4 w-4 flex items-center justify-center"
                >
                  {alertCount}
                </motion.span>
              )}
            </motion.button>
            <AnimatePresence>
              {showAlerts && alertCount > 0 && (
                <motion.div
                  ref={alertsPopupRef}
                  initial={{ opacity: 0, y: -10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -10, scale: 0.95 }}
                  transition={{ duration: 0.2, ease: "easeOut" }}
                  className="absolute right-0 mt-2 w-64 rounded-lg shadow-xl z-50 backdrop-blur-sm bg-popover/95 border border-border"
                >
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-sm font-semibold">Critical Alerts</h3>
                      <button
                        onClick={() => setShowAlerts(false)}
                        className="text-xs text-muted-foreground hover:text-foreground"
                      >
                        &times;
                      </button>
                    </div>
                    <div className="space-y-2">
                      {hasLowStockAlert && (
                        <motion.div
                          initial={{ opacity: 0, x: -5 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.1 }}
                          className="flex items-center text-xs text-yellow-600 dark:text-yellow-400"
                        >
                          <AlertTriangle size={14} className="mr-1" /> {stockStatus.lowStock} item(s) are low on stock.
                        </motion.div>
                      )}
                      {hasOutOfStockAlert && (
                        <motion.div
                          initial={{ opacity: 0, x: -5 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.2 }}
                          className="flex items-center text-xs text-red-600 dark:text-red-400"
                        >
                          <AlertCircle size={14} className="mr-1" /> {stockStatus.outOfStock} item(s) are out of stock.
                        </motion.div>
                      )}
                    </div>
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={handleViewAllAlerts}
                      className="mt-3 w-full text-center text-xs px-2 py-1.5 rounded bg-primary hover:bg-primary/90 text-primary-foreground transition-colors"
                    >
                      View All Alerts
                    </motion.button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </Header>

      <AnimatePresence>
        {filterOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden px-6 py-4 mb-4 bg-card/70 backdrop-blur-sm"
          >
            <div className="flex items-center space-x-4">
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-grow px-4 py-2 rounded-lg border bg-input border-border placeholder-muted-foreground text-foreground focus:outline-none focus:ring-2 focus:ring-primary transition-all duration-200"
              />
              {/* Add filter dropdowns/buttons here if needed */}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {isLoading && (
        <div className="p-6 flex justify-center items-center h-64">
          <div className="relative">
            <div className="h-16 w-16 rounded-full border-t-4 border-b-4 border-primary animate-spin"></div>
            <div className="mt-4 text-center text-muted-foreground">Loading dashboard data...</div>
          </div>
        </div>
      )}

      {error && <ErrorDisplay error={error} message="Error loading dashboard data" />}

      {!isLoading && !error && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="p-6 space-y-8"
        >
          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <AccessibleUnifiedCard
              variant="action"
              size="md"
              color="green"
              onClick={handleAddStock}
              className="flex items-center justify-center space-x-3"

              accessibleLabel="Add new stock to inventory"
              role="button"
            >
              <Plus size={20} aria-hidden="true" />
              <span className="text-sm font-medium">Add New Stock</span>
            </AccessibleUnifiedCard>

            <UnifiedCard
              variant="action"
              size="md"
              color="blue"
              onClick={handleCreateOrder}
              className="flex items-center justify-center space-x-3"
            >
              <ShoppingCart size={20} />
              <span className="text-sm font-medium">Create Purchase Order</span>
            </UnifiedCard>

            <UnifiedCard
              variant="action"
              size="md"
              color="purple"
              onClick={handleGenerateReport}
              className="flex items-center justify-center space-x-3"
            >
              <FileText size={20} />
              <span className="text-sm font-medium">Generate Report</span>
            </UnifiedCard>
          </div>

          {/* Stats Overview - Group status cards in one section */}
          <div className="mb-8" role="region" aria-labelledby="overview-heading">
            <h2 id="overview-heading" className="text-xl font-semibold mb-4 px-1">Overview</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
              {isLoading ? (
                // Loading state for status cards
                <>
                  <LoadingSkeleton variant="card" count={1} itemHeight="h-32" />
                  <LoadingSkeleton variant="card" count={1} itemHeight="h-32" />
                  <LoadingSkeleton variant="card" count={1} itemHeight="h-32" />
                  <LoadingSkeleton variant="card" count={1} itemHeight="h-32" />
                </>
              ) : (
                <>
                  <AccessibleUnifiedCard
                    variant="status"
                    size="md"
                    title="Total Items"
                    mainStat={{ value: stockStatus.total, label: 'Total' }}
                    data={{
                      'Out of Stock': stockStatus.outOfStock,
                      'Low Stock': stockStatus.lowStock,
                      'Healthy': stockStatus.total - stockStatus.lowStock - stockStatus.outOfStock
                    }}
                    icon={<Package />}
                    color="orange"
                    accessibleLabel={`Inventory status: ${stockStatus.total} total items. ${stockStatus.outOfStock} out of stock, ${stockStatus.lowStock} low stock, ${stockStatus.total - stockStatus.lowStock - stockStatus.outOfStock} healthy stock levels`}
                    role="status"
                    announceChanges={true}
                  />

                  <UnifiedCard
                    variant="status"
                    size="md"
                    title="Total Orders"
                    mainStat={{ value: orderStatus.total, label: 'Total' }}
                    data={{
                      'Pending': orderStatus.pending,
                      'Processing': orderStatus.processing,
                      'Shipped': orderStatus.shipped,
                      'Delivered': orderStatus.delivered
                    }}
                    icon={<ShoppingCart />}
                    color="blue"
                  />

                  <UnifiedCard
                    variant="status"
                    size="md"
                    title="Critical Reorder"
                    mainStat={{ value: stockStatus.outOfStock + stockStatus.lowStock, label: 'Items needing attention' }}
                    data={{
                      'Out of Stock': stockStatus.outOfStock,
                      'Low Stock': stockStatus.lowStock,
                      'Healthy': stockStatus.total - stockStatus.lowStock - stockStatus.outOfStock
                    }}
                    icon={<AlertTriangle />}
                    color="red"
                    onViewDetails={handleViewCriticalItems}
                    viewDetailsText="View Critical Items"
                  />

                  <UnifiedCard
                    variant="status"
                    size="md"
                    title="Deliveries"
                    mainStat={{ value: orderStatus.delivered, label: 'Completed deliveries' }}
                    icon={<Package size={18} />}
                    color="blue"
                    onViewDetails={handleViewDeliveries}
                    viewDetailsText="View Details"
                  >
                    <div className="text-sm text-muted-foreground">
                      Delivery Rate: 95%
                    </div>
                  </UnifiedCard>
                </>
              )}
            </div>
          </div>

          {/* High Demand / Featured Product */}
          <div className="mb-8" role="region" aria-labelledby="featured-products-heading">
            <h2 id="featured-products-heading" className="text-xl font-semibold mb-4 px-1">Featured Products</h2>
            {isLoading ? (
              <LoadingSkeleton variant="card" count={1} itemHeight="h-64" showHeader />
            ) : (
              <UnifiedCard
                variant="base"
                size="lg"
                title="High Demand Items"
                icon={<BarChart3 size={18} />}
                color="green"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                  {highDemandItems.length > 0 ? (
                    highDemandItems.slice(0, 2).map((item, index) => (
                      <div key={item._id}>
                        <MemoizedProductCard product={{ ...item, id: item._id, currentStock: item.inventory?.currentStock || 0, reorderLevel: item.reorderLevel || 0 }} />
                      </div>
                    ))
                  ) : (
                    <div>
                      <MemoizedProductCard product={mockHighDemandProduct} isFeatured={true}/>
                    </div>
                  )}
                </div>
              </UnifiedCard>
            )}
          </div>

          {/* Production Overview */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4 px-1">Production Overview</h2>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-5">
              {isLoading ? (
                // Loading state for production cards
                <>
                  <LoadingSkeleton variant="card" count={1} itemHeight="h-80" showHeader />
                  <LoadingSkeleton variant="card" count={1} itemHeight="h-80" showHeader />
                  <LoadingSkeleton variant="card" count={1} itemHeight="h-80" showHeader />
                </>
              ) : (
                <>
                  <UnifiedCard
                    variant="default"
                    size="sm"
                    className="overflow-hidden h-[500px]"
                  >
                    <LazyProductionPlanning
                      workOrders={workOrders}
                      products={productsWithFrontendProps}
                    />
                  </UnifiedCard>
                  <UnifiedCard
                    variant="default"
                    size="sm"
                    className="overflow-hidden h-[500px]"
                  >
                    <LazyProductionCapacity
                      capacityData={productionCapacityData.capacityData}
                      forecastAccuracy={productionCapacityData.forecastAccuracy}
                      productionTrend={productionCapacityData.productionTrend}
                      bottleneckImpact={productionCapacityData.bottleneckImpact}
                    />
                  </UnifiedCard>
                  <UnifiedCard
                    variant="default"
                    size="sm"
                    className="overflow-hidden h-[500px]"
                  >
                    <AssemblyStatus
                      assemblies={extendedAssemblies}
                    />
                  </UnifiedCard>
                </>
              )}
            </div>
          </div>

          {/* Recent Activity / Top Products */}
          <div>
            {isLoading ? (
              <LoadingSkeleton variant="card" count={1} itemHeight="h-96" showHeader showFooter />
            ) : (
              <UnifiedCard
                variant="base"
                title="Top Items by Value"
                size="md"
                className="overflow-hidden"
              >
                <ProductsTable products={topItemsByValue as any} simple={true} />
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={handleViewCriticalItems}
                  className="mt-4 w-full py-2 px-4 bg-muted hover:bg-muted-foreground/10 rounded-lg text-sm font-medium text-foreground transition-colors"
                >
                  View All Items
                </motion.button>
              </UnifiedCard>
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default DashboardPage;
