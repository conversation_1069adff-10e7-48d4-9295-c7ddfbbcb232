'use client';

import React, { Suspense } from 'react';
import dynamic from 'next/dynamic';
import { PageLoadingSkeleton } from '@/app/components/data-display/loading';

// Loading component using standardized PageLoadingSkeleton
const loading = () => <PageLoadingSkeleton contentType="grid" />;

// Dynamically import the EnhancedAssembliesPageWrapper component
const DynamicEnhancedAssembliesPageWrapper = dynamic(
  () => import('./EnhancedAssembliesPageWrapper'),
  {
    loading,
    ssr: false,
  }
);

/**
 * Lazy-loaded wrapper for EnhancedAssembliesPageWrapper
 * This component dynamically imports the actual page component only when needed
 */
export default function LazyAssembliesPageWrapper() {
  return (
    <Suspense fallback={<PageLoadingSkeleton contentType="grid" />}>
      <DynamicEnhancedAssembliesPageWrapper />
    </Suspense>
  );
}
