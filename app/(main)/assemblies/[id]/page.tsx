'use client';

import {
    <PERSON><PERSON><PERSON><PERSON>,
    Edit,
    Layers,
    ListIcon,
    Loader2,
    Tag,
    Trash2
} from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { showNetworkErrorToast, showErrorToast, showSuccessToast } from '@/app/components/feedback';

import { Badge } from '@/app/components/data-display/badge';
// BomViewerButton removed - using ViewAssemblyButton with integrated BOM functionality
import { ViewAssemblyButton } from '@/app/components/modals/ViewAssemblyModal';
import { Button } from '@/app/components/forms/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
import { useTheme } from '@/app/context/ThemeContext';
import { asApiResponse, extractApiError, hasApiError } from '@/app/types/api-responses';
import { confirmAlert } from 'react-confirm-alert';
import 'react-confirm-alert/src/react-confirm-alert.css';

/**
 * Interface for route parameters including the assembly ID
 */
interface AssemblyDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

/**
 * Assembly Detail page component - shows detailed information about an assembly
 */
export default function AssemblyDetailPage({ params }: AssemblyDetailPageProps) {
  // Unwrap params using React.use() to handle the Promise
  const unwrappedParams = React.use(params);
  const router = useRouter();
  const { theme } = useTheme();
  const [assembly, setAssembly] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Fetch assembly data by ID
   */
  useEffect(() => {
    const fetchAssembly = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Add includeParts=true parameter to get detailed parts data
        const url = new URL(`/api/assemblies/${unwrappedParams.id}`, window.location.origin);
        url.searchParams.append('includeParts', 'true');

        const response = await fetch(url.toString());

        if (!response.ok) {
          throw new Error('Failed to fetch assembly');
        }

        const data = asApiResponse<any>(await response.json());

        if (hasApiError(data)) {
          throw new Error(extractApiError(data) || 'Failed to fetch assembly');
        }

        if (!data.data) {
          throw new Error('Assembly not found');
        }

        // Log the assembly data to see its structure
        console.log('Assembly data:', data.data);

        // Check if any parts are marked as assemblies using canonical partsRequired
        if (data.data && data.data.partsRequired) {
          const assemblies = data.data.partsRequired.filter((part: any) =>
            (typeof part.partId === 'object' &&
              (part.partId.isAssembly === true || part.partId.is_assembly === true))
          );
          console.log('Sub-assemblies found:', assemblies.length, assemblies);
        }

        setAssembly(data.data);
      } catch (error) {
        console.error('Error fetching assembly:', error);
        setError(error instanceof Error ? error.message : 'An unknown error occurred');
        showNetworkErrorToast(fetchAssembly, { customMessage: 'Failed to load assembly' });
      } finally {
        setIsLoading(false);
      }
    };

    fetchAssembly();
  }, [unwrappedParams.id]);

  /**
   * Handle assembly deletion
   */
  const handleDelete = () => {
    confirmAlert({
      title: "Confirm Deletion",
      message: `Are you sure you want to delete assembly "${assembly.name}" (${assembly.assemblyCode})?`,
      buttons: [
        {
          label: 'Yes, Delete It',
          onClick: async () => {
            try {
              setIsDeleting(true);

              const response = await fetch(`/api/assemblies/${unwrappedParams.id}`, {
                method: 'DELETE',
              });

              if (response.ok) {
                showSuccessToast(`Assembly ${assembly.assemblyCode} deleted successfully`);
                router.push('/assemblies');
              } else {
                const data = asApiResponse<any>(await response.json());
                const apiError = extractApiError(data);
                throw new Error(apiError || "Failed to delete assembly");
              }
            } catch (error) {
              showErrorToast({ error: error instanceof Error ? error.message : String(error) });
              console.error("Delete error:", error);
            } finally {
              setIsDeleting(false);
            }
          }
        },
        {
          label: 'Cancel',
          onClick: () => {}
        }
      ]
    });
  };

  if (isLoading) {
    return (
      <div className="container py-8 space-y-8">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-lg text-muted-foreground">Loading assembly details...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container py-8 space-y-8">
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-6">
          <h2 className="text-lg font-medium text-destructive mb-2">Error Loading Assembly</h2>
          <p className="text-destructive-foreground mb-4">{error}</p>
          <div className="flex gap-4">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Back
            </Button>
            <Button variant="default" onClick={() => router.refresh()}>
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Make sure assembly is defined before rendering
  if (!assembly) {
    return (
      <div className="container py-8 space-y-8">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-lg text-muted-foreground">Loading assembly details...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8 space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <div className="flex items-center gap-2">
            <h1 className="text-3xl font-bold tracking-tight">{assembly?.name || 'Unnamed Assembly'}</h1>
            <Badge variant="outline" className="ml-2 font-mono text-xs">
              {assembly?.assemblyCode || 'No ID'}
            </Badge>
          </div>
          <p className="text-muted-foreground mt-1">
            {assembly?.status || 'Standard'} Assembly
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/assemblies">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Assemblies
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href={`/assemblies/${unwrappedParams.id}/edit`}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Link>
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="mr-2 h-4 w-4" />
            )}
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Assembly Details */}
        <Card className="border shadow-sm lg:col-span-1 bg-card">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2">
              <Tag className="h-5 w-5" />
              Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="divide-y divide-border">
              <div className="py-3 grid grid-cols-3 gap-1">
                <dt className="text-sm font-medium text-muted-foreground">ID</dt>
                <dd className="col-span-2 text-sm font-mono text-foreground">
                  {assembly?.assemblyCode || 'No ID'}
                </dd>
              </div>
              <div className="py-3 grid grid-cols-3 gap-1">
                <dt className="text-sm font-medium text-muted-foreground">Name</dt>
                <dd className="col-span-2 text-sm text-foreground">
                  {assembly?.name || 'Unnamed Assembly'}
                </dd>
              </div>
              <div className="py-3 grid grid-cols-3 gap-1">
                <dt className="text-sm font-medium text-muted-foreground">Status</dt>
                <dd className="col-span-2 text-sm text-foreground">
                  {assembly?.status || 'Standard'}
                </dd>
              </div>
              {assembly?.version && (
                <div className="py-3 grid grid-cols-3 gap-1">
                  <dt className="text-sm font-medium text-muted-foreground">Version</dt>
                  <dd className="col-span-2 text-sm font-mono text-foreground">
                    {assembly.version}
                  </dd>
                </div>
              )}
              <div className="py-3 grid grid-cols-3 gap-1">
                <dt className="text-sm font-medium text-muted-foreground">Parts</dt>
                <dd className="col-span-2 text-sm text-foreground">
                  <Badge>{(assembly?.partsRequired && Array.isArray(assembly.partsRequired) ? assembly.partsRequired.length : 0)}</Badge>
                </dd>
              </div>
              {assembly?.description && (
                <div className="py-3 grid grid-cols-3 gap-1">
                  <dt className="text-sm font-medium text-muted-foreground">Description</dt>
                  <dd className="col-span-2 text-sm text-foreground">
                    {assembly.description}
                  </dd>
                </div>
              )}
            </dl>
          </CardContent>
        </Card>

        {/* Assembly Parts */}
        <Card className="border shadow-sm lg:col-span-2 bg-card">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center gap-2">
              <Layers className="h-5 w-5" />
              Parts List
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center py-8 space-y-4">
              {(assembly.partsRequired && Array.isArray(assembly.partsRequired) && assembly.partsRequired.length > 0) ? (
                <>
                  <div className="text-center">
                    <p className="text-sm text-muted-foreground mb-2">
                      This assembly contains {assembly.partsRequired.length} component{assembly.partsRequired.length !== 1 ? 's' : ''}
                    </p>
                    <ViewAssemblyButton
                      assembly={assembly}
                      variant="outline"
                      size="lg"
                      className="min-w-[200px] bg-primary/5 hover:bg-primary/10 border-primary/20"
                      buttonContent={
                        <>
                          <ListIcon className="h-4 w-4 mr-2" />
                          View Assembly & BOM Details
                        </>
                      }
                    />
                  </div>
                </>
              ) : (
                <div className="py-4 text-center text-muted-foreground">
                  No parts found for this assembly.
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
