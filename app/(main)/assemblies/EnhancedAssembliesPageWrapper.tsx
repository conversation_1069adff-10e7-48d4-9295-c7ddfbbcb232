'use client';

import React from 'react';
import { AssembliesProvider } from '@/app/contexts/AssembliesContext';
import EnhancedAssembliesPageContent from './EnhancedAssembliesPageContent';

// SCHEMA ALIGNMENT: Ensured context and props use only canonical assembly schema fields from database_schema_updated.md. No legacy/incorrect field usage.

/**
 * Enhanced wrapper component for the assemblies page that provides the context
 * and uses the Magic UI enhanced components
 */
export default function EnhancedAssembliesPageWrapper() {
  return (
    <AssembliesProvider>
      <EnhancedAssembliesPageContent />
    </AssembliesProvider>
  );
}