'use client';

import { Clock, Package, LayoutGrid, List } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { Button } from '@/app/components/forms/Button';
import { Card, CardContent } from '@/app/components/layout/cards/card';
import { EnhancedBackground } from '@/app/components/layout/EnhancedBackground';
import Header from '@/app/components/layout/Header';
import { StandardizedTable } from '@/app/components/tables/StandardizedTable';
import { ViewProductButton } from '@/app/components/modals/ViewProductModal';
import { QuickEditProductAction } from '@/app/components/actions/QuickEditProductAction';
import { ProductsGrid } from '@/app/components/tables/ProductsGrid';
import { Eye, Trash2 } from 'lucide-react';
import { Product } from '@/app/components/tables/ProductsTable/types';
import { getApiUrl } from '@/app/utils/env';
import { safeFetch } from '@/app/utils/safeFetch';
import { toast } from 'sonner';
import { ProductFormButton } from '@/app/components/forms/ProductFormModal';
import { DataTableColumn } from '@/app/components/data-display/data-table/types';

/**
 * Products page content component - displays list of products with view mode switching
 */
const ProductsPageContent: React.FC = () => {
  const router = useRouter();

  // State management
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Fetch products data
  const fetchProducts = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const result = await safeFetch(getApiUrl('/api/products?includeAssembly=true'), {
        cache: 'no-store',
      });

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch products');
      }

      setProducts((result.data as { data?: any[] })?.data || []);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching products:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch products');
      setProducts([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initial data fetch
  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  // Handle delete action
  const handleDelete = async (product: Product) => {
    if (!confirm(`Are you sure you want to delete "${product.name}"?`)) {
      return;
    }

    try {
      const result = await safeFetch(getApiUrl(`/api/products/${product._id}`), {
        method: 'DELETE',
      });

      if (!result.success) {
        throw new Error(result.error || 'Failed to delete product');
      }

      toast.success('Product deleted successfully');
      fetchProducts(); // Refresh the list
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete product');
    }
  };

  // Define columns for the StandardizedTable with individual action buttons
  const columns = useMemo<DataTableColumn<Product>[]>(() => [
    {
      accessorKey: 'name',
      header: 'Product Name',
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.original.name}</div>
          <div className="text-sm text-muted-foreground">{row.original.productCode}</div>
        </div>
      ),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const statusColors = {
          active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
          discontinued: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
          in_development: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
        };
        return (
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
            statusColors[row.original.status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'
          }`}>
            {row.original.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </span>
        );
      },
    },
    {
      accessorKey: 'sellingPrice',
      header: 'Price',
      cell: ({ row }) => (
        <div className="font-medium">
          ${row.original.sellingPrice?.toFixed(2) || '0.00'}
        </div>
      ),
    },
    {
      accessorKey: 'description',
      header: 'Description',
      cell: ({ row }) => (
        <div className="max-w-xs truncate text-sm text-muted-foreground">
          {row.original.description || 'No description'}
        </div>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      enableSorting: false,
      enableHiding: false,
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          {/* View Action */}
          <ViewProductButton
            product={row.original}
            variant="ghost"
            size="sm"
          >
            <Eye className="h-4 w-4" />
          </ViewProductButton>

          {/* Edit Action */}
          <QuickEditProductAction
            product={row.original}
            variant="icon"
            size="sm"
            onSuccess={fetchProducts}
          />

          {/* Delete Action */}
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              handleDelete(row.original);
            }}
            className="text-destructive hover:text-destructive"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ], [router, handleDelete]);



  // Loading state
  if (isLoading) {
    return (
      <EnhancedBackground patternType="grid" interactiveMode={false}>
        <Header title="Products">
          <div className="flex items-center gap-2">
            <ProductFormButton onSuccess={fetchProducts} />
          </div>
        </Header>
        <div className="container mx-auto px-4 py-8">
          <Card>
            <CardContent className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading products...</p>
            </CardContent>
          </Card>
        </div>
      </EnhancedBackground>
    );
  }

  // Error state
  if (error) {
    return (
      <EnhancedBackground patternType="grid" interactiveMode={false}>
        <Header title="Products & Catalog">
          <div className="flex items-center gap-2">
            <ProductFormButton onSuccess={fetchProducts} />
          </div>
        </Header>
        <div className="container mx-auto px-4 py-8">
          <Card>
            <CardContent className="p-8 text-center">
              <Package className="h-12 w-12 mx-auto mb-4 text-red-400" />
              <h3 className="text-lg font-medium mb-2 text-red-600">Error Loading Products</h3>
              <p className="text-sm text-muted-foreground mb-4">{error}</p>
              <Button onClick={fetchProducts} variant="outline">
                Try Again
              </Button>
            </CardContent>
          </Card>
        </div>
      </EnhancedBackground>
    );
  }

  return (
    <EnhancedBackground patternType="grid" interactiveMode={false}>
      <Header title="Products & Catalog">
        <div className="flex items-center gap-2">
          <ProductFormButton onSuccess={fetchProducts} />
        </div>
      </Header>

      <div className="container mx-auto px-4 py-8">

        {/* StandardizedTable */}
        <StandardizedTable
          data={products}
          columns={columns}
          searchPlaceholder="Search products..."
          viewOptions={[
            { id: 'table', label: 'Table', icon: <List className="h-4 w-4" /> },
            { id: 'grid', label: 'Grid', icon: <LayoutGrid className="h-4 w-4" /> }
          ]}
          GridComponent={({ data }) => <ProductsGrid products={data as any} />}
          tableProps={{
            renderEmptyState: () => (
              <div className="text-center py-8">
                <Package className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium mb-2">No Products Found</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  There are no products in the system yet.
                </p>
                <ProductFormButton onSuccess={fetchProducts} />
              </div>
            )
          }}
        />

        {/* Results count */}
        <div className="mt-4 text-sm text-muted-foreground flex items-center">
          <Clock size={14} className="mr-1" />
          <span>Last updated: {lastUpdated ? new Date(lastUpdated).toLocaleTimeString() : 'Never'}</span>
          <span className="mx-2">•</span>
          <span>{products.length} {products.length === 1 ? 'product' : 'products'} found</span>
        </div>
      </div>
    </EnhancedBackground>
  );
};

export default ProductsPageContent;
