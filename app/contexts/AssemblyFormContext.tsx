'use client';

import { Assembly } from '@/app/components/tables/AssembliesTable/types';
import { getApiUrl } from '@/app/utils/apiUtils';
import {
    detectStaleStockData,
    refreshPartStockData,
    shouldRefreshStock
} from '@/app/utils/partDataPreservation';
import { useRouter } from 'next/navigation';
import React, { createContext, useCallback, useContext, useEffect, useRef, useState } from 'react';
import { showErrorToast, showSuccessToast, showValidationErrorToast, showInfoToast } from '@/app/components/feedback';

// Define the initial form state
const initialFormState: Partial<Assembly> = {
  name: '',
  assemblyCode: '',
  status: 'active',
  partsRequired: [],
  productId: null,
  parentId: null,
  isTopLevel: true,
  version: 1,
  manufacturingInstructions: null,
  estimatedBuildTime: null,
};

interface AssemblyFormContextType {
  formData: Partial<Assembly>;
  isLoading: boolean;
  isSaving: boolean;
  isEditing: boolean;
  isDirty: boolean;
  setFormData: (data: Partial<Assembly> | ((prev: Partial<Assembly>) => Partial<Assembly>)) => void;
  updateFormField: (field: string, value: any) => void;
  resetForm: () => void;
  loadAssembly: (id: string) => Promise<void>;
  saveAssembly: (onSuccess?: () => void) => Promise<boolean>;
  saveAssemblyWithFreshData: (freshData: Partial<Assembly>, onSuccess?: () => void) => Promise<boolean>;
  addPart: (part: any) => void;
  updatePart: (index: number, part: any) => void;
  removePart: (index: number) => void;
  refreshStockData: () => Promise<void>;
  checkStockStaleness: () => { hasStaleData: boolean; staleParts: string[]; recommendations: string[] };
  autoRefreshStockIfNeeded: () => Promise<boolean>;
  getStockRefreshRecommendation: () => { shouldRefresh: boolean; reasons: string[]; priority: 'low' | 'medium' | 'high' };
}

const AssemblyFormContext = createContext<AssemblyFormContextType | undefined>(undefined);

export function useAssemblyForm() {
  const context = useContext(AssemblyFormContext);
  if (context === undefined) {
    throw new Error('useAssemblyForm must be used within an AssemblyFormProvider');
  }
  return context;
}

interface AssemblyFormProviderProps {
  children: React.ReactNode;
  assemblyId?: string | undefined;
}

export function AssemblyFormProvider({ children, assemblyId }: AssemblyFormProviderProps) {
  const router = useRouter();
  
  // Use refs to store the latest state values for use in callbacks
  const formDataRef = useRef<Partial<Assembly>>(initialFormState);
  const originalDataRef = useRef<Partial<Assembly>>(initialFormState);
  const isEditingRef = useRef<boolean>(!!assemblyId);

  const [formData, setFormDataState] = useState<Partial<Assembly>>(initialFormState);
  const [originalData, setOriginalData] = useState<Partial<Assembly>>(initialFormState);
  const [isLoading, setIsLoading] = useState(assemblyId ? true : false);
  const [isSaving, setIsSaving] = useState(false);
  const [isEditing, setIsEditing] = useState(!!assemblyId);

  // Update the refs when the state changes
  useEffect(() => {
    formDataRef.current = formData;
    originalDataRef.current = originalData;
    isEditingRef.current = isEditing;
  }, [formData, originalData, isEditing]);

  // Custom setFormData function that updates both state and ref
  const setFormData = useCallback((data: Partial<Assembly> | ((prev: Partial<Assembly>) => Partial<Assembly>)) => {
    if (typeof data === 'function') {
      setFormDataState(prev => {
        const newData = data(prev);
        formDataRef.current = newData;
        return newData;
      });
    } else {
      setFormDataState(data);
      formDataRef.current = data;
    }
  }, []);

  // Determine if form has been modified
  const isDirty = JSON.stringify(formData) !== JSON.stringify(originalData);

  /**
   * Update a single form field
   */
  const updateFormField = useCallback((field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []); // setFormData is stable, no dependencies needed

  /**
   * Reset form to initial state or original data
   */
  const resetForm = useCallback(() => {
    setFormData(isEditingRef.current ? originalDataRef.current : initialFormState);
  }, []); // setFormData is stable, no dependencies needed

  /**
   * Load assembly data for editing
   */
  const loadAssembly = useCallback(async (id: string) => {
    setIsLoading(true);
    try {
      console.log('Loading assembly with ID:', id);

      // Add includeParts=true parameter to get detailed parts data
      const url = new URL(`/api/assemblies/${id}`, window.location.origin);
      url.searchParams.append('includeParts', 'true');

      // Log the URL being fetched for debugging
      console.log('Fetching from URL:', url.toString());

      const response = await fetch(url.toString());

      console.log('Fetch response status:', response.status);
      if (!response.ok) {
        const errorData = await response.json();
        console.error('API error response:', errorData);
        throw new Error(errorData.error || 'Failed to fetch assembly');
      }

      const data = await response.json();
      console.log('API response data:', data); // Log the full response data

      if (!data.data) {
        console.warn('API response data.data is null or undefined.'); // Log if data.data is missing
        throw new Error('Assembly not found');
      }

      // Process the assembly data to ensure hierarchical structure is preserved
      const assemblyData = data.data;

      // Log the received data for debugging
      console.log('Loaded assembly data:', assemblyData);

      // Initialize partsRequired array if it doesn't exist
      if (!assemblyData.partsRequired) {
        assemblyData.partsRequired = [];
      }

      // Helper function to recursively process part entries for the form data
      const processPartEntryForForm = (entry: any): any => {
        const partDetails = entry.partId; // This is the populated Part object or an ID string
        const isPartDetailsObject = typeof partDetails === 'object' && partDetails !== null;

        return {
          ...entry, // Preserve original fields from the entry (like _id of BOM item, quantityRequired, unitOfMeasure)
          partId: isPartDetailsObject ? partDetails._id?.toString() : (partDetails?.toString() || ''), // Actual Part's _id as string
          name: isPartDetailsObject ? partDetails.name : 'Unknown Part',
          description: isPartDetailsObject ? partDetails.description : '',
          partDisplayIdentifier: isPartDetailsObject
            ? (partDetails.partNumber || partDetails.assemblyCode || partDetails._id) 
            : partDetails, // If partDetails is just an ID string, use it as display
          quantityRequired: Number(entry.quantityRequired || entry.quantity || 1),
          unitOfMeasure: entry.unitOfMeasure || entry.unit_of_measure || 'ea',
          isExpanded: entry.isExpanded !== undefined ? entry.isExpanded : true,
          // Recursively process children
          children: entry.children && Array.isArray(entry.children)
            ? entry.children.map(processPartEntryForForm)
            : [],
        };
      };

      // Convert partsRequired from the API schema format to the format expected by the form
      // IMPORTANT: Preserve the populated Part object to maintain inventory.currentStock data
      if (assemblyData.partsRequired && Array.isArray(assemblyData.partsRequired)) {
        assemblyData.partsRequired = assemblyData.partsRequired.map((part: any) => {
          const actualPartData = part.partId; // This is the populated part object or string ID

          return {
            // Preserve the original BOM entry fields
            _id: part._id,
            quantityRequired: Number(part.quantityRequired || 1),
            unitOfMeasure: part.unitOfMeasure || 'ea',

            // Keep the populated Part object as partId for stock access
            partId: actualPartData, // Keep the full populated object, not just the ID

            // Extract commonly used fields for form convenience, but keep the full object available
            name: typeof actualPartData === 'object' ? (actualPartData?.name || 'Unknown Part') : 'Unknown Part',
            description: typeof actualPartData === 'object' ? (actualPartData?.description || '') : '',
            partDisplayIdentifier: typeof actualPartData === 'object' ? (actualPartData?.partNumber || actualPartData?.assemblyCode || actualPartData?._id?.toString()) : (actualPartData?.toString() || ''),

            // Form-specific fields
            isExpanded: true,
            children: part.children || []
          };
        });
      }
      // For backward compatibility, check for components field (legacy schema)
      else if (assemblyData.components && Array.isArray(assemblyData.components)) {
        console.log('Processing components (legacy schema):', assemblyData.components.length);
        // Adapt processPartEntryForForm or create a specific one for legacy components if structure differs significantly
        const processLegacyComponentForForm = (component: any): any => {
          // Assuming component.item_id is the part's ID, component.item_name is its name.
          // Legacy might have component.item_part_number or component.item_code for display identifier.
          return {
            ...component, // Preserve original legacy fields
            partId: component.item_id,
            name: component.item_name || 'Unknown Part',
            description: component.item_description || '', // Assuming item_description might exist
            partDisplayIdentifier: component.item_part_number || component.item_code || component.item_id,
            quantityRequired: component.quantity || 1,
            unitOfMeasure: component.unit_of_measure || 'ea',
            isExpanded: component.isExpanded !== undefined ? component.isExpanded : true,
            children: component.children && Array.isArray(component.children)
              ? component.children.map(processLegacyComponentForForm)
              : [],
          };
        };
        assemblyData.partsRequired = assemblyData.components.map((component: any) => {
          // Map from legacy schema to form format using canonical field names
          // Assuming component.item_id is just the ID string and not a populated object here for legacy
          return {
            partId: component.item_id,
            partDisplayIdentifier: component.item_id, // In legacy, item_id might be the only identifier available
            name: component.item_name || 'Unknown Part',
            quantityRequired: component.quantity || 1,
            quantity: component.quantity || 1, // Legacy field for UI compatibility
            unitOfMeasure: component.unit_of_measure || 'ea',
            unit_of_measure: component.unit_of_measure || 'ea', // Legacy field for UI compatibility
            isExpanded: true,
            children: component.children || [],
          };
        });
      }

      // Make sure assemblyCode is set (could be in assembly_id for backward compatibility)
      if (!assemblyData.assemblyCode && assemblyData.assembly_id) {
        assemblyData.assemblyCode = assemblyData.assembly_id;
      }

      setFormData(assemblyData);
      setOriginalData(assemblyData);
      setIsEditing(true);
    } catch (err) {
      console.error('Error loading assembly:', err);
      showErrorToast({ error: err instanceof Error ? err.message : 'Failed to load assembly' });
      // Do not redirect immediately, let the user see the error in the modal if possible
      // router.push('/assemblies'); // Removed immediate redirect
    } finally {
      setIsLoading(false);
    }
  }, []); // Removed router from dependencies, as its usage is in an error path and router object might be unstable

  // Recursive function to transform hierarchical parts
  const transformPartForDB = (part: any): any => {
    // Ensure partId is a string (ObjectId string) for saving to DB
    // Since we now preserve the populated Part object, we need to extract the _id
    let partIdString = '';

    if (typeof part.partId === 'object' && part.partId !== null) {
      // Handle populated Part object
      partIdString = part.partId._id?.toString() || part.partId.toString();
    } else if (part.partId) {
      // Handle string partId (could be ObjectId string or SKU)
      partIdString = part.partId.toString();
    }

    // Validate that we have a valid partId
    if (!partIdString) {
      console.warn('Part missing partId:', part);
      return null; // Will be filtered out
    }

    const transformedPart: any = {
      partId: partIdString,
      quantityRequired: Number(part.quantityRequired || 1), // ✅ Use correct database field name
      unitOfMeasure: part.unitOfMeasure || 'ea' // Use canonical unitOfMeasure
    };

    // Recursively transform children if they exist
    if (part.children && Array.isArray(part.children) && part.children.length > 0) {
      const transformedChildren = part.children
        .map(transformPartForDB)
        .filter(Boolean); // Remove any null entries

      if (transformedChildren.length > 0) {
        transformedPart.children = transformedChildren;
      }
    }

    return transformedPart;
  };

  // Add this function to normalize form data before saving
  const normalizeAssemblyData = (formData: any) => {
    // Create a copy to avoid mutating the original
    const normalizedData = { ...formData };

    // Handle parts required conversion from UI format to DB format using canonical field names
    if (normalizedData.partsRequired && Array.isArray(normalizedData.partsRequired)) {
      normalizedData.partsRequired = normalizedData.partsRequired
        .map(transformPartForDB)
        .filter(Boolean); // Remove any null entries

      delete normalizedData.components;
    } else {
      // Ensure partsRequired is an empty array if not provided
      normalizedData.partsRequired = [];
    }

    // Map assembly_stage to status if status is not provided (for backward compatibility)
    if (!normalizedData.status && normalizedData.assembly_stage) {
      // Map assembly_stage values to canonical status values
      switch (normalizedData.assembly_stage) {
        case 'Final Assembly':
        case 'FINAL ASSEMBLY':
          normalizedData.status = 'active';
          break;
        case 'Sub-Assembly':
        case 'SUB ASSEMBLY':
          normalizedData.status = 'pending_review';
          break;
        default:
          normalizedData.status = 'active';
      }
    }

    // Remove legacy fields that have been migrated to canonical fields
    if (normalizedData.assembly_id && normalizedData.assemblyCode) {
      delete normalizedData.assembly_id;
    }

    // Ensure version is a number
    if (normalizedData.version && typeof normalizedData.version === 'string') {
      normalizedData.version = parseInt(normalizedData.version, 10) || 1;
    } else if (normalizedData.version === undefined || normalizedData.version === null) {
      normalizedData.version = 1; // Default to version 1 if not set
    }

    // Ensure isTopLevel is a boolean
    if (typeof normalizedData.isTopLevel !== 'boolean') {
      normalizedData.isTopLevel = normalizedData.parentId === null || normalizedData.parentId === undefined;
    }

    // Ensure productId and parentId are null if empty strings, or ObjectId strings if provided
    if (normalizedData.productId === '') {
      normalizedData.productId = null;
    }
    if (normalizedData.parentId === '') {
      normalizedData.parentId = null;
    }

    return normalizedData;
  };

  /**
   * Save assembly (create or update) with retry mechanism
   */
  const saveAssembly = useCallback(async (onSuccess?: () => void, retryCount = 0, maxRetries = 2) => {
    // Use the ref to get the latest formData
    const currentFormData = formDataRef.current;

    console.log('[AssemblyFormContext] saveAssembly called with currentFormData:', {
      assemblyCode: currentFormData.assemblyCode,
      name: currentFormData.name,
      description: currentFormData.description,
      status: currentFormData.status
    });

    // Validate form
    if (!currentFormData.name?.trim()) {
      console.log('[AssemblyFormContext] Validation failed: Assembly name is required');
      showValidationErrorToast('Assembly name is required');
      return false;
    }

    // Check for assembly code
    if (!currentFormData.assemblyCode?.trim()) {
      console.log('[AssemblyFormContext] Validation failed: Assembly code is required');
      showValidationErrorToast('Assembly code is required');
      return false;
    }

    // Validate status is set
    if (!currentFormData.status) {
      showValidationErrorToast('Assembly status is required');
      return false;
    }

    // Note: Assemblies can be created without parts (empty partsRequired array is allowed)
    // This validation has been removed to allow creating assemblies without parts initially

    // Create a copy of the data to manipulate
    const dataToSave = { ...currentFormData };

    // Ensure status is set to a valid value
    dataToSave.status = dataToSave.status || 'active';

    try {
      setIsSaving(true);

      // Check if stock refresh is recommended before saving
      const refreshRecommendation = getStockRefreshRecommendation();
      if (refreshRecommendation.shouldRefresh && refreshRecommendation.priority === 'high') {
        await refreshStockData();
        // Update dataToSave with the refreshed data
        const refreshedFormData = formDataRef.current;
        Object.assign(dataToSave, refreshedFormData);
      }

      // Normalize the data before saving
      const normalizedData = normalizeAssemblyData(dataToSave);

      let url = getApiUrl('/api/assemblies');
      let method = 'POST';

      // For edit mode, use PUT with the assembly ID
      if (isEditing && currentFormData._id) {
        url = getApiUrl(`/api/assemblies/${currentFormData._id}`);
        method = 'PUT';
        // Remove assemblyCode from the payload for updates
        if (normalizedData.hasOwnProperty('assemblyCode')) {
          delete normalizedData.assemblyCode;
        }
      }

      let response;

      console.log('[AssemblyFormContext] Saving assembly data:', {
        url,
        method,
        assemblyCode: normalizedData.assemblyCode,
        name: normalizedData.name,
        status: normalizedData.status,
        partsRequiredCount: normalizedData.partsRequired?.length || 0,
        isTopLevel: normalizedData.isTopLevel
      });

      console.log('[AssemblyFormContext] Full normalized data:', JSON.stringify(normalizedData, null, 2));

      response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(normalizedData),
      });

      console.log('[AssemblyFormContext] API response status:', response.status, response.statusText);

      if (!response.ok) {
        let errorMessage = 'Failed to save assembly';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorData.message || errorMessage;
          console.error('[AssemblyFormContext] API error response:', errorData);
        } catch (parseError) {
          console.error('[AssemblyFormContext] Failed to parse error response:', parseError);
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      const savedData = await response.json();
      console.log('[AssemblyFormContext] Assembly saved successfully:', savedData);

      const successMessage = isEditing ? 'Assembly updated successfully' : 'Assembly created successfully';
      showSuccessToast(successMessage);

      // Update form data with saved data
      const assemblyData = savedData.data || savedData;
      setFormData(assemblyData);

      console.log('[AssemblyFormContext] Form data updated with saved assembly:', assemblyData);

      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }

      return true;
    } catch (error) {
      console.error('[AssemblyFormContext] Error saving assembly:', error);

      let errorMessage = 'Failed to save assembly';
      let shouldRetry = false;

      if (error instanceof Error) {
        errorMessage = error.message;

        // Determine if we should retry based on error type
        if (error.message.includes('network') || error.message.includes('fetch') ||
            error.message.includes('timeout') || error.message.includes('500')) {
          shouldRetry = retryCount < maxRetries;
        }

        // Provide more specific error messages for common issues
        if (error.message.includes('duplicate') || error.message.includes('already exists')) {
          errorMessage = `Assembly code already exists. Please use a unique assembly code.`;
        } else if (error.message.includes('validation')) {
          errorMessage = `Validation error: ${error.message}`;
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = shouldRetry
            ? `Network error. Retrying... (${retryCount + 1}/${maxRetries + 1})`
            : 'Network error. Please check your connection and try again.';
        }
      }

      // Retry if appropriate
      if (shouldRetry) {
        console.log(`[AssemblyFormContext] Retrying save operation (${retryCount + 1}/${maxRetries + 1})`);
        showInfoToast(`Retrying save operation... (${retryCount + 1}/${maxRetries + 1})`);

        // Wait a bit before retrying (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));

        // Recursive retry
        return saveAssembly(onSuccess, retryCount + 1, maxRetries);
      }

      showErrorToast({ error: errorMessage });
      return false;
    } finally {
      setIsSaving(false);
    }
  }, [isEditing]); // Removed dependencies that are defined later

  /**
   * Save assembly with fresh data from child form (bypasses stale context state)
   */
  const saveAssemblyWithFreshData = useCallback(async (freshData: Partial<Assembly>, onSuccess?: () => void, retryCount = 0, maxRetries = 2) => {
    console.log('[AssemblyFormContext] saveAssemblyWithFreshData called with freshData:', {
      assemblyCode: freshData.assemblyCode,
      name: freshData.name,
      description: freshData.description,
      status: freshData.status
    });

    // Validate form using fresh data
    if (!freshData.name?.trim()) {
      console.log('[AssemblyFormContext] Validation failed: Assembly name is required');
      showValidationErrorToast('Assembly name is required');
      return false;
    }

    // Check for assembly code
    if (!freshData.assemblyCode?.trim()) {
      console.log('[AssemblyFormContext] Validation failed: Assembly code is required');
      showValidationErrorToast('Assembly code is required');
      return false;
    }

    // Validate status is set
    if (!freshData.status) {
      showValidationErrorToast('Assembly status is required');
      return false;
    }

    // Create a copy of the fresh data to manipulate
    const dataToSave = { ...freshData };

    // Ensure status is set to a valid value
    dataToSave.status = dataToSave.status || 'active';

    // For editing, preserve the original _id
    if (isEditing && formDataRef.current._id) {
      dataToSave._id = formDataRef.current._id;
    }

    try {
      setIsSaving(true);

      // Normalize the data before saving
      const normalizedData = normalizeAssemblyData(dataToSave);

      let url = getApiUrl('/api/assemblies');
      let method = 'POST';

      // For edit mode, use PUT with the assembly ID
      if (isEditing && normalizedData._id) {
        url = getApiUrl(`/api/assemblies/${normalizedData._id}`);
        method = 'PUT';
        // Remove assemblyCode from the payload for updates
        if (normalizedData.hasOwnProperty('assemblyCode')) {
          delete normalizedData.assemblyCode;
        }
      }

      console.log('[AssemblyFormContext] Saving assembly with fresh data:', {
        url,
        method,
        assemblyCode: normalizedData.assemblyCode,
        name: normalizedData.name,
        status: normalizedData.status,
        partsRequiredCount: normalizedData.partsRequired?.length || 0,
        isTopLevel: normalizedData.isTopLevel
      });

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(normalizedData),
      });

      console.log('[AssemblyFormContext] API response status:', response.status, response.statusText);

      if (!response.ok) {
        let errorMessage = 'Failed to save assembly';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorData.message || errorMessage;
          console.error('[AssemblyFormContext] API error response:', errorData);
        } catch (parseError) {
          console.error('[AssemblyFormContext] Failed to parse error response:', parseError);
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      const savedData = await response.json();
      console.log('[AssemblyFormContext] Assembly saved successfully with fresh data:', savedData);

      const successMessage = isEditing ? 'Assembly updated successfully' : 'Assembly created successfully';
      showSuccessToast(successMessage);

      // Update form data with saved data
      const assemblyData = savedData.data || savedData;
      setFormData(assemblyData);

      console.log('[AssemblyFormContext] Form data updated with saved assembly:', assemblyData);

      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }

      return true;
    } catch (error) {
      console.error('[AssemblyFormContext] Error saving assembly with fresh data:', error);

      let errorMessage = 'Failed to save assembly';
      let shouldRetry = false;

      if (error instanceof Error) {
        errorMessage = error.message;

        // Determine if we should retry based on error type
        if (error.message.includes('network') || error.message.includes('fetch') ||
            error.message.includes('timeout') || error.message.includes('500')) {
          shouldRetry = retryCount < maxRetries;
        }

        // Provide more specific error messages for common issues
        if (error.message.includes('duplicate') || error.message.includes('already exists')) {
          errorMessage = `Assembly code already exists. Please use a unique assembly code.`;
        } else if (error.message.includes('validation')) {
          errorMessage = `Validation error: ${error.message}`;
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = shouldRetry
            ? `Network error. Retrying... (${retryCount + 1}/${maxRetries + 1})`
            : 'Network error. Please check your connection and try again.';
        }
      }

      // Retry if appropriate
      if (shouldRetry) {
        console.log(`[AssemblyFormContext] Retrying save operation with fresh data (${retryCount + 1}/${maxRetries + 1})`);
        showInfoToast(`Retrying save operation... (${retryCount + 1}/${maxRetries + 1})`);

        // Wait a bit before retrying (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000));

        // Recursive retry
        return saveAssemblyWithFreshData(freshData, onSuccess, retryCount + 1, maxRetries);
      }

      showErrorToast({ error: errorMessage });
      return false;
    } finally {
      setIsSaving(false);
    }
  }, [isEditing]); // Removed setFormData and normalizeAssemblyData as they are stable functions

  /**
   * Add a part to the assembly
   */
  const addPart = useCallback((part: any) => {
    setFormData(prev => {
      // Always add to partsRequired for consistency in the form
      // The save function will handle converting to components if needed
      return {
        ...prev,
        partsRequired: [...(prev.partsRequired || []), part],
      };
    });
  }, []); // setFormData is stable, no dependencies needed

  /**
   * Update a part in the assembly
   */
  const updatePart = useCallback((index: number, part: any) => {
    setFormData(prev => {
      const partsRequired = [...(prev.partsRequired || [])];
      partsRequired[index] = part;
      return { ...prev, partsRequired };
    });
  }, []); // setFormData is stable, no dependencies needed

  /**
   * Remove a part from the assembly
   */
  const removePart = useCallback((index: number) => {
    setFormData(prev => {
      const partsRequired = [...(prev.partsRequired || [])];
      partsRequired.splice(index, 1);
      return { ...prev, partsRequired };
    });
  }, []); // setFormData is stable, no dependencies needed

  /**
   * Refresh stock data for all parts in the assembly using the enhanced utility function
   */
  const refreshStockData = useCallback(async () => {
    const currentFormData = formDataRef.current;

    if (!currentFormData.partsRequired || !Array.isArray(currentFormData.partsRequired)) {
      return;
    }

    try {

      const refreshedParts = await refreshPartStockData(
        currentFormData.partsRequired,
        {
          batchSize: 10,
          logProgress: true,
          skipInvalidParts: true
        }
      );

      // Update formData with refreshed parts
      const updatedFormData = {
        ...currentFormData,
        partsRequired: refreshedParts
      };

      setFormData(updatedFormData);
      showSuccessToast('Stock data refreshed successfully');

    } catch (error) {
      console.error('[AssemblyFormContext] Error during stock refresh:', error);
      showErrorToast({ error: error instanceof Error ? error.message : 'Failed to refresh stock' });
    }
  }, []); // setFormData is stable, no dependencies needed

  /**
   * Check for stale stock data in the current assembly
   */
  const checkStockStaleness = useCallback(() => {
    const currentFormData = formDataRef.current;

    if (!currentFormData.partsRequired || !Array.isArray(currentFormData.partsRequired)) {
      return { hasStaleData: false, staleParts: [], recommendations: [] };
    }

    return detectStaleStockData(currentFormData.partsRequired);
  }, []);

  /**
   * Get recommendation for stock refresh based on current data
   */
  const getStockRefreshRecommendation = useCallback(() => {
    const currentFormData = formDataRef.current;

    if (!currentFormData.partsRequired || !Array.isArray(currentFormData.partsRequired)) {
      return { shouldRefresh: false, reasons: [], priority: 'low' as const };
    }

    return shouldRefreshStock(currentFormData.partsRequired, {
      maxAgeHours: 1,
      forceRefreshOnSave: false, // Can be configured based on user preferences
      checkForDiscrepancies: true
    });
  }, []);

  /**
   * Automatically refresh stock if needed based on staleness detection
   */
  const autoRefreshStockIfNeeded = useCallback(async (): Promise<boolean> => {
    const recommendation = getStockRefreshRecommendation();

    if (!recommendation.shouldRefresh) {
      return false;
    }

    if (recommendation.priority === 'high') {
      await refreshStockData();
      return true;
    } else if (recommendation.priority === 'medium') {
      // Could show a toast asking user if they want to refresh
      showInfoToast('Stock data may be outdated. Consider refreshing before saving.');
      return false;
    }

    return false;
  }, [getStockRefreshRecommendation, refreshStockData]);

  // Load assembly data if editing
  useEffect(() => {
    if (assemblyId) {
      console.log('Loading assembly with ID:', assemblyId);
      loadAssembly(assemblyId);
    }
  }, [assemblyId]); // Removed loadAssembly from dependencies to prevent infinite loop

  // Debug log for tracking form data changes
  useEffect(() => {
    if (isEditing) {
      console.log('Form data updated:', formData);
    }
  }, [formData, isEditing]);

  const value = {
    formData,
    isLoading,
    isSaving,
    isEditing,
    isDirty,
    setFormData,
    updateFormField,
    resetForm,
    loadAssembly,
    saveAssembly,
    saveAssemblyWithFreshData,
    addPart,
    updatePart,
    removePart,
    refreshStockData,
    checkStockStaleness,
    autoRefreshStockIfNeeded,
    getStockRefreshRecommendation,
  };

  return (
    <AssemblyFormContext.Provider value={value}>
      {children}
    </AssemblyFormContext.Provider>
  );
}
