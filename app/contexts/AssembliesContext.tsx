'use client';

import { Assembly } from '@/app/components/tables/AssembliesTable/types';
import { getApiUrl } from '@/app/utils/apiUtils';
import { optimisticAdd, optimisticDelete, optimisticUpdateById } from '@/app/utils/optimistic-updates';
import React, { createContext, useCallback, useContext, useEffect, useRef, useState } from 'react';
import { showErrorToast, showSuccessToast } from "@/app/components/feedback/ErrorToast";

interface AssembliesContextType {
  assemblies: Assembly[];
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  isAutoRefreshEnabled: boolean;
  autoRefreshInterval: number;
  setAutoRefreshInterval: (interval: number) => void;
  toggleAutoRefresh: () => void;
  refreshAssemblies: (options?: { includeParts?: boolean }) => Promise<Assembly[]>;
  getAssembly: (id: string) => Assembly | undefined;
  deleteAssembly: (id: string) => Promise<boolean>;
  updateAssembly: (id: string, data: Partial<Assembly>) => Promise<boolean>;
  createAssembly: (data: Omit<Assembly, '_id'>) => Promise<Assembly | null>;
  duplicateAssembly: (id: string) => Promise<Assembly | null>;
}

const AssembliesContext = createContext<AssembliesContextType | undefined>(undefined);

export function useAssemblies() {
  const context = useContext(AssembliesContext);
  if (context === undefined) {
    throw new Error('useAssemblies must be used within an AssembliesProvider');
  }
  return context;
}

interface AssembliesProviderProps {
  children: React.ReactNode;
}

export function AssembliesProvider({ children }: AssembliesProviderProps) {
  const [assemblies, setAssemblies] = useState<Assembly[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [isAutoRefreshEnabled, setIsAutoRefreshEnabled] = useState(false);
  const [autoRefreshInterval, setAutoRefreshInterval] = useState(30000); // 30 seconds default

  // Use a ref to store the interval ID so it persists across renders
  const autoRefreshIntervalRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * Toggle auto-refresh on/off
   */
  const toggleAutoRefresh = useCallback(() => {
    setIsAutoRefreshEnabled(prev => !prev);
    // The actual interval setup/teardown is handled in the useEffect
  }, []);

  /**
   * Update the auto-refresh interval
   */
  const handleSetAutoRefreshInterval = useCallback((interval: number) => {
    setAutoRefreshInterval(interval);
    // The actual interval reset is handled in the useEffect
  }, []);

  /**
   * Refresh assemblies data from server
   * Using useRef to create a stable function reference that doesn't cause infinite loops
   */
  const refreshAssembliesRef = useRef<(options?: { includeParts?: boolean; isBackgroundRefresh?: boolean }) => Promise<Assembly[]>>(async () => []);

  refreshAssembliesRef.current = async (options: { includeParts?: boolean; isBackgroundRefresh?: boolean } = {}) => {
    const { includeParts = true } = options;

    // Use a simple flag to determine if this is a background refresh
    const isBackgroundRefresh = options.isBackgroundRefresh || false;

    if (!isBackgroundRefresh) {
      setIsLoading(true);
    }
    setError(null);

    try {
      // Build the URL with query parameters
      const url = new URL('/api/assemblies', window.location.origin);
      url.searchParams.append('includeParts', includeParts ? 'true' : 'false');
      
      console.log(`[AssembliesContext] Fetching assemblies with includeParts=${includeParts}`);

      const response = await fetch(url.toString());

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || 'Failed to fetch assemblies');
      }

      const data = await response.json() as { data?: any[] };
      console.log(`[AssembliesContext] Received ${data.data?.length || 0} assemblies from API`);
      
      // Transform the API data to match the expected schema with better validation
      const transformedAssemblies = (data.data || []).map((assembly: any) => {
        // Validate partsRequired is properly structured and include partDetails
        // The type AssemblyPartRequired is defined in '@/app/components/tables/AssembliesTable/types'
        // and includes partId: string, partDetails?: Part, etc.
        let partsRequiredField: Assembly['partsRequired'] = []; // Use the type from Assembly interface

        if (assembly.partsRequired && Array.isArray(assembly.partsRequired)) {
          // Recursive function to transform parts and their children
          const transformPart = (apiPart: any): any => {
            // Map API part structure to our frontend AssemblyPartRequired structure
            // Handle both populated (object) and unpopulated (string) partId
            const isPopulated = typeof apiPart.partId === 'object' && apiPart.partId !== null && apiPart.partId._id;

            const transformedPart = {
              _id: apiPart._id || undefined,
              partId: isPopulated ? apiPart.partId._id : apiPart.partId, // Extract _id from populated object or use string directly
              partDetails: isPopulated ? apiPart.partId : (apiPart.partDetails || undefined), // Use populated object as partDetails
              quantityRequired: typeof apiPart.quantityRequired === 'number' ? apiPart.quantityRequired : 1,
              unitOfMeasure: apiPart.unitOfMeasure || 'pcs',
              notes: apiPart.notes || undefined,
              substitutionsAllowed: typeof apiPart.substitutionsAllowed === 'boolean' ? apiPart.substitutionsAllowed : undefined,
              children: [], // Initialize empty, will be populated below if children exist
            };

            // Recursively transform children if they exist
            if (apiPart.children && Array.isArray(apiPart.children) && apiPart.children.length > 0) {
              transformedPart.children = apiPart.children
                .filter((childPart: any) =>
                  childPart &&
                  typeof childPart === 'object' &&
                  (typeof childPart.partId === 'string' || (typeof childPart.partId === 'object' && childPart.partId !== null && childPart.partId._id))
                )
                .map(transformPart); // Recursive call
            }

            return transformedPart;
          };

          partsRequiredField = assembly.partsRequired
            .filter((apiPart: any) =>
              apiPart &&
              typeof apiPart === 'object' &&
              (typeof apiPart.partId === 'string' || (typeof apiPart.partId === 'object' && apiPart.partId !== null && apiPart.partId._id)) // Handle both populated and unpopulated partId, with null check
            )
            .map(transformPart);
        }
        return {
          ...assembly,
          partsRequired: partsRequiredField, // Validated parts array with preserved Part objects
          isTopLevel: Boolean(assembly.isTopLevel), // Ensure boolean
          status: assembly.status || 'design_complete', // Default status
        } as Assembly; // Cast to Assembly type
      });
      
      setAssemblies(transformedAssemblies);
      setLastUpdated(new Date());

      // Only show success toast for manual refreshes
      if (!isBackgroundRefresh) {
        showSuccessToast('Assemblies refreshed successfully');
      }

      return transformedAssemblies;
    } catch (err) {
      console.error('[AssembliesContext] Error fetching assemblies:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while fetching assemblies');

      // Only show error toast for manual refreshes or if auto-refresh fails with a serious error
      if (!isBackgroundRefresh || (err instanceof Error && err.message !== 'Failed to fetch')) {
        showErrorToast({
          error: err instanceof Error ? err.message : 'Failed to load assemblies',
          duration: 5000
        });
      }
      return [];
    } finally {
      if (!isBackgroundRefresh) {
        setIsLoading(false);
      }
    }
  };

  // Create a stable callback that uses the ref
  const refreshAssemblies = useCallback(async (options?: { includeParts?: boolean; isBackgroundRefresh?: boolean }) => {
    if (refreshAssembliesRef.current) {
      return refreshAssembliesRef.current(options);
    }
    return [];
  }, []); // Empty dependency array is safe now because we use ref

  /**
   * Get a single assembly by ID
   */
  const getAssembly = useCallback((id: string) => {
    return assemblies.find(assembly => assembly._id === id);
  }, [assemblies]);

  /**
   * Delete an assembly
   */
  const deleteAssembly = useCallback(async (id: string) => {
    try {
      // Store original assemblies for rollback if needed
      const originalAssemblies = [...assemblies];

      // Optimistically update UI
      setAssemblies(prev => optimisticDelete(prev, id));

      // Make API call
      const response = await fetch(`/api/assemblies/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        // Rollback optimistic update on error
        setAssemblies(originalAssemblies);
        throw new Error(errorData.error || 'Failed to delete assembly');
      }

      showSuccessToast('Assembly deleted successfully', { duration: 3000 });
      return true;
    } catch (err) {
      console.error('Error deleting assembly:', err);
      showErrorToast({
        error: err instanceof Error ? err.message : 'Failed to delete assembly',
        duration: 5000
      });
      return false;
    }
  }, [assemblies]);

  /**
   * Update an existing assembly
   */
  const updateAssembly = useCallback(async (id: string, data: Partial<Assembly>) => {
    try {
      // Store original assemblies for rollback if needed
      const originalAssemblies = [...assemblies];
      const originalAssembly = assemblies.find(a => a._id === id);
      
      if (!originalAssembly) {
        showErrorToast({ error: 'Assembly not found' });
        return false;
      }
      
      // Log update operation
      console.log('[AssembliesContext] Updating assembly:', id, {
        fields: Object.keys(data),
        name: data.name || originalAssembly.name,
        assemblyCode: data.assemblyCode || originalAssembly.assemblyCode
      });

      // Optimistically update UI
      setAssemblies(prev => optimisticUpdateById(prev, id, data));

      // Make API call
      const response = await fetch(`/api/assemblies/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      console.log('[AssembliesContext] Update assembly response status:', response.status);

      // Handle non-OK responses
      if (!response.ok) {
        // Get the response text for better error reporting
        const responseText = await response.text();
        let errorMessage = 'Failed to update assembly';
        
        try {
          const errorData = JSON.parse(responseText);
          errorMessage = errorData.error || errorData.message || errorMessage;
          console.error('[AssembliesContext] Update assembly error response:', errorData);
        } catch (parseError) {
          console.error('[AssembliesContext] Failed to parse error response:', responseText);
        }
        
        // Rollback optimistic update on error
        setAssemblies(originalAssemblies);
        throw new Error(errorMessage);
      }

      const updatedData = await response.json() as { data: Assembly };
      console.log('[AssembliesContext] Assembly updated successfully:', updatedData.data);

      // Update the assembly in state with the server response
      setAssemblies(prev =>
        prev.map(assembly => (assembly._id === id ? updatedData.data : assembly))
      );

      showSuccessToast('Assembly updated successfully', { duration: 3000 });
      return true;
    } catch (err) {
      console.error('[AssembliesContext] Error updating assembly:', err);
      showErrorToast({
        error: err instanceof Error ? err.message : 'Failed to update assembly',
        duration: 5000
      });
      return false;
    }
  }, [assemblies]);

  /**
   * Create a new assembly
   */
  const createAssembly = useCallback(async (data: Omit<Assembly, '_id'>) => {
    try {
      // Create a temporary ID for optimistic update
      const tempId = `temp-${Date.now()}`;

      // Create optimistic assembly with temporary ID
      const optimisticAssembly = {
        ...data,
        _id: tempId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      } as Assembly;

      // Optimistically update UI
      setAssemblies(prev => optimisticAdd(prev, optimisticAssembly));

      // Log the request payload for debugging
      console.log('[AssembliesContext] Creating assembly with data:', {
        assemblyCode: data.assemblyCode,
        name: data.name,
        partsRequired: data.partsRequired?.length || 0,
        status: data.status
      });

      // Make API call
      const response = await fetch(getApiUrl('/api/assemblies'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      console.log('[AssembliesContext] Create assembly response status:', response.status);

      // Handle non-OK responses
      if (!response.ok) {
        // Get the response text for better error reporting
        const responseText = await response.text();
        let errorMessage = 'Failed to create assembly';
        
        try {
          const errorData = JSON.parse(responseText);
          errorMessage = errorData.error || errorData.message || errorMessage;
          console.error('[AssembliesContext] Create assembly error response:', errorData);
        } catch (parseError) {
          console.error('[AssembliesContext] Failed to parse error response:', responseText);
        }
        
        // Remove optimistic assembly on error
        setAssemblies(prev => prev.filter(a => a._id !== tempId));
        throw new Error(errorMessage);
      }

      const newAssembly = await response.json() as { data: Assembly };
      console.log('[AssembliesContext] Assembly created successfully:', newAssembly.data);

      // Replace optimistic assembly with actual server response
      setAssemblies(prev =>
        prev.map(assembly =>
          assembly._id === tempId ? newAssembly.data : assembly
        )
      );

      showSuccessToast('Assembly created successfully', { duration: 3000 });
      return newAssembly.data;
    } catch (err) {
      console.error('[AssembliesContext] Error creating assembly:', err);
      showErrorToast({
        error: err instanceof Error ? err.message : 'Failed to create assembly',
        duration: 5000
      });
      return null;
    }
  }, []);

  /**
   * Duplicate an assembly
   */
  const duplicateAssembly = useCallback(async (id: string) => {
    try {
      // Get original assembly
      const originalAssembly = getAssembly(id);
      
      if (!originalAssembly) {
        showErrorToast({ error: 'Assembly not found, cannot duplicate.' });
        throw new Error('Assembly not found');
      }

      // Create a temporary ID for optimistic update
      const tempId = `temp-duplicate-${Date.now()}`;
      
      // Create duplicate assembly data
      const duplicateData = {
        ...originalAssembly,
        _id: tempId, // Will be replaced with server-generated ID
        name: `${originalAssembly.name} (Copy)`,
        assemblyCode: `${originalAssembly.assemblyCode}-COPY-${Date.now().toString().slice(-5)}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      // Optimistically update UI
      const optimisticAssembly = duplicateData as Assembly;
      setAssemblies(prev => optimisticAdd(prev, optimisticAssembly));
      
      // Make API call with proper headers
      const response = await fetch(`/api/assemblies/${id}/duplicate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'include' // Include cookies if needed for auth
      });
      
      const responseData = await response.json();
      
      if (!response.ok) {
        // Remove optimistic assembly on error
        setAssemblies(prev => prev.filter(a => a._id !== tempId));
        throw new Error(responseData.error?.message || 'Failed to duplicate assembly');
      }
      
      // Replace optimistic assembly with server response
      setAssemblies(prev =>
        prev.map(assembly =>
          assembly._id === tempId ? responseData.data : assembly
        )
      );
      
      showSuccessToast('Assembly duplicated successfully', { duration: 3000 });
      return responseData.data;
    } catch (err) {
      console.error('Error duplicating assembly:', err);
      showErrorToast({
        error: err instanceof Error ? err.message : 'Failed to duplicate assembly',
        duration: 5000
      });
      return null;
    }
  }, [getAssembly, assemblies]);

  // Initial fetch and set up auto-refresh
  useEffect(() => {
    refreshAssemblies();

    // Clean up any existing interval
    return () => {
      if (autoRefreshIntervalRef.current) {
        clearInterval(autoRefreshIntervalRef.current);
        autoRefreshIntervalRef.current = null;
      }
    };
  }, []); // Remove refreshAssemblies dependency to prevent infinite loop

  // Set up or tear down auto-refresh when enabled state changes
  useEffect(() => {
    // Clean up existing interval first to prevent duplicates
    if (autoRefreshIntervalRef.current) {
      clearInterval(autoRefreshIntervalRef.current);
      autoRefreshIntervalRef.current = null;
    }

    // Only set up new interval if auto-refresh is enabled
    if (isAutoRefreshEnabled && autoRefreshInterval > 0) {
      // Use a non-state variable to track the last refresh time to avoid unnecessary renders
      let lastRefreshTime = Date.now();

      // Create new interval with the current interval value
      autoRefreshIntervalRef.current = setInterval(() => {
        // Add extra check to prevent too-frequent refreshes in case of state changes
        const now = Date.now();
        if (now - lastRefreshTime >= autoRefreshInterval * 0.9) { // 90% of interval time passed
          refreshAssemblies({ includeParts: true, isBackgroundRefresh: true }); // Always include parts, even in background refresh
          lastRefreshTime = now;
        }
      }, autoRefreshInterval);
    }

    return () => {
      if (autoRefreshIntervalRef.current) {
        clearInterval(autoRefreshIntervalRef.current);
        autoRefreshIntervalRef.current = null;
      }
    };
  }, [isAutoRefreshEnabled, autoRefreshInterval]); // Remove refreshAssemblies dependency

  const value = {
    assemblies,
    isLoading,
    error,
    lastUpdated,
    isAutoRefreshEnabled,
    autoRefreshInterval,
    setAutoRefreshInterval: handleSetAutoRefreshInterval,
    toggleAutoRefresh,
    refreshAssemblies,
    getAssembly,
    deleteAssembly,
    updateAssembly,
    createAssembly,
    duplicateAssembly,
  };

  return (
    <AssembliesContext.Provider value={value}>
      {children}
    </AssembliesContext.Provider>
  );
}
