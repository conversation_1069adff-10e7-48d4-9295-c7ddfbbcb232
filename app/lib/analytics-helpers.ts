/**
 * Utility functions for standardizing analytics responses
 */

/**
 * Standardizes the analytics response format to ensure consistent structure
 * across all analytics endpoints
 * @param data - The analytics data to standardize
 * @returns Standardized analytics response
 */
export function standardizeAnalyticsResponse(data: any) {
  // Handle null or undefined input
  if (!data) {
    return {
      summary: {
        totalItems: 0,
        totalCategories: 0,
        lowStockCount: 0,
        outOfStockCount: 0,
        totalValue: 0,
      },
      inventoryTrends: null,
      stockLevels: null,
      categoryDistribution: null,
      inventoryValue: null,
      generatedAt: new Date().toISOString(),
      error: "No data provided to standardize"
    };
  }
  
  // Ensure summary object exists and has all required fields
  const summary = data.summary || {};
  
  // Create a standardized summary with all required fields
  const standardizedSummary = {
    totalItems: Number(summary.totalItems || summary.total || 0),
    totalCategories: Number(summary.totalCategories || 0),
    lowStockCount: Number(summary.lowStockCount || summary.lowStock || 0),
    outOfStockCount: Number(summary.outOfStockCount || summary.outOfStock || 0),
    totalValue: Number(summary.totalValue || 0),
    // Add any other required summary fields here
  };

  // Create a standardized response
  return {
    summary: standardizedSummary,
    // Pass through any series data, ensuring they're arrays
    inventoryTrends: Array.isArray(data.inventoryTrends || data.trends) 
      ? data.inventoryTrends || data.trends 
      : null,
    stockLevels: Array.isArray(data.stockLevels || data.weeklyData) 
      ? data.stockLevels || data.weeklyData 
      : null,
    categoryDistribution: Array.isArray(data.categoryDistribution || data.distribution) 
      ? data.categoryDistribution || data.distribution 
      : null,
    inventoryValue: Array.isArray(data.inventoryValue || data.valueByCategory) 
      ? data.inventoryValue || data.valueByCategory 
      : null,
    // Always include generatedAt
    generatedAt: data.generatedAt || new Date().toISOString()
  };
}