/**
 * Advanced Pagination Utilities for Performance Optimization
 * 
 * Provides both offset-based and cursor-based pagination for different use cases:
 * - Offset-based: Traditional page/limit pagination (good for small datasets)
 * - Cursor-based: More efficient for large datasets and real-time data
 */

import { Types } from 'mongoose';

export interface OffsetPaginationOptions {
  page: number;
  limit: number;
  maxLimit?: number;
}

export interface CursorPaginationOptions {
  cursor?: string; // Base64 encoded cursor
  limit: number;
  maxLimit?: number;
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginationResult<T> {
  data: T[];
  pagination: {
    totalCount?: number;
    currentPage?: number;
    totalPages?: number;
    limit: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    nextCursor?: string;
    previousCursor?: string;
  };
  meta: {
    type: 'offset' | 'cursor';
    performance: {
      queryTime?: number;
      totalTime: number;
    };
  };
}

/**
 * Validates and normalizes offset-based pagination parameters
 */
export function validateOffsetPagination(options: Partial<OffsetPaginationOptions>): OffsetPaginationOptions {
  const maxLimit = options.maxLimit || 100;
  const page = Math.max(1, options.page || 1);
  const limit = Math.min(Math.max(1, options.limit || 20), maxLimit);

  return { page, limit, maxLimit };
}

/**
 * Validates and normalizes cursor-based pagination parameters
 */
export function validateCursorPagination(options: Partial<CursorPaginationOptions>): CursorPaginationOptions {
  const maxLimit = options.maxLimit || 100;
  const limit = Math.min(Math.max(1, options.limit || 20), maxLimit);
  const sortField = options.sortField || '_id';
  const sortOrder = options.sortOrder || 'desc';

  return {
    ...(options.cursor && { cursor: options.cursor }),
    limit,
    maxLimit,
    sortField,
    sortOrder
  };
}

/**
 * Encodes a cursor for cursor-based pagination
 */
export function encodeCursor(value: any, sortField: string = '_id'): string {
  const cursorData = {
    value: value instanceof Types.ObjectId ? value.toString() : value,
    field: sortField,
    timestamp: Date.now()
  };
  return Buffer.from(JSON.stringify(cursorData)).toString('base64');
}

/**
 * Decodes a cursor for cursor-based pagination
 */
export function decodeCursor(cursor: string): { value: any; field: string; timestamp: number } | null {
  try {
    const decoded = Buffer.from(cursor, 'base64').toString('utf-8');
    const cursorData = JSON.parse(decoded);
    
    // Convert back to ObjectId if it looks like one
    if (cursorData.field === '_id' && Types.ObjectId.isValid(cursorData.value)) {
      cursorData.value = new Types.ObjectId(cursorData.value);
    }
    
    return cursorData;
  } catch (error) {
    console.warn('Invalid cursor format:', error);
    return null;
  }
}

/**
 * Builds MongoDB aggregation pipeline stages for cursor-based pagination
 */
export function buildCursorPaginationStages(options: CursorPaginationOptions): any[] {
  const { cursor, limit, sortField = '_id', sortOrder = 'desc' } = options;
  const stages: any[] = [];

  // Add cursor filter if provided
  if (cursor) {
    const decodedCursor = decodeCursor(cursor);
    if (decodedCursor) {
      const operator = sortOrder === 'desc' ? '$lt' : '$gt';
      stages.push({
        $match: {
          [decodedCursor.field]: { [operator]: decodedCursor.value }
        }
      });
    }
  }

  // Add sorting
  stages.push({
    $sort: { [sortField]: sortOrder === 'desc' ? -1 : 1 }
  });

  // Add limit (fetch one extra to determine if there's a next page)
  stages.push({
    $limit: limit + 1
  });

  return stages;
}

/**
 * Processes cursor pagination results and generates pagination metadata
 */
export function processCursorResults<T extends Record<string, any>>(
  results: T[],
  options: CursorPaginationOptions,
  queryTime?: number
): PaginationResult<T> {
  const { limit, sortField = '_id' } = options;
  const hasNextPage = results.length > limit;
  
  // Remove the extra item if it exists
  if (hasNextPage) {
    results.pop();
  }

  const hasPreviousPage = !!options.cursor;
  
  // Generate cursors
  const nextCursor = hasNextPage && results.length > 0
    ? encodeCursor(results[results.length - 1]![sortField], sortField)
    : undefined;

  const previousCursor = hasPreviousPage && results.length > 0
    ? encodeCursor(results[0]![sortField], sortField)
    : undefined;

  return {
    data: results,
    pagination: {
      limit,
      hasNextPage,
      hasPreviousPage,
      ...(nextCursor && { nextCursor }),
      ...(previousCursor && { previousCursor })
    },
    meta: {
      type: 'cursor',
      performance: {
        ...(queryTime !== undefined && { queryTime }),
        totalTime: queryTime || 0
      }
    }
  };
}

/**
 * Processes offset pagination results and generates pagination metadata
 */
export function processOffsetResults<T>(
  results: T[],
  totalCount: number,
  options: OffsetPaginationOptions,
  queryTime?: number
): PaginationResult<T> {
  const { page, limit } = options;
  const totalPages = Math.ceil(totalCount / limit);
  const hasNextPage = page < totalPages;
  const hasPreviousPage = page > 1;

  return {
    data: results,
    pagination: {
      totalCount,
      currentPage: page,
      totalPages,
      limit,
      hasNextPage,
      hasPreviousPage
    },
    meta: {
      type: 'offset',
      performance: {
        ...(queryTime !== undefined && { queryTime }),
        totalTime: queryTime || 0
      }
    }
  };
}

/**
 * Determines the best pagination strategy based on request parameters and dataset size
 */
export function choosePaginationStrategy(
  requestParams: URLSearchParams,
  estimatedDatasetSize?: number
): 'offset' | 'cursor' {
  // Use cursor pagination if explicitly requested
  if (requestParams.has('cursor')) {
    return 'cursor';
  }
  
  // Use cursor pagination for large datasets (>10k records)
  if (estimatedDatasetSize && estimatedDatasetSize > 10000) {
    return 'cursor';
  }
  
  // Use cursor pagination for deep pagination (page > 100)
  const page = parseInt(requestParams.get('page') || '1');
  if (page > 100) {
    return 'cursor';
  }
  
  // Default to offset pagination for smaller datasets and shallow pagination
  return 'offset';
}

/**
 * Cache key generator for pagination results
 */
export function generateCacheKey(
  endpoint: string,
  params: Record<string, any>,
  paginationType: 'offset' | 'cursor'
): string {
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}:${params[key]}`)
    .join('|');
  
  return `pagination:${paginationType}:${endpoint}:${sortedParams}`;
}

/**
 * Response compression utility for large datasets
 */
export function shouldCompress(dataSize: number, itemCount: number): boolean {
  // Compress if response is larger than 1KB or has more than 10 items
  return dataSize > 1024 || itemCount > 10;
}
