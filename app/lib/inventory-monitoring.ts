/**
 * INVENTORY SERVICES MONITORING AND LOGGING
 * 
 * This module provides comprehensive monitoring and logging capabilities
 * specifically for the new inventory services and migration process.
 * 
 * Features:
 * - Real-time inventory operation monitoring
 * - Stock level change tracking
 * - Performance metrics for inventory queries
 * - Error tracking and alerting
 * - Migration progress monitoring
 * - Service health checks
 */

import { captureException, setTag, addBreadcrumb } from './logging-utils';

// Monitoring configuration
interface InventoryMonitoringConfig {
  enableStockTracking: boolean;
  enablePerformanceMonitoring: boolean;
  enableErrorAlerting: boolean;
  stockChangeThreshold: number;
  performanceThresholds: {
    queryTimeWarning: number;
    queryTimeError: number;
    stockUpdateTime: number;
  };
  alertingThresholds: {
    errorRate: number;
    stockDiscrepancy: number;
    performanceDegradation: number;
  };
}

// Stock change event interface
interface StockChangeEvent {
  partId: string;
  warehouseId: string;
  stockType: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected';
  previousQuantity: number;
  newQuantity: number;
  changeAmount: number;
  changeType: 'increment' | 'decrement' | 'set';
  timestamp: Date;
  source: 'api' | 'migration' | 'system' | 'manual';
  userId?: string;
  transactionId?: string;
  reason?: string;
}

// Inventory operation metrics
interface InventoryOperationMetric {
  operation: 'query' | 'update' | 'create' | 'delete' | 'transfer';
  service: 'legacy' | 'v3' | 'dual';
  startTime: number;
  endTime: number;
  duration: number;
  recordsAffected: number;
  success: boolean;
  error?: string;
  metadata?: Record<string, any>;
}

// Service health status
interface InventoryServiceHealth {
  service: 'legacy' | 'v3';
  status: 'healthy' | 'degraded' | 'unhealthy';
  lastCheck: Date;
  responseTime: number;
  errorRate: number;
  stockConsistency: number;
  details?: Record<string, any>;
}

class InventoryMonitor {
  private config: InventoryMonitoringConfig;
  private stockChangeEvents: StockChangeEvent[] = [];
  private operationMetrics: InventoryOperationMetric[] = [];
  private serviceHealth: Map<string, InventoryServiceHealth> = new Map();
  private alertThrottling: Map<string, number> = new Map();

  constructor() {
    this.config = this.loadConfiguration();
    this.initializeMonitoring();
  }

  private loadConfiguration(): InventoryMonitoringConfig {
    return {
      enableStockTracking: process.env.ENABLE_INVENTORY_STOCK_TRACKING === 'true',
      enablePerformanceMonitoring: process.env.ENABLE_INVENTORY_PERFORMANCE_MONITORING === 'true',
      enableErrorAlerting: process.env.ENABLE_INVENTORY_ERROR_ALERTING === 'true',
      stockChangeThreshold: parseInt(process.env.INVENTORY_STOCK_CHANGE_THRESHOLD || '100'),
      performanceThresholds: {
        queryTimeWarning: parseInt(process.env.INVENTORY_QUERY_TIME_WARNING || '500'), // 500ms
        queryTimeError: parseInt(process.env.INVENTORY_QUERY_TIME_ERROR || '2000'), // 2 seconds
        stockUpdateTime: parseInt(process.env.INVENTORY_UPDATE_TIME_LIMIT || '1000') // 1 second
      },
      alertingThresholds: {
        errorRate: parseFloat(process.env.INVENTORY_ERROR_RATE_THRESHOLD || '0.05'), // 5%
        stockDiscrepancy: parseFloat(process.env.INVENTORY_DISCREPANCY_THRESHOLD || '0.01'), // 1%
        performanceDegradation: parseFloat(process.env.INVENTORY_PERFORMANCE_DEGRADATION || '2.0') // 2x slower
      }
    };
  }

  private initializeMonitoring(): void {
    if (this.config.enableStockTracking) {
      console.log('[InventoryMonitor] Stock tracking enabled');
    }

    // Set up periodic cleanup
    setInterval(() => {
      this.cleanupOldData();
    }, 24 * 60 * 60 * 1000); // Daily cleanup
  }

  /**
   * Track stock changes
   */
  trackStockChange(event: Omit<StockChangeEvent, 'timestamp'>): void {
    if (!this.config.enableStockTracking) return;

    const stockChangeEvent: StockChangeEvent = {
      ...event,
      timestamp: new Date()
    };

    this.stockChangeEvents.push(stockChangeEvent);

    // Keep only recent events (last 1000)
    if (this.stockChangeEvents.length > 1000) {
      this.stockChangeEvents = this.stockChangeEvents.slice(-1000);
    }

    // Log significant stock changes
    if (Math.abs(event.changeAmount) >= this.config.stockChangeThreshold) {
      console.log(`[InventoryMonitor] Significant stock change detected:`, {
        partId: event.partId,
        stockType: event.stockType,
        changeAmount: event.changeAmount,
        source: event.source
      });

      // Add breadcrumb for tracking
      addBreadcrumb({
        message: `Stock change: ${event.changeType} ${Math.abs(event.changeAmount)} units`,
        category: 'inventory',
        level: 'info'
      });
    }

    // Check for potential stock discrepancies
    this.checkStockConsistency(stockChangeEvent);
  }

  /**
   * Track inventory operation performance
   */
  trackInventoryOperation<T>(
    operation: 'query' | 'update' | 'create' | 'delete' | 'transfer',
    service: 'legacy' | 'v3' | 'dual',
    fn: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    if (!this.config.enablePerformanceMonitoring) {
      return fn();
    }

    const startTime = Date.now();

    return fn()
      .then((result) => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        const metric: InventoryOperationMetric = {
          operation,
          service,
          startTime,
          endTime,
          duration,
          recordsAffected: this.extractRecordsAffected(result),
          success: true,
          ...(metadata && { metadata })
        };

        this.recordOperationMetric(metric);
        this.checkPerformanceThresholds(metric);

        return result;
      })
      .catch((error) => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        const metric: InventoryOperationMetric = {
          operation,
          service,
          startTime,
          endTime,
          duration,
          recordsAffected: 0,
          success: false,
          error: error.message,
          ...(metadata && { metadata })
        };

        this.recordOperationMetric(metric);
        this.triggerAlert('inventory_operation_error', {
          operation,
          service,
          error: error.message,
          duration,
          metadata
        });

        throw error;
      });
  }

  private recordOperationMetric(metric: InventoryOperationMetric): void {
    this.operationMetrics.push(metric);

    // Keep only recent metrics (last 500 operations)
    if (this.operationMetrics.length > 500) {
      this.operationMetrics = this.operationMetrics.slice(-500);
    }

    if (this.config.enablePerformanceMonitoring) {
      console.log(`[InventoryMonitor] Operation: ${metric.operation} (${metric.service}) - ${metric.duration}ms`, {
        recordsAffected: metric.recordsAffected,
        success: metric.success
      });
    }
  }

  private checkPerformanceThresholds(metric: InventoryOperationMetric): void {
    const { queryTimeWarning, queryTimeError, stockUpdateTime } = this.config.performanceThresholds;

    // Check query time thresholds
    if (metric.operation === 'query' && metric.duration > queryTimeError) {
      this.triggerAlert('inventory_query_performance_critical', {
        operation: metric.operation,
        service: metric.service,
        duration: metric.duration,
        threshold: queryTimeError
      });
    } else if (metric.operation === 'query' && metric.duration > queryTimeWarning) {
      this.triggerAlert('inventory_query_performance_warning', {
        operation: metric.operation,
        service: metric.service,
        duration: metric.duration,
        threshold: queryTimeWarning
      });
    }

    // Check stock update time thresholds
    if ((metric.operation === 'update' || metric.operation === 'create') && metric.duration > stockUpdateTime) {
      this.triggerAlert('inventory_update_performance_warning', {
        operation: metric.operation,
        service: metric.service,
        duration: metric.duration,
        threshold: stockUpdateTime
      });
    }
  }

  private extractRecordsAffected(result: any): number {
    if (typeof result === 'object' && result !== null) {
      if (Array.isArray(result)) {
        return result.length;
      }
      if (result.recordsAffected) {
        return result.recordsAffected;
      }
      if (result.modifiedCount) {
        return result.modifiedCount;
      }
      if (result.insertedCount) {
        return result.insertedCount;
      }
    }
    return 1;
  }

  /**
   * Check stock consistency between services
   */
  private checkStockConsistency(event: StockChangeEvent): void {
    // This would involve comparing stock levels between legacy and V3 services
    // For now, we'll log potential discrepancies
    if (event.source === 'migration' && Math.abs(event.changeAmount) > this.config.stockChangeThreshold) {
      console.warn(`[InventoryMonitor] Large stock change during migration:`, {
        partId: event.partId,
        changeAmount: event.changeAmount,
        stockType: event.stockType
      });
    }
  }

  /**
   * Update service health status
   */
  updateServiceHealth(service: 'legacy' | 'v3', health: Partial<InventoryServiceHealth>): void {
    const currentHealth = this.serviceHealth.get(service) || {
      service,
      status: 'healthy',
      lastCheck: new Date(),
      responseTime: 0,
      errorRate: 0,
      stockConsistency: 1.0
    };

    const updatedHealth: InventoryServiceHealth = {
      ...currentHealth,
      ...health,
      lastCheck: new Date()
    };

    this.serviceHealth.set(service, updatedHealth);

    console.log(`[InventoryMonitor] Service health updated: ${service} - ${updatedHealth.status}`);

    // Trigger alerts for unhealthy services
    if (updatedHealth.status === 'unhealthy') {
      this.triggerAlert('inventory_service_unhealthy', {
        service,
        status: updatedHealth.status,
        responseTime: updatedHealth.responseTime,
        errorRate: updatedHealth.errorRate,
        stockConsistency: updatedHealth.stockConsistency
      });
    }
  }

  /**
   * Trigger alerts with throttling
   */
  private triggerAlert(alertType: string, details: Record<string, any>): void {
    if (!this.config.enableErrorAlerting) return;

    // Implement alert throttling (max 1 alert per type per 5 minutes)
    const throttleKey = `${alertType}-${JSON.stringify(details)}`;
    const lastAlert = this.alertThrottling.get(throttleKey) || 0;
    const now = Date.now();
    
    if (now - lastAlert < 5 * 60 * 1000) { // 5 minutes
      return; // Throttled
    }

    this.alertThrottling.set(throttleKey, now);

    // Log the alert
    console.error(`[InventoryMonitor] ALERT: ${alertType}`, details);

    // Send to error tracking service
    setTag('alert.type', alertType);
    setTag('inventory.component', 'monitoring');
    
    captureException(new Error(`Inventory alert: ${alertType}`), {
      level: details.severity === 'critical' ? 'fatal' : 'warning',
      tags: {
        alertType,
        component: 'inventory-monitor'
      },
      extra: details
    });
  }

  /**
   * Get inventory analytics
   */
  getInventoryAnalytics(): {
    stockChanges: any;
    operationMetrics: any;
    serviceHealth: any;
  } {
    const recentStockChanges = this.stockChangeEvents.slice(-50);
    const recentOperations = this.operationMetrics.slice(-50);

    const stockChanges = {
      totalChanges: this.stockChangeEvents.length,
      recentChanges: recentStockChanges.length,
      changesByType: this.groupBy(recentStockChanges, 'changeType'),
      changesBySource: this.groupBy(recentStockChanges, 'source'),
      largestChanges: recentStockChanges
        .sort((a, b) => Math.abs(b.changeAmount) - Math.abs(a.changeAmount))
        .slice(0, 10)
    };

    const operationMetrics = {
      totalOperations: this.operationMetrics.length,
      recentOperations: recentOperations.length,
      avgResponseTime: recentOperations.reduce((sum, m) => sum + m.duration, 0) / recentOperations.length || 0,
      successRate: recentOperations.filter(m => m.success).length / recentOperations.length || 0,
      operationsByType: this.groupBy(recentOperations, 'operation'),
      operationsByService: this.groupBy(recentOperations, 'service')
    };

    const serviceHealth = Object.fromEntries(this.serviceHealth.entries());

    return { stockChanges, operationMetrics, serviceHealth };
  }

  private groupBy<T>(array: T[], key: keyof T): Record<string, number> {
    return array.reduce((acc, item) => {
      const groupKey = String(item[key]);
      acc[groupKey] = (acc[groupKey] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  /**
   * Clean up old data
   */
  private cleanupOldData(): void {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 7); // Keep 7 days of data

    // Clean up old stock change events
    this.stockChangeEvents = this.stockChangeEvents.filter(
      event => event.timestamp > cutoffDate
    );

    // Clean up old operation metrics
    const cutoffTime = cutoffDate.getTime();
    this.operationMetrics = this.operationMetrics.filter(
      metric => metric.startTime > cutoffTime
    );

    console.log(`[InventoryMonitor] Cleaned up old data. Stock changes: ${this.stockChangeEvents.length}, Operations: ${this.operationMetrics.length}`);
  }

  /**
   * Export monitoring data for analysis
   */
  exportMonitoringData(): {
    config: InventoryMonitoringConfig;
    stockChangeEvents: StockChangeEvent[];
    operationMetrics: InventoryOperationMetric[];
    serviceHealth: Record<string, InventoryServiceHealth>;
    exportTimestamp: Date;
  } {
    return {
      config: this.config,
      stockChangeEvents: this.stockChangeEvents,
      operationMetrics: this.operationMetrics,
      serviceHealth: Object.fromEntries(this.serviceHealth.entries()),
      exportTimestamp: new Date()
    };
  }
}

// Singleton instance
const inventoryMonitor = new InventoryMonitor();

// Export convenience functions
export const trackStockChange = (event: Omit<StockChangeEvent, 'timestamp'>) =>
  inventoryMonitor.trackStockChange(event);

export const trackInventoryOperation = <T>(
  operation: 'query' | 'update' | 'create' | 'delete' | 'transfer',
  service: 'legacy' | 'v3' | 'dual',
  fn: () => Promise<T>,
  metadata?: Record<string, any>
) => inventoryMonitor.trackInventoryOperation(operation, service, fn, metadata);

export const updateInventoryServiceHealth = (service: 'legacy' | 'v3', health: Partial<InventoryServiceHealth>) =>
  inventoryMonitor.updateServiceHealth(service, health);

export const getInventoryAnalytics = () => inventoryMonitor.getInventoryAnalytics();

export const exportInventoryMonitoringData = () => inventoryMonitor.exportMonitoringData();

export default inventoryMonitor;
