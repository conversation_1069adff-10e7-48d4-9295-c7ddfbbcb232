import mongoose from 'mongoose';
import { Part } from '@/app/models';

/**
 * Interface for validation error
 */
export interface ValidationError {
  field: string;
  message: string;
}

/**
 * Validate assembly data for creation or update
 * @param data The assembly data to validate
 * @returns An array of validation errors, empty if valid
 */
/**
 * Validates assembly data before creation or update operations.
 *
 * This function checks for required fields, data types, and consistency of the assembly data,
 * including its constituent parts. It queries the database to ensure referenced parts exist.
 *
 * @param {any} data - The assembly data object to validate.
 * @param {mongoose.ClientSession} [session] - Optional Mongoose client session for transactional database operations.
 * @returns {Promise<ValidationError[]>} A promise that resolves to an array of validation errors. An empty array indicates the data is valid.
 */
export async function validateAssemblyData(data: any, session?: mongoose.ClientSession): Promise<ValidationError[]> {
  const errors: ValidationError[] = [];

  // Validate top-level assembly data
  if (!data || typeof data !== 'object') {
    return [{ field: 'data', message: 'Assembly data must be a valid object' }];
  }

  // Check required fields
  if (!data.name || typeof data.name !== 'string' || data.name.trim() === '') {
    errors.push({ field: 'name', message: 'Assembly name is required and must be a non-empty string' });
  }

  // Check for assembly_id field - optional with validation
  if (data.assembly_id !== undefined && data.assembly_id !== null) {
    if (typeof data.assembly_id !== 'string' || data.assembly_id.trim() === '') {
      errors.push({ field: 'assembly_id', message: 'Assembly ID must be a non-empty string if provided' });
    }
  }

  // Check for _id field - optional with validation
  if (data._id !== undefined && data._id !== null) {
    if (typeof data._id !== 'string' || data._id.trim() === '') {
      errors.push({ field: '_id', message: 'Assembly MongoDB ID must be a non-empty string if provided' });
    }
  }

  // Validate description if provided
  if (data.description !== undefined && data.description !== null && typeof data.description !== 'string') {
    errors.push({ field: 'description', message: 'Description must be a string' });
  }

  // Check if partsRequired is provided and is an array
  if (!data.partsRequired || !Array.isArray(data.partsRequired)) {
    errors.push({ field: 'partsRequired', message: 'Parts required must be a valid array' });
    // Cannot proceed with part validation
    return errors;
  }

  // Check if partsRequired is not empty
  if (data.partsRequired.length === 0) {
    errors.push({ field: 'partsRequired', message: 'At least one part is required in the assembly' });
    return errors;
  }

  // Check parts for duplicates
  const partIds = data.partsRequired.map((p: any) => p.partId).filter(Boolean);
  if (partIds.length !== new Set(partIds).size) {
    errors.push({ field: 'partsRequired', message: 'Duplicate part IDs found. Each part ID must be unique within an assembly.' });
  }

  // Collect all part IDs and parent IDs for validation in a single query
  const uniquePartIds = new Set<string>();
  const uniqueParentIds = new Set<string>();

  for (let i = 0; i < data.partsRequired.length; i++) {
    const part = data.partsRequired[i];
    if (!part || typeof part !== 'object') continue;

    const partId = part.partId; 
    const parentId = part.parentId;

    if (partId && typeof partId === 'string' && partId.trim() !== '') {
      uniquePartIds.add(partId);
    }

    if (parentId && typeof parentId === 'string' && parentId.trim() !== '') {
      uniqueParentIds.add(parentId);
    }
  }

  // Fetch all parts and parent parts in a single query each
  const allPartIds = Array.from(uniquePartIds);
  const allParentIds = Array.from(uniqueParentIds);
  
  // Create a map to quickly check if a part exists
  const existingPartIds = new Map<string, boolean>();
  const existingParentIds = new Map<string, boolean>();
  
  try {
    // Query to fetch all needed part IDs in a single operation
    if (allPartIds.length > 0) {
      const foundParts = await Part.find({ _id: { $in: allPartIds } })
        .session(session ?? null)
        .lean()
        .select('_id');
      
      // Create a map for quick lookups
      foundParts.forEach((part: any) => {
        existingPartIds.set(part._id.toString(), true);
      });
    }
    
    // Query to fetch all needed parent IDs in a single operation
    if (allParentIds.length > 0) {
      const foundParentParts = await Part.find({ _id: { $in: allParentIds } })
        .session(session ?? null)
        .lean()
        .select('_id');
      
      // Create a map for quick lookups
      foundParentParts.forEach((part: any) => {
        existingParentIds.set(part._id.toString(), true);
      });
    }
  } catch (err) {
    console.error(`[Validate] Error fetching parts:`, err);
    errors.push({
      field: 'partsRequired',
      message: `Error validating parts: ${err instanceof Error ? err.message : String(err)}`
    });
    return errors;
  }

  // Now validate individual parts using the lookup maps
  for (let i = 0; i < data.partsRequired.length; i++) {
    const part = data.partsRequired[i];
    const fieldPrefix = `partsRequired[${i}]`;

    // Check if part object is valid
    if (!part || typeof part !== 'object') {
      console.log(`[Validate] Part at index ${i} is not a valid object`);
      errors.push({ field: fieldPrefix, message: 'Part entry must be a valid object' });
      continue;
    }

    // Validate structure within partsRequired array (e.g., partId, quantityRequired)
    const partId = part.partId; // Field name is partId
    const qty = part.quantityRequired; // Field name is quantityRequired (matches database schema)
    const level = part.level !== undefined ? part.level : 0; // Optional level field
    const parentId = part.parentId; // Optional parentId field

    // Check if partId is provided and is a string
    if (!partId || typeof partId !== 'string' || partId.trim() === '') {
      errors.push({ field: `${fieldPrefix}.partId`, message: 'Part ID is required and must be a non-empty string' });
    } else if (!existingPartIds.has(partId)) {
      // Part doesn't exist in database
      console.error(`[Validate] Could not find part with _id: ${partId}`);
      errors.push({
        field: `${fieldPrefix}.partId`,
        message: `Part with ID "${partId}" was not found in the database. Please ensure it exists.`
      });
    }

    // Check if quantity is valid
    if (qty === undefined || qty === null) {
       errors.push({
        field: `${fieldPrefix}.quantity`,
        message: 'Quantity is missing'
      });
    } else {
        const numQty = Number(qty);
        if (isNaN(numQty) || !Number.isInteger(numQty) || numQty < 1) {
          errors.push({
            field: `${fieldPrefix}.quantity`,
            message: 'Quantity must be a positive integer (at least 1)'
          });
        }
    }
    
    // Validate level if provided
    if (level !== undefined && level !== null) {
      const numLevel = Number(level);
      if (isNaN(numLevel) || !Number.isInteger(numLevel) || numLevel < 0) {
        errors.push({
          field: `${fieldPrefix}.level`,
          message: 'Level must be a non-negative integer'
        });
      }
    }
    
    // Validate parentId if provided and not null
    if (parentId !== undefined && parentId !== null) {
      if (typeof parentId !== 'string' || parentId.trim() === '') {
        errors.push({
          field: `${fieldPrefix}.parentId`,
          message: 'Parent ID must be a non-empty string if provided'
        });
      } else if (!existingParentIds.has(parentId)) {
        // Parent part doesn't exist in database
        console.error(`[Validate] Could not find parent part with _id: ${parentId}`);
        errors.push({
          field: `${fieldPrefix}.parentId`,
          message: `Parent part with ID "${parentId}" was not found in the database. Please ensure it exists.`
        });
      }
    }
  } // End of loop through partsRequired

  console.log("[Validate] Finished assembly validation. Errors found:", errors.length > 0 ? errors : "None");
  return errors;
}

/**
 * Validate part data for creation or update
 * @param data The part data to validate
 * @returns An array of validation errors, empty if valid
 */
export function validatePartData(data: any): ValidationError[] {
  const errors: ValidationError[] = [];

  // Check required fields from the new schema
  // _id (String, Primary Key - Part Number/Code)
  if (!data._id || typeof data._id !== 'string' || data._id.trim() === '') {
    errors.push({ field: '_id', message: 'Part ID (_id) is required and must be a non-empty string' });
  } else if (!/^[A-Za-z0-9-_]+$/.test(data._id)) { // Keep similar validation for code format
    errors.push({
      field: '_id',
      message: 'Part ID (_id) must contain only letters, numbers, hyphens, and underscores'
    });
  }

  // name (String)
  if (!data.name || typeof data.name !== 'string' || data.name.trim() === '') {
    errors.push({ field: 'name', message: 'Part name is required and must be a non-empty string' });
  } else if (data.name.length < 2) {
    errors.push({ field: 'name', message: 'Part name must be at least 2 characters' });
  } else if (data.name.length > 100) { // Keep reasonable length limit
    errors.push({ field: 'name', message: 'Part name cannot exceed 100 characters' });
  }

  // description (String, Optional)
  if (data.description !== undefined && data.description !== null) {
    if (typeof data.description !== 'string') {
      errors.push({ field: 'description', message: 'Description must be a string' });
    } else if (data.description.length > 1000) { // Keep reasonable length limit
      errors.push({ field: 'description', message: 'Description cannot exceed 1000 characters' });
    }
  }

  // technical_specs (String, Optional)
  if (data.technical_specs !== undefined && data.technical_specs !== null) {
    if (typeof data.technical_specs !== 'string') {
      errors.push({ field: 'technical_specs', message: 'Technical specs must be a string' });
    } // Add length validation if needed
  }

  // is_manufactured (Boolean)
  if (data.is_manufactured === undefined || data.is_manufactured === null) {
     errors.push({ field: 'is_manufactured', message: 'Is manufactured field is required' });
  } else if (typeof data.is_manufactured !== 'boolean') {
    errors.push({ field: 'is_manufactured', message: 'Is manufactured must be a boolean (true or false)' });
  }

  // reorder_level (Int32 | Null)
  if (data.reorder_level !== undefined && data.reorder_level !== null) {
    const value = Number(data.reorder_level);
    if (isNaN(value) || !Number.isInteger(value)) {
      errors.push({ field: 'reorder_level', message: 'Reorder level must be an integer' });
    } else if (value < 0) {
      errors.push({ field: 'reorder_level', message: 'Reorder level cannot be negative' });
    }
  }

  // status (String)
  if (!data.status || typeof data.status !== 'string' || data.status.trim() === '') {
    errors.push({ field: 'status', message: 'Status is required and must be a non-empty string' });
  } else {
    // Optional: Add validation for specific allowed status values if known
    // const allowedStatus = ['active', 'inactive', 'obsolete'];
    // if (!allowedStatus.includes(data.status)) {
    //   errors.push({ field: 'status', message: `Status must be one of: ${allowedStatus.join(', ')}` });
    // }
  }

  // inventory (Object) - Add validation if structure is known and required
  // Example:
  // if (data.inventory === undefined || data.inventory === null || typeof data.inventory !== 'object') {
  //   errors.push({ field: 'inventory', message: 'Inventory object is required' });
  // } else {
  //   if (data.inventory.stock === undefined || typeof data.inventory.stock !== 'number') {
  //     errors.push({ field: 'inventory.stock', message: 'Inventory stock level is required and must be a number' });
  //   }
  //   // Add more checks for inventory object properties as needed
  // }

  // REMOVED validation for deprecated fields: cost, weight, inventoryLevel, reorderPoint, etc.
  // REMOVED validation for isAssembly/is_assembly and sub_parts

  return errors;
}

/**
 * Format validation errors for API response
 * @param errors Array of validation errors
 * @returns Formatted error message
 */
export function formatValidationErrors(errors: ValidationError[]): string {
  return errors.map(error => `${error.field}: ${error.message}`).join('; ');
}
