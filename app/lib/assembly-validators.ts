/**
 * Assembly validation utilities for validating assembly DTOs
 *
 * This module provides validation functions for assembly creation and update operations.
 * It validates the structure, types, and business rules for assembly data.
 *
 * ⚠️  TEMPORARY AUTHENTICATION BYPASS ⚠️
 *
 * The following authentication-related validations are currently DISABLED:
 * - createdBy field requirement in validateCreateAssemblyDto (line ~72)
 * - updatedBy field requirement in validateUpdateAssemblyDto (line ~142)
 *
 * These were temporarily disabled because the authentication system is not yet implemented.
 *
 * 🔧 TO RE-ENABLE WHEN AUTHENTICATION IS IMPLEMENTED:
 * 1. Uncomment and restore the required field validations for createdBy/updatedBy
 * 2. Update the error messages to be more specific about authentication requirements
 * 3. Ensure the frontend sends proper user IDs in these fields
 * 4. Test the validation with actual authenticated users
 *
 * Search for "TODO: Re-enable when authentication is implemented" to find all related changes.
 */

import Assembly from '@/app/models/assembly.model';
import Part from '@/app/models/part.model';
import { Types } from 'mongoose';

// Define interfaces for the lean documents
interface ILeanDocument {
  _id: Types.ObjectId;
  [key: string]: any;
}

interface IPartDocument extends ILeanDocument {
  _id: Types.ObjectId;
}

interface IAssemblyDocument extends ILeanDocument {
  _id: Types.ObjectId;
}

/**
 * Common validation error interface
 */
export interface ValidationError {
  field: string;
  message: string;
}

/**
 * Validates if a string is a valid MongoDB ObjectId
 */
export const isValidObjectId = (id: string): boolean => {
  return Types.ObjectId.isValid(id);
};

/**
 * Validates if a string is a valid assembly status
 */
export const isValidAssemblyStatus = (status: string): boolean => {
  return ['active', 'pending_review', 'design_phase', 'design_complete', 'obsolete', 'archived'].includes(status);
};

/**
 * Validates the create assembly DTO
 */
export const validateCreateAssemblyDto = (data: any): { success: boolean; errors: ValidationError[]; data?: any } => {
  const errors: ValidationError[] = [];
  
  // Required fields
  if (!data.assemblyCode || typeof data.assemblyCode !== 'string' || data.assemblyCode.trim() === '') {
    errors.push({ field: 'assemblyCode', message: 'Assembly code is required' });
  }
  
  if (!data.name || typeof data.name !== 'string' || data.name.trim() === '') {
    errors.push({ field: 'name', message: 'Name is required' });
  }
  
  if (!data.version || (typeof data.version !== 'string' && typeof data.version !== 'number') ||
      (typeof data.version === 'string' && data.version.trim() === '') ||
      (typeof data.version === 'number' && data.version <= 0)) {
    errors.push({ field: 'version', message: 'Version is required and must be a positive number or string' });
  }
  
  if (!data.status || !isValidAssemblyStatus(data.status)) {
    errors.push({ 
      field: 'status', 
      message: 'Valid status is required (active, pending_review, design_phase, design_complete, obsolete, archived)' 
    });
  }
  
  // TODO: Re-enable when authentication is implemented
  // Temporarily making createdBy optional since authentication is not yet implemented
  if (data.createdBy && !isValidObjectId(data.createdBy)) {
    errors.push({ field: 'createdBy', message: 'Invalid createdBy user ID format' });
  }
  
  // Optional fields with validation if provided
  if (data.parentId && !isValidObjectId(data.parentId)) {
    errors.push({ field: 'parentId', message: 'Invalid parentId format' });
  }
  
  // Validate partsRequired if provided
  if (data.partsRequired && Array.isArray(data.partsRequired)) {
    data.partsRequired.forEach((part: any, index: number) => {
      if (!part.partId || !isValidObjectId(part.partId)) {
        errors.push({ 
          field: `partsRequired[${index}].partId`, 
          message: 'Valid partId is required' 
        });
      }
      
      if (part.quantityRequired === undefined || typeof part.quantityRequired !== 'number' || part.quantityRequired <= 0) {
        errors.push({
          field: `partsRequired[${index}].quantityRequired`,
          message: 'Quantity must be a positive number'
        });
      }
      
      // unitOfMeasure is optional, but if provided should be a string
      if (part.unitOfMeasure !== undefined && typeof part.unitOfMeasure !== 'string') {
        errors.push({ 
          field: `partsRequired[${index}].unitOfMeasure`,
          message: 'unitOfMeasure must be a string if provided'
        });
      }
    });
  }
  
  // Validate subAssemblies if provided
  if (data.subAssemblies && Array.isArray(data.subAssemblies)) {
    data.subAssemblies.forEach((sub: any, index: number) => {
      if (!sub.subAssemblyId || !isValidObjectId(sub.subAssemblyId)) {
        errors.push({ 
          field: `subAssemblies[${index}].subAssemblyId`, 
          message: 'Valid subAssemblyId is required' 
        });
      }
      
      if (sub.quantity === undefined || typeof sub.quantity !== 'number' || sub.quantity <= 0 || !Number.isInteger(sub.quantity)) {
        errors.push({ 
          field: `subAssemblies[${index}].quantity`, 
          message: 'Quantity must be a positive integer' 
        });
      }
    });
  }
  
  if (errors.length > 0) {
    return { success: false, errors };
  }
  
  return { success: true, data, errors: [] };
};

/**
 * Validates the update assembly DTO
 */
export const validateUpdateAssemblyDto = (data: any): { success: boolean; errors: ValidationError[]; data?: any } => {
  const errors: ValidationError[] = [];
  
  // TODO: Re-enable when authentication is implemented
  // Temporarily making updatedBy optional since authentication is not yet implemented
  if (data.updatedBy && !isValidObjectId(data.updatedBy)) {
    errors.push({ field: 'updatedBy', message: 'Invalid updatedBy user ID format' });
  }
  
  // Optional fields with validation if provided
  if (data.status && !isValidAssemblyStatus(data.status)) {
    errors.push({ 
      field: 'status', 
      message: 'Status must be one of: active, pending_review, design_phase, design_complete, obsolete, archived' 
    });
  }
  
  if (data.parentId !== undefined && data.parentId !== null && !isValidObjectId(data.parentId)) {
    errors.push({ field: 'parentId', message: 'Invalid parentId format' });
  }
  
  // Validate partsRequired if provided (can be null to clear)
  if (data.partsRequired !== undefined) {
    if (data.partsRequired === null) {
      // This is valid - it means clear all parts
      data.partsRequired = [];
    } else if (Array.isArray(data.partsRequired)) {
      data.partsRequired.forEach((part: any, index: number) => {
        if (!part.partId || !isValidObjectId(part.partId)) {
          errors.push({ 
            field: `partsRequired[${index}].partId`, 
            message: 'Valid partId is required' 
          });
        }
        
        if (part.quantityRequired === undefined || typeof part.quantityRequired !== 'number' || part.quantityRequired <= 0 || !Number.isInteger(part.quantityRequired)) {
          errors.push({
            field: `partsRequired[${index}].quantityRequired`,
            message: 'Quantity must be a positive integer'
          });
        }
        
        // unitOfMeasure is optional, but if provided should be a string
        if (part.unitOfMeasure !== undefined && typeof part.unitOfMeasure !== 'string') {
          errors.push({ 
            field: `partsRequired[${index}].unitOfMeasure`,
            message: 'unitOfMeasure must be a string if provided'
          });
        }
      });
    } else {
      errors.push({ 
        field: 'partsRequired', 
        message: 'partsRequired must be an array or null' 
      });
    }
  }
  
  // Validate subAssemblies if provided (can be null to clear)
  if (data.subAssemblies !== undefined) {
    if (data.subAssemblies === null) {
      // This is valid - it means clear all sub-assemblies
      data.subAssemblies = [];
    } else if (Array.isArray(data.subAssemblies)) {
      data.subAssemblies.forEach((sub: any, index: number) => {
        if (!sub.subAssemblyId || !isValidObjectId(sub.subAssemblyId)) {
          errors.push({ 
            field: `subAssemblies[${index}].subAssemblyId`, 
            message: 'Valid subAssemblyId is required' 
          });
        }
        
        if (sub.quantity === undefined || typeof sub.quantity !== 'number' || sub.quantity <= 0 || !Number.isInteger(sub.quantity)) {
          errors.push({ 
            field: `subAssemblies[${index}].quantity`, 
            message: 'Quantity must be a positive integer' 
          });
        }
      });
    } else {
      errors.push({ 
        field: 'subAssemblies', 
        message: 'subAssemblies must be an array or null' 
      });
    }
  }
  
  if (errors.length > 0) {
    return { success: false, errors };
  }
  
  return { success: true, data, errors: [] };
};

/**
 * Validates that all parts in partsRequired exist in the database (including hierarchical children)
 */
export const validatePartsExist = async (parts: Array<{ partId: string; children?: Array<{ partId: string; children?: any[] }> }>): Promise<{ success: boolean; errors: ValidationError[] }> => {
  if (!parts || !parts.length) return { success: true, errors: [] };

  // Recursively collect all part IDs from the hierarchical structure
  const collectAllPartIds = (partsList: Array<{ partId: string; children?: any[] }>): string[] => {
    const ids: string[] = [];
    for (const part of partsList) {
      ids.push(part.partId);
      if (part.children && Array.isArray(part.children) && part.children.length > 0) {
        ids.push(...collectAllPartIds(part.children));
      }
    }
    return ids;
  };

  const allPartIds = collectAllPartIds(parts);
  const partObjectIds = allPartIds.map((id) => new Types.ObjectId(id));
  const existingParts = await Part.find({ _id: { $in: partObjectIds } }).select('_id').lean<IPartDocument[]>();
  const existingPartIds = new Set(existingParts.map((p) => p._id.toString()));

  const missingParts = allPartIds.filter(id => !existingPartIds.has(id));

  if (missingParts.length > 0) {
    return {
      success: false,
      errors: [{
        field: 'partsRequired',
        message: `The following part IDs do not exist: ${missingParts.join(', ')}`
      }]
    };
  }

  return { success: true, errors: [] };
};

/**
 * Validates that all sub-assemblies exist in the database
 */
export const validateSubAssembliesExist = async (subAssemblies: Array<{ subAssemblyId: string }>): Promise<{ success: boolean; errors: ValidationError[] }> => {
  if (!subAssemblies || !subAssemblies.length) return { success: true, errors: [] };
  
  const assemblyObjectIds = subAssemblies.map((s) => new Types.ObjectId(s.subAssemblyId));
  const existingAssemblies = (await (Assembly as any).find({ _id: { $in: assemblyObjectIds } })) as IAssemblyDocument[];
  const existingAssemblyIds = new Set(existingAssemblies.map((a: IAssemblyDocument) => a._id.toString()));
  
  const missingAssemblies = assemblyObjectIds
    .map(id => id.toString())
    .filter(id => !existingAssemblyIds.has(id));
  
  if (missingAssemblies.length > 0) {
    return {
      success: false,
      errors: [{
        field: 'subAssemblies',
        message: `The following assembly IDs do not exist: ${missingAssemblies.join(', ')}`
      }]
    };
  }
  
  return { success: true, errors: [] };
};

/**
 * Validates that a parent assembly exists if parentId is provided
 */
export const validateParentAssemblyExists = async (parentId: string | null | undefined): Promise<{ success: boolean; errors: ValidationError[] }> => {
  if (!parentId) return { success: true, errors: [] };
  
  const parent = (await (Assembly as any).findById(parentId)) as IAssemblyDocument;
  
  if (!parent) {
    return {
      success: false,
      errors: [{
        field: 'parentId',
        message: `Parent assembly with ID ${parentId} not found`
      }]
    };
  }
  
  return { success: true, errors: [] };
};
