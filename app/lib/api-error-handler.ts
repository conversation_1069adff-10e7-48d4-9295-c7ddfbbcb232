import { errorResponse } from '@/app/lib/api-response';
import {
    isBaseError
} from '@/app/lib/errors';
import { captureException, setTag } from '@/app/lib/logging-utils';
import { logError } from '@/app/services/logging';

/**
 * Standardized error handler for API routes
 * @param error - The caught error
 * @param routePath - The API route path (for logging and tagging)
 * @param startTime - The request start time (for duration calculation)
 * @returns NextResponse with standardized error format
 */
export async function handleApiError(error: unknown, routePath: string, startTime: number) {
  const duration = Date.now() - startTime;
  
  // Log the error with context
  await logError('API', `Error in ${routePath} (${duration}ms)`, error);
  
  // Set Sentry tags for better filtering
  setTag('error.type', 'api');
  setTag('error.route', routePath);
  setTag('error.duration', duration.toString());
  
  // Capture in Sentry
  captureException(error);
  
  // Handle different error types with appropriate responses
  let response;

  if (isBaseError(error)) {
    // Our custom error types already have the structure we need
    response = errorResponse(
      error.code,
      error.message,
      error.details,
      error.status
    );
  } else if (error instanceof Error) {
    // Standard JS Error object
    let code = 'INTERNAL_ERROR';
    let status = 500;
    
    // Try to determine more specific error types
    if (error.message.includes('validation') || error.message.includes('required')) {
      code = 'VALIDATION_ERROR';
      status = 400;
    } else if (error.message.includes('not found') || error.message.includes('does not exist')) {
      code = 'NOT_FOUND';
      status = 404;
    } else if (error.message.includes('duplicate') || error.message.includes('already exists')) {
      code = 'CONFLICT';
      status = 409;
    }
    
    response = errorResponse(
      code,
      error.message,
      [{
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      }],
      status
    );
  } else {
    // Unknown error type (could be a string, object, etc.)
    response = errorResponse(
      'UNKNOWN_ERROR',
      typeof error === 'string' ? error : 'An unknown error occurred',
      [],
      500
    );
  }

  // CRITICAL: Always ensure Content-Type is set to application/json
  response.headers.set('Content-Type', 'application/json');

  return response;
}