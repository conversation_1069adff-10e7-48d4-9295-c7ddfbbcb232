/**
 * MIGRATION FEATURE FLAGS SYSTEM
 * 
 * This module provides comprehensive feature flag management for the inventory
 * schema migration, enabling safe rollout and rollback capabilities.
 * 
 * Features:
 * - Environment-based configuration
 * - Service-specific feature flags
 * - Performance monitoring integration
 * - Rollback safety mechanisms
 * - A/B testing capabilities
 */

import { captureException, setTag } from './logging-utils';

// Migration modes
export enum MigrationMode {
  LEGACY = 'legacy',           // Use only old embedded schema
  DUAL = 'dual',              // Use both schemas with fallback
  V3_PRIMARY = 'v3-primary',  // Use V3 as primary, embedded as fallback
  V3_ONLY = 'v3-only',        // Use only new inventories collection
  TESTING = 'testing'         // Special mode for testing both schemas
}

// Production migration status
export enum ProductionMigrationStatus {
  NOT_STARTED = 'not-started',
  IN_PROGRESS = 'in-progress',
  PARTIAL = 'partial',         // Some parts migrated, some still embedded
  COMPLETE = 'complete',       // All parts migrated to V3
  VALIDATED = 'validated'      // Migration validated and ready for V3-only mode
}

// Service types that can be toggled
export enum ServiceType {
  PART_SERVICE = 'part-service',
  INVENTORY_SERVICE = 'inventory-service',
  STOCK_MOVEMENT = 'stock-movement',
  TRANSACTION_SERVICE = 'transaction-service'
}

// Feature flag configuration interface
interface FeatureFlagConfig {
  migrationMode: MigrationMode;
  productionMigrationStatus: ProductionMigrationStatus;
  enableV3Services: boolean;
  enablePerformanceMonitoring: boolean;
  enableDetailedLogging: boolean;
  rollbackThreshold: number;
  serviceOverrides: Partial<Record<ServiceType, boolean>>;
  testingPercentage: number;
}

// Performance metrics interface
interface PerformanceMetrics {
  queryTime: number;
  memoryUsage: number;
  errorRate: number;
  successRate: number;
  timestamp: Date;
}

class MigrationFeatureFlags {
  private config: FeatureFlagConfig;
  private performanceMetrics: Map<string, PerformanceMetrics[]> = new Map();
  private rollbackTriggered: boolean = false;

  constructor() {
    this.config = this.loadConfiguration();
    this.logConfiguration();
  }

  private loadConfiguration(): FeatureFlagConfig {
    const migrationMode = (process.env.MIGRATION_MODE as MigrationMode) || MigrationMode.LEGACY;
    const enableV3Services = process.env.USE_INVENTORIES_V3 === 'true';
    
    return {
      migrationMode,
      productionMigrationStatus: ProductionMigrationStatus.COMPLETE, // Migration is complete
      enableV3Services,
      enablePerformanceMonitoring: process.env.ENABLE_MIGRATION_PERFORMANCE_MONITORING === 'true',
      enableDetailedLogging: process.env.ENABLE_MIGRATION_DETAILED_LOGGING === 'true',
      rollbackThreshold: parseFloat(process.env.MIGRATION_ROLLBACK_THRESHOLD || '0.95'), // 95% success rate
      testingPercentage: parseFloat(process.env.MIGRATION_TESTING_PERCENTAGE || '10'), // 10% of requests
      serviceOverrides: {
        [ServiceType.PART_SERVICE]: process.env.OVERRIDE_PART_SERVICE_V3 === 'true',
        [ServiceType.INVENTORY_SERVICE]: process.env.OVERRIDE_INVENTORY_SERVICE_V3 === 'true',
        [ServiceType.STOCK_MOVEMENT]: process.env.OVERRIDE_STOCK_MOVEMENT_V3 === 'true',
        [ServiceType.TRANSACTION_SERVICE]: process.env.OVERRIDE_TRANSACTION_SERVICE_V3 === 'true'
      }
    };
  }

  private logConfiguration(): void {
    if (this.config.enableDetailedLogging) {
      console.log('[MigrationFeatureFlags] Configuration loaded:', {
        migrationMode: this.config.migrationMode,
        enableV3Services: this.config.enableV3Services,
        serviceOverrides: this.config.serviceOverrides,
        rollbackThreshold: this.config.rollbackThreshold
      });
    }
  }

  /**
   * Determine if V3 services should be used for a specific service type
   */
  shouldUseV3Service(serviceType: ServiceType, context?: { userId?: string; requestId?: string }): boolean {
    try {
      // Check for rollback state
      if (this.rollbackTriggered) {
        this.logDecision('rollback-triggered', serviceType, false, context);
        return false;
      }

      // Check service-specific overrides first
      const override = this.config.serviceOverrides[serviceType];
      if (override !== undefined) {
        this.logDecision('service-override', serviceType, override, context);
        return override;
      }

      // Apply migration mode logic
      switch (this.config.migrationMode) {
        case MigrationMode.LEGACY:
          this.logDecision('legacy-mode', serviceType, false, context);
          return false;

        case MigrationMode.V3_ONLY:
          this.logDecision('v3-only-mode', serviceType, true, context);
          return true;

        case MigrationMode.DUAL:
          const useV3 = this.config.enableV3Services;
          this.logDecision('dual-mode', serviceType, useV3, context);
          return useV3;

        case MigrationMode.TESTING:
          // A/B testing based on percentage
          const shouldTest = this.shouldIncludeInTesting(context);
          this.logDecision('testing-mode', serviceType, shouldTest, context);
          return shouldTest && this.config.enableV3Services;

        default:
          this.logDecision('default-fallback', serviceType, false, context);
          return false;
      }
    } catch (error) {
      // Fail safe to legacy service
      this.logError('shouldUseV3Service', error, { serviceType, context });
      return false;
    }
  }

  /**
   * Determine if request should be included in A/B testing
   */
  private shouldIncludeInTesting(context?: { userId?: string; requestId?: string }): boolean {
    if (!context?.userId && !context?.requestId) {
      return Math.random() * 100 < this.config.testingPercentage;
    }

    // Use consistent hashing for user-based testing
    const identifier = context.userId || context.requestId || '';
    const hash = this.simpleHash(identifier);
    return (hash % 100) < this.config.testingPercentage;
  }

  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Record performance metrics for monitoring
   */
  recordPerformanceMetric(
    operation: string, 
    serviceType: ServiceType, 
    metrics: Omit<PerformanceMetrics, 'timestamp'>
  ): void {
    if (!this.config.enablePerformanceMonitoring) return;

    const key = `${serviceType}-${operation}`;
    const metric: PerformanceMetrics = {
      ...metrics,
      timestamp: new Date()
    };

    if (!this.performanceMetrics.has(key)) {
      this.performanceMetrics.set(key, []);
    }

    const metricsArray = this.performanceMetrics.get(key)!;
    metricsArray.push(metric);

    // Keep only last 100 metrics per operation
    if (metricsArray.length > 100) {
      metricsArray.shift();
    }

    // Check for rollback conditions
    this.checkRollbackConditions(key, metricsArray);
  }

  /**
   * Check if rollback should be triggered based on performance metrics
   */
  private checkRollbackConditions(key: string, metrics: PerformanceMetrics[]): void {
    if (metrics.length < 10) return; // Need minimum sample size

    const recentMetrics = metrics.slice(-10); // Last 10 operations
    const successRate = recentMetrics.reduce((sum, m) => sum + m.successRate, 0) / recentMetrics.length;

    if (successRate < this.config.rollbackThreshold) {
      this.triggerRollback(`Low success rate detected: ${successRate.toFixed(2)} for ${key}`);
    }
  }

  /**
   * Trigger automatic rollback to legacy services
   */
  private triggerRollback(reason: string): void {
    if (this.rollbackTriggered) return;

    this.rollbackTriggered = true;
    
    console.error('[MigrationFeatureFlags] AUTOMATIC ROLLBACK TRIGGERED:', reason);
    
    // Set Sentry tags for monitoring
    setTag('migration.rollback', 'triggered');
    setTag('migration.rollback.reason', reason);
    
    captureException(new Error(`Migration rollback triggered: ${reason}`), {
      level: 'warning',
      extra: {
        reason,
        config: this.config,
        timestamp: new Date().toISOString()
      }
    });

    // In production, you might want to update environment variables or notify ops team
    if (process.env.NODE_ENV === 'production') {
      this.notifyOpsTeam(reason);
    }
  }

  private notifyOpsTeam(reason: string): void {
    // Implementation would depend on your notification system
    // Could be Slack, PagerDuty, email, etc.
    console.error('[URGENT] Migration rollback triggered in production:', reason);
  }

  /**
   * Get current performance metrics summary
   */
  getPerformanceMetrics(): Record<string, any> {
    const summary: Record<string, any> = {};

    // Convert MapIterator to Array for compatibility
    const entries = Array.from(this.performanceMetrics.entries());

    for (const [key, metrics] of entries) {
      if (metrics.length === 0) continue;

      const recent = metrics.slice(-10);
      summary[key] = {
        totalOperations: metrics.length,
        recentOperations: recent.length,
        avgQueryTime: recent.reduce((sum: number, m: PerformanceMetrics) => sum + m.queryTime, 0) / recent.length,
        avgSuccessRate: recent.reduce((sum: number, m: PerformanceMetrics) => sum + m.successRate, 0) / recent.length,
        lastOperation: metrics[metrics.length - 1]?.timestamp
      };
    }

    return summary;
  }

  /**
   * Force rollback (for manual intervention)
   */
  forceRollback(reason: string): void {
    this.triggerRollback(`Manual rollback: ${reason}`);
  }

  /**
   * Reset rollback state (for recovery)
   */
  resetRollback(): void {
    this.rollbackTriggered = false;
    console.log('[MigrationFeatureFlags] Rollback state reset');
  }

  /**
   * Get current configuration
   */
  getConfiguration(): FeatureFlagConfig {
    return { ...this.config };
  }

  /**
   * Update configuration (for runtime changes)
   */
  updateConfiguration(updates: Partial<FeatureFlagConfig>): void {
    this.config = { ...this.config, ...updates };
    this.logConfiguration();
  }

  private logDecision(
    reason: string, 
    serviceType: ServiceType, 
    decision: boolean, 
    context?: { userId?: string; requestId?: string }
  ): void {
    if (this.config.enableDetailedLogging) {
      console.log(`[MigrationFeatureFlags] Decision: ${serviceType} -> ${decision ? 'V3' : 'Legacy'} (${reason})`, {
        context,
        timestamp: new Date().toISOString()
      });
    }
  }

  private logError(operation: string, error: any, context?: any): void {
    console.error(`[MigrationFeatureFlags] Error in ${operation}:`, error);
    captureException(error, {
      tags: {
        component: 'migration-feature-flags',
        operation
      },
      extra: context
    });
  }
}

// Singleton instance
const migrationFeatureFlags = new MigrationFeatureFlags();

// Export convenience functions
export const shouldUseV3Service = (serviceType: ServiceType, context?: { userId?: string; requestId?: string }) =>
  migrationFeatureFlags.shouldUseV3Service(serviceType, context);

export const recordPerformanceMetric = (
  operation: string, 
  serviceType: ServiceType, 
  metrics: Omit<PerformanceMetrics, 'timestamp'>
) => migrationFeatureFlags.recordPerformanceMetric(operation, serviceType, metrics);

export const getPerformanceMetrics = () => migrationFeatureFlags.getPerformanceMetrics();

export const forceRollback = (reason: string) => migrationFeatureFlags.forceRollback(reason);

export const resetRollback = () => migrationFeatureFlags.resetRollback();

export const getConfiguration = () => migrationFeatureFlags.getConfiguration();

export const updateConfiguration = (updates: Partial<FeatureFlagConfig>) => 
  migrationFeatureFlags.updateConfiguration(updates);

export default migrationFeatureFlags;
