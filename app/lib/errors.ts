/**
 * Custom error classes for structured error handling
 * These classes extend the base Error class and provide additional context
 * such as error code, HTTP status code, and additional details.
 */

export class BaseError extends Error {
  code: string;
  status: number;
  details: any[];

  constructor(message: string, code: string, status: number = 500, details: any[] = []) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.status = status;
    this.details = details;
  }
}

/**
 * Error thrown when input parameters are invalid
 */
export class InvalidParameterError extends BaseError {
  constructor(message: string, details: any[] = []) {
    super(message, 'INVALID_PARAMETER', 400, details);
  }
}

/**
 * Error thrown when there's an issue processing or calculating data
 */
export class DataProcessingError extends BaseError {
  constructor(message: string, details: any[] = []) {
    super(message, 'DATA_PROCESSING_ERROR', 500, details);
  }
}

/**
 * Error thrown when there's an issue with database queries
 */
export class DatabaseQueryError extends BaseError {
  constructor(message: string, details: any[] = []) {
    super(message, 'DATABASE_QUERY_ERROR', 500, details);
  }
}

/**
 * Error thrown when resource is not found
 */
export class NotFoundError extends BaseError {
  constructor(message: string, details: any[] = []) {
    super(message, 'RESOURCE_NOT_FOUND', 404, details);
  }
}

/**
 * Error thrown when there's an issue with analytics operations
 */
export class AnalyticsError extends BaseError {
  constructor(message: string, details: any[] = []) {
    super(message, 'ANALYTICS_ERROR', 500, details);
  }
}

/**
 * Utility function to determine if an error is an instance of BaseError
 */
export function isBaseError(error: any): error is BaseError {
  return error instanceof BaseError;
} 