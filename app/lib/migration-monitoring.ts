/**
 * MIGRATION MONITORING AND LOGGING SYSTEM
 * 
 * This module provides comprehensive monitoring and logging capabilities
 * for the inventory schema migration process.
 * 
 * Features:
 * - Real-time performance monitoring
 * - Error tracking and alerting
 * - Migration progress tracking
 * - Service health monitoring
 * - Detailed audit logging
 * - Metrics collection and analysis
 */

import { captureException, setTag, addBreadcrumb } from './logging-utils';

// Monitoring configuration
interface MonitoringConfig {
  enableDetailedLogging: boolean;
  enablePerformanceTracking: boolean;
  enableErrorAlerting: boolean;
  performanceThresholds: {
    queryTimeWarning: number;
    queryTimeError: number;
    memoryUsageWarning: number;
    errorRateWarning: number;
  };
  logRetentionDays: number;
}

// Performance metrics interface
interface PerformanceMetric {
  operation: string;
  service: 'legacy' | 'v3' | 'dual';
  startTime: number;
  endTime: number;
  duration: number;
  memoryBefore: number;
  memoryAfter: number;
  memoryDelta: number;
  success: boolean;
  error?: string;
  metadata?: Record<string, any>;
}

// Migration event interface
interface MigrationEvent {
  eventType: 'migration_start' | 'migration_progress' | 'migration_complete' | 'migration_error' | 'service_switch' | 'performance_alert';
  timestamp: Date;
  details: Record<string, any>;
  severity: 'info' | 'warning' | 'error' | 'critical';
  source: string;
}

// Service health status
interface ServiceHealth {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  lastCheck: Date;
  responseTime: number;
  errorRate: number;
  uptime: number;
  details?: Record<string, any>;
}

class MigrationMonitor {
  private config: MonitoringConfig;
  private performanceMetrics: PerformanceMetric[] = [];
  private migrationEvents: MigrationEvent[] = [];
  private serviceHealthStatus: Map<string, ServiceHealth> = new Map();
  private alertThrottling: Map<string, number> = new Map();

  constructor() {
    this.config = this.loadConfiguration();
    this.initializeMonitoring();
  }

  private loadConfiguration(): MonitoringConfig {
    return {
      enableDetailedLogging: process.env.ENABLE_MIGRATION_DETAILED_LOGGING === 'true',
      enablePerformanceTracking: process.env.ENABLE_MIGRATION_PERFORMANCE_TRACKING === 'true',
      enableErrorAlerting: process.env.ENABLE_MIGRATION_ERROR_ALERTING === 'true',
      performanceThresholds: {
        queryTimeWarning: parseInt(process.env.MIGRATION_QUERY_TIME_WARNING || '1000'), // 1 second
        queryTimeError: parseInt(process.env.MIGRATION_QUERY_TIME_ERROR || '5000'), // 5 seconds
        memoryUsageWarning: parseInt(process.env.MIGRATION_MEMORY_WARNING || '100000000'), // 100MB
        errorRateWarning: parseFloat(process.env.MIGRATION_ERROR_RATE_WARNING || '0.05') // 5%
      },
      logRetentionDays: parseInt(process.env.MIGRATION_LOG_RETENTION_DAYS || '30')
    };
  }

  private initializeMonitoring(): void {
    if (this.config.enableDetailedLogging) {
      console.log('[MigrationMonitor] Monitoring initialized with config:', this.config);
    }

    // Set up periodic cleanup
    setInterval(() => {
      this.cleanupOldData();
    }, 24 * 60 * 60 * 1000); // Daily cleanup
  }

  /**
   * Track performance metrics for operations
   */
  trackPerformance<T>(
    operation: string,
    service: 'legacy' | 'v3' | 'dual',
    fn: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    if (!this.config.enablePerformanceTracking) {
      return fn();
    }

    const startTime = Date.now();
    const memoryBefore = process.memoryUsage().heapUsed;

    return fn()
      .then((result) => {
        const endTime = Date.now();
        const memoryAfter = process.memoryUsage().heapUsed;
        
        const metric: PerformanceMetric = {
          operation,
          service,
          startTime,
          endTime,
          duration: endTime - startTime,
          memoryBefore,
          memoryAfter,
          memoryDelta: memoryAfter - memoryBefore,
          success: true,
          ...(metadata && { metadata })
        };

        this.recordPerformanceMetric(metric);
        this.checkPerformanceThresholds(metric);

        return result;
      })
      .catch((error) => {
        const endTime = Date.now();
        const memoryAfter = process.memoryUsage().heapUsed;
        
        const metric: PerformanceMetric = {
          operation,
          service,
          startTime,
          endTime,
          duration: endTime - startTime,
          memoryBefore,
          memoryAfter,
          memoryDelta: memoryAfter - memoryBefore,
          success: false,
          error: error.message,
          ...(metadata && { metadata })
        };

        this.recordPerformanceMetric(metric);
        this.logMigrationEvent({
          eventType: 'migration_error',
          timestamp: new Date(),
          details: { operation, service, error: error.message, metadata },
          severity: 'error',
          source: 'performance-tracker'
        });

        throw error;
      });
  }

  private recordPerformanceMetric(metric: PerformanceMetric): void {
    this.performanceMetrics.push(metric);

    // Keep only recent metrics (last 1000 operations)
    if (this.performanceMetrics.length > 1000) {
      this.performanceMetrics = this.performanceMetrics.slice(-1000);
    }

    if (this.config.enableDetailedLogging) {
      console.log(`[MigrationMonitor] Performance: ${metric.operation} (${metric.service}) - ${metric.duration}ms`, {
        memoryDelta: `${Math.round(metric.memoryDelta / 1024)}KB`,
        success: metric.success,
        metadata: metric.metadata
      });
    }
  }

  private checkPerformanceThresholds(metric: PerformanceMetric): void {
    const { queryTimeWarning, queryTimeError, memoryUsageWarning } = this.config.performanceThresholds;

    // Check query time thresholds
    if (metric.duration > queryTimeError) {
      this.triggerAlert('performance_critical', {
        message: `Query time exceeded critical threshold: ${metric.duration}ms`,
        operation: metric.operation,
        service: metric.service,
        threshold: queryTimeError,
        actual: metric.duration
      });
    } else if (metric.duration > queryTimeWarning) {
      this.triggerAlert('performance_warning', {
        message: `Query time exceeded warning threshold: ${metric.duration}ms`,
        operation: metric.operation,
        service: metric.service,
        threshold: queryTimeWarning,
        actual: metric.duration
      });
    }

    // Check memory usage thresholds
    if (metric.memoryDelta > memoryUsageWarning) {
      this.triggerAlert('memory_warning', {
        message: `Memory usage exceeded warning threshold: ${Math.round(metric.memoryDelta / 1024 / 1024)}MB`,
        operation: metric.operation,
        service: metric.service,
        threshold: Math.round(memoryUsageWarning / 1024 / 1024),
        actual: Math.round(metric.memoryDelta / 1024 / 1024)
      });
    }
  }

  /**
   * Log migration events
   */
  logMigrationEvent(event: MigrationEvent): void {
    this.migrationEvents.push(event);

    // Keep only recent events (last 500 events)
    if (this.migrationEvents.length > 500) {
      this.migrationEvents = this.migrationEvents.slice(-500);
    }

    if (this.config.enableDetailedLogging) {
      console.log(`[MigrationMonitor] Event: ${event.eventType} (${event.severity})`, event.details);
    }

    // Add breadcrumb for error tracking
    addBreadcrumb({
      message: `Migration event: ${event.eventType}`,
      category: 'migration',
      level: event.severity === 'error' || event.severity === 'critical' ? 'error' : 'info'
    });

    // Trigger alerts for critical events
    if (event.severity === 'critical' || event.severity === 'error') {
      this.triggerAlert(event.eventType, event.details);
    }
  }

  /**
   * Update service health status
   */
  updateServiceHealth(serviceName: string, health: Partial<ServiceHealth>): void {
    const currentHealth = this.serviceHealthStatus.get(serviceName) || {
      service: serviceName,
      status: 'healthy',
      lastCheck: new Date(),
      responseTime: 0,
      errorRate: 0,
      uptime: 0
    };

    const updatedHealth: ServiceHealth = {
      ...currentHealth,
      ...health,
      lastCheck: new Date()
    };

    this.serviceHealthStatus.set(serviceName, updatedHealth);

    if (this.config.enableDetailedLogging) {
      console.log(`[MigrationMonitor] Service health updated: ${serviceName} - ${updatedHealth.status}`, {
        responseTime: `${updatedHealth.responseTime}ms`,
        errorRate: `${(updatedHealth.errorRate * 100).toFixed(2)}%`
      });
    }

    // Trigger alerts for unhealthy services
    if (updatedHealth.status === 'unhealthy') {
      this.triggerAlert('service_unhealthy', {
        service: serviceName,
        status: updatedHealth.status,
        responseTime: updatedHealth.responseTime,
        errorRate: updatedHealth.errorRate,
        details: updatedHealth.details
      });
    }
  }

  /**
   * Trigger alerts with throttling
   */
  private triggerAlert(alertType: string, details: Record<string, any>): void {
    if (!this.config.enableErrorAlerting) return;

    // Implement alert throttling (max 1 alert per type per 5 minutes)
    const throttleKey = `${alertType}-${JSON.stringify(details)}`;
    const lastAlert = this.alertThrottling.get(throttleKey) || 0;
    const now = Date.now();
    
    if (now - lastAlert < 5 * 60 * 1000) { // 5 minutes
      return; // Throttled
    }

    this.alertThrottling.set(throttleKey, now);

    // Log the alert
    console.error(`[MigrationMonitor] ALERT: ${alertType}`, details);

    // Send to error tracking service
    setTag('alert.type', alertType);
    setTag('migration.component', 'monitoring');
    
    captureException(new Error(`Migration alert: ${alertType}`), {
      level: details.severity === 'critical' ? 'fatal' : 'warning',
      tags: {
        alertType,
        component: 'migration-monitor'
      },
      extra: details
    });

    // In production, you might want to send to external alerting systems
    if (process.env.NODE_ENV === 'production') {
      this.sendExternalAlert(alertType, details);
    }
  }

  private sendExternalAlert(alertType: string, details: Record<string, any>): void {
    // Implementation would depend on your alerting system
    // Examples: Slack, PagerDuty, email, etc.
    console.error(`[PRODUCTION ALERT] ${alertType}:`, details);
  }

  /**
   * Get performance analytics
   */
  getPerformanceAnalytics(): {
    summary: Record<string, any>;
    recentMetrics: PerformanceMetric[];
    trends: Record<string, any>;
  } {
    const recentMetrics = this.performanceMetrics.slice(-100);
    
    const summary = {
      totalOperations: this.performanceMetrics.length,
      recentOperations: recentMetrics.length,
      avgResponseTime: recentMetrics.reduce((sum, m) => sum + m.duration, 0) / recentMetrics.length || 0,
      successRate: recentMetrics.filter(m => m.success).length / recentMetrics.length || 0,
      errorRate: recentMetrics.filter(m => !m.success).length / recentMetrics.length || 0,
      avgMemoryUsage: recentMetrics.reduce((sum, m) => sum + m.memoryDelta, 0) / recentMetrics.length || 0
    };

    // Calculate trends by service
    const trends: Record<string, any> = {};
    ['legacy', 'v3', 'dual'].forEach(service => {
      const serviceMetrics = recentMetrics.filter(m => m.service === service);
      if (serviceMetrics.length > 0) {
        trends[service] = {
          operations: serviceMetrics.length,
          avgResponseTime: serviceMetrics.reduce((sum, m) => sum + m.duration, 0) / serviceMetrics.length,
          successRate: serviceMetrics.filter(m => m.success).length / serviceMetrics.length,
          avgMemoryUsage: serviceMetrics.reduce((sum, m) => sum + m.memoryDelta, 0) / serviceMetrics.length
        };
      }
    });

    return { summary, recentMetrics, trends };
  }

  /**
   * Get service health summary
   */
  getServiceHealthSummary(): Record<string, ServiceHealth> {
    const summary: Record<string, ServiceHealth> = {};

    // Convert MapIterator to Array for compatibility
    const entries = Array.from(this.serviceHealthStatus.entries());

    for (const [serviceName, health] of entries) {
      summary[serviceName] = { ...health };
    }

    return summary;
  }

  /**
   * Get recent migration events
   */
  getRecentEvents(limit = 50): MigrationEvent[] {
    return this.migrationEvents.slice(-limit);
  }

  /**
   * Clean up old data
   */
  private cleanupOldData(): void {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.config.logRetentionDays);

    // Clean up old events
    this.migrationEvents = this.migrationEvents.filter(
      event => event.timestamp > cutoffDate
    );

    // Clean up old performance metrics
    const cutoffTime = cutoffDate.getTime();
    this.performanceMetrics = this.performanceMetrics.filter(
      metric => metric.startTime > cutoffTime
    );

    if (this.config.enableDetailedLogging) {
      console.log(`[MigrationMonitor] Cleaned up old data. Events: ${this.migrationEvents.length}, Metrics: ${this.performanceMetrics.length}`);
    }
  }

  /**
   * Export monitoring data for analysis
   */
  exportMonitoringData(): {
    config: MonitoringConfig;
    performanceMetrics: PerformanceMetric[];
    migrationEvents: MigrationEvent[];
    serviceHealth: Record<string, ServiceHealth>;
    exportTimestamp: Date;
  } {
    return {
      config: this.config,
      performanceMetrics: this.performanceMetrics,
      migrationEvents: this.migrationEvents,
      serviceHealth: this.getServiceHealthSummary(),
      exportTimestamp: new Date()
    };
  }
}

// Singleton instance
const migrationMonitor = new MigrationMonitor();

// Export convenience functions
export const trackPerformance = <T>(
  operation: string,
  service: 'legacy' | 'v3' | 'dual',
  fn: () => Promise<T>,
  metadata?: Record<string, any>
) => migrationMonitor.trackPerformance(operation, service, fn, metadata);

export const logMigrationEvent = (event: Omit<MigrationEvent, 'timestamp'>) =>
  migrationMonitor.logMigrationEvent({ ...event, timestamp: new Date() });

export const updateServiceHealth = (serviceName: string, health: Partial<ServiceHealth>) =>
  migrationMonitor.updateServiceHealth(serviceName, health);

export const getPerformanceAnalytics = () => migrationMonitor.getPerformanceAnalytics();

export const getServiceHealthSummary = () => migrationMonitor.getServiceHealthSummary();

export const getRecentEvents = (limit?: number) => migrationMonitor.getRecentEvents(limit);

export const exportMonitoringData = () => migrationMonitor.exportMonitoringData();

export default migrationMonitor;
