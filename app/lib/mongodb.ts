import mongoose, { Mongoose, ConnectOptions as MongooseConnectOptions } from 'mongoose';
import { env, getMongoDBUri, logEnvironmentInfo, validateEnvironment } from '../utils/env';

/**
 * MongoDB Connection Module for Trend IMS
 *
 * This module provides a robust, production-ready MongoDB connection setup using Mongoose.
 * It includes connection caching, retry logic, monitoring, and proper error handling.
 *
 * Features:
 * - Connection caching for serverless environments
 * - Retry logic with exponential backoff
 * - Connection monitoring and profiling (development only)
 * - Proper error handling and logging
 * - Support for both development and production environments
 * - Optimized for Vercel serverless deployment
 * - Centralized environment variable handling
 */

// --- Type Definitions ---
interface MongooseCache {
  conn: typeof mongoose | null;
  promise: Promise<typeof mongoose> | null;
  connectionState: ConnectionState;
  lastError: Error | null;
  isConnecting: boolean;
}

interface DatabaseConnection {
  mongoose: typeof mongoose;
  db: any; // MongoDB database instance
}

// Connection state enum for better state tracking
enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error'
}

declare global {
  var mongoose: MongooseCache;
}

// --- Configuration ---
// Use centralized environment variable handler for safe access
const MONGODB_URI = getMongoDBUri();
const MONGODB_DB_NAME = env.MONGODB_DB_NAME;
const CONNECTION_TIMEOUT_MS = 30000; // 30 seconds timeout for connection

// Validate environment configuration on module load (server-side only)
if (typeof window === 'undefined' && env.NEXT_PHASE !== 'phase-production-build') {
  const validation = validateEnvironment();
  if (!validation.isValid) {
    console.error('❌ Environment validation failed:', validation.errors);
    // Don't throw during build, just log the error
    if (env.NODE_ENV !== 'production' || !env.CI) {
      logEnvironmentInfo();
    }
  }

  // Log warnings for optional variables
  if (validation.warnings && validation.warnings.length > 0) {
    console.warn('⚠️ Environment warnings:', validation.warnings);
  }
}

console.log(
  '[MongoDB] Connection string loaded:',
  MONGODB_URI ? MONGODB_URI.replace(/:([^@]+)@/, ':****@') : 'undefined'
);

// --- Connection Caching ---
let cached = global.mongoose;

if (!cached) {
  cached = global.mongoose = { 
    conn: null, 
    promise: null, 
    connectionState: ConnectionState.DISCONNECTED,
    lastError: null,
    isConnecting: false
  };
}

// Simple mutex implementation for preventing concurrent connection attempts
const connectionMutex = {
  acquire: async function() {
    // Wait until any ongoing connection attempt completes
    while (cached.isConnecting) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    cached.isConnecting = true;
    return true;
  },
  release: function() {
    cached.isConnecting = false;
  }
};

// --- New Improved Connection Logic ---
const newConnectionOptions: MongooseConnectOptions = {
  connectTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  serverSelectionTimeoutMS: 30000,
  heartbeatFrequencyMS: 10000,
  maxPoolSize: 10,
  minPoolSize: 2,
  retryWrites: true,
  retryReads: true,
  ...(MONGODB_DB_NAME && { dbName: MONGODB_DB_NAME }), // Only include dbName if it exists
  // useNewUrlParser: true, // Deprecated
  // useUnifiedTopology: true, // Deprecated
};

const newConnectWithRetry = async (uri: string, options: MongooseConnectOptions, maxRetries = 5, delay = 5000): Promise<Mongoose> => {
  let retries = 0;
  const targetDB = uri.substring(0, uri.lastIndexOf('/')) + '/' + (options.dbName || 'your_default_db_name'); // Obfuscate credentials
  console.log(`Attempting to connect to MongoDB: ${targetDB.replace(/\/\/.*@/, '//<credentials>@')}`);

  while (retries < maxRetries) {
    try {
      const mongooseInstance = await mongoose.connect(uri, options);
      console.log('MongoDB connection successful.');
      // Call monitoring setup after successful connection (server-side only)
      if (typeof window === 'undefined' && (env.NODE_ENV === 'development' || env.ENABLE_DB_MONITORING)) {
        try {
          const { setupMongoDBMonitoring, enableMongoDBProfiler } = await import('./mongodb-monitoring');
          setupMongoDBMonitoring();
          if (env.ENABLE_DB_PROFILER) {
            await enableMongoDBProfiler();
          }
        } catch (error) {
          console.warn('Failed to load MongoDB monitoring:', error);
        }
      }
      return mongooseInstance;
    } catch (err: any) {
      retries += 1;
      console.error(`MongoDB connection attempt ${retries} failed: ${err.message}`);
      if (retries < maxRetries) {
        console.log(`Retrying in ${delay / 1000}s...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  const finalErrorMsg = `Failed to connect to MongoDB after ${maxRetries} attempts.`;
  console.error(finalErrorMsg);
  throw new Error(finalErrorMsg);
};
// --- End New Improved Connection Logic ---

/**
 * Connects to MongoDB using Mongoose with connection caching.
 * Implements retry logic with exponential backoff for better resilience.
 *
 * @returns Promise resolving to an object containing both mongoose instance and MongoDB db instance
 */
export async function connectToDatabase(): Promise<DatabaseConnection> {
  // Skip actual connection in test environment or during build if explicitly requested
  if (env.SKIP_MONGODB_CONNECTION || env.NEXT_PHASE === 'phase-production-build') {
    console.log('[MongoDB][SKIP MODE] Skipping actual database connection');
    // Return mock connection for tests/build
    return {
      mongoose: mongoose as any,
      db: {
        collection: () => ({
          find: () => ({
            sort: () => ({
              limit: () => ({
                skip: () => ({
                  toArray: () => Promise.resolve([])
                })
              })
            }),
            toArray: () => Promise.resolve([])
          }),
          findOne: () => Promise.resolve(null),
          insertOne: () => Promise.resolve({ insertedId: 'test-id' }),
          updateOne: () => Promise.resolve({ modifiedCount: 1 }),
          deleteOne: () => Promise.resolve({ deletedCount: 1 }),
          aggregate: () => ({
            toArray: () => Promise.resolve([])
          }),
          countDocuments: () => Promise.resolve(0)
        }),
        command: () => Promise.resolve({ ok: 1 })
      }
    };
  }

  // If already connected, return immediately
  if (cached.conn && cached.connectionState === ConnectionState.CONNECTED) {
    console.log('[MongoDB] Using cached connection');
    // Return both mongoose instance and db instance
    return {
      mongoose: cached.conn,
      db: cached.conn.connection.db
    };
  }

  // If connection is in error state, clear the cache to allow reconnection
  if (cached.connectionState === ConnectionState.ERROR) {
    console.log('[MongoDB] Previous connection in error state, reconnecting...');
    cached.conn = null;
    cached.promise = null;
    cached.connectionState = ConnectionState.DISCONNECTED;
  }

  // Acquire mutex to prevent concurrent connection attempts
  await connectionMutex.acquire();

  try {
    // If connection is already in progress, wait for it
    if (cached.promise) {
      console.log('[MongoDB] Connection in progress, waiting for it to complete...');
      try {
        cached.conn = await Promise.race([
          cached.promise,
          new Promise<never>((_, reject) => {
            setTimeout(() => reject(new Error(`Connection timed out after ${CONNECTION_TIMEOUT_MS}ms`)), 
              CONNECTION_TIMEOUT_MS);
          })
        ]);
        
        cached.connectionState = ConnectionState.CONNECTED;
        
        // Return both mongoose instance and db instance
        return {
          mongoose: cached.conn,
          db: cached.conn.connection.db
        };
      } catch (error) {
        // If waiting for the existing promise fails, reset it and try again
        cached.promise = null;
        cached.connectionState = ConnectionState.ERROR;
        cached.lastError = error instanceof Error ? error : new Error(String(error));
        console.error('[MongoDB] Connection attempt failed, will retry:', 
          error instanceof Error ? error.message : String(error));
        
        // Release the mutex and retry connecting
        connectionMutex.release();
        return connectToDatabase();
      }
    }

    // --- Serverless-Optimized Connection Options ---
    const connectionOpts = {
      // Serverless-optimized settings for Vercel
      bufferCommands: true, // Allow buffering during build, disable in runtime if needed
      ssl: true, // Ensure SSL is enabled (standard for Atlas)

      // SERVERLESS OPTIMIZATION: Minimal pool sizes for Vercel functions
      minPoolSize: 0, // Start with 0 connections for serverless
      maxPoolSize: 3, // Very small pool for serverless functions

      // Aggressive timeouts for Vercel's 10-second function limit
      socketTimeoutMS: 8000, // 8 seconds max for socket operations
      connectTimeoutMS: 8000, // 8 seconds max for initial connection
      serverSelectionTimeoutMS: 8000, // 8 seconds max for server selection

      // Serverless-friendly settings
      maxIdleTimeMS: 30000, // Close idle connections after 30 seconds
      autoIndex: false, // Never auto-index in any environment for serverless
      retryWrites: true, // Enable retry writes for better reliability
      retryReads: true, // Enable retry reads for better reliability

      // Additional serverless optimizations
      heartbeatFrequencyMS: 30000, // Reduce heartbeat frequency

      // Database name (if specified)
      ...(MONGODB_DB_NAME && { dbName: MONGODB_DB_NAME }),
    };

    console.log('[MongoDB] Creating new connection attempt...');
    console.log(`[MongoDB] Using options: ${JSON.stringify(connectionOpts)}`);

    // Update connection state
    cached.connectionState = ConnectionState.CONNECTING;

    // Set up MongoDB monitoring before connection (server-side only)
    if (typeof window === 'undefined') {
      try {
        const { setupMongoDBMonitoring } = await import('./mongodb-monitoring');
        setupMongoDBMonitoring();
      } catch (error) {
        console.warn('Failed to load MongoDB monitoring:', error);
      }
    }

    // Implement connection with retry logic
    cached.promise = newConnectWithRetry(MONGODB_URI!, connectionOpts);

    try {
      console.log('[MongoDB] Awaiting connection promise...');
      // Add timeout to the connection promise
      cached.conn = await Promise.race([
        cached.promise,
        new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error(`Connection timed out after ${CONNECTION_TIMEOUT_MS}ms`)), 
            CONNECTION_TIMEOUT_MS);
        })
      ]);

      // Update connection state
      cached.connectionState = ConnectionState.CONNECTED;
      cached.lastError = null;

      // Enable MongoDB profiler if available
      if (typeof window === 'undefined' && env.ENABLE_DB_PROFILER) {
        try {
          const { enableMongoDBProfiler } = await import('./mongodb-monitoring');
          await enableMongoDBProfiler();
        } catch (profilerError) {
          console.warn('[MongoDB] Could not enable profiler:',
            profilerError instanceof Error ? profilerError.message : 'Unknown error');
        }
      }

      // Set up connection event listeners for monitoring
      setupConnectionEventListeners();

      // Return both mongoose instance and db instance
      return {
        mongoose: cached.conn,
        db: cached.conn.connection.db
      };
    } catch (error) {
      // Connection failed
      cached.promise = null;
      cached.connectionState = ConnectionState.ERROR;
      cached.lastError = error instanceof Error ? error : new Error(String(error));
      
      console.error('[MongoDB] Connection failed:',
        error instanceof Error ? error.message : String(error));
      
      throw error;
    } finally {
      // Release the mutex regardless of success or failure
      connectionMutex.release();
    }
  } catch (error) {
    // Ensure mutex is released if there's an error during the connection attempt
    connectionMutex.release();
    throw error;
  }
}

/**
 * Set up event listeners for the mongoose connection to monitor its status
 */
function setupConnectionEventListeners() {
  if (!mongoose.connection) return;

  // Remove any existing listeners to prevent duplicates
  mongoose.connection.removeAllListeners('error');
  mongoose.connection.removeAllListeners('disconnected');
  mongoose.connection.removeAllListeners('connected');
  mongoose.connection.removeAllListeners('reconnected');

  mongoose.connection.on('error', (err) => {
    console.error('[MongoDB] Connection error event:', err);
    cached.connectionState = ConnectionState.ERROR;
    cached.lastError = err;
  });

  mongoose.connection.on('disconnected', () => {
    console.warn('[MongoDB] Disconnected event');
    cached.connectionState = ConnectionState.DISCONNECTED;
  });

  mongoose.connection.on('connected', () => {
    console.log('[MongoDB] Connected event');
    cached.connectionState = ConnectionState.CONNECTED;
  });

  mongoose.connection.on('reconnected', () => {
    console.log('[MongoDB] Reconnected event');
    cached.connectionState = ConnectionState.CONNECTED;
  });
}

// Standardized error handling
export const handleMongoDBError = (error: any) => {
  console.error('[MongoDB Error]', error);

  // Return appropriate error message based on error type
  if (error.name === 'ValidationError') {
    // Format mongoose validation errors
    const validationErrors = Object.values(error.errors)
      .map((err: any) => err.message)
      .join(', ');
    return `Validation failed: ${validationErrors}`;
  } else if (error.code === 11000) {
    // Handle duplicate key errors (common for unique indexes)
    const field = Object.keys(error.keyPattern)[0];
    return `Duplicate entry: A record with this ${field} already exists`;
  } else if (error.name === 'MongoNetworkError') {
    // Network connectivity issues
    return `Network error: Unable to connect to MongoDB. Please check your connection and try again.`;
  } else if (error.name === 'MongoServerError') {
    // Handle specific server errors by code
    if (error.code === 50) {
      return `MongoDB operation timed out. Please try again later.`;
    } else if (error.code === 13) {
      return `Authentication failed. Please check your credentials.`;
    } else if (error.code === 251) {
      return `Transaction aborted. Please try again.`;
    }
    return `Database error: ${error.message}`;
  } else if (error.name === 'MongooseError') {
    // Generic mongoose errors
    return `Database operation failed: ${error.message}`;
  } else {
    // Fallback for unknown errors
    return `Database error: ${error.message || 'Unknown error occurred'}`;
  }
};

// Check database connection health
export async function checkDatabaseHealth() {
  try {
    if (!mongoose.connection || mongoose.connection.readyState !== 1) {
      return {
        status: 'error',
        message: 'Database not connected',
        readyState: mongoose.connection ? mongoose.connection.readyState : 'undefined',
        connectionState: cached.connectionState
      };
    }

    // Ping the database to check responsiveness
    // Use null check to satisfy TypeScript
    if (!mongoose.connection.db) {
      return {
        status: 'error',
        message: 'Database connection exists but db is not initialized',
        readyState: mongoose.connection.readyState,
        connectionState: cached.connectionState
      };
    }

    const pingResult = await mongoose.connection.db.admin().ping();
    if (pingResult && pingResult.ok === 1) {
      return {
        status: 'healthy',
        message: 'Database connection is healthy',
        readyState: mongoose.connection.readyState,
        connectionState: cached.connectionState,
        ping: pingResult
      };
    } else {
      return {
        status: 'warning',
        message: 'Database ping failed',
        readyState: mongoose.connection.readyState,
        connectionState: cached.connectionState,
        ping: pingResult
      };
    }
  } catch (error) {
    return {
      status: 'error',
      message: error instanceof Error ? error.message : 'Unknown error checking database health',
      readyState: mongoose.connection ? mongoose.connection.readyState : 'undefined',
      connectionState: cached.connectionState
    };
  }
}

/**
 * Get the current connection state
 */
export function getConnectionState(): {
  state: ConnectionState;
  readyState?: number;
  lastError: Error | null;
} {
  return {
    state: cached.connectionState,
    ...(mongoose.connection && typeof mongoose.connection.readyState === 'number' && { readyState: mongoose.connection.readyState }),
    lastError: cached.lastError
  };
}

/**
 * Force reconnection to the database, clearing any cached connection
 */
export async function forceReconnect(): Promise<DatabaseConnection> {
  console.log('[MongoDB] Forcing reconnection...');
  cached.conn = null;
  cached.promise = null;
  cached.connectionState = ConnectionState.DISCONNECTED;
  return connectToDatabase();
}

export default connectToDatabase;