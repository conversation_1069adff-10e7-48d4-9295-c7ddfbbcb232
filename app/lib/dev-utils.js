/**
 * Development utilities to improve development experience
 * These utilities are only used in development mode and not included in production builds
 */

/**
 * Suppress HMR-related errors by patching the console error and warning methods
 * This helps clean up the console output during development
 */
export function suppressHmrErrors() {
  if (typeof process === 'undefined' || process.env.NODE_ENV !== 'development') {
    return;
  }

  // Save original console methods
  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;

  // HMR-related error patterns to suppress
  const suppressPatterns = [
    /Unrecognized HMR message/,
    /\[HMR]/,
    /Invalid HMR message/,
    /Cannot find module '.\/\w+\.hot-update\.json'/,
    /The following Module Warning occurred/,
    /Failed to parse source map/,
    /Critical dependency: the request of a dependency is an expression/,
    /\/\/ @refresh reset/, // React Fast Refresh directive warnings
  ];

  // Patch console.error to filter out HMR-related errors
  console.error = function(...args) {
    // Check if this is an HMR-related error
    const message = String(args[0] || '');
    if (suppressPatterns.some(pattern => pattern.test(message))) {
      // Optionally log to debug if needed
      // console.debug('[Suppressed HMR Error]', ...args);
      return;
    }
    
    // Pass through all other errors
    originalConsoleError.apply(console, args);
  };

  // Patch console.warn to filter out HMR-related warnings
  console.warn = function(...args) {
    // Check if this is an HMR-related warning
    const message = String(args[0] || '');
    if (suppressPatterns.some(pattern => pattern.test(message))) {
      // Optionally log to debug if needed
      // console.debug('[Suppressed HMR Warning]', ...args);
      return;
    }
    
    // Pass through all other warnings
    originalConsoleWarn.apply(console, args);
  };

  // Return function to restore original console behavior if needed
  return function restoreConsole() {
    console.error = originalConsoleError;
    console.warn = originalConsoleWarn;
  };
}

/**
 * Wrap window.fetch to fix HMR in development mode
 * This ensures HMR assets can be correctly loaded without triggering errors
 */
export function fixHmrFetch() {
  if (typeof window === 'undefined' || process.env.NODE_ENV !== 'development') {
    return;
  }

  // Save the original fetch function
  const originalFetch = window.fetch;

  // Patch window.fetch
  window.fetch = async function(resource, init) {
    // Check if this is an HMR-related request
    if (typeof resource === 'string' && (
      resource.includes('.hot-update.') ||
      resource.includes('_next/webpack-hmr')
    )) {
      try {
        const response = await originalFetch(resource, init);
        return response;
      } catch (error) {
        // Silently ignore HMR fetch errors to prevent console spam
        // Return a fake successful response for HMR
        return new Response('{}', {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }
    
    // Pass through all other fetch calls
    return originalFetch(resource, init);
  };

  // Return function to restore original fetch behavior if needed
  return function restoreFetch() {
    window.fetch = originalFetch;
  };
} 