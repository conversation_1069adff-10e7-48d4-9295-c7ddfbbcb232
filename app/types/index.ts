/**
 * Main Types Index for Trend IMS
 * 
 * This file contains general application types that are imported by multiple files,
 * including common interfaces and utility types used throughout the application.
 */

// ============================================================================
// User and Authentication Types
// ============================================================================

/**
 * User role enumeration
 */
export type UserRole = 'admin' | 'manager' | 'operator' | 'viewer';

/**
 * User interface for authentication and user management
 */
export interface User {
  id: string;
  email: string;
  role: UserRole;
  firstName?: string;
  lastName?: string;
  avatarUrl?: string;
}

// ============================================================================
// Product and Inventory Types
// ============================================================================

/**
 * Interface for hierarchical component in product BOM
 * Supports recursive nesting through the children array
 */
export interface HierarchicalComponent {
  assemblyId: string;
  quantityRequired: number;
  children?: HierarchicalComponent[];
}

/**
 * Product interface for general use throughout the application
 */
export interface Product {
  _id: string;
  id: string; // Frontend compatibility mapping from _id
  productCode: string;
  name: string;
  description: string;
  categoryId: string;
  status: 'active' | 'discontinued' | 'in_development';
  sellingPrice: number;
  components?: HierarchicalComponent[]; // New hierarchical components array
  // Legacy fields for backward compatibility
  assemblyId?: string | null;
  partId?: string | null;
  assemblyStage?: string; // Legacy compatibility
  createdAt: Date;
  updatedAt: Date;
  // Additional properties for frontend compatibility
  currentStock?: number;
  reorderLevel?: number;
  supplierManufacturer?: string;
  inventory?: {
    currentStock: number;
    warehouseId?: string;
    safetyStockLevel?: number;
    maximumStockLevel?: number;
    averageDailyUsage?: number;
    abcClassification?: string;
    lastStockUpdate?: Date | null;
  };
}

/**
 * Inventory Part interface for context usage
 */
export interface InventoryPart {
  _id: string;
  partNumber: string;
  name: string;
  businessName?: string | null;
  description?: string | null;
  reorderLevel?: number | null;
  status: 'active' | 'inactive' | 'obsolete';
  costPrice: number;
  unitOfMeasure: string;
  supplierId?: string | null;
  categoryId?: string | null;
  isManufactured: boolean;
  inventory: {
    stockLevels: {
      raw: number;
      hardening: number;
      grinding: number;
      finished: number;
      rejected: number;
    };
    warehouseId: string;
    safetyStockLevel: number;
    maximumStockLevel: number;
    averageDailyUsage: number;
    abcClassification: string;
    lastStockUpdate?: Date | null;
    // BACKWARD COMPATIBILITY: Virtual field for legacy access
    currentStock?: number; // Computed as stockLevels.finished
  };
}

// ============================================================================
// Work Order Types
// ============================================================================

/**
 * Work Order interface
 */
export interface WorkOrder {
  _id: string;
  id?: string; // Legacy compatibility
  woNumber: string;
  assemblyId?: string | null;
  partIdToManufacture?: string | null;
  productId?: string | null;
  quantity: number;
  status: 'planned' | 'released' | 'in_progress' | 'completed' | 'on_hold' | 'cancelled' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  dueDate: Date;
  startDate?: Date | null;
  endDate?: Date | null; // Legacy compatibility
  assignedTo?: string | null;
  assignee?: { fullName?: string } | null; // Legacy compatibility
  description?: string; // Legacy compatibility
  notes?: string | null;
  completedAt?: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// Assembly Types
// ============================================================================

/**
 * Assembly Item interface for context and components
 */
export interface AssemblyItem {
  id: string;
  name: string;
  stage: string;
  status: 'completed' | 'in_progress' | 'pending' | 'delayed';
  progress: number;
  startDate?: string;
  endDate?: string;
  assignedTo?: string;
  dependencies?: string[];
  parts?: {
    id: string;
    name: string;
    status: 'available' | 'low' | 'unavailable';
    quantity?: number;
    inStock?: number;
  }[];
}

/**
 * BOM (Bill of Materials) Item interface
 */
export interface BOMItem {
  id: string;
  partId: string;
  partName: string;
  quantity: number;
  unitOfMeasure: string;
  costPerUnit: number;
  totalCost: number;
  supplier?: string;
  leadTime?: number;
  notes?: string;
  parent_id?: string; // Legacy compatibility
  child_id?: string; // Legacy compatibility
}

// ============================================================================
// Status and Analytics Types
// ============================================================================

/**
 * Stock Status interface for dashboard
 */
export interface StockStatus {
  inStock: number;
  lowStock: number;
  outOfStock: number;
  totalItems: number;
  total: number; // Legacy compatibility
  overstock?: number; // Legacy compatibility
}

/**
 * Order Status Count interface for dashboard
 */
export interface OrderStatusCount {
  pending: number;
  processing: number;
  completed: number;
  cancelled: number;
  total: number;
  shipped: number; // Legacy compatibility
  delivered: number; // Legacy compatibility
}

/**
 * Logistics Information interface
 */
export interface LogisticsInfo {
  inTransit: number;
  delivered: number;
  pending: number;
  delayed: number;
  totalShipments: number;
  total: number; // Legacy compatibility
  status?: string; // Legacy compatibility
  delayDuration?: string; // Legacy compatibility
  delayedItems?: any[]; // Legacy compatibility
  warehouses?: any[]; // Legacy compatibility
  routes?: any[]; // Legacy compatibility
}

// ============================================================================
// Report and Analytics Types
// ============================================================================

/**
 * Report Type interface
 */
export interface ReportType {
  id: string;
  name: string;
  description: string;
  endpoint: string;
}

/**
 * Inventory Summary interface for reports
 */
export interface InventorySummary {
  totalItems: number;
  totalStock: number;
  lowStockCount: number;
  outOfStockCount: number;
}

/**
 * Production Summary interface for reports
 */
export interface ProductionSummary {
  totalWorkOrders: number;
  pendingCount: number;
  inProgressCount: number;
  completedCount: number;
  overdueCount: number;
}

/**
 * Procurement Summary interface for reports
 */
export interface ProcurementSummary {
  totalPOs: number;
  pendingCount: number;
  receivedCount: number;
  totalSpend: number;
}

/**
 * Assembly Summary interface for reports
 */
export interface AssemblySummary {
  totalAssemblies: number;
  completableCount: number;
  incompleteCount: number;
}

// ============================================================================
// Utility Types
// ============================================================================

/**
 * Generic ID type for database entities
 */
export type EntityId = string;

/**
 * Status types commonly used across entities
 */
export type EntityStatus = 'active' | 'inactive' | 'pending' | 'archived';

/**
 * Priority levels for work orders and tasks
 */
export type Priority = 'low' | 'medium' | 'high' | 'urgent';

/**
 * Common timestamp fields
 */
export interface Timestamps {
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Pagination metadata
 */
export interface PaginationMeta {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}
