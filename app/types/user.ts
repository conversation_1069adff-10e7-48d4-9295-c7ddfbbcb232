/**
 * User and Supplier Types for Trend IMS
 * 
 * This file contains type definitions for user management and supplier entities.
 */

// ============================================================================
// User Types
// ============================================================================

/**
 * User role enumeration
 */
export type UserRole = 'admin' | 'manager' | 'operator' | 'viewer';

/**
 * User interface based on the user model
 */
export interface User {
  _id: string;
  userId: string; // Unique business identifier
  username: string;
  passwordHash?: string; // Usually not included in frontend responses
  email: string;
  firstName: string;
  lastName: string;
  roles: string[];
  role?: UserRole; // Legacy compatibility - derived from roles[0]
  fullName?: string; // Legacy compatibility - computed from firstName + lastName
  department?: string | null;
  jobTitle?: string | null;
  phoneNumber?: string | null;
  isActive: boolean;
  lastLoginDate?: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * User profile interface for frontend display
 */
export interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  role: UserRole;
  department?: string;
  jobTitle?: string;
  phoneNumber?: string;
  avatarUrl?: string;
  isActive: boolean;
  lastLoginDate?: Date | null;
}

/**
 * User creation request interface
 */
export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  roles: string[];
  department?: string;
  jobTitle?: string;
  phoneNumber?: string;
  isActive?: boolean;
}

/**
 * User update request interface
 */
export interface UpdateUserRequest {
  username?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  roles?: string[];
  department?: string;
  jobTitle?: string;
  phoneNumber?: string;
  isActive?: boolean;
}

/**
 * User authentication interface
 */
export interface UserAuth {
  id: string;
  email: string;
  role: UserRole;
  firstName?: string;
  lastName?: string;
  avatarUrl?: string;
  permissions?: string[];
}

// ============================================================================
// Supplier Types
// ============================================================================

/**
 * Supplier interface based on the supplier model
 */
export interface Supplier {
  _id: string;
  supplierId: string; // Unique business identifier
  supplier_id?: string; // Legacy compatibility
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  specialty: string[];
  rating?: number | null;
  paymentTerms?: string | null;
  deliveryTerms?: string | null;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Supplier creation request interface
 */
export interface CreateSupplierRequest {
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  specialty?: string[];
  rating?: number;
  paymentTerms?: string;
  deliveryTerms?: string;
  isActive?: boolean;
}

/**
 * Supplier update request interface
 */
export interface UpdateSupplierRequest {
  name?: string;
  contactPerson?: string;
  email?: string;
  phone?: string;
  address?: string;
  specialty?: string[];
  rating?: number;
  paymentTerms?: string;
  deliveryTerms?: string;
  isActive?: boolean;
}

/**
 * Supplier contact information
 */
export interface SupplierContact {
  name: string;
  title?: string;
  email: string;
  phone: string;
  isPrimary: boolean;
}

/**
 * Supplier performance metrics
 */
export interface SupplierPerformance {
  supplierId: string;
  onTimeDeliveryRate: number;
  qualityRating: number;
  responseTime: number; // in hours
  totalOrders: number;
  totalValue: number;
  lastOrderDate?: Date;
  averageLeadTime: number; // in days
}

// ============================================================================
// Authentication and Session Types
// ============================================================================

/**
 * Login request interface
 */
export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

/**
 * Login response interface
 */
export interface LoginResponse {
  success: boolean;
  user?: UserAuth;
  token?: string;
  refreshToken?: string;
  expiresAt?: Date;
  error?: string;
}

/**
 * Session interface
 */
export interface UserSession {
  id: string;
  userId: string;
  token: string;
  refreshToken?: string;
  expiresAt: Date;
  createdAt: Date;
  lastAccessedAt: Date;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Password reset request interface
 */
export interface PasswordResetRequest {
  email: string;
}

/**
 * Password reset confirmation interface
 */
export interface PasswordResetConfirmation {
  token: string;
  newPassword: string;
}

// ============================================================================
// Permission and Role Types
// ============================================================================

/**
 * Permission interface
 */
export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
}

/**
 * Role interface
 */
export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
  isSystemRole: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * User role assignment interface
 */
export interface UserRoleAssignment {
  userId: string;
  roleId: string;
  assignedBy: string;
  assignedAt: Date;
  expiresAt?: Date;
}

// ============================================================================
// Utility Types
// ============================================================================

/**
 * User status enumeration
 */
export type UserStatus = 'active' | 'inactive' | 'suspended' | 'pending';

/**
 * Supplier status enumeration
 */
export type SupplierStatus = 'active' | 'inactive' | 'suspended' | 'pending_approval';

/**
 * Contact method enumeration
 */
export type ContactMethod = 'email' | 'phone' | 'sms' | 'mail';

/**
 * User search filters
 */
export interface UserSearchFilters {
  roles?: string[];
  departments?: string[];
  status?: UserStatus[];
  searchTerm?: string;
  isActive?: boolean;
  lastLoginAfter?: Date;
  lastLoginBefore?: Date;
}

/**
 * Supplier search filters
 */
export interface SupplierSearchFilters {
  specialty?: string[];
  status?: SupplierStatus[];
  searchTerm?: string;
  isActive?: boolean;
  ratingMin?: number;
  ratingMax?: number;
  location?: string;
}

/**
 * User sort options
 */
export interface UserSortOptions {
  field: 'firstName' | 'lastName' | 'email' | 'department' | 'lastLoginDate' | 'createdAt';
  direction: 'asc' | 'desc';
}

/**
 * Supplier sort options
 */
export interface SupplierSortOptions {
  field: 'name' | 'contactPerson' | 'rating' | 'createdAt' | 'lastOrderDate';
  direction: 'asc' | 'desc';
}
