/**
 * Database Types for Trend IMS
 * 
 * This file contains type definitions for database-specific entities
 * including Purchase Orders and related database structures.
 */

// ============================================================================
// Purchase Order Types
// ============================================================================

/**
 * Purchase Order status enumeration
 */
export type PurchaseOrderStatus = 'draft' | 'pending_approval' | 'ordered' | 'partially_received' | 'fully_received' | 'cancelled';

/**
 * Purchase Order Item interface
 */
export interface PurchaseOrderItem {
  itemId: string; // Reference to part, assembly, or product
  itemType: 'Part' | 'Assembly' | 'Product';
  itemName?: string;
  itemCode?: string;
  description?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  unitOfMeasure?: string;
  expectedDeliveryDate?: Date;
  receivedQuantity?: number;
  notes?: string;
}

/**
 * Purchase Order interface based on the purchaseOrder model
 */
export interface PurchaseOrder {
  _id: string;
  poNumber: string; // Unique business identifier
  supplierId: string; // Reference to suppliers._id
  orderDate: Date;
  expectedDeliveryDate: Date;
  items: PurchaseOrderItem[];
  totalAmount: number;
  status: PurchaseOrderStatus;
  notes?: string;
  shippingAddress: string;
  billingAddress: string;
  termsAndConditions?: string;
  approvedBy?: string | null; // Reference to users._id
  approvedAt?: Date | null;
  createdBy?: string | null; // Reference to users._id
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Purchase Order creation request interface
 */
export interface CreatePurchaseOrderRequest {
  supplierId: string;
  expectedDeliveryDate: Date;
  items: Omit<PurchaseOrderItem, 'totalPrice'>[];
  notes?: string;
  shippingAddress: string;
  billingAddress: string;
  termsAndConditions?: string;
}

/**
 * Purchase Order update request interface
 */
export interface UpdatePurchaseOrderRequest {
  supplierId?: string;
  expectedDeliveryDate?: Date;
  items?: Omit<PurchaseOrderItem, 'totalPrice'>[];
  status?: PurchaseOrderStatus;
  notes?: string;
  shippingAddress?: string;
  billingAddress?: string;
  termsAndConditions?: string;
  approvedBy?: string;
}

/**
 * Purchase Order with populated references for display
 */
export interface PurchaseOrderWithDetails extends PurchaseOrder {
  supplier?: {
    _id: string;
    name: string;
    contactPerson: string;
    email: string;
    phone: string;
  };
  approvedByUser?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  createdByUser?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

// ============================================================================
// Purchase Order Receipt Types
// ============================================================================

/**
 * Purchase Order receipt interface for tracking deliveries
 */
export interface PurchaseOrderReceipt {
  _id: string;
  purchaseOrderId: string;
  receiptNumber: string;
  receivedDate: Date;
  receivedBy: string; // Reference to users._id
  items: PurchaseOrderReceiptItem[];
  notes?: string;
  attachments?: string[]; // File paths or URLs
  qualityInspectionRequired: boolean;
  qualityInspectionPassed?: boolean | null;
  inspectedBy?: string | null; // Reference to users._id
  inspectedAt?: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Purchase Order receipt item interface
 */
export interface PurchaseOrderReceiptItem {
  itemId: string;
  itemType: 'Part' | 'Assembly' | 'Product';
  orderedQuantity: number;
  receivedQuantity: number;
  unitPrice: number;
  condition: 'good' | 'damaged' | 'defective';
  batchNumber?: string;
  expirationDate?: Date;
  notes?: string;
}

// ============================================================================
// Warehouse and Location Types
// ============================================================================

/**
 * Warehouse interface
 */
export interface Warehouse {
  _id: string;
  warehouseCode: string;
  name: string;
  description?: string;
  address: string;
  managerId?: string | null; // Reference to users._id
  isActive: boolean;
  capacity?: number;
  currentUtilization?: number;
  zones?: WarehouseZone[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Warehouse zone interface for organizing storage areas
 */
export interface WarehouseZone {
  zoneId: string;
  zoneName: string;
  zoneType: 'receiving' | 'storage' | 'picking' | 'shipping' | 'quarantine';
  capacity?: number;
  currentUtilization?: number;
  temperatureControlled?: boolean;
  securityLevel?: 'low' | 'medium' | 'high';
}

/**
 * Storage location interface
 */
export interface StorageLocation {
  _id: string;
  warehouseId: string;
  zoneId?: string;
  aisle?: string;
  rack?: string;
  shelf?: string;
  bin?: string;
  locationCode: string; // Formatted location identifier
  capacity?: number;
  currentUtilization?: number;
  isActive: boolean;
  restrictions?: string[]; // e.g., ['hazardous', 'temperature_controlled']
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// Category and Classification Types
// ============================================================================

/**
 * Category interface for organizing items
 */
export interface Category {
  _id: string;
  categoryCode: string;
  name: string;
  description?: string;
  parentCategoryId?: string | null; // Reference to parent category
  level: number; // Hierarchy level (0 = root)
  isActive: boolean;
  sortOrder?: number;
  attributes?: CategoryAttribute[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Category attribute interface for defining category-specific properties
 */
export interface CategoryAttribute {
  attributeName: string;
  attributeType: 'text' | 'number' | 'boolean' | 'date' | 'select';
  isRequired: boolean;
  defaultValue?: any;
  selectOptions?: string[]; // For select type attributes
  validationRules?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

// ============================================================================
// System and Audit Types
// ============================================================================

/**
 * System log interface for audit trails
 */
export interface SystemLog {
  _id: string;
  logType: 'info' | 'warning' | 'error' | 'audit';
  module: string; // e.g., 'inventory', 'purchase_orders', 'work_orders'
  action: string; // e.g., 'create', 'update', 'delete', 'login'
  entityType?: string; // e.g., 'PurchaseOrder', 'WorkOrder'
  entityId?: string;
  userId?: string | null; // Reference to users._id
  ipAddress?: string;
  userAgent?: string;
  message: string;
  details?: Record<string, any>;
  timestamp: Date;
  createdAt: Date;
}

/**
 * Settings interface for system configuration
 */
export interface Settings {
  _id: string;
  category: string; // e.g., 'general', 'inventory', 'notifications'
  key: string;
  value: any;
  dataType: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description?: string;
  isUserConfigurable: boolean;
  isSystemCritical: boolean;
  validationRules?: {
    required?: boolean;
    min?: number;
    max?: number;
    pattern?: string;
    allowedValues?: any[];
  };
  lastModifiedBy?: string | null; // Reference to users._id
  lastModifiedAt?: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// Delivery and Shipment Types
// ============================================================================

/**
 * Delivery interface for tracking shipments
 */
export interface Delivery {
  _id: string;
  deliveryNumber: string;
  purchaseOrderId?: string | null; // Reference to purchase orders
  workOrderId?: string | null; // Reference to work orders
  supplierId?: string | null; // Reference to suppliers
  customerId?: string | null; // Reference to customers
  deliveryType: 'inbound' | 'outbound' | 'internal_transfer';
  status: 'scheduled' | 'in_transit' | 'delivered' | 'failed' | 'cancelled';
  scheduledDate: Date;
  actualDeliveryDate?: Date | null;
  carrierName?: string;
  trackingNumber?: string;
  deliveryAddress: string;
  items: DeliveryItem[];
  notes?: string;
  signedBy?: string;
  deliveryProof?: string[]; // File paths or URLs
  createdBy?: string | null; // Reference to users._id
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Delivery item interface
 */
export interface DeliveryItem {
  itemId: string;
  itemType: 'Part' | 'Assembly' | 'Product';
  itemName?: string;
  quantity: number;
  unitOfMeasure?: string;
  condition: 'good' | 'damaged' | 'missing';
  batchNumber?: string;
  serialNumbers?: string[];
  notes?: string;
}

// ============================================================================
// Utility Types
// ============================================================================

/**
 * Database search filters for purchase orders
 */
export interface PurchaseOrderSearchFilters {
  status?: PurchaseOrderStatus[];
  supplierId?: string[];
  createdBy?: string[];
  orderDateFrom?: Date;
  orderDateTo?: Date;
  expectedDeliveryFrom?: Date;
  expectedDeliveryTo?: Date;
  searchTerm?: string;
  amountMin?: number;
  amountMax?: number;
}

/**
 * Database sort options for purchase orders
 */
export interface PurchaseOrderSortOptions {
  field: 'poNumber' | 'orderDate' | 'expectedDeliveryDate' | 'totalAmount' | 'status' | 'createdAt';
  direction: 'asc' | 'desc';
}
