/**
 * Theme Types for Trend IMS
 * 
 * This file contains type definitions for theme-related functionality
 * including theme modes, variants, and configuration.
 */

// ============================================================================
// Basic Theme Types
// ============================================================================

/**
 * Theme mode enumeration
 */
export type ThemeMode = 'light' | 'dark' | 'system';

/**
 * Theme variant enumeration for different color schemes
 */
export type ThemeVariant =
  | 'default'
  | 'blue'
  | 'github'
  | 'linear'
  | 'vercel'
  | 'enterprise'
  | 'navy'
  | 'green'
  | 'purple'
  | 'orange'
  | 'red'
  | 'yellow'
  | 'pink'
  | 'indigo'
  | 'teal'
  | 'cyan'
  | 'emerald'
  | 'lime'
  | 'amber'
  | 'rose'
  | 'violet'
  | 'slate'
  | 'gray'
  | 'zinc'
  | 'neutral'
  | 'stone'
  | 'modern';

// ============================================================================
// Theme Configuration Types
// ============================================================================

/**
 * Theme configuration interface
 */
export interface ThemeConfig {
  id: string;
  name: string;
  variant: ThemeVariant;
  mode: ThemeMode;
  description: string;
  preview: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
  };
  className: string;
  colors?: {
    [mode: string]: ThemeColorPalette;
  };
  customColors?: ThemeCustomColors;
  preferences?: ThemePreferences;
}

/**
 * Custom color definitions for themes
 */
export interface ThemeCustomColors {
  primary?: string;
  secondary?: string;
  accent?: string;
  background?: string;
  foreground?: string;
  muted?: string;
  mutedForeground?: string;
  popover?: string;
  popoverForeground?: string;
  card?: string;
  cardForeground?: string;
  border?: string;
  input?: string;
  ring?: string;
  destructive?: string;
  destructiveForeground?: string;
}

/**
 * Theme preferences interface
 */
export interface ThemePreferences {
  autoSwitchTime?: {
    lightModeStart: string; // e.g., "06:00"
    darkModeStart: string;  // e.g., "18:00"
  };
  followSystemTheme?: boolean;
  highContrast?: boolean;
  reducedMotion?: boolean;
  fontSize?: 'small' | 'medium' | 'large';
  borderRadius?: 'none' | 'small' | 'medium' | 'large';
}

// ============================================================================
// Theme Variant Definitions
// ============================================================================

/**
 * Theme variant definition with color palette
 */
export interface ThemeVariantDefinition {
  name: string;
  variant: ThemeVariant;
  description?: string;
  colors: {
    light: ThemeColorPalette;
    dark: ThemeColorPalette;
  };
  preview?: {
    primary: string;
    secondary: string;
    accent: string;
  };
  // Legacy support for old theme config structure
  light?: ThemeConfig;
  dark?: ThemeConfig;
}

/**
 * Complete color palette for a theme
 */
export interface ThemeColorPalette {
  background: string;
  foreground: string;
  card: string;
  cardForeground: string;
  popover: string;
  popoverForeground: string;
  primary: string;
  primaryForeground: string;
  secondary: string;
  secondaryForeground: string;
  muted: string;
  mutedForeground: string;
  accent: string;
  accentForeground: string;
  destructive: string;
  destructiveForeground: string;
  border: string;
  input: string;
  ring: string;
  radius?: string;
}

// ============================================================================
// Current Theme State Types
// ============================================================================

/**
 * Current theme state interface
 */
export interface CurrentTheme {
  mode: ThemeMode;
  variant: ThemeVariant;
  resolvedMode: 'light' | 'dark'; // Actual resolved mode (system resolved to light/dark)
  colors?: ThemeColorPalette;
  isDark: boolean;
  isLight: boolean;
  isSystem: boolean;
  config: ThemeConfig; // Theme configuration object
}

/**
 * Theme context value interface
 */
export interface ThemeContextValue {
  // New theme system properties
  currentTheme: CurrentTheme;
  availableThemes: ThemeVariantDefinition[];
  setTheme: (variant: ThemeVariant, mode: ThemeMode) => void;
  setMode: (mode: ThemeMode) => void;
  setVariant: (variant: ThemeVariant) => void;
  toggleTheme: () => void;

  // Legacy properties for backward compatibility
  theme: CurrentTheme; // Updated to return CurrentTheme object for component compatibility
  mode: ThemeMode; // Legacy theme mode string
  toggleMode?: () => void;
  resetTheme?: () => void;
  availableVariants?: ThemeVariantDefinition[];
  isLoading?: boolean;
}

/**
 * Legacy theme context type alias
 */
export type ThemeContextType = ThemeContextValue;

// ============================================================================
// Theme History and Favorites Types
// ============================================================================

/**
 * Theme history entry interface
 */
export interface ThemeHistoryEntry {
  mode: ThemeMode;
  variant: ThemeVariant;
  timestamp: number;
  name?: string;
}

/**
 * Theme favorite entry interface
 */
export interface ThemeFavoriteEntry {
  mode: ThemeMode;
  variant: ThemeVariant;
  name: string;
  description?: string;
  createdAt: number;
  lastUsed?: number;
}

/**
 * Theme history context value interface
 */
export interface ThemeHistoryContextValue {
  history: ThemeHistoryEntry[];
  favorites: ThemeFavoriteEntry[];
  addToHistory: (theme: { mode: ThemeMode; variant: ThemeVariant }) => void;
  addToFavorites: (theme: ThemeFavoriteEntry) => void;
  removeFromFavorites: (index: number) => void;
  clearHistory: () => void;
  getRecentThemes: (limit?: number) => ThemeHistoryEntry[];
  getPopularThemes: (limit?: number) => ThemeHistoryEntry[];
}

// ============================================================================
// Theme Utility Types
// ============================================================================

/**
 * Theme transition configuration
 */
export interface ThemeTransition {
  duration: number; // in milliseconds
  easing: string; // CSS easing function
  properties: string[]; // CSS properties to transition
}

/**
 * Theme media query configuration
 */
export interface ThemeMediaQuery {
  dark: string;
  light: string;
  reducedMotion: string;
  highContrast: string;
}

/**
 * Theme storage configuration
 */
export interface ThemeStorage {
  key: string;
  version: number;
  migrate?: (oldData: any, oldVersion: number) => ThemeConfig;
}

/**
 * Theme validation result
 */
export interface ThemeValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// ============================================================================
// Theme Event Types
// ============================================================================

/**
 * Theme change event data
 */
export interface ThemeChangeEvent {
  previousTheme: CurrentTheme;
  newTheme: CurrentTheme;
  timestamp: number;
  source: 'user' | 'system' | 'auto';
}

/**
 * Theme event listener type
 */
export type ThemeEventListener = (event: ThemeChangeEvent) => void;

// ============================================================================
// Component-Specific Theme Types
// ============================================================================

/**
 * Theme selector props interface
 */
export interface ThemeSelectorProps {
  showModeToggle?: boolean;
  showVariantSelector?: boolean;
  showPreview?: boolean;
  compact?: boolean;
  className?: string;
}

/**
 * Theme preview card props interface
 */
export interface ThemePreviewCardProps {
  theme: ThemeVariantDefinition;
  isSelected?: boolean;
  onClick?: () => void;
  showName?: boolean;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

/**
 * Theme toggle props interface
 */
export interface ThemeToggleProps {
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
}
