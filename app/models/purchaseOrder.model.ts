import mongoose, { Document, Schema, Types } from 'mongoose';

// Interface for items within the purchase order (canonical schema)
export interface IPOItem {
  partId: Types.ObjectId; // Canonical field name
  description: string; // Part description, copied from part record at time of PO creation
  quantity: number; // Canonical field name
  unitPrice: number; // Canonical field name (Double)
  lineTotal: number; // Canonical field name (calculated as quantity * unitPrice)
  receivedQuantity: number; // Canonical field name
}



// Define interface for Purchase Order document (canonical schema)
export interface IPurchaseOrder extends Document {
  _id: Types.ObjectId;
  poNumber: string; // Canonical field name
  supplierId: Types.ObjectId; // Canonical field name, ref: 'Supplier'
  orderDate: Date; // Canonical field name
  expectedDeliveryDate: Date; // Canonical field name
  items: IPOItem[]; // Canonical items structure
  totalAmount: number; // Canonical field name (Double)
  status: 'pending_approval' | 'ordered' | 'partially_received' | 'fully_received' | 'cancelled'; // Canonical enum values
  notes: string; // Canonical field name
  shippingAddress: string; // Canonical field name
  billingAddress: string; // Canonical field name
  termsAndConditions: string; // Canonical field name
  createdBy: Types.ObjectId; // Canonical field name, ref: 'User'
  approvedBy?: Types.ObjectId | null; // Canonical field name, ref: 'User'
  approvalDate?: Date | null; // Canonical field name
  createdAt: Date;
  updatedAt: Date;
}

// Sub-schema for purchase order items (canonical schema)
const POItemSchema: Schema = new Schema({
  partId: {
    type: Schema.Types.ObjectId,
    ref: 'Part',
    required: [true, 'Part ID is required'],
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true
  },
  quantity: {
    type: Number,
    required: [true, 'Quantity is required'],
    min: [1, 'Quantity must be at least 1'],
    validate: {
      validator: Number.isInteger,
      message: 'Quantity must be a whole number'
    }
  },
  unitPrice: {
    type: Number,
    required: [true, 'Unit price is required'],
    min: [0, 'Unit price cannot be negative']
  },
  lineTotal: {
    type: Number,
    required: [true, 'Line total is required'],
    min: [0, 'Line total cannot be negative']
  },
  receivedQuantity: {
    type: Number,
    default: 0,
    min: [0, 'Received quantity cannot be negative'],
    validate: {
      validator: Number.isInteger,
      message: 'Received quantity must be a whole number'
    }
  }
}, { _id: false });



// Define schema for Purchase Order model (canonical schema)
const PurchaseOrderSchema: Schema<IPurchaseOrder> = new Schema(
  {
    poNumber: {
      type: String,
      required: [true, 'PO number is required'],
      unique: true,
      trim: true,
      index: true
    },
    supplierId: {
      type: Schema.Types.ObjectId,
      ref: 'Supplier',
      required: [true, 'Supplier ID is required'],
      index: true
    },
    orderDate: {
      type: Date,
      required: [true, 'Order date is required'],
      default: Date.now
    },
    expectedDeliveryDate: {
      type: Date,
      required: [true, 'Expected delivery date is required']
    },
    items: {
      type: [POItemSchema],
      required: [true, 'Items are required'],
      validate: {
        validator: function(v: any[]) {
          return Array.isArray(v) && v.length > 0;
        },
        message: 'At least one item is required'
      }
    },
    totalAmount: {
      type: Number,
      required: [true, 'Total amount is required'],
      min: [0, 'Total amount cannot be negative']
    },
    status: {
      type: String,
      required: [true, 'Status is required'],
      enum: {
        values: ['pending_approval', 'ordered', 'partially_received', 'fully_received', 'cancelled'],
        message: 'Status must be one of the predefined values'
      },
      default: 'pending_approval',
      index: true
    },
    shippingAddress: {
      type: String,
      required: [true, 'Shipping address is required'],
      trim: true
    },
    billingAddress: {
      type: String,
      required: [true, 'Billing address is required'],
      trim: true
    },
    termsAndConditions: {
      type: String,
      required: [true, 'Terms and conditions are required'],
      trim: true
    },
    notes: {
      type: String,
      required: [true, 'Notes are required'],
      trim: true
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Created by user ID is required']
    },
    approvedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      default: null
    },
    approvalDate: {
      type: Date,
      default: null
    },

  },
  {
    timestamps: true,
    toJSON: { getters: true },
    toObject: { getters: true },
    strictPopulate: false,
  }
);

// Add indexes for improved query performance (canonical schema)
PurchaseOrderSchema.index({ orderDate: -1 });
PurchaseOrderSchema.index({ status: 1, orderDate: -1 });
PurchaseOrderSchema.index({ supplierId: 1, status: 1 });
PurchaseOrderSchema.index({ 'items.partId': 1 });

// Pre-save hook to calculate amounts (canonical schema)
PurchaseOrderSchema.pre('save', function(this: IPurchaseOrder & Document, next) {
  if (this.isModified('items') || this.isNew) {
    let calculatedTotal = 0;
    this.items.forEach((item: any) => {
      const quantity = item.quantity || 0;
      const unitPrice = item.unitPrice || 0;
      // Calculate line total
      const lineTotal = quantity * unitPrice;
      item.lineTotal = lineTotal;
      calculatedTotal += lineTotal;
    });
    this.totalAmount = calculatedTotal;
  }
  next();
});

// Create and export Purchase Order model with proper TypeScript typing
const PurchaseOrder = mongoose.models?.PurchaseOrder as mongoose.Model<IPurchaseOrder> || mongoose.model<IPurchaseOrder>('PurchaseOrder', PurchaseOrderSchema);

export { PurchaseOrder };
export default PurchaseOrder;