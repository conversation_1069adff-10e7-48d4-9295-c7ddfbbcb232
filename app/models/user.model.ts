import mongoose, { Schema, Document, Types } from 'mongoose';

// Define interface for User document
export interface IUser extends Document {
  _id: Types.ObjectId;
  user_id: string; // Added, unique business identifier
  username: string;
  password_hash: string; // Renamed from passwordHash
  email: string;
  first_name: string; // Renamed from firstName, now required
  last_name: string; // Renamed from lastName, now required
  roles: string[];
  department?: string | null;
  job_title?: string | null; // Renamed from jobTitle
  phone_number?: string | null; // Replaced contactInfo
  is_active: boolean; // Renamed from isActive (schema name, not Mongoose field name necessarily)
  last_login_date?: Date | null; // Renamed from lastLogin
  createdAt: Date;
  updatedAt: Date;
}

// Define schema for User model
const UserSchema: Schema<IUser> = new Schema(
  {
    user_id: { // Added
      type: String,
      required: [true, 'User ID is required'],
      unique: true,
      index: true,
      trim: true
    },
    username: { 
      type: String, 
      required: [true, 'Username is required'],
      unique: true, 
      index: true,
      trim: true 
    },
    password_hash: { // Renamed
      type: String,
      required: [true, 'Password hash is required']
    },
    email: { 
      type: String, 
      required: [true, 'Email is required'], 
      unique: true,
      trim: true,
      lowercase: true,
      match: [
        /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
        'Please provide a valid email address'
      ]
    },
    first_name: { // Renamed and made required
      type: String,
      required: [true, 'First name is required'],
      trim: true
    },
    last_name: { // Renamed and made required
      type: String,
      required: [true, 'Last name is required'],
      trim: true
    },
    roles: {
      type: [String],
      required: [true, 'At least one role is required'],
      validate: {
        validator: function(v: string[]) {
          return Array.isArray(v) && v.length > 0;
        },
        message: 'At least one role must be assigned'
      },
      default: ['viewer'] // Default role as per target schema example
    },
    department: { 
      type: String,
      default: null,
      trim: true
    },
    job_title: { // Renamed
      type: String,
      default: null,
      trim: true
    },
    phone_number: { // Replaced contactInfo
        type: String,
        default: null,
        trim: true
    },
    is_active: { // Renamed (field name for consistency with target)
      type: Boolean, 
      default: true 
    },
    last_login_date: { // Renamed
      type: Date,
      default: null
    }
  },
  { timestamps: true }
);

// Add indexes for improved query performance
UserSchema.index({ roles: 1 });
UserSchema.index({ is_active: 1 });
// UserSchema.index({ employeeId: 1 }, { sparse: true }); // Removed as employeeId is removed

// Create and export User model with proper TypeScript typing
const User = mongoose.models?.User as mongoose.Model<IUser> || mongoose.model<IUser>('User', UserSchema);

// Export as both named export and default export for compatibility
export { User };
export default User;
