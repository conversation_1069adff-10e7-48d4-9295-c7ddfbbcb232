/**
 * UI Enhancement Model
 * Defines the structure for enhanced UI components using Magic UI
 */

// Base interface for all enhanced components
export interface EnhancedComponent {
  id: string;
  type: string;
  animationEnabled: boolean;
}

// Enhanced card component with magic effects
export interface EnhancedCard extends EnhancedComponent {
  type: 'card';
  effectType: 'magic-card' | 'shine-border' | 'neon-gradient';
  hoverAnimation: boolean;
  backgroundEffect?: string;
}

// Enhanced table row with expandable features
export interface EnhancedExpandableRow extends EnhancedComponent {
  type: 'expandable-row';
  expandAnimation: 'slide' | 'fade' | 'scale';
  highlightOnHover: boolean;
  detailsBackgroundEffect?: string;
}

// Enhanced background patterns
export interface EnhancedBackground extends EnhancedComponent {
  type: 'background';
  patternType: 'grid' | 'dots' | 'warp' | 'interactive-grid';
  interactiveMode: boolean;
  colorScheme: 'light' | 'dark' | 'system';
}

// Enhanced button with effects
export interface EnhancedButton extends EnhancedComponent {
  type: 'button';
  effectType: 'shimmer' | 'rainbow' | 'ripple' | 'pulsating';
  size: 'sm' | 'md' | 'lg';
  variant: 'default' | 'outline' | 'ghost';
}

// Enhanced text with animations
export interface EnhancedText extends EnhancedComponent {
  type: 'text';
  animationType: 'shine' | 'gradient' | 'typing' | 'reveal';
  importance: 'low' | 'medium' | 'high';
}

// Mapping between entity types and their enhanced UI components
export interface EntityUIMapping {
  entityType: string; // e.g., 'assembly', 'part', 'product'
  cardComponent?: EnhancedCard;
  rowComponent?: EnhancedExpandableRow;
  backgroundComponent?: EnhancedBackground;
  buttonComponents?: EnhancedButton[];
  textComponents?: EnhancedText[];
}

// Configuration for phased implementation
export interface ImplementationPhase {
  phaseNumber: number;
  name: string;
  components: string[]; // IDs of components to implement in this phase
  completed: boolean;
  startDate?: Date;
  endDate?: Date;
}

// Main UI enhancement configuration
export interface UIEnhancementConfig {
  version: string;
  lastUpdated: Date;
  entityMappings: EntityUIMapping[];
  implementationPhases: ImplementationPhase[];
  globalSettings: {
    animationsEnabled: boolean;
    performanceMode: boolean;
    accessibilityMode: boolean;
  };
}