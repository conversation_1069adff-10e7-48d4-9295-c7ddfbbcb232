// Export all models from this index file
export * from './part.model';

// Import and re-export Supplier model explicitly
import SupplierModel from './supplier.model';
export const Supplier = SupplierModel;

export * from './purchaseOrder.model';
export * from './user.model';
export * from './workOrder.model';
export * from './warehouse.model';

// Import and re-export Assembly model explicitly
import AssemblyModel from './assembly.model';
export const Assembly = AssemblyModel;

// Replace transaction with inventorytransaction
export * from './inventorytransaction.model';
export * from './product.model';

// Add exports for newly added/updated models
export * from './batch.model';
export * from './batchLog.model';
export * from './delivery.model';
export * from './settings.model';
export * from './category.model';
export * from './systemLog.model';

// Add export for new inventory model
export * from './inventory.model';

// Keep backward compatibility
import InventoryTransactionModel from './inventorytransaction.model';
export const Transaction = InventoryTransactionModel;
