import mongoose, { Schema, Document, Types } from 'mongoose';

export interface IWarehouse extends Document {
  _id: Types.ObjectId; // MongoDB ObjectId identifier
  location_id: string; // Changed from warehouseCode
  name: string;
  location: string;
  capacity: number; // Target schema: Int32
  manager: string;
  contact: string;
  isActive: boolean;
  isBinTracked: boolean;
  schemaVersion?: number; // Retaining for good practice
  createdAt: Date;
  updatedAt: Date;
}

const WarehouseSchema: Schema<IWarehouse> = new Schema({
  location_id: { // Changed from warehouseCode
    type: String,
    required: [true, 'Location ID is required'],
    unique: true,
    trim: true,
    index: true // Ensure index is on the new field name
  },
  name: {
    type: String,
    required: [true, 'Warehouse name is required'],
    trim: true
  },
  location: {
    type: String,
    required: [true, 'Location details are required'],
    trim: true
  },
  capacity: {
    type: Number, // Mongoose Number can represent Int32
    required: [true, 'Storage capacity is required'],
    min: [0, 'Capacity cannot be negative'],
    validate: {
      validator: Number.isInteger,
      message: 'Capacity must be a whole number (integer)'
    }
  },
  manager: {
    type: String,
    required: [true, 'Manager name is required'],
    trim: true
  },
  contact: {
    type: String,
    required: [true, 'Contact information is required'],
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isBinTracked: {
    type: Boolean,
    default: false
  },
  schemaVersion: { // Retaining for good practice
    type: Number,
    default: 1
  }
}, {
  timestamps: true // Handles createdAt and updatedAt
});

// Ensure indexes are correctly defined if not automatically handled by `index: true` on field
// WarehouseSchema.index({ location_id: 1 }, { unique: true }); // This is covered by index:true and unique:true on location_id
WarehouseSchema.index({ name: 1 });

// Create the model with proper TypeScript typing
const Warehouse = mongoose.models?.Warehouse as mongoose.Model<IWarehouse> || mongoose.model<IWarehouse>('Warehouse', WarehouseSchema);

export { Warehouse };
export default Warehouse;