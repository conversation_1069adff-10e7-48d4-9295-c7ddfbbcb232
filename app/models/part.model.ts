import mongoose, { Schema, Document, Types, Model } from 'mongoose';

// Interface for Stock Levels tracking parts through manufacturing states
export interface IStockLevels {
  raw: number;        // Raw materials received from suppliers
  hardening: number;  // Parts in hardening process
  grinding: number;   // Parts in grinding process
  finished: number;   // Completed parts ready for sale
  rejected: number;   // Rejected parts (quality issues)
}

// Interface for the Inventory sub-document with granular stock tracking
export interface IInventory {
  stockLevels: IStockLevels;
  warehouseId: Types.ObjectId; // Refers to 'warehouses._id'
  safetyStockLevel: number;
  maximumStockLevel: number;
  averageDailyUsage: number; // Stored as Double in schema, Mongoose Number handles this
  abcClassification: string; // e.g., 'A', 'B', 'C'
  lastStockUpdate?: Date | null;
  // BACKWARD COMPATIBILITY: Virtual field for legacy currentStock access
  currentStock?: number; // Will be computed as stockLevels.finished
}

// Schema for Stock Levels sub-document
const StockLevelsSchema = new Schema<IStockLevels>({
  raw: { type: Number, required: true, min: 0, default: 0 },
  hardening: { type: Number, required: true, min: 0, default: 0 },
  grinding: { type: Number, required: true, min: 0, default: 0 },
  finished: { type: Number, required: true, min: 0, default: 0 },
  rejected: { type: Number, required: true, min: 0, default: 0 },
}, { _id: false });

// Schema for the Inventory sub-document with granular stock tracking
const InventorySchema = new Schema<IInventory>({
  stockLevels: { type: StockLevelsSchema, required: true },
  warehouseId: { type: Schema.Types.ObjectId, ref: 'Warehouse', required: true },
  safetyStockLevel: { type: Number, required: true }, // Int32 in schema
  maximumStockLevel: { type: Number, required: true }, // Int32 in schema
  averageDailyUsage: { type: Number, required: true }, // Double in schema
  abcClassification: { type: String, required: true }, // e.g., 'A', 'B', 'C'
  lastStockUpdate: { type: Date, default: null, required: false },
}, {
  _id: false,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// BACKWARD COMPATIBILITY: Virtual field for legacy currentStock access
InventorySchema.virtual('currentStock').get(function(this: IInventory) {
  return this.stockLevels?.finished || 0;
});

// Interface for the plain Part data object
export interface IPart {
  partNumber: string;
  name: string;
  businessName?: string | null; // NEW FIELD: Human-readable business name for the part
  description?: string | null;
  technicalSpecs?: string | null;
  isManufactured: boolean;
  reorderLevel?: number | null;
  status: 'active' | 'inactive' | 'obsolete';
  // UPDATED: inventory is now virtual, computed from inventories collection
  inventory?: IInventory; // Virtual field for backward compatibility
  supplierId?: Types.ObjectId | null;
  unitOfMeasure: string;
  costPrice: number;
  categoryId?: Types.ObjectId | null;
  createdAt?: Date;
  updatedAt?: Date;

  // NEW: Virtual fields for aggregated inventory data
  totalStock?: number; // Total stock across all locations and types
  stockLevels?: IStockLevels; // Aggregated stock levels across all locations
}

// Interface for the Part Mongoose Document
export interface IPartDocument extends IPart, Document {
  // Inherits all fields from IPart (partNumber, name, description, etc.)
  // Inherits Mongoose document properties and methods from Document.
  // Schema options like timestamps: true will add createdAt and updatedAt.
  // _id is also automatically added by Mongoose schemas.
}

// Main Part Schema
const PartSchema: Schema<IPartDocument> = new Schema({
  partNumber: { type: String, required: true, unique: true, index: true, trim: true },
  name: { type: String, required: true, trim: true },
  businessName: { type: String, trim: true, default: null, required: false }, // NEW FIELD: Human-readable business name
  description: { type: String, trim: true, default: null, required: false },
  technicalSpecs: { type: String, trim: true, default: null, required: false },
  isManufactured: { type: Boolean, default: false, required: true },
  reorderLevel: { type: Number, default: null, required: false }, // Int32 | Null
  status: {
    type: String,
    enum: ['active', 'inactive', 'obsolete'], // Canonical schema values
    default: 'active',
    required: true,
  },
  // UPDATED: inventory is now virtual, computed from inventories collection
  // BACKWARD COMPATIBILITY: Keep field for migration period but make it optional
  inventory: { type: InventorySchema, required: false, default: undefined },
  supplierId: { type: Schema.Types.ObjectId, ref: 'Supplier', default: null, required: false },
  unitOfMeasure: { type: String, required: true },
  costPrice: { type: Number, required: true }, // Double
  categoryId: { type: Schema.Types.ObjectId, ref: 'Category', default: null, required: false },
}, { timestamps: true });

// PERFORMANCE OPTIMIZATION: Add indexes for aggregation pipeline $lookup operations
// These indexes eliminate N+1 queries by optimizing the $lookup stages
// UPDATED: Legacy inventory index - now sparse since inventory field is optional
PartSchema.index({ 'inventory.warehouseId': 1 }, {
  name: 'inventory_warehouseId_1_legacy',
  background: true,
  sparse: true, // Since inventory field is now optional
  comment: 'Legacy index for warehouse $lookup during migration period'
});

PartSchema.index({ 'supplierId': 1 }, {
  name: 'supplierId_1',
  background: true,
  sparse: true, // Since supplierId can be null
  comment: 'Index for supplier $lookup in aggregation pipeline'
});

PartSchema.index({ 'categoryId': 1 }, {
  name: 'categoryId_1',
  background: true,
  sparse: true, // Since categoryId can be null
  comment: 'Index for category $lookup in aggregation pipeline'
});

// Compound index for common query patterns (status filtering with sorting)
PartSchema.index({ 'status': 1, 'updatedAt': -1 }, {
  name: 'status_updatedAt_compound',
  background: true,
  comment: 'Compound index for status filtering with updatedAt sorting'
});

// Text index for search functionality
PartSchema.index({
  'name': 'text',
  'businessName': 'text', // NEW FIELD: Include businessName in search
  'description': 'text',
  'partNumber': 'text',
  'technicalSpecs': 'text'
}, {
  name: 'parts_text_search',
  background: true,
  comment: 'Text index for parts search functionality including businessName'
});

// VIRTUAL PROPERTIES for backward compatibility
// These will be populated by aggregation pipelines in services

// Virtual property for total stock across all locations
PartSchema.virtual('totalStock').get(function(this: IPartDocument) {
  // This will be populated by aggregation pipeline
  return (this as any)._totalStock || 0;
});

// Virtual property for aggregated stock levels
PartSchema.virtual('stockLevels').get(function(this: IPartDocument) {
  // This will be populated by aggregation pipeline
  return (this as any)._stockLevels || {
    raw: 0,
    hardening: 0,
    grinding: 0,
    finished: 0,
    rejected: 0
  };
});

// Virtual property for backward compatibility with legacy inventory access
PartSchema.virtual('currentStock').get(function(this: IPartDocument) {
  // For backward compatibility, return finished stock
  const stockLevels = (this as any)._stockLevels;
  return stockLevels?.finished || 0;
});

// Ensure virtuals are included in JSON output
PartSchema.set('toJSON', { virtuals: true });
PartSchema.set('toObject', { virtuals: true });

// Create the model with proper TypeScript typing
const Part: Model<IPartDocument> = mongoose.models?.Part as mongoose.Model<IPartDocument> || mongoose.model<IPartDocument>('Part', PartSchema);

export { PartSchema, Part }; // Export both schema and model as named exports
export default Part;