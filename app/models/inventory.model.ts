import mongoose, { Schema, Document, Types } from 'mongoose';

// Define interface for InventoryLevel document
export interface IInventoryLevel extends Document {
  _id: Types.ObjectId;
  item_id: Types.ObjectId; // refPath: 'item_type'
  item_type: 'Part' | 'Assembly' | 'Product';
  warehouse_id: Types.ObjectId; // ref: 'Warehouse'
  quantity_on_hand: number;
  quantity_allocated?: number;
  quantity_available?: number; // Virtual field
  location_in_warehouse?: string | null;
  reorder_level?: number | null;
  safety_stock_level?: number | null;
  maximum_stock_level?: number | null;
  average_daily_usage?: mongoose.Types.Decimal128 | null;
  abc_classification?: string | null;
  last_stock_update: Date;
  notes?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// Define schema for InventoryLevel model
const InventoryLevelSchema: Schema<IInventoryLevel> = new Schema(
  {
    item_id: { type: Schema.Types.ObjectId, required: true, refPath: 'item_type', index: true },
    item_type: { type: String, required: true, enum: ['Part', 'Assembly', 'Product'], index: true },
    warehouse_id: { type: Schema.Types.ObjectId, ref: 'Warehouse', required: true, index: true },
    quantity_on_hand: { type: Number, required: true, default: 0, min: 0, validate: Number.isInteger },
    quantity_allocated: { type: Number, default: 0, min: 0, validate: Number.isInteger },
    location_in_warehouse: { type: String, trim: true, default: null },
    reorder_level: { type: Number, default: null, min: 0, validate: Number.isInteger },
    safety_stock_level: { type: Number, default: null, min: 0, validate: Number.isInteger },
    maximum_stock_level: { type: Number, default: null, min: 0, validate: Number.isInteger },
    average_daily_usage: { type: Schema.Types.Decimal128, default: null },
    abc_classification: { type: String, trim: true, default: null }, // e.g., "A", "B", "C"
    last_stock_update: { type: Date, default: Date.now },
    notes: { type: String, trim: true, default: null },
  },
  { 
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Compound unique index
InventoryLevelSchema.index({ item_id: 1, item_type: 1, warehouse_id: 1 }, { unique: true });

// Virtual for quantity_available
InventoryLevelSchema.virtual('quantity_available').get(function(this: IInventoryLevel) {
  return (this.quantity_on_hand || 0) - (this.quantity_allocated || 0);
});

// Pre-save hook to update last_stock_update
InventoryLevelSchema.pre('save', function(this: IInventoryLevel & Document, next) {
  if (this.isModified('quantity_on_hand') || this.isModified('quantity_allocated') || this.isNew) {
    this.last_stock_update = new Date();
  }
  next();
});

// Create and export InventoryLevel model with proper TypeScript typing
// Using 'InventoryLevel' as model name. Collection name will be 'inventory_levels'.
const InventoryLevel = mongoose.models?.InventoryLevel as mongoose.Model<IInventoryLevel> || mongoose.model<IInventoryLevel>('InventoryLevel', InventoryLevelSchema, 'inventory_levels');

export { InventoryLevel };
export default InventoryLevel;