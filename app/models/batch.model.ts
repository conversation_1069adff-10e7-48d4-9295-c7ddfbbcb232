import mongoose, { Schema, Document, Types } from 'mongoose';

// Define valid batch statuses - Aligned with checklist
export const BATCH_STATUSES = {
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  PAUSED: 'paused',
  CANCELLED: 'cancelled'
};

// Define valid status transitions - Updated based on new BATCH_STATUSES
const VALID_STATUS_TRANSITIONS = {
  [BATCH_STATUSES.PENDING]: [BATCH_STATUSES.IN_PROGRESS, BATCH_STATUSES.CANCELLED, BATCH_STATUSES.PAUSED],
  [BATCH_STATUSES.IN_PROGRESS]: [BATCH_STATUSES.COMPLETED, BATCH_STATUSES.PAUSED, BATCH_STATUSES.CANCELLED],
  [BATCH_STATUSES.PAUSED]: [BATCH_STATUSES.IN_PROGRESS, BATCH_STATUSES.CANCELLED],
  [BATCH_STATUSES.COMPLETED]: [],
  [BATCH_STATUSES.CANCELLED]: []
};

// Interface for Batch document
export interface I<PERSON>atch extends Document {
  _id: Types.ObjectId;
  batchCode: string;
  partId?: Types.ObjectId;
  assemblyId?: Types.ObjectId;
  quantityPlanned: number;
  quantityProduced?: number;
  quantityScrapped?: number;
  startDate: Date | null;
  endDate?: Date | null;
  status: string;
  notes?: string | null;
  workOrderId: Types.ObjectId;
  assignedMachine?: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// Schema for Batch model
const BatchSchema: Schema = new Schema(
  {
    batchCode: { type: String, required: true, unique: true, index: true, trim: true },
    partId: { type: Schema.Types.ObjectId, ref: 'Part', index: true, default: null },
    assemblyId: { type: Schema.Types.ObjectId, ref: 'Assembly', index: true, default: null },
    quantityPlanned: {
      type: Number,
      required: true,
      min: [0, 'Quantity planned must be a non-negative number'],
      validate: { validator: Number.isInteger, message: 'Quantity planned must be a whole number'}
    },
    quantityProduced: {
      type: Number,
      default: 0,
      min: [0, 'Quantity produced must be a non-negative number'],
      validate: [
        { validator: Number.isInteger, message: 'Quantity produced must be a whole number'},
        {
          validator: function(this: IBatch, value: number) {
            if (value === undefined || value === null) return true;
            return value <= this.quantityPlanned;
          },
          message: 'Quantity produced cannot exceed quantity planned'
        }
      ]
    },
    quantityScrapped: {
      type: Number,
      default: 0,
      min: [0, 'Quantity scrapped must be a non-negative number'],
      validate: { validator: Number.isInteger, message: 'Quantity scrapped must be a whole number'}
    },
    startDate: { type: Date, default: null },
    endDate: {
      type: Date,
      default: null,
      validate: {
        validator: function(this: IBatch, value: Date | null) {
          if (value === undefined || value === null || this.startDate === null) return true;
          return value >= this.startDate;
        },
        message: 'End date must be after start date'
      }
    },
    status: {
      type: String,
      required: true,
      index: true,
      enum: {
        values: Object.values(BATCH_STATUSES),
        message: 'Invalid batch status'
      },
      default: BATCH_STATUSES.PENDING
    },
    notes: { type: String, default: null, trim: true },
    workOrderId: { type: Schema.Types.ObjectId, ref: 'WorkOrder', required: true, index: true },
    assignedMachine: { type: String, default: null, trim: true }
  },
  { timestamps: true }
);

BatchSchema.pre('validate', function(this: IBatch & Document, next) {
  if (!this.partId && !this.assemblyId) {
    this.invalidate('partId', 'Either partId or assemblyId must be provided');
    this.invalidate('assemblyId', 'Either partId or assemblyId must be provided');
  } else if (this.partId && this.assemblyId) {
    this.invalidate('partId', 'Only one of partId or assemblyId should be provided');
    this.invalidate('assemblyId', 'Only one of partId or assemblyId should be provided');
  }
  next();
});

BatchSchema.pre('save', function(this: IBatch & Document, next) {
  if (this.isNew) return next();
  if (this.isModified('status')) {
    const oldStatus = this.get('status', String, { getters: false });
    const newStatus = this.status;
    if (VALID_STATUS_TRANSITIONS[oldStatus] && !VALID_STATUS_TRANSITIONS[oldStatus].includes(newStatus)) {
      return next(new Error(`Invalid status transition from ${oldStatus} to ${newStatus}`));
    }
    if (newStatus === BATCH_STATUSES.COMPLETED && !this.endDate) {
      this.endDate = new Date();
    }
  }
  next();
});

// Create the model with proper TypeScript typing
const Batch = mongoose.models?.Batch as mongoose.Model<IBatch> || mongoose.model<IBatch>('Batch', BatchSchema);

export { Batch };
export default Batch;
