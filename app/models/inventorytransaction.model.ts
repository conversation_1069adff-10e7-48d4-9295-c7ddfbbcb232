import mongoose, { Schema, Document, Types } from 'mongoose';

// Interface for movement location (from/to)
export interface IMovementLocation {
  locationId?: Types.ObjectId; // Reference to locations._id (optional for backward compatibility)
  stockType: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected' | 'scrap';
  // BACKWARD COMPATIBILITY: Deprecated field maintained during transition
  warehouseId?: Types.ObjectId; // DEPRECATED: Use locationId instead, but still required for legacy support
}

// Define interface for event-sourced InventoryTransaction document
export interface IInventoryTransaction extends Document {
  _id: Types.ObjectId;
  transactionId: string; // Human-readable transaction ID (e.g., "MOV-20250722-001")
  partId: Types.ObjectId; // Reference to the part being moved
  itemType?: 'Part' | 'Assembly' | 'Product'; // Made optional for backward compatibility

  // EVENT-SOURCED MOVEMENT STRUCTURE
  from: IMovementLocation | null; // null for external receipts (purchases)
  to: IMovementLocation | null;   // null for external shipments (sales, scrap)
  quantity: number; // Always positive - represents amount moved

  // TRANSACTION METADATA
  transactionType: 'purchase_receipt' | 'internal_transfer' | 'sales_shipment' | 'scrap_disposal' | 'process_move' | 'adjustment';
  transactionDate: Date;
  referenceNumber?: string | null;
  referenceType?: 'PurchaseOrder' | 'WorkOrder' | 'SalesOrder' | 'StockAdjustment' | 'ProcessOrder' | null;
  userId: Types.ObjectId;
  notes?: string | null;

  // BACKWARD COMPATIBILITY FIELDS (deprecated but maintained during transition)
  warehouseId?: Types.ObjectId; // Legacy field - will be derived from from/to
  previousStock?: number; // Legacy field - no longer used in event-sourced model
  newStock?: number; // Legacy field - no longer used in event-sourced model

  createdAt: Date;
  updatedAt: Date;
}

// Schema for movement location (from/to)
const MovementLocationSchema = new Schema<IMovementLocation>({
  locationId: {
    type: Schema.Types.ObjectId,
    ref: 'Location',
    required: false // Made optional for backward compatibility
  },
  stockType: {
    type: String,
    required: true,
    enum: {
      values: ['raw', 'hardening', 'grinding', 'finished', 'rejected', 'scrap'],
      message: 'Stock type must be one of: raw, hardening, grinding, finished, rejected, scrap'
    }
  },
  // BACKWARD COMPATIBILITY: Deprecated field maintained during transition
  warehouseId: {
    type: Schema.Types.ObjectId,
    ref: 'Warehouse',
    required: false // DEPRECATED: Use locationId instead
  }
}, { _id: false });

// Define schema for event-sourced InventoryTransaction model
const InventoryTransactionSchema: Schema = new Schema(
  {
    transactionId: {
      type: String,
      required: [true, 'Transaction ID is required'],
      unique: true,
      index: true,
      trim: true
    },
    partId: {
      type: Schema.Types.ObjectId,
      ref: 'Part',
      required: [true, 'Part ID is required'],
      index: true
    },
    itemType: {
      type: String,
      required: false,
      enum: {
        values: ['Part', 'Assembly', 'Product'],
        message: 'Item type must be one of: Part, Assembly, Product'
      },
      index: true,
      default: 'Part'
    },

    // EVENT-SOURCED MOVEMENT STRUCTURE
    from: {
      type: MovementLocationSchema,
      required: false, // null for external receipts
      default: null
    },
    to: {
      type: MovementLocationSchema,
      required: false, // null for external shipments
      default: null
    },
    quantity: {
      type: Number,
      required: [true, 'Quantity is required'],
      min: [0, 'Quantity must be positive'],
      validate: {
        validator: Number.isInteger,
        message: 'Quantity must be a whole number'
      }
    },

    transactionType: {
      type: String,
      required: [true, 'Transaction type is required'],
      enum: {
        values: ['purchase_receipt', 'internal_transfer', 'sales_shipment', 'scrap_disposal', 'process_move', 'adjustment'],
        message: 'Transaction type must be one of the predefined values'
      },
      index: true
    },

    transactionDate: {
      type: Date,
      required: [true, 'Transaction date is required'],
      default: Date.now,
      index: true
    },
    referenceNumber: {
      type: String,
      default: null,
      index: true
    },
    referenceType: {
      type: String,
      enum: {
        values: ['PurchaseOrder', 'WorkOrder', 'SalesOrder', 'StockAdjustment', 'ProcessOrder', null],
        message: 'Reference type must be a valid model name or null'
      },
      default: null
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
      index: true
    },
    notes: {
      type: String,
      default: null,
      trim: true
    },

    // BACKWARD COMPATIBILITY FIELDS (deprecated but maintained during transition)
    warehouseId: {
      type: Schema.Types.ObjectId,
      ref: 'Warehouse',
      required: false, // No longer required in event-sourced model
      index: true
    },
    previousStock: {
      type: Number,
      required: false, // No longer required in event-sourced model
      validate: {
        validator: function(value: number) {
          return value === undefined || Number.isInteger(value);
        },
        message: 'Previous stock must be a whole number'
      }
    },
    newStock: {
      type: Number,
      required: false, // No longer required in event-sourced model
      validate: {
        validator: function(value: number) {
          return value === undefined || Number.isInteger(value);
        },
        message: 'New stock must be a whole number'
      }
    }
  },
  { timestamps: true, collection: 'transactions' }
);

// Add indexes for efficient queries on event-sourced structure
InventoryTransactionSchema.index({ partId: 1, transactionDate: -1 });
InventoryTransactionSchema.index({ 'from.warehouseId': 1, transactionDate: -1 });
InventoryTransactionSchema.index({ 'to.warehouseId': 1, transactionDate: -1 });
InventoryTransactionSchema.index({ transactionType: 1, transactionDate: -1 });

// Add validation for event-sourced structure
InventoryTransactionSchema.pre('validate', function(this: IInventoryTransaction & Document, next) {
  // Ensure at least one of from or to is specified (not both null)
  if (!this.from && !this.to) {
    this.invalidate('from', 'Either from or to location must be specified');
    this.invalidate('to', 'Either from or to location must be specified');
  }

  // Validate transaction type matches from/to structure
  if (this.transactionType === 'purchase_receipt' && this.from !== null) {
    this.invalidate('from', 'Purchase receipts should have from=null (external source)');
  }
  if (this.transactionType === 'sales_shipment' && this.to !== null) {
    this.invalidate('to', 'Sales shipments should have to=null (external destination)');
  }
  if (this.transactionType === 'internal_transfer' && (!this.from || !this.to)) {
    this.invalidate('from', 'Internal transfers require both from and to locations');
    this.invalidate('to', 'Internal transfers require both from and to locations');
  }

  next();
});

// Create and export InventoryTransaction model with proper TypeScript typing
const InventoryTransaction = mongoose.models?.InventoryTransaction as mongoose.Model<IInventoryTransaction> || mongoose.model<IInventoryTransaction>('InventoryTransaction', InventoryTransactionSchema);

// Export as both named export and default export for compatibility
export { InventoryTransaction };
export default InventoryTransaction;