"use client";

import { useTheme } from '@/app/context/ThemeContext';
import { useMemo } from 'react';

/**
 * Utility function to get CSS variable value from document
 */
const getCSSVariableValue = (variable: string): string => {
  if (typeof window === 'undefined') return '';
  
  try {
    const value = getComputedStyle(document.documentElement)
      .getPropertyValue(variable)
      .trim();
    return value;
  } catch (error) {
    console.warn(`Failed to get CSS variable ${variable}:`, error);
    return '';
  }
};

/**
 * Hook for accessing theme colors as CSS variable values
 * Useful for inline styles and dynamic color calculations
 */
export const useThemeColors = () => {
  const { currentTheme } = useTheme();
  
  return useMemo(() => ({
    // Primary theme colors
    primary: getCSSVariableValue('--primary'),
    primaryForeground: getCSSVariableValue('--primary-foreground'),
    secondary: getCSSVariableValue('--secondary'),
    secondaryForeground: getCSSVariableValue('--secondary-foreground'),
    
    // Base colors
    background: getCSSVariableValue('--background'),
    foreground: getCSSVariableValue('--foreground'),
    
    // Card colors
    card: getCSSVariableValue('--card'),
    cardForeground: getCSSVariableValue('--card-foreground'),
    
    // Muted colors
    muted: getCSSVariableValue('--muted'),
    mutedForeground: getCSSVariableValue('--muted-foreground'),
    
    // Accent colors
    accent: getCSSVariableValue('--accent'),
    accentForeground: getCSSVariableValue('--accent-foreground'),
    
    // Popover colors
    popover: getCSSVariableValue('--popover'),
    popoverForeground: getCSSVariableValue('--popover-foreground'),
    
    // Border and input colors
    border: getCSSVariableValue('--border'),
    input: getCSSVariableValue('--input'),
    ring: getCSSVariableValue('--ring'),
    
    // Destructive colors
    destructive: getCSSVariableValue('--destructive'),
    destructiveForeground: getCSSVariableValue('--destructive-foreground'),
    
    // Status colors
    status: {
      success: getCSSVariableValue('--success'),
      successForeground: getCSSVariableValue('--success-foreground'),
      warning: getCSSVariableValue('--warning'),
      warningForeground: getCSSVariableValue('--warning-foreground'),
      info: getCSSVariableValue('--info'),
      infoForeground: getCSSVariableValue('--info-foreground'),
      error: getCSSVariableValue('--destructive'),
      errorForeground: getCSSVariableValue('--destructive-foreground'),
    },
    
    // Chart colors for data visualization
    chart: {
      1: getCSSVariableValue('--chart-1'),
      2: getCSSVariableValue('--chart-2'),
      3: getCSSVariableValue('--chart-3'),
      4: getCSSVariableValue('--chart-4'),
      5: getCSSVariableValue('--chart-5'),
      6: getCSSVariableValue('--chart-6'),
      7: getCSSVariableValue('--chart-7'),
      8: getCSSVariableValue('--chart-8'),
    },
    
    // Brand colors
    brand: {
      yellow: '#FFEB3B',
      orange: '#FF9800',
      black: '#212121',
      blue: '#1274F3',
      pink: '#EC3A76',
      mint: '#4BFFB2',
    },
    
    /**
     * Utility function for dynamic color access
     * @param variable CSS variable name (with or without --)
     * @returns CSS variable value
     */
    get: (variable: string) => {
      const varName = variable.startsWith('--') ? variable : `--${variable}`;
      return getCSSVariableValue(varName);
    },
    
    /**
     * Get HSL color value for use in Tailwind classes
     * @param variable CSS variable name
     * @returns HSL color string for Tailwind
     */
    getHSL: (variable: string) => {
      const varName = variable.startsWith('--') ? variable : `--${variable}`;
      const value = getCSSVariableValue(varName);
      return value ? `hsl(${value})` : '';
    },
    
    /**
     * Get RGB color value for use in calculations
     * @param variable CSS variable name
     * @returns RGB color object
     */
    getRGB: (variable: string) => {
      const varName = variable.startsWith('--') ? variable : `--${variable}`;
      const hslValue = getCSSVariableValue(varName);
      
      if (!hslValue) return { r: 0, g: 0, b: 0 };
      
      // Convert HSL to RGB (simplified for common HSL formats)
      try {
        const hslMatch = hslValue.match(/(\d+\.?\d*)\s+(\d+\.?\d*)%\s+(\d+\.?\d*)%/);
        if (!hslMatch || !hslMatch[1] || !hslMatch[2] || !hslMatch[3]) return { r: 0, g: 0, b: 0 };

        const h = parseFloat(hslMatch[1]) / 360;
        const s = parseFloat(hslMatch[2]) / 100;
        const l = parseFloat(hslMatch[3]) / 100;
        
        const hue2rgb = (p: number, q: number, t: number) => {
          if (t < 0) t += 1;
          if (t > 1) t -= 1;
          if (t < 1/6) return p + (q - p) * 6 * t;
          if (t < 1/2) return q;
          if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
          return p;
        };
        
        const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
        const p = 2 * l - q;
        
        const r = Math.round(hue2rgb(p, q, h + 1/3) * 255);
        const g = Math.round(hue2rgb(p, q, h) * 255);
        const b = Math.round(hue2rgb(p, q, h - 1/3) * 255);
        
        return { r, g, b };
      } catch (error) {
        console.warn(`Failed to convert HSL to RGB for ${varName}:`, error);
        return { r: 0, g: 0, b: 0 };
      }
    },
  }), [currentTheme]);
};

/**
 * Hook for getting theme-aware inline styles
 * Useful for components that need dynamic styling
 */
export const useThemeStyles = () => {
  const colors = useThemeColors();
  
  return useMemo(() => ({
    card: {
      backgroundColor: colors.getHSL('--card'),
      color: colors.getHSL('--card-foreground'),
      borderColor: colors.getHSL('--border'),
    },
    
    button: {
      primary: {
        backgroundColor: colors.getHSL('--primary'),
        color: colors.getHSL('--primary-foreground'),
      },
      secondary: {
        backgroundColor: colors.getHSL('--secondary'),
        color: colors.getHSL('--secondary-foreground'),
      },
    },
    
    status: {
      success: {
        backgroundColor: colors.status.success ? `hsl(${colors.status.success} / 0.1)` : '',
        color: colors.getHSL('--success'),
        borderColor: colors.status.success ? `hsl(${colors.status.success} / 0.2)` : '',
      },
      warning: {
        backgroundColor: colors.status.warning ? `hsl(${colors.status.warning} / 0.1)` : '',
        color: colors.getHSL('--warning'),
        borderColor: colors.status.warning ? `hsl(${colors.status.warning} / 0.2)` : '',
      },
      info: {
        backgroundColor: colors.status.info ? `hsl(${colors.status.info} / 0.1)` : '',
        color: colors.getHSL('--info'),
        borderColor: colors.status.info ? `hsl(${colors.status.info} / 0.2)` : '',
      },
      error: {
        backgroundColor: colors.destructive ? `hsl(${colors.destructive} / 0.1)` : '',
        color: colors.getHSL('--destructive'),
        borderColor: colors.destructive ? `hsl(${colors.destructive} / 0.2)` : '',
      },
    },
  }), [colors]);
};
