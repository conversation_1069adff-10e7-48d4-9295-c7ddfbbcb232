import { useState, useEffect } from 'react';

export interface LocationInventoryBreakdown {
  locationId: string;
  locationName: string;
  stockTypes: {
    stockType: string;
    quantity: number;
    safetyStockLevel?: number;
    maximumStockLevel?: number;
  }[];
}

export interface WarehouseInventoryBreakdown {
  warehouseId: string;
  warehouseName: string;
  totalStock: number;
  locations: LocationInventoryBreakdown[];
  // Legacy fields for backward compatibility
  warehouseLocationId?: string;
  warehouseLocation?: string;
  stockLevels?: {
    raw: number;
    hardening: number;
    grinding: number;
    finished: number;
    rejected: number;
  };
  totalQuantity?: number;
  lastUpdated?: string;
}

export interface PartInventoryDetails {
  partId: string;
  warehouses: WarehouseInventoryBreakdown[];
  totalStockAllWarehouses: number;
  stockLevelsSummary: {
    raw: number;
    hardening: number;
    grinding: number;
    finished: number;
    rejected: number;
  };
  // Legacy fields for backward compatibility
  warehouseBreakdown?: WarehouseInventoryBreakdown[];
  totals?: {
    raw: number;
    hardening: number;
    grinding: number;
    finished: number;
    rejected: number;
  };
  grandTotal?: number;
  warehouseCount?: number;
}

interface UsePartInventoryDetailsResult {
  inventoryDetails: PartInventoryDetails | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

/**
 * Custom hook to fetch detailed inventory breakdown for a part
 * Provides multi-warehouse inventory data for the part view modal
 */
export function usePartInventoryDetails(partId: string | null): UsePartInventoryDetailsResult {
  const [inventoryDetails, setInventoryDetails] = useState<PartInventoryDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchInventoryDetails = async () => {
    if (!partId) {
      setInventoryDetails(null);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Try the new v2 API first
      let response = await fetch(`/api/inventories/part/${partId}?format=v2`);
      let result = await response.json();

      // If v2 fails, fall back to legacy API
      if (!response.ok) {
        response = await fetch(`/api/inventories/part/${partId}`);
        result = await response.json();
      }

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch inventory details');
      }

      if (result.success) {
        let inventoryData = result.data;

        // Transform legacy format to new format for backward compatibility
        if (inventoryData.warehouseBreakdown && !inventoryData.warehouses) {
          inventoryData = {
            ...inventoryData,
            partId: partId,
            warehouses: inventoryData.warehouseBreakdown.map((warehouse: any) => ({
              warehouseId: warehouse.warehouseId,
              warehouseName: warehouse.warehouseName,
              totalStock: warehouse.totalQuantity || 0,
              locations: [], // Legacy format doesn't have location breakdown
              // Keep legacy fields for backward compatibility
              warehouseLocationId: warehouse.warehouseLocationId,
              warehouseLocation: warehouse.warehouseLocation,
              stockLevels: warehouse.stockLevels,
              totalQuantity: warehouse.totalQuantity,
              lastUpdated: warehouse.lastUpdated
            })),
            totalStockAllWarehouses: inventoryData.grandTotal || 0,
            stockLevelsSummary: inventoryData.totals || {
              raw: 0, hardening: 0, grinding: 0, finished: 0, rejected: 0
            }
          };
        }

        setInventoryDetails(inventoryData);
      } else {
        throw new Error(result.error || 'Failed to fetch inventory details');
      }
    } catch (err: any) {
      console.error('Error fetching part inventory details:', err);
      setError(err.message || 'Failed to fetch inventory details');
      setInventoryDetails(null);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchInventoryDetails();
  }, [partId]);

  return {
    inventoryDetails,
    isLoading,
    error,
    refetch: fetchInventoryDetails
  };
}
