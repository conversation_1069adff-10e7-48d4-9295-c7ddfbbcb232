"use client";

import { useState, useEffect, useCallback } from 'react';
import { ThemeVariant, ThemeMode } from '@/app/types/theme.types';

/**
 * Theme history entry
 */
interface ThemeHistoryEntry {
  variant: ThemeVariant;
  mode: ThemeMode;
  timestamp: number;
  name: string;
}

/**
 * Storage keys for theme history
 */
const STORAGE_KEYS = {
  RECENT_THEMES: 'theme-recent-history',
  FAVORITE_THEMES: 'theme-favorites'
} as const;

/**
 * Hook for managing theme history and favorites
 * Provides functionality to track recent themes and manage favorite themes
 */
export const useThemeHistory = () => {
  const [recentThemes, setRecentThemes] = useState<ThemeHistoryEntry[]>([]);
  const [favoriteThemes, setFavoriteThemes] = useState<ThemeHistoryEntry[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);

  /**
   * Load theme history from localStorage
   */
  const loadThemeHistory = useCallback(() => {
    if (typeof window === 'undefined') return;

    try {
      const recentData = localStorage.getItem(STORAGE_KEYS.RECENT_THEMES);
      const favoriteData = localStorage.getItem(STORAGE_KEYS.FAVORITE_THEMES);

      if (recentData) {
        const parsed = JSON.parse(recentData);
        setRecentThemes(Array.isArray(parsed) ? parsed : []);
      }

      if (favoriteData) {
        const parsed = JSON.parse(favoriteData);
        setFavoriteThemes(Array.isArray(parsed) ? parsed : []);
      }

      setIsLoaded(true);
    } catch (error) {
      console.warn('Failed to load theme history:', error);
      setIsLoaded(true);
    }
  }, []);

  /**
   * Save recent themes to localStorage
   */
  const saveRecentThemes = useCallback((themes: ThemeHistoryEntry[]) => {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem(STORAGE_KEYS.RECENT_THEMES, JSON.stringify(themes));
    } catch (error) {
      console.warn('Failed to save recent themes:', error);
    }
  }, []);

  /**
   * Save favorite themes to localStorage
   */
  const saveFavoriteThemes = useCallback((themes: ThemeHistoryEntry[]) => {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem(STORAGE_KEYS.FAVORITE_THEMES, JSON.stringify(themes));
    } catch (error) {
      console.warn('Failed to save favorite themes:', error);
    }
  }, []);

  /**
   * Add a theme to recent history
   */
  const addToRecentThemes = useCallback((
    variant: ThemeVariant, 
    mode: ThemeMode, 
    name: string
  ) => {
    const newEntry: ThemeHistoryEntry = {
      variant,
      mode,
      timestamp: Date.now(),
      name
    };

    setRecentThemes(prev => {
      // Remove existing entry if it exists
      const filtered = prev.filter(
        theme => !(theme.variant === variant && theme.mode === mode)
      );
      
      // Add new entry at the beginning
      const updated = [newEntry, ...filtered];
      
      // Keep only the last 10 recent themes
      const trimmed = updated.slice(0, 10);
      
      saveRecentThemes(trimmed);
      return trimmed;
    });
  }, [saveRecentThemes]);

  /**
   * Add a theme to favorites
   */
  const addToFavorites = useCallback((
    variant: ThemeVariant, 
    mode: ThemeMode, 
    name: string
  ) => {
    const newEntry: ThemeHistoryEntry = {
      variant,
      mode,
      timestamp: Date.now(),
      name
    };

    setFavoriteThemes(prev => {
      // Check if already in favorites
      const exists = prev.some(
        theme => theme.variant === variant && theme.mode === mode
      );

      if (exists) return prev;

      const updated = [...prev, newEntry];
      saveFavoriteThemes(updated);
      return updated;
    });
  }, [saveFavoriteThemes]);

  /**
   * Remove a theme from favorites
   */
  const removeFromFavorites = useCallback((variant: ThemeVariant, mode: ThemeMode) => {
    setFavoriteThemes(prev => {
      const updated = prev.filter(
        theme => !(theme.variant === variant && theme.mode === mode)
      );
      saveFavoriteThemes(updated);
      return updated;
    });
  }, [saveFavoriteThemes]);

  /**
   * Check if a theme is in favorites
   */
  const isFavorite = useCallback((variant: ThemeVariant, mode: ThemeMode) => {
    return favoriteThemes.some(
      theme => theme.variant === variant && theme.mode === mode
    );
  }, [favoriteThemes]);

  /**
   * Toggle favorite status of a theme
   */
  const toggleFavorite = useCallback((
    variant: ThemeVariant, 
    mode: ThemeMode, 
    name: string
  ) => {
    if (isFavorite(variant, mode)) {
      removeFromFavorites(variant, mode);
    } else {
      addToFavorites(variant, mode, name);
    }
  }, [isFavorite, removeFromFavorites, addToFavorites]);

  /**
   * Get recent themes (excluding current theme)
   */
  const getRecentThemes = useCallback((
    currentVariant?: ThemeVariant,
    currentMode?: ThemeMode,
    limit = 5
  ) => {
    // Always return an array, even if not loaded yet
    if (!Array.isArray(recentThemes)) return [];

    return recentThemes
      .filter(theme =>
        !(theme.variant === currentVariant && theme.mode === currentMode)
      )
      .slice(0, limit);
  }, [recentThemes]);

  /**
   * Get favorite themes (excluding current theme)
   */
  const getFavoriteThemes = useCallback((
    currentVariant?: ThemeVariant,
    currentMode?: ThemeMode
  ) => {
    // Always return an array, even if not loaded yet
    if (!Array.isArray(favoriteThemes)) return [];

    return favoriteThemes.filter(theme =>
      !(theme.variant === currentVariant && theme.mode === currentMode)
    );
  }, [favoriteThemes]);

  /**
   * Clear all recent themes
   */
  const clearRecentThemes = useCallback(() => {
    setRecentThemes([]);
    saveRecentThemes([]);
  }, [saveRecentThemes]);

  /**
   * Clear all favorite themes
   */
  const clearFavoriteThemes = useCallback(() => {
    setFavoriteThemes([]);
    saveFavoriteThemes([]);
  }, [saveFavoriteThemes]);

  // Load theme history on mount
  useEffect(() => {
    loadThemeHistory();
  }, [loadThemeHistory]);

  return {
    // State
    recentThemes,
    favoriteThemes,
    isLoaded,

    // Actions
    addToRecentThemes,
    addToFavorites,
    removeFromFavorites,
    toggleFavorite,

    // Getters
    getRecentThemes,
    getFavoriteThemes,
    isFavorite,

    // Utilities
    clearRecentThemes,
    clearFavoriteThemes
  };
};
