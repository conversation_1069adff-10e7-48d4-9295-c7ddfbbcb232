"use client";

import React, { useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

const CalendarComponent: React.FC = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [showYear, setShowYear] = useState(false);
  const [selectedDateFormat, setSelectedDateFormat] = useState<
    'MM/DD/YYYY' | 'DD/MM/YYYY'
  >('MM/DD/YYYY');

  // --- Navigation: Month/Year ---
  const goToPreviousMonth = () => {
    setCurrentDate((prevDate) => {
      const newDate = new Date(prevDate);
      newDate.setMonth(prevDate.getMonth() - 1);
      return newDate;
    });
  };

  const goToNextMonth = () => {
    setCurrentDate((prevDate) => {
      const newDate = new Date(prevDate);
      newDate.setMonth(prevDate.getMonth() + 1);
      return newDate;
    });
  };

  const goToPreviousYear = () => {
    setCurrentDate((prevDate) => {
      const newDate = new Date(prevDate);
      newDate.setFullYear(prevDate.getFullYear() - 1);
      return newDate;
    });
  };

  const goToNextYear = () => {
    setCurrentDate((prevDate) => {
      const newDate = new Date(prevDate);
      newDate.setFullYear(prevDate.getFullYear() + 1);
      return newDate;
    });
  };

  // --- Toggle between Month and Year views ---
  const toggleYearView = () => {
    setShowYear((prev) => !prev);
  };

  // --- Date Format ---
  const changeDateFormat = (format: 'MM/DD/YYYY' | 'DD/MM/YYYY') => {
    setSelectedDateFormat(format);
  };

  // Formats a given date according to the selected format
  const formatDate = (date: Date) => {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return selectedDateFormat === 'MM/DD/YYYY'
      ? `${month}/${day}/${year}`
      : `${day}/${month}/${year}`;
  };

  // --- Render the days in the current month ---
  const renderCalendar = () => {
    const firstDayOfMonth = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      1
    );
    const lastDayOfMonth = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth() + 1,
      0
    );
    const daysInMonth = lastDayOfMonth.getDate();
    const startingDayOfWeek = firstDayOfMonth.getDay();

    const calendarDays: React.ReactNode[] = [];

    // Empty slots for days of the week before the 1st
    for (let i = 0; i < startingDayOfWeek; i++) {
      calendarDays.push(
        <div
          key={`empty-${i}`}
          className="w-8 h-8 flex items-center justify-center"
        />
      );
    }

    // Actual days of the month
    for (let i = 1; i <= daysInMonth; i++) {
      const isToday =
        i === new Date().getDate() &&
        currentDate.getMonth() === new Date().getMonth() &&
        currentDate.getFullYear() === new Date().getFullYear();

      calendarDays.push(
        <div
          key={i}
          className={`w-8 h-8 flex items-center justify-center rounded-full transition-colors duration-200 ${
            isToday ? 'bg-blue-500 text-white' : 'hover:bg-gray-200'
          }`}
        >
          {i}
        </div>
      );
    }

    return calendarDays;
  };

  // --- Current Month/Year labels ---
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.toLocaleString('default', { month: 'long' });

  return (
    <div className="bg-white/70 backdrop-blur-md rounded-lg shadow-lg p-4 w-72 relative z-10">
      {/* Header with navigation */}
      <div className="flex justify-between items-center mb-2">
        <button
          onClick={showYear ? goToPreviousYear : goToPreviousMonth}
          className="hover:bg-gray-200 rounded-full p-2 transition-colors duration-200"
        >
          <ChevronLeft size={20} />
        </button>
        <div
          className="text-base font-semibold cursor-pointer select-none"
          onClick={toggleYearView}
        >
          {showYear ? currentYear : `${currentMonth} ${currentYear}`}
        </div>
        <button
          onClick={showYear ? goToNextYear : goToNextMonth}
          className="hover:bg-gray-200 rounded-full p-2 transition-colors duration-200"
        >
          <ChevronRight size={20} />
        </button>
      </div>

      {/* Year view: list of months */}
      {showYear ? (
        <div className="grid grid-cols-3 gap-1 mb-4">
          {[...Array(12)].map((_, index) => {
            const monthName = new Date(currentYear, index).toLocaleString(
              'default',
              { month: 'short' }
            );
            return (
              <button
                key={index}
                className="text-sm py-1 rounded hover:bg-gray-200"
                onClick={() => {
                  setCurrentDate(new Date(currentYear, index, 1));
                  setShowYear(false);
                }}
              >
                {monthName}
              </button>
            );
          })}
        </div>
      ) : (
        <>
          {/* Days of the week headers */}
          <div className="grid grid-cols-7 gap-1 mb-4">
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(
              (day, index) => (
                <div
                  key={index}
                  className="text-xs font-medium text-center text-gray-600"
                >
                  {day}
                </div>
              )
            )}
          </div>
          {/* Days in the current month */}
          <div className="grid grid-cols-7 gap-1">{renderCalendar()}</div>
        </>
      )}

      {/* Settings */}
      <div className="mt-4">
        <h3 className="text-sm font-semibold mb-2">Settings</h3>
        <div className="bg-gray-100 rounded-lg p-2">
          <label
            htmlFor="dateFormat"
            className="block text-xs font-medium text-gray-700"
          >
            Date Format
          </label>
          <select
            id="dateFormat"
            className="mt-1 block w-full pl-2 pr-8 py-1 text-xs border-gray-300
                       focus:outline-none focus:ring-indigo-500 focus:border-indigo-500
                       sm:text-xs rounded-md"
            value={selectedDateFormat}
            onChange={(e) =>
              changeDateFormat(e.target.value as 'MM/DD/YYYY' | 'DD/MM/YYYY')
            }
          >
            <option value="MM/DD/YYYY">MM/DD/YYYY</option>
            <option value="DD/MM/YYYY">DD/MM/YYYY</option>
          </select>
        </div>
      </div>

      {/* Example usage of formatDate */}
      <div className="mt-2 text-sm text-gray-600">
        Today is: <span className="font-medium">{formatDate(new Date())}</span>
      </div>
    </div>
  );
};

export default CalendarComponent; 