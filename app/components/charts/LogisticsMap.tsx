"use client";

import { LogisticsInfo } from '@/app/types';
import { motion } from 'framer-motion';
import { ArrowRight } from 'lucide-react';
import React from 'react';
import { useTheme } from '../../context/ThemeContext';

// Using LogisticsInfo directly as it already contains all needed properties

interface LogisticsMapProps {
  logisticsInfo: LogisticsInfo;
}

const LogisticsMap: React.FC<LogisticsMapProps> = ({ logisticsInfo }) => {
  const { theme } = useTheme();

  const status = logisticsInfo.status || 'Logistics status unavailable';
  const delayDuration = logisticsInfo.delayDuration || 'N/A';
  const delayedItems = logisticsInfo.delayedItems || [];

  return (
    <motion.div
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="bg-gray-100 dark:bg-card rounded-3xl p-6 relative overflow-hidden shadow-md dark:shadow-gray-900/30"
    >
      <div className="flex justify-between items-start">
        <h3 className="text-xl font-medium text-gray-800 dark:text-text-primary">Logistics</h3>
        <motion.div
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          className="w-8 h-8 bg-gray-200 dark:bg-sidebar rounded-full flex items-center justify-center cursor-pointer"
        >
          <ArrowRight size={16} className="text-gray-600 dark:text-text-secondary" />
        </motion.div>
      </div>

      <div className="mt-4">
        <p className="text-lg text-gray-700 dark:text-text-secondary">{status}</p>
      </div>

      <div className="mt-6 relative h-40">
        {/* Simplified US Map */}
        <div className="absolute inset-0 flex items-center justify-center">
          <svg width="100%" height="100%" viewBox="0 0 800 400" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M100,120 C150,100 200,90 250,100 C300,110 350,130 400,120 C450,110 500,90 550,100 C600,110 650,130 700,120"
                  stroke={theme.isDark ? '#444' : '#ddd'} strokeWidth="2" fill="#FFEB3B" fillOpacity={theme.isDark ? "0.2" : "0.3"}/>
          </svg>

          {/* Origin Point */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="absolute left-1/4 top-1/2 w-4 h-4 bg-black dark:bg-gray-300 rounded-full"
          />

          {/* Destination Point */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.7 }}
            className="absolute right-1/4 top-1/2 w-4 h-4 bg-orange-400 dark:bg-orange-500 rounded-full"
          />

          {/* Delay Indicator */}
          {delayDuration !== 'N/A' && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.9 }}
              className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2"
            >
              <div className="bg-orange-400 dark:bg-orange-500 text-white text-xs px-3 py-1 rounded-full flex items-center">
                <span>Delay</span>
                <span className="ml-1 text-xs">+{delayDuration}</span>
              </div>
            </motion.div>
          )}

          {/* Dotted Path */}
          <svg className="absolute inset-0" width="100%" height="100%" viewBox="0 0 800 400" fill="none" xmlns="http://www.w3.org/2000/svg">
            <motion.path
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 1.5, delay: 0.5 }}
              d="M200,200 L400,200 L600,200"
              stroke={theme.isDark ? '#aaa' : '#000'}
              strokeWidth="2"
              strokeDasharray="5 5"
              strokeLinecap="round"
            />
          </svg>
        </div>
      </div>

      <div className="mt-4 bg-white dark:bg-sidebar rounded-xl p-3 flex items-center">
        {delayedItems.slice(0, 3).map((item: any, index: number) => (
          <motion.div
            key={item.id}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3, delay: 0.5 + (index * 0.1) }}
            className="w-12 h-12 bg-gray-200 dark:bg-hover rounded-lg mr-2 overflow-hidden"
          >
            {item.image ? (
              <img src={item.image} alt={item.name} className="w-full h-full object-cover" />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-500 dark:text-text-secondary">
                {item.name.substring(0, 1)}
              </div>
            )}
          </motion.div>
        ))}

        {delayedItems.length > 3 && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3, delay: 0.8 }}
            className="bg-gray-100 dark:bg-hover rounded-lg px-2 py-1 text-sm text-gray-600 dark:text-text-secondary"
          >
            +{delayedItems.length - 3}
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export default LogisticsMap;