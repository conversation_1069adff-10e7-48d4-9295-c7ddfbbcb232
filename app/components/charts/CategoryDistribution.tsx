"use client";

import { motion } from 'framer-motion';
import React, { useMemo } from 'react';
import {
    Bar,
    <PERSON><PERSON>hart,
    CartesianGrid,
    Cell,
    ResponsiveContainer,
    Tooltip,
    XAxis,
    YAxis
} from 'recharts';
import { useTheme } from '../../context/ThemeContext';

// Mock data if none is provided via props
const categoryData = {
  "Frames & Structural": 7,
  "Shafts & Related Components": 6,
  "Bearings": 5,
  "Seals & Gaskets": 7,
  "Fasteners": 15,
  "Cylinders & Actuators": 7,
  "Bushings & Spacers": 16,
  "Tamping Mechanism": 4,
  "Guide Components": 2,
  "Covers & Flanges": 5,
  "Flywheels": 2,
  "Hydraulic System": 18,
  "Lubrication System": 8
};

interface CategoryDistributionProps {
  data?: Record<string, number>;
  othersThreshold?: number; // percentage threshold below which to group as "Others"
}

// Define interfaces for chart data
interface CategoryItem {
  name: string;
  value: number;
  percentage: number;
}

interface ChartDataItem extends CategoryItem {
  others?: CategoryItem[];
}

const COLORS = [
  'var(--T-accent-active, #E0E0E0)',
  'var(--T-text-secondary, #A0A0A0)',
  'var(--T-border-color, #444444)',
  'var(--T-border-subtle, #383838)',
  '#607D8B',
  '#78909C',
  '#90A4AE',
  '#B0BEC5',
  '#CFD8DC',
  '#ECEFF1'
];

// Custom tooltip component
const CustomTooltip = ({ active, payload, label, theme }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className={`${theme === 'dark' ? 'bg-card border-border' : 'bg-white border-gray-200'} p-3 border rounded-lg shadow-lg`}>
        <p className="text-gray-800 dark:text-text-primary font-medium">{payload[0].payload.name}</p>
        <p className="text-sm text-gray-600 dark:text-text-secondary">
          {payload[0].value} items ({payload[0].payload.percentage}%)
        </p>
        {payload[0].payload.others && (
          <div className="mt-2 text-xs">
            <p className="font-medium text-gray-700 dark:text-text-secondary">Includes:</p>
            <ul className="list-disc pl-4">
              {payload[0].payload.others.map((item: CategoryItem, index: number) => (
                <li key={index} className="text-gray-600 dark:text-text-secondary">
                  {item.name}: {item.value} ({item.percentage}%)
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    );
  }
  return null;
};

const CategoryDistribution: React.FC<CategoryDistributionProps> = ({
  data = categoryData,
  othersThreshold = 5 // default threshold of 5%
}) => {
  const { theme } = useTheme();

  const chartData = useMemo<ChartDataItem[]>(() => {
    // Ensure data is not null/undefined before processing
    const safeData = data || {};
    const total = Object.values(safeData).reduce((sum, value) => sum + value, 0);

    // Prevent division by zero if total is 0
    if (total === 0) return [];

    // Calculate percentage for each category
    const categoriesWithPercentage: CategoryItem[] = Object.entries(safeData).map(([name, value]) => ({
      name,
      value,
      percentage: Math.round((value / total) * 100)
    }));

    // Sort by value in descending order
    const sortedCategories = [...categoriesWithPercentage].sort((a, b) => b.value - a.value);

    // Filter out small categories for "Others" group
    const mainCategories = sortedCategories.filter(cat => cat.percentage >= othersThreshold);
    const smallCategories = sortedCategories.filter(cat => cat.percentage < othersThreshold);

    // Create the "Others" category if there are small categories
    const result: ChartDataItem[] = [...mainCategories];

    if (smallCategories.length > 0) {
      const othersValue = smallCategories.reduce((sum, cat) => sum + cat.value, 0);
      // Recalculate othersPercentage based on actual sum to avoid rounding issues
      const othersPercentage = Math.round((othersValue / total) * 100);

      result.push({
        name: "Others",
        value: othersValue,
        percentage: othersPercentage,
        others: smallCategories
      });
    }

    return result;
  }, [data, othersThreshold]);

  return (
    <motion.div
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className="bg-white dark:bg-card rounded-3xl p-6 h-full shadow-md dark:shadow-gray-900/30"
    >
      <h3 className="text-xl font-medium text-gray-800 dark:text-text-primary mb-4">Part Category Distribution</h3>

      {chartData.length > 0 ? (
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              layout="vertical"
              data={chartData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke={theme.isDark ? 'var(--T-border-color, #444444)' : '#ddd'} />
              <XAxis
                type="number"
                domain={[0, 'dataMax']}
                tick={{ fill: theme.isDark ? 'var(--T-text-secondary, #A0A0A0)' : '#333' }}
              />
              <YAxis
                type="category"
                dataKey="name"
                width={150}
                tick={{ fill: theme.isDark ? 'var(--T-text-secondary, #A0A0A0)' : '#333' }}
                style={{
                  fontSize: '12px',
                  fontFamily: 'sans-serif'
                }}
              />
              <Tooltip
                content={<CustomTooltip theme={theme} />}
              />
              <Bar
                dataKey="value"
                name="Items"
                radius={[0, 4, 4, 0]}
              >
                {chartData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={entry.name === "Others" ? "var(--T-text-secondary, #A0A0A0)" : COLORS[index % COLORS.length]}
                  />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>
      ) : (
        <div className="h-80 flex items-center justify-center">
          <p className="text-gray-500 dark:text-text-secondary">No category data available.</p>
        </div>
      )}

      <div className="mt-4 text-xs text-gray-500 dark:text-text-secondary italic">
        Categories below {othersThreshold}% are grouped as "Others". Hover for details.
      </div>
    </motion.div>
  );
};

export default CategoryDistribution;