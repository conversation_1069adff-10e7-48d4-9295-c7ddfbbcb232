"use client";

import { useEnhancedTheme } from '@/app/hooks/useEnhancedTheme';
import { cn } from '@/app/lib/utils';
import { motion } from 'framer-motion';
import { Moon, Sun, Monitor } from 'lucide-react';
import React from 'react';

export interface AccessibleThemeToggleProps {
  /**
   * Whether to show minimal styling
   */
  isMinimal?: boolean;
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Whether to show system mode option
   */
  showSystemMode?: boolean;
  
  /**
   * Size variant
   */
  size?: 'sm' | 'md' | 'lg';
  
  /**
   * Whether to show labels
   */
  showLabels?: boolean;
  
  /**
   * Custom aria label
   */
  ariaLabel?: string;
}

/**
 * Accessible theme toggle component with proper focus management
 * Replaces hardcoded color implementations with semantic CSS variables
 */
export const AccessibleThemeToggle: React.FC<AccessibleThemeToggleProps> = ({
  isMinimal = false,
  className,
  showSystemMode = false,
  size = 'md',
  showLabels = false,
  ariaLabel,
}) => {
  const { currentTheme, toggleTheme, setMode, themeClasses } = useEnhancedTheme();
  
  // Size configurations
  const sizeConfig = {
    sm: {
      button: 'p-1.5',
      icon: 'h-3.5 w-3.5',
      text: 'text-xs',
    },
    md: {
      button: 'p-2',
      icon: 'h-4 w-4',
      text: 'text-sm',
    },
    lg: {
      button: 'p-3',
      icon: 'h-5 w-5',
      text: 'text-base',
    },
  };
  
  const config = sizeConfig[size];
  
  // Get current mode icon and label
  const getModeInfo = (mode: string) => {
    switch (mode) {
      case 'light':
        return { icon: Sun, label: 'Light mode' };
      case 'dark':
        return { icon: Moon, label: 'Dark mode' };
      case 'system':
        return { icon: Monitor, label: 'System mode' };
      default:
        return { icon: Sun, label: 'Light mode' };
    }
  };
  
  const currentModeInfo = getModeInfo(currentTheme.mode);
  const CurrentIcon = currentModeInfo.icon;
  
  // Handle toggle action
  const handleToggle = () => {
    if (showSystemMode) {
      // Cycle through light -> dark -> system
      switch (currentTheme.mode) {
        case 'light':
          setMode('dark');
          break;
        case 'dark':
          setMode('system');
          break;
        case 'system':
          setMode('light');
          break;
        default:
          setMode('dark');
      }
    } else {
      // Simple light/dark toggle
      toggleTheme();
    }
  };
  
  // Get next mode for aria-label
  const getNextMode = () => {
    if (showSystemMode) {
      switch (currentTheme.mode) {
        case 'light':
          return 'dark';
        case 'dark':
          return 'system';
        case 'system':
          return 'light';
        default:
          return 'dark';
      }
    } else {
      return currentTheme.mode === 'light' ? 'dark' : 'light';
    }
  };
  
  const nextMode = getNextMode();
  const defaultAriaLabel = ariaLabel || `Switch to ${nextMode} mode. Current mode: ${currentModeInfo.label}`;
  
  // Button classes using semantic CSS variables
  const buttonClasses = cn(
    // Base classes with semantic variables
    'inline-flex items-center justify-center rounded-full transition-all duration-300',
    'text-foreground',
    
    // Interactive states using theme classes
    themeClasses.interactive,
    
    // Size-specific classes
    config.button,
    
    // Minimal vs full styling
    isMinimal
      ? 'text-muted-foreground hover:text-foreground'
      : cn(
          'backdrop-blur-md shadow-lg',
          'bg-card/20 hover:bg-card/40',
          'border border-border/50'
        ),
    
    // Custom classes
    className
  );
  
  return (
    <div className="flex items-center gap-2">
      <motion.button
        onClick={handleToggle}
        className={buttonClasses}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        aria-label={defaultAriaLabel}
        aria-pressed={currentTheme.mode === 'dark'}
        role="button"
        type="button"
        data-testid="accessible-theme-toggle"
      >
        <motion.div
          key={currentTheme.mode}
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          exit={{ scale: 0, rotate: 180 }}
          transition={{ 
            duration: 0.3, 
            type: 'spring',
            stiffness: 200,
            damping: 20
          }}
          className={cn('flex items-center justify-center', config.icon)}
        >
          <CurrentIcon className={config.icon} />
        </motion.div>
      </motion.button>
      
      {showLabels && (
        <motion.span
          key={currentTheme.mode}
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 10 }}
          transition={{ duration: 0.2 }}
          className={cn(
            'text-muted-foreground font-medium',
            config.text
          )}
        >
          {currentModeInfo.label}
        </motion.span>
      )}
    </div>
  );
};

/**
 * Simple theme toggle button with just light/dark modes
 */
export const SimpleThemeToggle: React.FC<Omit<AccessibleThemeToggleProps, 'showSystemMode'>> = (props) => {
  return <AccessibleThemeToggle {...props} showSystemMode={false} />;
};

/**
 * Advanced theme toggle with system mode support
 */
export const AdvancedThemeToggle: React.FC<AccessibleThemeToggleProps> = (props) => {
  return <AccessibleThemeToggle {...props} showSystemMode={true} />;
};

/**
 * Minimal theme toggle for compact spaces
 */
export const MinimalThemeToggle: React.FC<Omit<AccessibleThemeToggleProps, 'isMinimal'>> = (props) => {
  return <AccessibleThemeToggle {...props} isMinimal={true} size="sm" />;
};

/**
 * Theme toggle with labels for better accessibility
 */
export const LabeledThemeToggle: React.FC<AccessibleThemeToggleProps> = (props) => {
  return <AccessibleThemeToggle {...props} showLabels={true} />;
};
