'use client';

import React, { CSSProperties } from 'react';
import { cn } from '@/app/lib/utils';

export interface BorderBeamProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  className?: string;
  containerClassName?: string;
  beamColor?: string;
  beamDuration?: string;
  beamOpacity?: string;
  beamSpread?: string;
  background?: string;
}

/**
 * BorderBeam Component
 * Creates a container with an animated beam effect around its border
 */
export const BorderBeam = ({
  children,
  className,
  containerClassName,
  beamColor = '#ffffff',
  beamDuration = '3s',
  beamOpacity = '0.6',
  beamSpread = '180deg',
  background = 'rgba(0, 0, 0, 0.8)',
  ...props
}: BorderBeamProps) => {
  return (
    <div
      className={cn(
        'relative h-full w-full overflow-hidden rounded-xl',
        containerClassName
      )}
      style={{
        '--beam-color': beamColor,
        '--beam-duration': beamDuration,
        '--beam-opacity': beamOpacity,
        '--beam-spread': beamSpread,
        '--bg': background,
      } as CSSProperties}
    >
      {/* Content */}
      <div
        className={cn(
          'relative z-10 h-full w-full overflow-hidden rounded-xl bg-background shadow-xl',
          className
        )}
        {...props}
      >
        {children}
      </div>
      
      {/* Animated border beam effect */}
      <div className="absolute inset-0 z-0 overflow-hidden rounded-xl">
        <div className="absolute inset-0 overflow-hidden rounded-xl bg-[var(--bg)]" />
        <div className="absolute -inset-[2px] animate-border-beam">
          <div 
            className="absolute inset-0 animate-spin-slow" 
            style={{
              background: `conic-gradient(from 0deg, transparent 0, var(--beam-color) var(--beam-spread), transparent var(--beam-spread))`,
              opacity: 'var(--beam-opacity)'
            }}
          />
        </div>
      </div>
    </div>
  );
};

// Note: Add these animations to your tailwind.config.js file:
/*
extend: {
  animation: {
    "border-beam": "border-beam 3s ease infinite",
    "spin-slow": "spin-slow 4s linear infinite",
  },
  keyframes: {
    "border-beam": {
      "0%, 100%": { opacity: "0.8" },
      "50%": { opacity: "0.4" },
    },
    "spin-slow": {
      to: {
        transform: "rotate(1turn)",
      },
    },
  },
}
*/