"use client";

import { Button } from '@/app/components/forms/Button/Button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/app/components/navigation/DropdownMenu';
import { useTheme } from '@/app/context/ThemeContext';
import { useThemeHistory } from '@/app/hooks/useThemeHistory';
import { ThemeMode, ThemeVariant, ThemeVariantDefinition } from '@/app/types/theme.types';
import { AnimatePresence, motion } from 'framer-motion';
import {
    Check,
    ChevronDown,
    Clock,
    Heart,
    Moon,
    Settings,
    Sparkles,
    Star,
    Sun,
    Zap
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useState, useCallback, useEffect } from 'react';
import { toast } from 'sonner';

// Local type definition for theme history entries
interface ThemeHistoryEntry {
  variant: ThemeVariant;
  mode: ThemeMode;
  timestamp: number;
  name: string;
}

type FavoriteTheme = ThemeHistoryEntry;
type RecentTheme = ThemeHistoryEntry;

/**
 * Props for the Enhanced Theme Toggle component
 */
interface EnhancedThemeToggleProps {
  /** Whether to show minimal version (icon only) */
  isMinimal?: boolean;
  /** Whether to show the dropdown arrow */
  showDropdown?: boolean;
  /** Custom className for styling */
  className?: string;
}

/**
 * Enhanced Theme Toggle Component
 * Provides quick light/dark mode switching with dropdown access to theme variants
 * Maintains backward compatibility while adding new multi-variant functionality
 */
// New Collapsible Section Component
const CollapsibleThemeSection: React.FC<{
  title: string;
  icon: React.ReactNode;
  isOpen: boolean;
  onToggle: () => void;
  children: React.ReactNode;
}> = ({ title, icon, isOpen, onToggle, children }) => (
  <div>
    <DropdownMenuItem
      onSelect={(e) => e.preventDefault()}
      onClick={onToggle}
      className="flex items-center justify-between cursor-pointer"
    >
      <div className="flex items-center space-x-2">
        {icon}
        <span>{title}</span>
      </div>
      <ChevronDown
        size={16}
        className={`transform transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
      />
    </DropdownMenuItem>
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ height: 0, opacity: 0 }}
          animate={{ height: 'auto', opacity: 1 }}
          exit={{ height: 0, opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="overflow-hidden pl-4"
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  </div>
);

// Define popular themes directly in the component
const POPULAR_THEMES: ThemeVariant[] = ['default', 'github', 'vercel', 'linear'];

export const EnhancedThemeToggle: React.FC<EnhancedThemeToggleProps> = ({
  isMinimal = false,
  showDropdown = true,
  className = ''
}) => {
  const {
    currentTheme,
    setTheme,
    toggleTheme: toggleThemeMode, // Alias for clarity
    availableThemes,
  } = useTheme();
  const {
    favoriteThemes,
    recentThemes,
    isFavorite,
    toggleFavorite,
    addToRecentThemes,
  } = useThemeHistory();
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [openSections, setOpenSections] = useState<Record<string, boolean>>({
    popular: true,
  });

  const toggleSection = useCallback((section: string) => {
    setOpenSections(prev => ({ ...prev, [section]: !prev[section] }));
  }, []);

  // Add keyboard shortcut listener
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.metaKey || event.ctrlKey) && event.key === 't') {
        event.preventDefault();
        toggleThemeMode();
        toast.success(`Theme switched to ${currentTheme.mode === 'light' ? 'dark' : 'light'} mode`);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [toggleThemeMode, currentTheme.mode]);

  /**
   * Handle quick mode toggle (light/dark)
   */
  const handleQuickToggle = useCallback((e?: React.MouseEvent) => {
    e?.stopPropagation();
    toggleThemeMode();
    toast.success(`Theme switched to ${currentTheme.mode === 'light' ? 'dark' : 'light'} mode`);
  }, [toggleThemeMode, currentTheme.mode]);

  /**
   * Handle theme variant selection
   */
  const handleThemeSelect = useCallback(
    (variant: ThemeVariant, mode: ThemeMode, name: string) => {
      setTheme(variant, mode);
      addToRecentThemes(variant, mode, name);
      toast.success(`Theme set to ${name}`);
    },
    [setTheme, addToRecentThemes]
  );

  /**
   * Navigate to settings page
   */
  const handleOpenSettings = () => {
    router.push('/settings?tab=appearance');
    setIsOpen(false);
  };

  const popularThemes = availableThemes.filter(theme => 
    POPULAR_THEMES.includes(theme.variant)
  );

  if (!showDropdown) {
    // Simple toggle mode (backward compatibility)
    return (
      <motion.button
        onClick={handleQuickToggle}
        className={`${isMinimal
          ? 'p-1 text-muted-foreground hover:text-foreground'
          : 'p-2 rounded-full backdrop-blur-md bg-card/20 text-foreground shadow-lg'} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring transition-all duration-300 ${className}`}
        whileHover={{ scale: 1.1, rotate: currentTheme.mode === 'light' ? -15 : 15 }}
        whileTap={{ scale: 0.9 }}
        aria-label={`Switch to ${currentTheme.mode === 'light' ? 'dark' : 'light'} mode`}
        data-testid="theme-toggle"
        role="button"
        type="button"
      >
        {currentTheme.mode === 'light' ? (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ duration: 0.4, type: 'spring' }}
          >
            <Moon size={isMinimal ? 16 : 20} className="text-muted-foreground" />
          </motion.div>
        ) : (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ duration: 0.4, type: 'spring' }}
          >
            <Sun size={isMinimal ? 16 : 20} className="text-yellow-500" />
          </motion.div>
        )}
      </motion.button>
    );
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={`flex items-center space-x-2 rounded-full hover:bg-accent/50 transition-all duration-300 ${className}`}
          aria-label={`Current theme: ${currentTheme.config.name} (${currentTheme.mode} mode). Click to change theme`}
          data-testid="theme-toggle"
          role="combobox"
          aria-expanded={isOpen}
          aria-haspopup="menu"
        >
          <motion.div
            whileHover={{ scale: 1.1, rotate: currentTheme.mode === 'light' ? -15 : 15 }}
            whileTap={{ scale: 0.9 }}
            animate={{
              rotate: currentTheme.mode === 'light' ? 0 : 180,
              scale: 1
            }}
            transition={{ duration: 0.5, type: 'spring', stiffness: 200 }}
            data-framer-motion="theme-icon-container"
          >
            <AnimatePresence mode="wait">
              {currentTheme.mode === 'light' ? (
                <motion.div
                  key="moon"
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  exit={{ scale: 0, rotate: 180 }}
                  transition={{ duration: 0.3 }}
                  data-framer-motion="moon-icon"
                >
                  <Moon size={16} className="text-muted-foreground" />
                </motion.div>
              ) : (
                <motion.div
                  key="sun"
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  exit={{ scale: 0, rotate: 180 }}
                  transition={{ duration: 0.3 }}
                  data-framer-motion="sun-icon"
                >
                  <Sun size={16} className="text-yellow-500" />
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
          {!isMinimal && (
            <>
              <motion.span
                className="text-sm font-medium text-foreground capitalize"
                key={currentTheme.variant}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
                data-framer-motion="theme-name"
              >
                {currentTheme.variant}
              </motion.span>
              <motion.div
                animate={{ rotate: isOpen ? 180 : 0 }}
                transition={{ duration: 0.2 }}
                data-framer-motion="chevron-icon"
              >
                <ChevronDown size={12} className="text-muted-foreground" />
              </motion.div>
            </>
          )}
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent className="w-64" align="end" sideOffset={5}>
        {/* Current Theme Info */}
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Current Theme</span>
          <div className="flex items-center space-x-1">
            <div
              className="w-3 h-3 rounded-full border border-border"
              style={{ backgroundColor: currentTheme.config.preview.primary }}
            />
            <span className="text-xs text-muted-foreground capitalize">
              {currentTheme.config.name}
            </span>
          </div>
        </DropdownMenuLabel>

        <DropdownMenuSeparator />

        {/* Quick Mode Toggle */}
        <DropdownMenuItem onSelect={(e) => e.preventDefault()} onClick={handleQuickToggle} className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {currentTheme.mode === 'light' ? (
              <Moon size={16} className="text-muted-foreground" />
            ) : (
              <Sun size={16} className="text-yellow-500" />
            )}
            <span>Switch to {currentTheme.mode === 'light' ? 'Dark' : 'Light'} Mode</span>
          </div>
          <div className="text-xs text-muted-foreground">⌘T</div>
        </DropdownMenuItem>

        {/* Collapsible Sections */}
        <CollapsibleThemeSection
          title="Popular Themes"
          icon={<Zap size={16} className="text-yellow-500" />}
          isOpen={!!openSections.popular}
          onToggle={() => toggleSection('popular')}
        >
          {popularThemes.map((themeVariant) => {
            const themeConfig = themeVariant.colors[currentTheme.resolvedMode];
            if (!themeConfig) return null;

            const isSelected = currentTheme.variant === themeVariant.variant;
            const isFav = isFavorite(themeVariant.variant, currentTheme.mode);

            return (
              <DropdownMenuItem
                key={themeVariant.variant}
                onSelect={(e) => e.preventDefault()}
                onClick={() => handleThemeSelect(themeVariant.variant, currentTheme.mode, themeVariant.name)}
                className="flex items-center justify-between group"
              >
                <div className="flex items-center space-x-2">
                  <div
                    className="w-4 h-4 rounded-full border"
                    style={{ backgroundColor: themeConfig.primary, borderColor: themeConfig.secondary }}
                  />
                  <span>{themeVariant.name}</span>
                </div>
                <div className="flex items-center space-x-2">
                  {isSelected && <Check size={16} className="text-accent-foreground" />}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleFavorite(themeVariant.variant, currentTheme.mode, themeVariant.name);
                    }}
                    className="p-1 rounded-full hover:bg-muted"
                  >
                    {isFav ? (
                      <Heart size={16} className="text-red-500 fill-current" />
                    ) : (
                      <Heart size={16} className="text-muted-foreground" />
                    )}
                  </button>
                </div>
              </DropdownMenuItem>
            );
          })}
        </CollapsibleThemeSection>

        <CollapsibleThemeSection
          title="Favorites"
          icon={<Heart size={16} className="text-red-500" />}
          isOpen={!!openSections.favorites}
          onToggle={() => toggleSection('favorites')}
        >
          {favoriteThemes.length > 0 ? (
            favoriteThemes.map((favoriteTheme: FavoriteTheme) => {
              const themeVariant = availableThemes.find(t => t.variant === favoriteTheme.variant);
              if (!themeVariant) return null;
              const themeConfig = themeVariant.colors[favoriteTheme.mode === 'system' ? currentTheme.resolvedMode : favoriteTheme.mode];
              if (!themeConfig) return null;

              return (
                <DropdownMenuItem
                  key={`${favoriteTheme.variant}-${favoriteTheme.mode}`}
                  onSelect={(e) => e.preventDefault()}
                  onClick={() => handleThemeSelect(favoriteTheme.variant, favoriteTheme.mode, themeVariant.name)}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-4 h-4 rounded-full border"
                      style={{ backgroundColor: themeConfig.primary, borderColor: themeConfig.secondary }}
                    />
                    <span>{themeVariant.name}</span>
                  </div>
                  <Check size={16} className={`text-accent-foreground ${currentTheme.variant === favoriteTheme.variant ? 'opacity-100' : 'opacity-0'}`} />
                </DropdownMenuItem>
              );
            })
          ) : (
            <DropdownMenuItem disabled>No favorites yet</DropdownMenuItem>
          )}
        </CollapsibleThemeSection>

        <CollapsibleThemeSection
          title="Recent"
          icon={<Clock size={16} className="text-blue-500" />}
          isOpen={!!openSections.recent}
          onToggle={() => toggleSection('recent')}
        >
          {recentThemes.length > 0 ? (
            recentThemes.map((recentTheme: RecentTheme) => {
              const themeVariant = availableThemes.find(t => t.variant === recentTheme.variant);
              if (!themeVariant) return null;
              const themeConfig = themeVariant.colors[recentTheme.mode === 'system' ? currentTheme.resolvedMode : recentTheme.mode];
              if (!themeConfig) return null;

              return (
                <DropdownMenuItem
                  key={`${recentTheme.variant}-${recentTheme.mode}`}
                  onSelect={(e) => e.preventDefault()}
                  onClick={() => handleThemeSelect(recentTheme.variant, recentTheme.mode, themeVariant.name)}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-4 h-4 rounded-full border"
                      style={{ backgroundColor: themeConfig.primary, borderColor: themeConfig.secondary }}
                    />
                    <span>{themeVariant.name}</span>
                  </div>
                  <Check size={16} className={`text-accent-foreground ${currentTheme.variant === recentTheme.variant ? 'opacity-100' : 'opacity-0'}`} />
                </DropdownMenuItem>
              );
            })
          ) : (
            <DropdownMenuItem disabled>No recent themes</DropdownMenuItem>
          )}
        </CollapsibleThemeSection>

        <CollapsibleThemeSection
          title="All Themes"
          icon={<Star size={16} className="text-orange-500" />}
          isOpen={!!openSections.all}
          onToggle={() => toggleSection('all')}
        >
          {availableThemes.map((themeVariant) => {
            const themeConfig = themeVariant.colors[currentTheme.resolvedMode];
            if (!themeConfig) return null;

            const isSelected = currentTheme.variant === themeVariant.variant;
            const isFav = isFavorite(themeVariant.variant, currentTheme.mode);

            return (
              <DropdownMenuItem
                key={themeVariant.variant}
                onSelect={(e) => e.preventDefault()}
                onClick={() => handleThemeSelect(themeVariant.variant, currentTheme.mode, themeVariant.name)}
                className="flex items-center justify-between group"
              >
                <div className="flex items-center space-x-2">
                  <div
                    className="w-4 h-4 rounded-full border"
                    style={{ backgroundColor: themeConfig.primary, borderColor: themeConfig.secondary }}
                  />
                  <span>{themeVariant.name}</span>
                </div>
                <div className="flex items-center space-x-2">
                  {isSelected && <Check size={16} className="text-accent-foreground" />}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleFavorite(themeVariant.variant, currentTheme.mode, themeVariant.name);
                    }}
                    className="p-1 rounded-full hover:bg-muted"
                  >
                    {isFav ? (
                      <Heart size={16} className="text-red-500 fill-current" />
                    ) : (
                      <Heart size={16} className="text-muted-foreground" />
                    )}
                  </button>
                </div>
              </DropdownMenuItem>
            );
          })}
        </CollapsibleThemeSection>

        <DropdownMenuSeparator />

        {/* Quick Actions */}
        <DropdownMenuItem
          onSelect={(e) => e.preventDefault()}
          onClick={() => {
            if (availableThemes.length > 0) {
              const randomTheme = availableThemes[Math.floor(Math.random() * availableThemes.length)];
              if (randomTheme) {
                handleThemeSelect(randomTheme.variant, currentTheme.mode, randomTheme.name);
              }
            }
          }}
          className="flex items-center space-x-2"
        >
          <Sparkles size={16} className="text-purple-500" />
          <span>Surprise Me!</span>
          <div className="text-xs text-muted-foreground ml-auto">Random</div>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        {/* Settings Link */}
        <DropdownMenuItem onClick={handleOpenSettings} className="flex items-center space-x-2">
          <div className="flex items-center space-x-2">
            <Settings size={16} className="text-muted-foreground" />
            <span>Theme Settings</span>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

