import { motion } from 'framer-motion';
import { Check } from 'lucide-react';
import React from 'react';
import { cn } from '@/app/lib/utils';
import { useEnhancedTheme } from '@/app/hooks/useEnhancedTheme';

interface ThemePreviewCardProps {
  /** Theme configuration to preview */
  theme: any;
  /** Whether this theme is currently selected */
  isSelected: boolean;
  /** Callback when theme is selected */
  onSelect: () => void;
  /** Optional className for styling */
  className?: string;
}

/**
 * Theme Preview Card Component
 * Displays a visual preview of a theme variant with interactive selection
 */
const ThemePreviewCardComponent: React.FC<ThemePreviewCardProps> = ({
  theme,
  isSelected,
  onSelect,
  className = ''
}) => {
  const { themeClasses } = useEnhancedTheme();
  return (
    <motion.div
      className={cn(
        "relative cursor-pointer rounded-xl border-2 transition-all duration-300",
        isSelected
          ? 'border-primary bg-primary/5 shadow-lg'
          : 'border-border bg-card hover:border-primary/50 hover:shadow-md',
        className
      )}
      onClick={onSelect}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Selection indicator */}
      {isSelected && (
        <motion.div
          className="absolute -top-2 -right-2 z-10 flex h-6 w-6 items-center justify-center rounded-full bg-primary text-primary-foreground shadow-lg"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: 'spring', duration: 0.4 }}
        >
          <Check size={14} />
        </motion.div>
      )}

      <div className="p-4">
        {/* Theme name and description */}
        <div className="mb-3">
          <h4 className={cn("font-medium", isSelected ? 'text-primary' : 'text-foreground')}>
            {theme.name}
          </h4>
          <p className="text-xs text-muted-foreground mt-1">
            {theme.description}
          </p>
        </div>

        {/* Enhanced Color Preview */}
        <div className="space-y-3">
          {/* Color palette grid */}
          <div className="grid grid-cols-4 gap-1">
            <div
              className="h-5 w-full rounded-sm border border-border/30 transition-transform hover:scale-110"
              style={{ backgroundColor: theme.preview?.primary }}
              title="Primary"
            />
            <div
              className="h-5 w-full rounded-sm border border-border/30 transition-transform hover:scale-110"
              style={{ backgroundColor: theme.preview?.secondary }}
              title="Secondary"
            />
            <div
              className="h-5 w-full rounded-sm border border-border/30 transition-transform hover:scale-110"
              style={{ backgroundColor: theme.preview?.accent }}
              title="Accent"
            />
            <div
              className="h-5 w-full rounded-sm border border-border/30 transition-transform hover:scale-110"
              style={{ backgroundColor: theme.preview?.background }}
              title="Background"
            />
          </div>

          {/* Enhanced mini preview layout */}
          <div
            className={cn(
              "rounded-lg border p-3 text-xs transition-all hover:shadow-sm",
              themeClasses.card,
              "border-border/30"
            )}
          >
            {/* Header bar */}
            <div className="mb-2 flex items-center justify-between">
              <div className="flex items-center space-x-1">
                <div
                  className="h-2 w-2 rounded-full"
                  style={{ backgroundColor: theme.preview?.primary }}
                />
                <span className="font-medium opacity-80">Dashboard</span>
              </div>
              <div className="flex space-x-1">
                <div
                  className="h-1.5 w-1.5 rounded-full opacity-60"
                  style={{ backgroundColor: theme.preview?.accent }}
                />
                <div
                  className="h-1.5 w-1.5 rounded-full opacity-60"
                  style={{ backgroundColor: theme.preview?.secondary }}
                />
              </div>
            </div>

            {/* Content area */}
            <div className="space-y-1.5">
              {/* Primary content bar */}
              <div
                className="h-1.5 w-full rounded-sm"
                style={{ backgroundColor: theme.preview?.primary, opacity: 0.8 }}
              />

              {/* Secondary content bars */}
              <div className="flex space-x-1">
                <div
                  className="h-1 flex-1 rounded-sm"
                  style={{ backgroundColor: theme.preview?.secondary, opacity: 0.6 }}
                />
                <div
                  className="h-1 w-4 rounded-sm"
                  style={{ backgroundColor: theme.preview?.accent, opacity: 0.7 }}
                />
              </div>

              {/* Card-like elements */}
              <div className="mt-2 grid grid-cols-2 gap-1">
                <div className="h-3 rounded-sm border border-border/20 bg-muted/20" />
                <div className="h-3 rounded-sm border border-border/20 bg-muted/20" />
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced theme info */}
        <div className="mt-3 flex items-center justify-between">
          <div className="flex items-center space-x-1">
            <div
              className={cn(
                "h-2 w-2 rounded-full",
                theme.mode === 'dark' ? 'bg-muted' : 'bg-warning'
              )}
              title={`${theme.mode} mode`}
            />
            <span className="text-xs text-muted-foreground capitalize">
              {theme.mode}
            </span>
          </div>
          <span className="text-xs font-medium text-muted-foreground capitalize">
            {theme.variant}
          </span>
        </div>
      </div>
    </motion.div>
  );
};

export const ThemePreviewCard = React.memo(ThemePreviewCardComponent);

export default ThemePreviewCard;
