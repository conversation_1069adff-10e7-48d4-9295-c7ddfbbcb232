"use client";

import { useTheme } from '@/app/context/ThemeContext';
import { useEnhancedTheme } from '@/app/hooks/useEnhancedTheme';
import { cn } from '@/app/lib/utils';
import { motion } from 'framer-motion';
import { Moon, Sun } from 'lucide-react';
import { ThemeToggleProps } from './types';

/**
 * Client component for ThemeToggle that handles interactive elements
 * Migrated to use semantic CSS variables instead of hardcoded colors
 */
export default function ThemeToggleClient({ isMinimal = false }: ThemeToggleProps) {
  const { theme, toggleTheme } = useTheme();
  const { themeClasses } = useEnhancedTheme();

  const handleToggle = () => {
    toggleTheme();
  };

  // Generate theme-aware classes using semantic CSS variables
  const buttonClasses = cn(
    // Base classes
    'transition-all duration-300',

    // Interactive states using semantic variables
    themeClasses.interactive,

    // Conditional styling based on minimal prop
    isMinimal
      ? 'p-1 text-muted-foreground hover:text-foreground'
      : cn(
          'p-2 rounded-full backdrop-blur-md shadow-lg',
          'bg-card/20 text-foreground',
          'border border-border/50'
        )
  );

  return (
    <motion.button
      onClick={handleToggle}
      className={buttonClasses}
      whileHover={{ scale: 1.1, rotate: theme.isLight ? -15 : 15 }}
      whileTap={{ scale: 0.9 }}
      aria-label={`Switch to ${theme.isLight ? 'dark' : 'light'} mode`}
      data-framer-motion="client-theme-toggle"
    >
      {theme.isLight ? (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ duration: 0.4, type: 'spring' }}
          data-framer-motion="client-moon-icon"
        >
          <Moon
            size={isMinimal ? 16 : 20}
            className={cn(
              isMinimal
                ? "text-muted-foreground"
                : "text-muted-foreground"
            )}
          />
        </motion.div>
      ) : (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ duration: 0.4, type: 'spring' }}
          data-framer-motion="client-sun-icon"
        >
          <Sun
            size={isMinimal ? 16 : 20}
            className={cn(
              isMinimal
                ? "text-muted-foreground"
                : "text-warning"
            )}
          />
        </motion.div>
      )}
    </motion.button>
  );
} 