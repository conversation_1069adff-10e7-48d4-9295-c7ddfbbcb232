"use client";

import { useTheme } from '@/app/context/ThemeContext';
import { motion } from 'framer-motion';
import { Moon, Sun } from 'lucide-react';
import React from 'react';

/**
 * Props for the ThemeToggle component.
 */
interface ThemeToggleProps {
  isMinimal?: boolean;
}

/**
 * A component that allows users to toggle between light and dark themes.
 *
 * It uses the `useTheme` hook to access and update the current theme.
 * The appearance can be a standard button or a minimal icon-only version.
 *
 * @param {ThemeToggleProps} props - The props for the ThemeToggle component.
 * @param {boolean} [props.isMinimal=false] - If true, renders a minimal version of the toggle button. Defaults to false.
 * @returns {JSX.Element} The rendered theme toggle button.
 */
const ThemeToggle: React.FC<ThemeToggleProps> = ({ isMinimal = false }) => {
  const { theme, toggleTheme } = useTheme();

  const handleToggle = () => {
    toggleTheme();
  };

  return (
    <motion.button
      onClick={handleToggle}
      className={`${isMinimal
        ? 'p-1 text-muted-foreground hover:text-foreground'
        : 'p-2 rounded-full backdrop-blur-md bg-card/20 text-foreground shadow-lg'} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring transition-all duration-300`}
      whileHover={{ scale: 1.1, rotate: theme.isLight ? -15 : 15 }}
      whileTap={{ scale: 0.9 }}
      aria-label={`Switch to ${theme.isLight ? 'dark' : 'light'} mode`}
      data-framer-motion="simple-theme-toggle"
    >
      {theme.isLight ? (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ duration: 0.4, type: 'spring' }}
          data-framer-motion="simple-moon-icon"
        >
          <Moon size={isMinimal ? 16 : 20} className={isMinimal ? "text-muted-foreground" : "text-muted-foreground"} />
        </motion.div>
      ) : (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ duration: 0.4, type: 'spring' }}
          data-framer-motion="simple-sun-icon"
        >
          <Sun size={isMinimal ? 16 : 20} className={isMinimal ? "text-muted-foreground" : "text-yellow"} />
        </motion.div>
      )}
    </motion.button>
  );
};

export default ThemeToggle;