'use client';

import { <PERSON><PERSON><PERSON>, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger } from '@/app/components/feedback/tooltip';
import { Button } from '@/app/components/forms/Button';
import { PartForm, PartFormData } from '@/app/components/forms/PartForm';
import { cn } from '@/app/lib/utils';
import { PencilIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

interface EditPartActionProps {
  partId: string;
  variant?: 'icon' | 'button' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  onSuccess?: () => void;
  className?: string;
  id?: string;
}

/**
 * Edit part action component
 * Opens the PartForm in a modal with the part data loaded
 */
export function EditPartAction({
  partId,
  variant = 'icon',
  size = 'sm',
  onSuccess,
  className,
  id,
}: EditPartActionProps) {
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [initialData, setInitialData] = useState<PartFormData | null>(null);
  
  const defaultPartData: PartFormData = {
    _id: '',
    name: '',
    businessName: null,
    description: '',
    technicalSpecs: '',
    isManufactured: false,
    reorderLevel: 0,
    status: 'active',
    inventory: {
      stockLevels: {
        raw: 0,
        hardening: 0,
        grinding: 0,
        finished: 0,
        rejected: 0
      },
      warehouseId: ''
    },
    isAssembly: false,
    schemaVersion: 1,
    supplierId: ''
  };
  const [isLoading, setIsLoading] = useState(false);

  // Fetch part data when modal is opened
  const handleOpenModal = async () => {
    setIsLoading(true);
    try {
      // URL-encode the part ID to handle special characters like slashes
      const encodedPartId = encodeURIComponent(partId);

      const response = await fetch(`/api/parts/${encodedPartId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch part data');
      }

      const data = await response.json();
      if ((data as any).data) {

        // Ensure _id is always a string
        const partId = (data as any).data._id;
        if (!partId) {
          throw new Error('Part ID is required');
        }

        // Transform API data to match PartFormData structure
        const apiData = (data as any).data;

        // Helper function to extract ID from populated field or return the value as-is
        const extractId = (field: any): string => {
          if (!field) return '';
          if (typeof field === 'string') return field;
          if (typeof field === 'object' && field._id) return field._id;
          if (typeof field === 'object' && field.id) return field.id;
          return '';
        };

        const partData: PartFormData = {
          _id: partId,
          name: apiData.name,
          businessName: apiData.businessName || null, // NEW FIELD: Human-readable business name
          partNumber: apiData.partNumber || '', // Add missing partNumber mapping
          description: apiData.description || '',
          technicalSpecs: apiData.technicalSpecs || '', // Use correct field name
          isManufactured: apiData.isManufactured || false, // Use correct field name
          reorderLevel: apiData.reorderLevel || null, // Use correct field name and allow null
          status: (apiData.status || 'active') as 'active' | 'inactive' | 'obsolete',
          inventory: {
            stockLevels: {
              raw: apiData.inventory?.stockLevels?.raw || 0,
              hardening: apiData.inventory?.stockLevels?.hardening || 0,
              grinding: apiData.inventory?.stockLevels?.grinding || 0,
              finished: apiData.inventory?.stockLevels?.finished || (apiData.inventory?.currentStock || 0),
              rejected: apiData.inventory?.stockLevels?.rejected || 0,
            },
            warehouseId: extractId(apiData.inventory?.warehouseId || apiData.warehouse), // Handle both nested and top-level warehouse
            lastStockUpdate: apiData.inventory?.lastStockUpdate ? new Date(apiData.inventory.lastStockUpdate) : null,
            safetyStockLevel: apiData.inventory?.safetyStockLevel || undefined,
            maximumStockLevel: apiData.inventory?.maximumStockLevel || undefined,
            averageDailyUsage: apiData.inventory?.averageDailyUsage || undefined,
            abcClassification: apiData.inventory?.abcClassification || undefined,
          },
          isAssembly: apiData.isAssembly || false,
          subParts: apiData.subParts || [], // Use correct field name
          schemaVersion: apiData.schemaVersion || 1,
          supplierId: extractId(apiData.supplierId || apiData.supplier), // Handle both nested and top-level supplier
          unitOfMeasure: apiData.unitOfMeasure || 'pcs', // Add missing field
          costPrice: apiData.costPrice || undefined, // Add missing field
          categoryId: extractId(apiData.categoryId || apiData.category), // Handle both nested and top-level category
        };

        // DEBUG: Log the transformed data before setting it
        console.log('[EditPartAction] Transformed part data:', {
          supplierId: partData.supplierId,
          warehouseId: partData.inventory.warehouseId,
          partNumber: partData.partNumber,
          businessName: partData.businessName,
          name: partData.name
        });

        // DEBUG: Log the raw API data for comparison
        console.log('[EditPartAction] Raw API data:', {
          supplierId: apiData.supplierId,
          supplier: apiData.supplier,
          warehouseId: apiData.inventory?.warehouseId,
          warehouse: apiData.warehouse,
          partNumber: apiData.partNumber,
          businessName: apiData.businessName,
          name: apiData.name
        });



        // Set initial data first, then open modal in the next tick to ensure state is updated
        setInitialData(partData);

        // Use setTimeout to ensure initialData is set before opening modal
        setTimeout(() => {
          setIsModalOpen(true);
        }, 0);
      }
    } catch (error) {
      console.error('Error fetching part data:', error);
      toast.error('Failed to load part data');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (data: PartFormData) => {
    try {
      // URL-encode the part ID to handle special characters like slashes
      const encodedPartId = encodeURIComponent(partId);

      // Remove immutable and system-managed fields from the request body
      // These fields should not be included in update requests:
      // - _id: should be in URL path, not body
      // - partNumber: immutable identifier
      // - isAssembly: system-managed field
      // - subParts: managed separately
      // - schemaVersion: system-managed field
      const {
        _id,
        partNumber,
        isAssembly,
        subParts,
        schemaVersion,
        ...updateData
      } = data;

      // Also exclude system-managed inventory fields
      if (updateData.inventory?.lastStockUpdate) {
        const { lastStockUpdate, ...inventoryWithoutTimestamp } = updateData.inventory;
        updateData.inventory = inventoryWithoutTimestamp;
      }

      // Handle ObjectId fields - convert empty strings to undefined for optional fields
      if (updateData.supplierId === '') {
        delete updateData.supplierId;
      }
      if (updateData.categoryId === '') {
        delete updateData.categoryId;
      }

      const response = await fetch(`/api/parts/${encodedPartId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error((errorData as any).error || 'Failed to update part');
      }

      // Close modal
      setIsModalOpen(false);

      // Clear cached initial data to ensure fresh data is fetched next time
      setInitialData(null);

      // Show success message
      toast.success(`Part "${data.name}" updated successfully`);

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Refresh the page
      router.refresh();
    } catch (error) {
      console.error('Error updating part:', error);
      toast.error(error instanceof Error ? error.message : 'An error occurred while updating the part');
    }
  };

  // Render button based on variant
  const renderButton = () => {
    switch (variant) {
      case 'button':
        return (
          <Button
            variant="outline"
            size={size}
            onClick={handleOpenModal}
            className={className}
            disabled={isLoading}
          >
            {isLoading ? (
              <>Loading...</>
            ) : (
              <>
                <PencilIcon size={16} className="mr-2" />
                Edit
              </>
            )}
          </Button>
        );
      case 'ghost':
        return (
          <Button
            variant="ghost"
            size={size}
            onClick={handleOpenModal}
            className={cn("h-8 px-2 hover:bg-muted/50", className)}
            disabled={isLoading}
          >
            {isLoading ? (
              <>Loading...</>
            ) : (
              <>
                <PencilIcon size={15} className="mr-1" />
                Edit
              </>
            )}
          </Button>
        );
      case 'icon':
      default:
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    handleOpenModal();
                  }}
                  className={cn("h-8 w-8 p-0", className)}
                  id={id || `edit-part-${partId}`}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>Loading...</>
                  ) : (
                    <PencilIcon size={15} />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Edit Part</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
    }
  };

  return (
    <>
      {renderButton()}

      {isModalOpen && initialData && (
        <PartForm
          onSubmit={(data) => Promise.resolve(handleSubmit(data))}
          onClose={() => setIsModalOpen(false)}
          initialData={initialData}
          isEdit={true}
          title={`Edit Part: ${initialData.name}`}
        />
      )}
    </>
  );
}
