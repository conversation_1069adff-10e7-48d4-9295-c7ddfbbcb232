'use client';

import { ConfirmationDialog } from '@/app/components/dialogs/ConfirmationDialog';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/app/components/feedback/tooltip';
import { Button } from '@/app/components/forms/Button';
import { cn } from '@/app/lib/utils';
import { Trash2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

// Type definitions for supported item types
export type ItemType = 'assembly' | 'product' | 'part';

export interface BaseItem {
  _id: string;
  name: string;
}

export interface AssemblyItem extends BaseItem {
  assemblyCode?: string;
}

export interface ProductItem extends BaseItem {
  productCode?: string;
}

export interface PartItem extends BaseItem {
  partNumber?: string;
}

export type SupportedItem = AssemblyItem | ProductItem | PartItem;

export interface DeleteItemActionProps {
  item: SupportedItem;
  itemType: ItemType;
  variant?: 'icon' | 'button' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  onSuccess?: () => void;
  className?: string;
  id?: string;
  customDeleteFunction?: (itemId: string) => Promise<void>;
}

/**
 * Generic delete action component that works for assemblies, products, and parts
 */
export function DeleteItemAction({
  item,
  itemType,
  variant = 'icon',
  size = 'sm',
  onSuccess,
  className,
  id,
  customDeleteFunction,
}: DeleteItemActionProps) {
  const router = useRouter();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Get the appropriate API endpoint based on item type
  const getApiEndpoint = (type: ItemType, itemId: string): string => {
    const encodedId = encodeURIComponent(itemId);
    switch (type) {
      case 'assembly':
        return `/api/assemblies/${encodedId}`;
      case 'product':
        return `/api/products/${encodedId}`;
      case 'part':
        return `/api/parts/${encodedId}`;
      default:
        throw new Error(`Unsupported item type: ${type}`);
    }
  };

  // Get display name for the item type
  const getItemTypeName = (type: ItemType): string => {
    switch (type) {
      case 'assembly':
        return 'Assembly';
      case 'product':
        return 'Product';
      case 'part':
        return 'Part';
      default:
        return 'Item';
    }
  };

  // Get item code based on type
  const getItemCode = (item: SupportedItem, type: ItemType): string => {
    switch (type) {
      case 'assembly':
        return (item as AssemblyItem).assemblyCode || item._id;
      case 'product':
        return (item as ProductItem).productCode || item._id;
      case 'part':
        return (item as PartItem).partNumber || item._id;
      default:
        return item._id;
    }
  };

  // Handle delete confirmation
  const handleDelete = async () => {
    if (!item._id) {
      console.log('[DeleteItemAction] item._id is null:', item._id);
      setIsDialogOpen(false);
      return;
    }

    setIsDeleting(true);

    try {
      if (customDeleteFunction) {
        // Use custom delete function if provided
        await customDeleteFunction(item._id);
      } else {
        // Use default API call
        const endpoint = getApiEndpoint(itemType, item._id);
        const response = await fetch(endpoint, {
          method: 'DELETE',
        });

        if (!response.ok) {
          let errorMessage = `Failed to delete ${getItemTypeName(itemType).toLowerCase()}`;
          try {
            const errorData = await response.json();
            errorMessage = errorData.error || errorMessage;
          } catch (e) {
            console.error('[DeleteItemAction] Error parsing error response:', e);
            errorMessage = `Server error ${response.status}: ${response.statusText}`;
          }
          throw new Error(errorMessage);
        }
      }

      console.log(`[DeleteItemAction] ${getItemTypeName(itemType)} deleted successfully`);

      // Close dialog
      setIsDialogOpen(false);

      // Show success message
      toast.success(`${getItemTypeName(itemType)} "${item.name}" deleted successfully`);

      // Refresh the page
      router.refresh();

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error(`[DeleteItemAction] Error deleting ${getItemTypeName(itemType)}:`, error);
      toast.error(error instanceof Error ? error.message : `Failed to delete ${getItemTypeName(itemType).toLowerCase()}`);
    } finally {
      setIsDeleting(false);
    }
  };

  // Render button based on variant
  const renderButton = () => {
    const buttonProps = {
      onClick: (e: React.MouseEvent) => {
        e.stopPropagation();
        e.preventDefault();
        setIsDialogOpen(true);
      },
      disabled: isDeleting,
      id: id || `delete-${itemType}-${item._id}`,
    };

    switch (variant) {
      case 'button':
        return (
          <Button
            variant="destructive"
            size={size}
            className={cn("", className)}
            {...buttonProps}
          >
            <Trash2 size={16} className="mr-2" />
            Delete {getItemTypeName(itemType)}
          </Button>
        );

      case 'ghost':
        return (
          <Button
            variant="ghost"
            size={size}
            className={cn("text-destructive hover:text-destructive hover:bg-destructive/10", className)}
            {...buttonProps}
          >
            <Trash2 size={16} className="mr-2" />
            Delete
          </Button>
        );

      case 'icon':
      default:
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn("h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10", className)}
                  {...buttonProps}
                >
                  <span className="sr-only">Delete {getItemTypeName(itemType)}</span>
                  <Trash2 size={15} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Delete {getItemTypeName(itemType)}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
    }
  };

  const itemCode = getItemCode(item, itemType);
  const itemTypeName = getItemTypeName(itemType);

  return (
    <>
      {renderButton()}

      <ConfirmationDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        onConfirm={handleDelete}
        title={`Delete ${itemTypeName}`}
        description={
          <>
            <p>Are you sure you want to delete the {itemTypeName.toLowerCase()} <strong>"{item.name}"</strong>?</p>
            {itemCode !== item._id && (
              <p className="mt-1 text-sm text-muted-foreground">Code: {itemCode}</p>
            )}
            <p className="mt-2 text-sm">This action cannot be undone. This will permanently delete the {itemTypeName.toLowerCase()} and remove all of its data from our servers.</p>
          </>
        }
        confirmLabel={`Delete ${itemTypeName}`}
        cancelLabel="Cancel"
        variant="destructive"
        isLoading={isDeleting}
      />
    </>
  );
}
