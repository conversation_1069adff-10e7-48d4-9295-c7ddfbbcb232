'use client';

import { <PERSON><PERSON><PERSON>, Toolt<PERSON>Content, Toolt<PERSON>Provider, TooltipTrigger } from '@/app/components/feedback/tooltip';
import { Button } from '@/app/components/forms/Button';
import { cn } from '@/app/lib/utils';
import { PencilIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

// Import types from DeleteItemAction for consistency
import type { SupportedItem, ItemType } from './DeleteItemAction';

export interface QuickEditItemActionProps {
  item: SupportedItem;
  itemType: ItemType;
  variant?: 'icon' | 'button' | 'ghost';
  size?: 'sm' | 'default' | 'lg';
  onSuccess?: () => void;
  className?: string;
  id?: string;
  customEditComponent?: React.ComponentType<{
    isOpen: boolean;
    onClose: () => void;
    itemId: string;
    mode: 'edit';
    onSuccess?: () => void;
  }>;
  useNavigationEdit?: boolean;
}

/**
 * Generic quick edit action component that works for assemblies, products, and parts
 */
export function QuickEditItemAction({
  item,
  itemType,
  variant = 'icon',
  size = 'sm',
  onSuccess,
  className,
  id,
  customEditComponent: CustomEditComponent,
  useNavigationEdit = false,
}: QuickEditItemActionProps) {
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Get display name for the item type
  const getItemTypeName = (type: ItemType): string => {
    switch (type) {
      case 'assembly':
        return 'Assembly';
      case 'product':
        return 'Product';
      case 'part':
        return 'Part';
      default:
        return 'Item';
    }
  };

  // Get edit URL for navigation-based editing
  const getEditUrl = (type: ItemType, itemId: string): string => {
    switch (type) {
      case 'assembly':
        return `/assemblies/${itemId}/edit`;
      case 'product':
        return `/products/${itemId}/edit`;
      case 'part':
        return `/inventory/${itemId}/edit`;
      default:
        return `/`;
    }
  };

  // Handle edit action
  const handleEdit = () => {
    if (useNavigationEdit) {
      // Navigate to edit page
      const editUrl = getEditUrl(itemType, item._id);
      router.push(editUrl);
    } else {
      // Open modal for editing
      console.log(`Opening quick edit modal for ${getItemTypeName(itemType)}:`, item._id);
      setIsModalOpen(true);
    }
  };

  // Close the modal
  const handleCloseModal = () => {
    console.log(`Closing quick edit modal for ${getItemTypeName(itemType)}:`, item._id);
    setIsModalOpen(false);
    if (onSuccess) {
      onSuccess();
    }
    router.refresh();
  };

  // Render button based on variant
  const renderButton = () => {
    const buttonProps = {
      onClick: (e: React.MouseEvent) => {
        e.stopPropagation();
        e.preventDefault();
        handleEdit();
      },
      id: id || `edit-${itemType}-${item._id}`,
    };

    switch (variant) {
      case 'button':
        return (
          <Button
            variant="outline"
            size={size}
            className={cn("", className)}
            {...buttonProps}
          >
            <PencilIcon size={16} className="mr-2" />
            Edit {getItemTypeName(itemType)}
          </Button>
        );

      case 'ghost':
        return (
          <Button
            variant="ghost"
            size={size}
            className={cn("", className)}
            {...buttonProps}
          >
            <PencilIcon size={16} className="mr-2" />
            Edit
          </Button>
        );

      case 'icon':
      default:
        return (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn("h-8 w-8 p-0", className)}
                  {...buttonProps}
                  style={{ position: 'relative', zIndex: 30 }}
                >
                  <span className="sr-only">Edit {getItemTypeName(itemType)}</span>
                  <PencilIcon size={15} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Edit {getItemTypeName(itemType)}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
    }
  };

  return (
    <>
      {renderButton()}

      {/* Render custom edit component if provided and modal is open */}
      {!useNavigationEdit && CustomEditComponent && isModalOpen && (
        <CustomEditComponent
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          itemId={item._id}
          mode="edit"
          onSuccess={handleCloseModal}
        />
      )}
    </>
  );
}
