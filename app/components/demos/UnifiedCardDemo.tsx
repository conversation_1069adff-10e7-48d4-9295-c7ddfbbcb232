'use client';

import React from 'react';
import { Package, ShoppingCart, Users, TrendingUp, Plus, Settings, AlertCircle } from 'lucide-react';
import { UnifiedCard, BaseCardCompat, ActionCardCompat, StatusCardCompat } from '../layout';

/**
 * Demo component showcasing the unified card functionality
 * Demonstrates all variants and compatibility wrappers
 */
export default function UnifiedCardDemo() {
  const handleCardClick = (cardType: string) => {
    console.log(`${cardType} card clicked`);
  };

  const handleViewDetails = (cardType: string) => {
    console.log(`View details clicked for ${cardType}`);
  };

  return (
    <div className="space-y-8 p-6">
      <div>
        <h2 className="text-2xl font-bold mb-4">Unified Card Component Demo</h2>
        <p className="text-muted-foreground mb-6">
          Showcasing all card variants and compatibility wrappers
        </p>
      </div>

      {/* Default Card Variants */}
      <section>
        <h3 className="text-xl font-semibold mb-4">Default Card Variants</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <UnifiedCard variant="default" size="md">
            <h4 className="font-semibold mb-2">Default Card</h4>
            <p className="text-sm text-muted-foreground">
              Basic card with default styling and hover effects.
            </p>
          </UnifiedCard>

          <UnifiedCard variant="elevated" size="md">
            <h4 className="font-semibold mb-2">Elevated Card</h4>
            <p className="text-sm text-muted-foreground">
              Card with enhanced shadow for prominence.
            </p>
          </UnifiedCard>

          <UnifiedCard 
            variant="interactive" 
            size="md"
            onClick={() => handleCardClick('Interactive')}
          >
            <h4 className="font-semibold mb-2">Interactive Card</h4>
            <p className="text-sm text-muted-foreground">
              Clickable card with hover effects.
            </p>
          </UnifiedCard>
        </div>
      </section>

      {/* Base Card Variants */}
      <section>
        <h3 className="text-xl font-semibold mb-4">Base Card Variants</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <UnifiedCard
            variant="base"
            title="Inventory Overview"
            subtitle="Current stock levels"
            icon={<Package size={20} />}
            color="blue"
            onViewDetails={() => handleViewDetails('Inventory')}
            onClick={() => handleCardClick('Base')}
          >
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Total Items</span>
                <span className="font-medium">1,234</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Low Stock</span>
                <span className="font-medium text-warning">23</span>
              </div>
            </div>
          </UnifiedCard>

          <UnifiedCard
            variant="base"
            title="User Management"
            subtitle="Active users and permissions"
            icon={<Users size={20} />}
            color="green"
            isFeatured={true}
            onViewDetails={() => handleViewDetails('Users')}
          >
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Active Users</span>
                <span className="font-medium">45</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Pending</span>
                <span className="font-medium text-info">3</span>
              </div>
            </div>
          </UnifiedCard>
        </div>
      </section>

      {/* Action Card Variants */}
      <section>
        <h3 className="text-xl font-semibold mb-4">Action Card Variants</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <UnifiedCard
            variant="action"
            label="Add Product"
            icon={<Plus size={16} />}
            color="blue"
            onClick={() => handleCardClick('Add Product')}
          />

          <UnifiedCard
            variant="action"
            label="Settings"
            icon={<Settings size={16} />}
            color="gray"
            onClick={() => handleCardClick('Settings')}
          />

          <UnifiedCard
            variant="action"
            label="View Reports"
            icon={<TrendingUp size={16} />}
            color="green"
            onClick={() => handleCardClick('Reports')}
          />

          <UnifiedCard
            variant="action"
            label="Alerts"
            icon={<AlertCircle size={16} />}
            color="red"
            onClick={() => handleCardClick('Alerts')}
          />
        </div>
      </section>

      {/* Status Card Variants */}
      <section>
        <h3 className="text-xl font-semibold mb-4">Status Card Variants</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <UnifiedCard
            variant="status"
            title="Inventory Status"
            icon={<Package />}
            color="blue"
            mainStat={{ value: 1234, label: 'Total Items' }}
            data={{
              'In Stock': 1000,
              'Low Stock': 200,
              'Out of Stock': 34
            }}
            onClick={() => handleCardClick('Inventory Status')}
          />

          <UnifiedCard
            variant="status"
            title="Order Status"
            icon={<ShoppingCart />}
            color="green"
            mainStat={{ value: 89, label: 'Active Orders' }}
            data={{
              'Pending': 25,
              'Processing': 40,
              'Shipped': 20,
              'Delivered': 4
            }}
            onClick={() => handleCardClick('Order Status')}
          />
        </div>
      </section>

      {/* Compatibility Wrappers */}
      <section>
        <h3 className="text-xl font-semibold mb-4">Compatibility Wrappers</h3>
        <div className="space-y-6">
          <div>
            <h4 className="text-lg font-medium mb-3">BaseCard Compatibility</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <BaseCardCompat
                title="Legacy Base Card"
                subtitle="Using compatibility wrapper"
                icon={<Package size={20} />}
                color="purple"
                onViewDetails={() => handleViewDetails('Legacy Base')}
              >
                <p className="text-sm text-muted-foreground">
                  This card uses the BaseCardCompat wrapper to maintain backward compatibility.
                </p>
              </BaseCardCompat>
            </div>
          </div>

          <div>
            <h4 className="text-lg font-medium mb-3">ActionCard Compatibility</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <ActionCardCompat
                label="Legacy Action"
                icon={<Plus size={16} />}
                color="orange"
                onClick={() => handleCardClick('Legacy Action')}
              />
            </div>
          </div>

          <div>
            <h4 className="text-lg font-medium mb-3">StatusCard Compatibility</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <StatusCardCompat
                title="Legacy Status"
                icon={<TrendingUp />}
                color="yellow"
                mainStat={{ value: 456, label: 'Legacy Items' }}
                data={{
                  'Active': 300,
                  'Inactive': 156
                }}
                onClick={() => handleCardClick('Legacy Status')}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Animation Variants */}
      <section>
        <h3 className="text-xl font-semibold mb-4">Animation Variants</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <UnifiedCard
            variant="default"
            animation="subtle"
            size="md"
          >
            <h4 className="font-semibold mb-2">Subtle Animation</h4>
            <p className="text-sm text-muted-foreground">
              Gentle hover effects with subtle movement.
            </p>
          </UnifiedCard>

          <UnifiedCard
            variant="default"
            animation="bounce"
            size="md"
          >
            <h4 className="font-semibold mb-2">Bounce Animation</h4>
            <p className="text-sm text-muted-foreground">
              More pronounced hover effects with bounce.
            </p>
          </UnifiedCard>

          <UnifiedCard
            variant="default"
            animation="glow"
            size="md"
          >
            <h4 className="font-semibold mb-2">Glow Animation</h4>
            <p className="text-sm text-muted-foreground">
              Glowing shadow effects on hover.
            </p>
          </UnifiedCard>
        </div>
      </section>
    </div>
  );
}
