"use client";

import { Badge } from "@/app/components/data-display/badge";
import { StandardizedTable } from '@/app/components/tables/StandardizedTable';
import { DataTableColumn } from "@/app/components/data-display/data-table";
import { Button } from "@/app/components/forms/Button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/app/components/navigation/DropdownMenu";
import { Eye, MoreHorizontal, Pencil, Trash2 } from "lucide-react";
import React, { useState } from "react";

// Sample data type
interface Product {
  id: string;
  name: string;
  stock: number;
  supplier: string;
  reorderLevel: number;
  status: "in-stock" | "low-stock" | "out-of-stock";
}

// Sample data
const sampleProducts: Product[] = Array.from({ length: 50 }).map((_, i) => {
  const stock = Math.floor(Math.random() * 100);
  const reorderLevel = Math.floor(Math.random() * 30);
  let status: "in-stock" | "low-stock" | "out-of-stock";
  
  if (stock === 0) {
    status = "out-of-stock";
  } else if (stock < reorderLevel) {
    status = "low-stock";
  } else {
    status = "in-stock";
  }
  
  return {
    id: `PROD-${(i + 1).toString().padStart(4, "0")}`,
    name: `Product ${i + 1}`,
    stock,
    supplier: `Supplier ${Math.floor(i / 10) + 1}`,
    reorderLevel,
    status,
  };
});

export function DataTableVariantsDemo() {
  // Column definitions for DataTable
  const columns: DataTableColumn<Product>[] = [
    {
      id: "name",
      header: "Name",
      accessorKey: "name",
      mobilePriority: 1,
      searchable: true,
      cell: ({ getValue }) => (
        <span className="font-medium">{getValue() as string}</span>
      ),
    },
    {
      id: "id",
      header: "Product ID",
      accessorKey: "id",
      mobilePriority: 2,
      searchable: true,
      cell: ({ getValue }) => (
        <span className="font-mono text-sm">{getValue() as string}</span>
      ),
    },
    {
      id: "stock",
      header: "Stock",
      accessorKey: "stock",
      mobilePriority: 3,
      cell: ({ getValue }) => (
        <span className="font-mono">{getValue() as number}</span>
      ),
    },
    {
      id: "supplier",
      header: "Supplier",
      accessorKey: "supplier",
      hideOnMobile: true,
      searchable: true,
    },
    {
      id: "reorderLevel",
      header: "Reorder Level",
      accessorKey: "reorderLevel",
      hideOnMobile: true,
      cell: ({ getValue }) => (
        <span className="font-mono">{getValue() as number}</span>
      ),
    },
    {
      id: "status",
      header: "Status",
      accessorKey: "status",
      mobilePriority: 4,
      cell: ({ getValue }) => {
        const value = getValue() as string;
        switch (value) {
          case "in-stock":
            return <Badge className="bg-green-500">In Stock</Badge>;
          case "low-stock":
            return <Badge className="bg-yellow-500">Low Stock</Badge>;
          case "out-of-stock":
            return <Badge className="bg-red-500">Out of Stock</Badge>;
          default:
            return null;
        }
      },
    },
  ];

  // Render row actions
  const renderRowActions = (row: Product) => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuItem
          onClick={() => console.log("View product", row.id)}
          className="cursor-pointer"
        >
          <Eye className="mr-2 h-4 w-4" />
          View
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => console.log("Edit product", row.id)}
          className="cursor-pointer"
        >
          <Pencil className="mr-2 h-4 w-4" />
          Edit
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => console.log("Delete product", row.id)}
          className="cursor-pointer text-red-600 focus:text-red-600"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold mb-4">DataTable Variants Demo</h2>
        <p className="text-gray-500 dark:text-gray-400 mb-6">
          Showcase of different DataTable configurations and styling options.
        </p>
      </div>

      <div className="space-y-8">
        <div>
          <h3 className="text-lg font-semibold mb-4">Full-Featured Table</h3>
          <StandardizedTable
            data={sampleProducts}
            columns={columns}
            searchPlaceholder="Search products..."
            enableSearch={true}
            enableViewToggle={false}
            renderActions={() => (
              <Button size="sm">
                Add New Product
              </Button>
            )}
            tableProps={{
              enableSorting: true,
              enableFiltering: true,
              enableGlobalSearch: false, // Using StandardizedTable's search instead
              enablePagination: true,
              enableColumnVisibility: false,
              mobileDisplayMode: "cards",
              density: "normal",
              initialPagination: { pageIndex: 0, pageSize: 10 },
              pageSizeOptions: [5, 10, 20, 50],
              caption: "Product inventory with stock levels and supplier information",
              onRowClick: (product) => console.log("Row clicked:", product),
              renderRowActions: renderRowActions,
            }}
          />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Compact Table</h3>
          <StandardizedTable
            data={sampleProducts.slice(0, 8)}
            columns={columns}
            enableSearch={false}
            enableViewToggle={false}
            tableProps={{
              enableSorting: true,
              enableFiltering: false,
              enableGlobalSearch: false,
              enablePagination: false,
              enableColumnVisibility: false,
              mobileDisplayMode: "cards",
              density: "compact",
              caption: "Compact product table",
            }}
          />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Mobile Cards View</h3>
          <StandardizedTable
            data={sampleProducts.slice(0, 6)}
            columns={columns}
            searchPlaceholder="Search products..."
            enableSearch={true}
            enableViewToggle={false}
            tableProps={{
              enableSorting: true,
              enableFiltering: true,
              enableGlobalSearch: false, // Using StandardizedTable's search instead
              enablePagination: false,
              mobileDisplayMode: "cards",
              density: "normal",
              caption: "Mobile-optimized card view",
            }}
          />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Dense Table with Search</h3>
          <StandardizedTable
            data={sampleProducts.slice(0, 10)}
            columns={columns}
            searchPlaceholder="Search products..."
            enableSearch={true}
            enableViewToggle={false}
            tableProps={{
              enableSorting: true,
              enableFiltering: false,
              enableGlobalSearch: false, // Using StandardizedTable's search instead
              enablePagination: true,
              density: "compact",
              initialPagination: { pageIndex: 0, pageSize: 5 },
              caption: "Dense table with global search",
            }}
          />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Simple Table</h3>
          <StandardizedTable
            data={sampleProducts.slice(0, 5)}
            columns={columns.slice(0, 4)} // Show fewer columns
            enableSearch={false}
            enableViewToggle={false}
            tableProps={{
              enableSorting: false,
              enableFiltering: false,
              enablePagination: false,
              enableColumnVisibility: false,
              density: "normal",
              caption: "Simple table without advanced features",
            }}
          />
        </div>
      </div>
    </div>
  );
}
