// This is a Server Component by default (no "use client" directive)
import React from 'react';
import ButtonClient from './ButtonClient';
import { ButtonProps } from './types';

/**
 * Button component with various styles and sizes
 *
 * Server component that delegates to client component since buttons
 * require client-side features like ref forwarding.
 */
const Button = React.forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
  return <ButtonClient {...props} ref={ref} />;
});

Button.displayName = "Button";

export { Button };
