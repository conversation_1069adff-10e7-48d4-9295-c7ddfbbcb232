"use client";

import React from "react";
import { useF<PERSON><PERSON>onte<PERSON><PERSON>, Controller } from "react-hook-form";
import { Package, FileText, DollarSign, Settings, Hash, Building2, Layers, Wrench } from "lucide-react";

import { EnhancedFormField, EnhancedFormItem, EnhancedFormLabel, EnhancedFormControl, EnhancedFormMessage } from "./enhanced-form/EnhancedFormField";
import { Input } from "@/app/components/forms/Input";
import { Textarea } from "@/app/components/forms/Textarea/Textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/forms/Select";
import { ProductFormData, ProductStatus } from "./ProductFormSchema";
import { HierarchicalBomForm } from "./HierarchicalBomForm";
import { BasicInformationSection, CategorySelectionField, PriceField, StatusSelectionField } from "./shared/StandardizedFormFields";
import { cn } from "@/app/lib/utils";

/**
 * Category option type
 */
interface CategoryOption {
  _id: string;
  name: string;
  description?: string;
}

/**
 * Assembly option type
 */
interface AssemblyOption {
  _id: string;
  name: string;
  assemblyCode: string;
}

/**
 * Part option type
 */
interface PartOption {
  _id: string;
  name: string;
  partNumber: string;
}

/**
 * Props for ProductFormFields component
 */
interface ProductFormFieldsProps {
  /**
   * Whether to show advanced fields
   */
  showAdvancedFields?: boolean;
  
  /**
   * Available categories for selection
   */
  categories?: CategoryOption[];
  
  /**
   * Available assemblies for selection
   */
  assemblies?: AssemblyOption[];
  
  /**
   * Available parts for selection
   */
  parts?: PartOption[];
  
  /**
   * Additional class names
   */
  className?: string;
}

export const ProductFormFields: React.FC<ProductFormFieldsProps> = ({
  showAdvancedFields = false,
  categories = [],
  assemblies = [],
  parts = [],
  className = "",
}) => {
  const { control, watch, formState: { errors } } = useFormContext<ProductFormData>();

  // Watch assemblyId and partId to show conditional logic
  const assemblyId = watch('assemblyId');
  const partId = watch('partId');

  return (
    <div className={cn("space-y-6", className)}>
      {/* Basic Information Section - Using Shared Component */}
      <BasicInformationSection
        entityType="product"
        className="space-y-4"
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Category - Using Shared Component */}
        <CategorySelectionField
          categories={categories}
          fieldName="categoryId"
          label="Category"
          placeholder="Select category"
        />

        {/* Selling Price - Using Shared Component */}
        <PriceField
          fieldName="sellingPrice"
          label="Selling Price"
          placeholder="0.00"
        />
      </div>

      {/* Advanced Settings Section */}
      {showAdvancedFields && (
        <div className="space-y-4">
          <div className="flex items-center gap-2 pb-2 border-b border-border">
            <Settings className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium">Advanced Settings</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Status - Using Shared Component */}
            <StatusSelectionField
              statusOptions={[
                { value: ProductStatus.ACTIVE, label: 'Active' },
                { value: ProductStatus.DISCONTINUED, label: 'Discontinued' },
                { value: ProductStatus.IN_DEVELOPMENT, label: 'In Development' }
              ]}
              fieldName="status"
              label="Status"
              placeholder="Select status"
            />

            {/* Assembly */}
            <EnhancedFormField
              control={control}
              name="assemblyId"
              render={({ field }) => (
                <EnhancedFormItem>
                  <EnhancedFormLabel className="flex items-center gap-2">
                    <Layers className="h-4 w-4" />
                    Assembly (Optional)
                  </EnhancedFormLabel>
                  <Select onValueChange={field.onChange} value={field.value || ""}>
                    <EnhancedFormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select assembly" />
                      </SelectTrigger>
                    </EnhancedFormControl>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      {assemblies.map((assembly) => (
                        <SelectItem key={assembly._id} value={assembly._id}>
                          {assembly.assemblyCode} - {assembly.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <EnhancedFormMessage />
                </EnhancedFormItem>
              )}
            />

            {/* Part */}
            <EnhancedFormField
              control={control}
              name="partId"
              render={({ field }) => (
                <EnhancedFormItem>
                  <EnhancedFormLabel className="flex items-center gap-2">
                    <Wrench className="h-4 w-4" />
                    Part (Optional)
                  </EnhancedFormLabel>
                  <Select onValueChange={field.onChange} value={field.value || ""}>
                    <EnhancedFormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select part" />
                      </SelectTrigger>
                    </EnhancedFormControl>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      {parts.map((part) => (
                        <SelectItem key={part._id} value={part._id}>
                          {part.partNumber} - {part.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <EnhancedFormMessage />
                </EnhancedFormItem>
              )}
            />
          </div>

          {/* Information about assembly/part selection */}
          {(assemblyId || partId) && (
            <div className="p-4 bg-muted rounded-lg">
              <p className="text-sm text-muted-foreground">
                {assemblyId && partId
                  ? "This product is linked to both an assembly and a part. This is unusual but allowed."
                  : assemblyId
                    ? "This product is manufactured from the selected assembly."
                    : "This product is a direct resale of the selected part."
                }
              </p>
            </div>
          )}
        </div>
      )}

      {/* Hierarchical Bill of Materials Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 pb-2 border-b border-border">
          <Layers className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-medium">Bill of Materials</h3>
        </div>

        <div className="space-y-4">
          <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mt-0.5">
                <Layers className="h-3 w-3 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
                  Hierarchical BOM Structure
                </h4>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Define the hierarchical structure of assemblies required for this product.
                  You can create nested sub-assemblies with unlimited depth. This replaces the legacy single assembly/part selection above.
                </p>
              </div>
            </div>
          </div>

          <HierarchicalBomForm
            name="components"
            maxDepth={5}
            assemblies={assemblies}
            className="mt-4"
          />
        </div>
      </div>
    </div>
  );
};
