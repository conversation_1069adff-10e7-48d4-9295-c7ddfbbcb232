'use client';

import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { z } from 'zod';

import { AssemblyFormContainer } from '@/app/components/forms/enhanced-form/AssemblyFormContainer';
import { AssemblyFormSchema } from '@/app/components/forms/enhanced-form/AssemblyFormSchema';
import { AssemblyFormFields } from '@/app/components/forms/enhanced-form/AssemblyFormFields';
import { HierarchicalPartsForm } from '@/app/components/forms/HierarchicalPartsForm';
import { useAssemblyForm } from '@/app/contexts/AssemblyFormContext';
import { useAssemblies } from '@/app/contexts/AssembliesContext';
import { showErrorToast, showSuccessToast } from '@/app/components/feedback';
import { getApiUrl } from '@/app/utils/apiUtils';

interface EnhancedAssemblyFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  assemblyId?: string | undefined;
  mode?: 'create' | 'edit';
  onSuccess?: () => void;
}

type AssemblyFormData = z.infer<typeof AssemblyFormSchema>;

/**
 * Enhanced Assembly Form Modal using standardized form system
 * Replaces the custom modal implementation with enhanced form container
 * Provides better error handling, accessibility, and user experience
 */
export function EnhancedAssemblyFormModal({ 
  isOpen, 
  onClose, 
  assemblyId, 
  mode = 'create', 
  onSuccess 
}: EnhancedAssemblyFormModalProps) {
  const router = useRouter();
  const hierarchicalPartsFormRef = useRef<{ triggerSubmit: () => Promise<void> }>(null);
  const { refreshAssemblies } = useAssemblies();
  const {
    formData,
    isLoading,
    isSaving,
    isEditing,
    isDirty,
    saveAssembly,
    resetForm,
    loadAssembly,
    setFormData,
    updateFormField
  } = useAssemblyForm();

  const [error, setError] = useState<string | null>(null);
  const [saveProgress, setSaveProgress] = useState<'idle' | 'validating' | 'saving' | 'success' | 'error'>('idle');

  // Load assembly data when modal opens in edit mode
  useEffect(() => {
    if (isOpen && mode === 'edit' && assemblyId) {
      loadAssembly(assemblyId);
    } else if (isOpen && mode === 'create') {
      resetForm();
    }
  }, [isOpen, mode, assemblyId, loadAssembly, resetForm]);

  // Prepare default values for the form
  const { defaultValues, defaultValuesForHierarchical } = useMemo(() => {
    if (!formData) {
      const emptyDefaults = {
        assemblyCode: '',
        name: '',
        description: null,
        status: 'active' as const,
        isTopLevel: true,
        version: 1,
        manufacturingInstructions: null,
        estimatedBuildTime: null,
        productId: null,
        parentId: null,
        partsRequired: []
      };
      return {
        defaultValues: emptyDefaults,
        defaultValuesForHierarchical: { ...emptyDefaults, partsRequired: [] }
      };
    }

    // Convert status to valid enum value
    let status = formData.status || 'active';
    if (status === 'in_production') {
      status = 'active'; // Map in_production to active
    }

    // Ensure partsRequired has correct structure for AssemblyFormData
    const partsRequired = (formData.partsRequired || []).map(part => ({
      partId: typeof part.partId === 'string' ? part.partId : (part.partId as any)?._id || '',
      quantityRequired: part.quantityRequired || 1,
      unitOfMeasure: part.unitOfMeasure || 'ea'
    }));

    // For HierarchicalPartsForm, we need FormPartData structure with name field
    const partsRequiredForHierarchical = (formData.partsRequired || []).map(part => ({
      partId: typeof part.partId === 'string' ? part.partId : (part.partId as any)?._id || '',
      name: (part as any).name || (part.partId as any)?.name || 'Unknown Part',
      quantityRequired: part.quantityRequired || 1,
      unitOfMeasure: part.unitOfMeasure || 'ea',
      isExpanded: true
    }));

    const defaultValues = {
      assemblyCode: formData.assemblyCode || '',
      name: formData.name || '',
      description: formData.description || null,
      status: status as 'active' | 'pending_review' | 'design_phase' | 'design_complete' | 'obsolete',
      productId: formData.productId || null,
      parentId: formData.parentId || null,
      isTopLevel: formData.isTopLevel !== undefined ? formData.isTopLevel : true,
      version: formData.version || 1,
      manufacturingInstructions: formData.manufacturingInstructions || null,
      estimatedBuildTime: formData.estimatedBuildTime || null,
      partsRequired
    };

    // Create a version for HierarchicalPartsForm with FormPartData structure
    const defaultValuesForHierarchical = {
      ...defaultValues,
      partsRequired: partsRequiredForHierarchical
    };

    return { defaultValues, defaultValuesForHierarchical };
  }, [formData]);

  // Handle form submission
  const handleSubmit = useCallback(async (data: AssemblyFormData) => {
    try {
      setError(null);
      setSaveProgress('validating');

      // Trigger hierarchical parts form submission first
      if (hierarchicalPartsFormRef.current) {
        await hierarchicalPartsFormRef.current.triggerSubmit();
      }

      setSaveProgress('saving');

      // Prepare assembly data for API
      const assemblyData = {
        ...data,
        _id: assemblyId,
        partsRequired: data.partsRequired || []
      };

      // Update form data with the submitted data
      const dataToSet: any = { ...assemblyData };
      const existingId = assemblyData._id || formData?._id;
      if (existingId) {
        dataToSet._id = existingId;
      }
      setFormData(dataToSet);

      // Save assembly using context method
      const result = await saveAssembly();

      if (result) {
        setSaveProgress('success');
        showSuccessToast(
          mode === 'create'
            ? 'Assembly created successfully!'
            : 'Assembly updated successfully!'
        );

        // Refresh assemblies list
        await refreshAssemblies();

        // Call success callback
        if (onSuccess) {
          onSuccess();
        }

        // Close modal after short delay
        setTimeout(() => {
          onClose();
          setSaveProgress('idle');
        }, 1000);
      } else {
        throw new Error('Failed to save assembly');
      }
    } catch (error) {
      console.error('Error saving assembly:', error);
      setSaveProgress('error');
      const errorMessage = error instanceof Error ? error.message : 'Failed to save assembly';
      setError(errorMessage);
      showErrorToast({ error: errorMessage });
    }
  }, [assemblyId, mode, saveAssembly, refreshAssemblies, onSuccess, onClose]);

  // Handle form error
  const handleError = useCallback((errors: any) => {
    console.error('Form validation errors:', errors);
    const errorMessage = 'Please fix the form errors and try again';
    setError(errorMessage);
    setSaveProgress('error');
    showErrorToast({ error: errorMessage });
  }, []);

  // Handle cancel/close
  const handleCancel = useCallback(() => {
    if (isDirty) {
      if (confirm('You have unsaved changes. Are you sure you want to cancel?')) {
        resetForm();
        onClose();
        setSaveProgress('idle');
        setError(null);
      }
    } else {
      onClose();
      setSaveProgress('idle');
      setError(null);
    }
  }, [isDirty, resetForm, onClose]);

  // Handle modal close (for enhanced form container)
  const handleModalClose = useCallback(() => {
    handleCancel();
  }, [handleCancel]);

  // Handle success callback
  const handleFormSuccess = useCallback(() => {
    // Success is handled in handleSubmit
  }, []);

  if (!isOpen) {
    return null;
  }

  const title = mode === 'create' ? 'Create New Assembly' : 'Edit Assembly';
  const description = mode === 'create' 
    ? 'Create a new assembly with parts and specifications'
    : 'Update assembly details and parts configuration';

  return (
    <AssemblyFormContainer
      title={title}
      description={description}
      defaultValues={defaultValues}
      schema={AssemblyFormSchema}
      onSubmit={handleSubmit}
      onError={handleError}
      isLoading={isLoading}
      isSaving={isSaving || saveProgress === 'saving' || saveProgress === 'validating'}
      error={error}
      onSuccess={handleFormSuccess}
      onCancel={handleCancel}
      onModalClose={handleModalClose}
      enableAutosave={false}
      animate={true}
      className="w-full"
      maxWidth="max-w-4xl"
      isModal={true}
    >
      {/* Basic Assembly Information */}
      <AssemblyFormFields
        showAdvancedFields={true}
        isSubAssembly={!defaultValues.isTopLevel}
        parentAssemblies={[]}
        products={[]}
      />

      {/* Parts Section */}
      <div className="space-y-4 mt-6">
        <h3 className="text-lg font-semibold">Parts Required</h3>
        <HierarchicalPartsForm
          ref={hierarchicalPartsFormRef}
          initialData={defaultValuesForHierarchical}
          mode={mode}
          onFormSubmit={(data) => {
            // Update form data with parts information
            if (data.partsRequired) {
              updateFormField('partsRequired', data.partsRequired);
            }
          }}
        />
      </div>
    </AssemblyFormContainer>
  );
}
