'use client';

import { Progress } from '@/app/components/data-display/progress';
import { Button } from '@/app/components/forms/Button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, CheckCircle2, <PERSON>ader2, RefreshCw } from 'lucide-react';
import React, { useState } from 'react';
import { useAssemblyForm } from './AssemblyFormWrapper';

export function StockSummary() {
  const { formData, refreshStockData } = useAssemblyForm();
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Count parts with sufficient/insufficient stock
  const stockStats = React.useMemo(() => {
    if (!formData.partsRequired || formData.partsRequired.length === 0) {
      return { sufficient: 0, insufficient: 0, unknown: 0, total: 0 };
    }
    
    return formData.partsRequired.reduce((acc, part) => {
      if (part.stockStatus === 'sufficient') {
        acc.sufficient += 1;
      } else if (part.stockStatus === 'insufficient') {
        acc.insufficient += 1;
      } else {
        acc.unknown += 1;
      }
      acc.total += 1;
      return acc;
    }, { sufficient: 0, insufficient: 0, unknown: 0, total: 0 });
  }, [formData.partsRequired]);
  
  // Calculate percentage of parts with sufficient stock
  const sufficiencyPercentage = stockStats.total === 0 
    ? 0 
    : Math.round((stockStats.sufficient / stockStats.total) * 100);
  
  // Handle refresh button click
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshStockData();
    } finally {
      setIsRefreshing(false);
    }
  };
  
  // If no parts, show empty state
  if (!formData.partsRequired || formData.partsRequired.length === 0) {
    return null;
  }
  
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">Stock Summary</CardTitle>
        <CardDescription>
          Availability status for parts in this assembly
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="text-sm font-medium">
              Parts with sufficient stock
            </div>
            <div className="text-sm font-medium">
              {stockStats.sufficient} / {stockStats.total}
            </div>
          </div>
          
          <Progress value={sufficiencyPercentage} className="h-2" />
          
          <div className="grid grid-cols-3 gap-4 pt-2">
            <div className="flex flex-col items-center justify-center p-3 border rounded-md bg-success/10 border-success/20">
              <CheckCircle2 className="h-5 w-5 text-success mb-1" />
              <div className="text-xl font-bold">{stockStats.sufficient}</div>
              <div className="text-xs text-muted-foreground">In Stock</div>
            </div>

            <div className="flex flex-col items-center justify-center p-3 border rounded-md bg-destructive/10 border-destructive/20">
              <AlertTriangle className="h-5 w-5 text-destructive mb-1" />
              <div className="text-xl font-bold">{stockStats.insufficient}</div>
              <div className="text-xs text-muted-foreground">Insufficient</div>
            </div>
            
            <div className="flex flex-col items-center justify-center p-3 border rounded-md bg-gray-50">
              <div className="text-xl font-bold">{stockStats.unknown}</div>
              <div className="text-xs text-muted-foreground">Unknown</div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button 
          variant="outline" 
          size="sm" 
          className="w-full flex items-center gap-2"
          onClick={handleRefresh}
          disabled={isRefreshing}
        >
          {isRefreshing ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Refreshing...
            </>
          ) : (
            <>
              <RefreshCw className="h-4 w-4" />
              Refresh Stock Data
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
} 