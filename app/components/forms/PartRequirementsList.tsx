'use client';

import { Badge } from '@/app/components/data-display/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/app/components/data-display/table';
import { Button } from '@/app/components/forms/Button';
import { Input } from '@/app/components/forms/Input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/forms/Select';
import { AlertCircle, Check, Trash2 } from 'lucide-react';
import { useAssemblyForm } from './AssemblyFormWrapper';
import { unitOfMeasureOptions } from './schema';

export function PartRequirementsList() {
  const { formData, updatePart, removePart, getStockStatus } = useAssemblyForm();
  
  // Handle quantity change
  const handleQuantityChange = (index: number, value: string) => {
    const quantity = parseInt(value);
    if (isNaN(quantity) || quantity < 1) return;
    
    updatePart(index, { quantityRequired: quantity });
  };
  
  // Handle unit of measure change
  const handleUomChange = (index: number, value: string) => {
    updatePart(index, { unitOfMeasure: value as any });
  };
  
  // Determine badge style based on stock status
  const getStatusBadge = (partId: string) => {
    const status = getStockStatus(partId);

    switch (status) {
      case 'sufficient':
        return (
          <Badge variant="success" className="flex items-center gap-1">
            <Check className="h-3 w-3" />
            In Stock
          </Badge>
        );
      case 'insufficient':
        return (
          <Badge variant="destructive" className="flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            Insufficient
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            Unknown
          </Badge>
        );
    }
  };
  
  // If no parts, show empty state
  if (!formData.partsRequired || formData.partsRequired.length === 0) {
    return (
      <div className="text-center p-8 border border-dashed rounded-md bg-muted/20">
        <h3 className="text-lg font-medium text-muted-foreground">No Parts Added</h3>
        <p className="text-sm text-muted-foreground mt-1">
          Use the part selector above to add parts to this assembly.
        </p>
      </div>
    );
  }
  
  return (
    <div className="border rounded-md">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[300px]">Part</TableHead>
            <TableHead className="w-[100px]">Quantity</TableHead>
            <TableHead className="w-[100px]">Unit</TableHead>
            <TableHead className="w-[120px]">Stock Status</TableHead>
            <TableHead className="w-[80px] text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {formData.partsRequired.map((part, index) => (
            <TableRow key={part.partId}>
              <TableCell>
                <div>
                  <div className="font-medium">{part.businessName || part.name}</div>
                  {part.businessName && (
                    <div className="text-xs text-muted-foreground italic">{part.name}</div>
                  )}
                  <div className="text-xs text-muted-foreground">{part.partNumber || part.partId}</div>
                </div>
              </TableCell>
              <TableCell>
                <Input
                  type="number"
                  min={1}
                  value={part.quantityRequired}
                  onChange={(e) => handleQuantityChange(index, e.target.value)}
                  className="w-20"
                />
              </TableCell>
              <TableCell>
                <Select
                  value={part.unitOfMeasure}
                  onValueChange={(value) => handleUomChange(index, value)}
                >
                  <SelectTrigger className="w-20">
                    <SelectValue placeholder="Unit" />
                  </SelectTrigger>
                  <SelectContent>
                    {unitOfMeasureOptions.map((unit) => (
                      <SelectItem key={unit} value={unit}>
                        {unit}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </TableCell>
              <TableCell>
                {getStatusBadge(part.partId)}
              </TableCell>
              <TableCell className="text-right">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => removePart(index)}
                  className="h-8 w-8 text-destructive hover:text-destructive/80"
                >
                  <Trash2 className="h-4 w-4" />
                  <span className="sr-only">Remove part</span>
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
} 