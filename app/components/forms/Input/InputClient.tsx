"use client";

import { cn } from "@/app/lib/utils";
import * as React from "react";
import { InputProps } from "./types";

/**
 * Enhanced client component for Input with improved accessibility and focus states
 */
const InputClient = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          // Base styles using theme variables
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background",
          // File input styles
          "file:border-0 file:bg-transparent file:text-sm file:font-medium",
          // Placeholder styles using theme variables
          "placeholder:text-muted-foreground",
          // Enhanced focus states using theme variables
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
          // Improved hover state
          "hover:border-ring/50 transition-colors duration-200",
          // Disabled states
          "disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-muted",
          // Enhanced accessibility
          "focus:border-ring focus:ring-ring/20",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
);
InputClient.displayName = "InputClient";

export default InputClient;