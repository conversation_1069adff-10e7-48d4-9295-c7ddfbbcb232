import { TextareaHTMLAttributes, DetailedHTMLProps, ReactNode } from "react";

/**
 * Props for the Textarea component
 */
export interface TextareaProps extends DetailedHTMLProps<TextareaHTMLAttributes<HTMLTextAreaElement>, HTMLTextAreaElement> {
  /**
   * Optional label for the textarea
   */
  label?: string;
  
  /**
   * Optional error message to display
   */
  error?: string;
  
  /**
   * Optional helper text to display below the textarea
   */
  helperText?: string;
  
  /**
   * Optional prefix content to display inside the textarea
   */
  prefixElement?: ReactNode;
  
  /**
   * Optional suffix content to display inside the textarea
   */
  suffixElement?: ReactNode;
  
  /**
   * Whether the textarea is disabled
   */
  disabled?: boolean;
} 