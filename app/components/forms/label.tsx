"use client"

import * as React from "react"
import * as LabelPrimitive from "@radix-ui/react-label"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/app/lib/utils"

const labelVariants = cva(
  "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
)

/**
 * @typedef {Object} LabelPropsExtendingLabelPrimitive
 * @property {string} [className] - Optional CSS classes for the label.
 * @property {VariantProps<typeof labelVariants>} [variant] - The visual style of the label (though not explicitly used by default in this variant).
 */

/**
 * A styled label component that can be associated with form inputs.
 * It uses `@radix-ui/react-label` as its base and applies custom styling via `cva`.
 *
 * @param {LabelPropsExtendingLabelPrimitive & React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> & VariantProps<typeof labelVariants>} props - Props for the Label component.
 * @param {React.Ref<React.ElementRef<typeof LabelPrimitive.Root>>} ref - Forwarded ref to the underlying Radix label element.
 * @returns {JSX.Element} The Label component.
 */
const Label = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &
    VariantProps<typeof labelVariants>
>(({ className, ...props }, ref) => (
  <LabelPrimitive.Root
    ref={ref}
    className={cn(labelVariants(), className)}
    {...props}
  />
))
Label.displayName = LabelPrimitive.Root.displayName

export { Label }