"use client";

import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/app/components/layout/cards/card";
import { FormErrorDisplay } from "@/app/components/feedback";
import { cn } from "@/app/lib/utils";
import { motion } from "framer-motion";
import { Loader2 } from "lucide-react";
import * as React from "react";
import { FormContainerProps } from "./types";

/**
 * Client component for FormContainer that handles animations and interactive elements
 */
export default function FormContainerClient({
  title,
  description,
  isLoading = false,
  error = null,
  animate = true,
  className = "",
  footer,
  maxWidth = "max-w-3xl",
  children
}: FormContainerProps) {
  const Container = animate ? motion.div : React.Fragment;
  const animationProps = animate
    ? {
        initial: { opacity: 0, y: 10 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.3 }
      }
    : {};

  return (
    <Container {...animationProps}>
      <Card className={cn("bg-card shadow-sm border border-border", maxWidth, className)}>
        <CardHeader className="pb-4 space-y-1">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl font-semibold text-foreground">
              {title}
            </CardTitle>
            {isLoading && (
              <Loader2 className="h-5 w-5 animate-spin text-warning" />
            )}
          </div>
          {description && (
            <CardDescription className="text-muted-foreground">
              {description}
            </CardDescription>
          )}
        </CardHeader>

        {error && <FormErrorDisplay error={error} />}

        <CardContent className="space-y-4">
          {/* Apply disabled styling when loading */}
          <div className={cn("transition-opacity", isLoading && "opacity-70 pointer-events-none")}>
            {children}
          </div>
        </CardContent>

        {footer && <CardFooter className="flex justify-end gap-2 pt-2">{footer}</CardFooter>}
      </Card>
    </Container>
  );
} 