// This is a Server Component by default (no "use client" directive)
import {
  FormClient,
  FormFieldClient,
  FormItemClient,
  FormLabelClient,
  FormControlClient,
  FormDescriptionClient,
  FormMessageClient,
  useFormFieldClient,
} from './FormClient';

/**
 * Form components for building forms with react-hook-form
 * Delegates to client components since they use React hooks and contexts
 */

export const Form = FormClient;
export const FormField = FormFieldClient;
export const FormItem = FormItemClient;
export const FormLabel = FormLabelClient;
export const FormControl = FormControlClient;
export const FormDescription = FormDescriptionClient;
export const FormMessage = FormMessageClient;
export const useFormField = useFormFieldClient; 