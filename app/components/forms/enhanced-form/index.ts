// Export all enhanced form components
export {
  EnhancedFormField,
  EnhancedFormItem,
  EnhancedFormLabel,
  EnhancedFormControl,
  EnhancedFormDescription,
  EnhancedFormMessage,
  useEnhancedFormField
} from './EnhancedFormField';

export { FormSubmitButton } from './FormSubmitButton';
export { AutosaveIndicator } from './AutosaveIndicator';
export { EnhancedFormContainer } from './EnhancedFormContainer';

// Assembly form components
export { AssemblyFormContainer } from './AssemblyFormContainer';
export { AssemblyFormFields } from './AssemblyFormFields';
export {
  StandardizedAssemblyForm,
  QuickAssemblyForm,
  ModalAssemblyForm,
  FullAssemblyForm
} from './StandardizedAssemblyForm';

// Assembly form schemas and types
export {
  AssemblyFormSchema,
  PartialAssemblyFormSchema,
  EditAssemblyFormSchema,
  QuickAssemblyFormSchema,
  AssemblyPartsUpdateSchema,
  PartRequirementSchema,
  AssemblyStatusEnum,
  defaultAssemblyFormValues,
  assemblyFormMessages,
  validateAssemblyCode,
  validatePartRequirement
} from './AssemblyFormSchema';

export type {
  AssemblyFormData,
  PartialAssemblyFormData,
  EditAssemblyFormData,
  QuickAssemblyFormData,
  AssemblyPartsUpdateData,
  PartRequirementData,
  AssemblyStatus
} from './AssemblyFormSchema';

// Re-export useAutosave hook
export { useAutosave } from '@/app/hooks/useAutosave';
export type { AutosaveStatus } from '@/app/hooks/useAutosave';
