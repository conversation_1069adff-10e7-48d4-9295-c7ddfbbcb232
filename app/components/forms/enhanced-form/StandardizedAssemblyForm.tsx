"use client";

import React from "react";
import { AssemblyFormContainer } from "./AssemblyFormContainer";
import { AssemblyFormFields } from "./AssemblyFormFields";
import { AssemblyFormSchema, AssemblyFormData, defaultAssemblyFormValues } from "./AssemblyFormSchema";

interface StandardizedAssemblyFormProps {
  /**
   * Form mode - create or edit
   */
  mode?: 'create' | 'edit';
  
  /**
   * Initial assembly data for editing
   */
  initialData?: Partial<AssemblyFormData>;
  
  /**
   * Whether to show advanced manufacturing fields
   */
  showAdvancedFields?: boolean;
  
  /**
   * Whether this form is displayed in a modal
   */
  isModal?: boolean;
  
  /**
   * Available parent assemblies for selection
   */
  parentAssemblies?: Array<{
    _id: string;
    name: string;
    assemblyCode: string;
  }>;
  
  /**
   * Available products for selection
   */
  products?: Array<{
    _id: string;
    name: string;
    productCode: string;
  }>;
  
  /**
   * Loading state
   */
  isLoading?: boolean;
  
  /**
   * Saving state
   */
  isSaving?: boolean;
  
  /**
   * Error message
   */
  error?: string | null;
  
  /**
   * Form submission handler
   */
  onSubmit: (data: AssemblyFormData) => Promise<void>;
  
  /**
   * Success callback
   */
  onSuccess?: () => void;
  
  /**
   * Cancel callback
   */
  onCancel?: () => void;
  
  /**
   * Modal close callback
   */
  onModalClose?: () => void;
  
  /**
   * Whether to enable autosave
   */
  enableAutosave?: boolean;
  
  /**
   * Additional class names
   */
  className?: string;
}

/**
 * Standardized Assembly Form Component
 * 
 * This component demonstrates the new standardized form system for assemblies.
 * It combines:
 * - AssemblyFormContainer for consistent form structure and behavior
 * - AssemblyFormFields for standardized field components
 * - AssemblyFormSchema for comprehensive validation
 * - Enhanced form features like autosave, accessibility, and animations
 */
export const StandardizedAssemblyForm: React.FC<StandardizedAssemblyFormProps> = ({
  mode = 'create',
  initialData = {},
  showAdvancedFields = false,
  isModal = false,
  parentAssemblies = [],
  products = [],
  isLoading = false,
  isSaving = false,
  error = null,
  onSubmit,
  onSuccess,
  onCancel,
  onModalClose,
  enableAutosave = false,
  className = "",
}) => {
  // Merge initial data with defaults
  const defaultValues = {
    ...defaultAssemblyFormValues,
    ...initialData,
  };

  // Form title based on mode
  const title = mode === 'create' ? 'Create New Assembly' : 'Edit Assembly';
  
  // Form description
  const description = mode === 'create' 
    ? 'Create a new assembly with parts and manufacturing details'
    : 'Update assembly information and configuration';

  return (
    <AssemblyFormContainer
      title={title}
      description={description}
      defaultValues={defaultValues}
      schema={AssemblyFormSchema}
      onSubmit={onSubmit}
      isLoading={isLoading}
      isSaving={isSaving}
      error={error}
      onSuccess={onSuccess}
      onCancel={onCancel}
      onModalClose={onModalClose}
      enableAutosave={enableAutosave}
      autosaveDelay={2000}
      localStorageKey={`assembly-form-${mode}`}
      animate={true}
      className={className}
      maxWidth="max-w-4xl"
      isModal={isModal}
    >
      <AssemblyFormFields
        showAdvancedFields={showAdvancedFields}
        isSubAssembly={!defaultValues.isTopLevel}
        parentAssemblies={parentAssemblies}
        products={products}
      />
    </AssemblyFormContainer>
  );
};

/**
 * Quick Assembly Creation Form
 * Simplified version for rapid assembly creation
 */
export const QuickAssemblyForm: React.FC<Omit<StandardizedAssemblyFormProps, 'showAdvancedFields' | 'parentAssemblies' | 'products'>> = (props) => {
  return (
    <StandardizedAssemblyForm
      {...props}
      showAdvancedFields={false}
      parentAssemblies={[]}
      products={[]}
    />
  );
};

/**
 * Modal Assembly Form
 * Pre-configured for modal usage
 */
export const ModalAssemblyForm: React.FC<StandardizedAssemblyFormProps> = (props) => {
  return (
    <StandardizedAssemblyForm
      {...props}
      isModal={true}
      enableAutosave={false} // Disable autosave in modals
    />
  );
};

/**
 * Full Assembly Form
 * Complete form with all features enabled
 */
export const FullAssemblyForm: React.FC<StandardizedAssemblyFormProps> = (props) => {
  return (
    <StandardizedAssemblyForm
      {...props}
      showAdvancedFields={true}
      enableAutosave={true}
    />
  );
};
