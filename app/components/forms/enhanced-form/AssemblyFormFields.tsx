"use client";

import React from "react";
import { use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import { Package, FileText, Clock, Settings, Hash, Building2 } from "lucide-react";

import { EnhancedFormField, EnhancedFormItem, EnhancedFormLabel, EnhancedFormControl, EnhancedFormMessage } from "./EnhancedFormField";
import { Input } from "@/app/components/forms/Input";
import { Textarea } from "@/app/components/forms/Textarea/Textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/forms/Select";
import { Switch } from "@/app/components/forms/switch";
import { AssemblyFormData, AssemblyStatus } from "./AssemblyFormSchema";
import { BasicInformationSection, StatusSelectionField } from "../shared/StandardizedFormFields";
import { cn } from "@/app/lib/utils";

// Assembly status options with descriptions
const ASSEMBLY_STATUS_OPTIONS: Array<{
  value: AssemblyStatus;
  label: string;
  description: string;
}> = [
  {
    value: 'active',
    label: 'Active',
    description: 'Ready for production and use'
  },
  {
    value: 'pending_review',
    label: 'Pending Review',
    description: 'Awaiting approval or review'
  },
  {
    value: 'design_phase',
    label: 'Design Phase',
    description: 'Currently being designed'
  },
  {
    value: 'design_complete',
    label: 'Design Complete',
    description: 'Design finished, ready for review'
  },
  {
    value: 'obsolete',
    label: 'Obsolete',
    description: 'No longer in use'
  }
];

interface AssemblyFormFieldsProps {
  /**
   * Whether to show advanced fields
   */
  showAdvancedFields?: boolean;
  
  /**
   * Whether this is a sub-assembly form
   */
  isSubAssembly?: boolean;
  
  /**
   * Available parent assemblies for selection
   */
  parentAssemblies?: Array<{
    _id: string;
    name: string;
    assemblyCode: string;
  }>;
  
  /**
   * Available products for selection
   */
  products?: Array<{
    _id: string;
    name: string;
    productCode: string;
  }>;
  
  /**
   * Additional class names
   */
  className?: string;
}

export const AssemblyFormFields: React.FC<AssemblyFormFieldsProps> = ({
  showAdvancedFields = false,
  isSubAssembly = false,
  parentAssemblies = [],
  products = [],
  className = "",
}) => {
  const { control, watch, formState: { errors } } = useFormContext<AssemblyFormData>();
  
  // Watch isTopLevel to conditionally show parent field
  const isTopLevel = watch('isTopLevel');

  return (
    <div className={cn("space-y-6", className)}>
      {/* Basic Information Section - Using Shared Component */}
      <BasicInformationSection
        entityType="assembly"
        className="space-y-4"
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Status - Using Shared Component */}
        <StatusSelectionField
          statusOptions={ASSEMBLY_STATUS_OPTIONS.map(option => ({
            value: option.value,
            label: option.label
          }))}
          fieldName="status"
          label="Status"
          placeholder="Select status"
        />

        {/* Version */}
        <EnhancedFormField
          control={control}
          name="version"
          render={({ field }) => (
            <EnhancedFormItem>
              <EnhancedFormLabel>Version</EnhancedFormLabel>
              <EnhancedFormControl>
                <Input
                  {...field}
                  type="number"
                  min="1"
                  max="999"
                  onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                />
              </EnhancedFormControl>
              <EnhancedFormMessage />
            </EnhancedFormItem>
          )}
        />
      </div>

      {/* Hierarchy Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 pb-2 border-b border-border">
          <Building2 className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-medium">Assembly Hierarchy</h3>
        </div>
        
        {/* Top Level Toggle */}
        <EnhancedFormField
          control={control}
          name="isTopLevel"
          render={({ field }) => (
            <EnhancedFormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <EnhancedFormLabel className="text-base">
                  Top-Level Assembly
                </EnhancedFormLabel>
                <div className="text-sm text-muted-foreground">
                  This assembly is not part of another assembly
                </div>
              </div>
              <EnhancedFormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </EnhancedFormControl>
            </EnhancedFormItem>
          )}
        />
        
        {/* Parent Assembly - only show if not top level */}
        {!isTopLevel && (
          <EnhancedFormField
            control={control}
            name="parentId"
            render={({ field }) => (
              <EnhancedFormItem>
                <EnhancedFormLabel>Parent Assembly</EnhancedFormLabel>
                <Select onValueChange={field.onChange} value={field.value || ''}>
                  <EnhancedFormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select parent assembly" />
                    </SelectTrigger>
                  </EnhancedFormControl>
                  <SelectContent>
                    {parentAssemblies.map((assembly) => (
                      <SelectItem key={assembly._id} value={assembly._id}>
                        <div className="flex flex-col">
                          <span>{assembly.name}</span>
                          <span className="text-xs text-muted-foreground font-mono">
                            {assembly.assemblyCode}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <EnhancedFormMessage />
              </EnhancedFormItem>
            )}
          />
        )}
        
        {/* Product Association */}
        {products.length > 0 && (
          <EnhancedFormField
            control={control}
            name="productId"
            render={({ field }) => (
              <EnhancedFormItem>
                <EnhancedFormLabel>Associated Product (Optional)</EnhancedFormLabel>
                <Select onValueChange={field.onChange} value={field.value || ''}>
                  <EnhancedFormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select associated product" />
                    </SelectTrigger>
                  </EnhancedFormControl>
                  <SelectContent>
                    <SelectItem value="">No associated product</SelectItem>
                    {products.map((product) => (
                      <SelectItem key={product._id} value={product._id}>
                        <div className="flex flex-col">
                          <span>{product.name}</span>
                          <span className="text-xs text-muted-foreground font-mono">
                            {product.productCode}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <EnhancedFormMessage />
              </EnhancedFormItem>
            )}
          />
        )}
      </div>

      {/* Advanced Fields */}
      {showAdvancedFields && (
        <div className="space-y-4">
          <div className="flex items-center gap-2 pb-2 border-b border-border">
            <Clock className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium">Manufacturing Details</h3>
          </div>
          
          {/* Manufacturing Instructions */}
          <EnhancedFormField
            control={control}
            name="manufacturingInstructions"
            render={({ field }) => (
              <EnhancedFormItem>
                <EnhancedFormLabel>Manufacturing Instructions</EnhancedFormLabel>
                <EnhancedFormControl>
                  <Textarea
                    {...field}
                    value={field.value || ''}
                    placeholder="Enter detailed manufacturing instructions (optional)"
                    rows={4}
                  />
                </EnhancedFormControl>
                <EnhancedFormMessage />
              </EnhancedFormItem>
            )}
          />
          
          {/* Estimated Build Time */}
          <EnhancedFormField
            control={control}
            name="estimatedBuildTime"
            render={({ field }) => (
              <EnhancedFormItem>
                <EnhancedFormLabel>Estimated Build Time</EnhancedFormLabel>
                <EnhancedFormControl>
                  <Input
                    {...field}
                    value={field.value || ''}
                    placeholder="e.g., 2 hours, 30 minutes"
                  />
                </EnhancedFormControl>
                <EnhancedFormMessage />
              </EnhancedFormItem>
            )}
          />
        </div>
      )}
    </div>
  );
};
