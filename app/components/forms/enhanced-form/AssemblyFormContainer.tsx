"use client";

import React from "react";
import { use<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, SubmitHandler, SubmitErrorHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { AnimatePresence, motion } from "framer-motion";
import { Save, X, AlertCircle } from "lucide-react";

import { EnhancedFormContainer } from "./EnhancedFormContainer";
import { FormSubmitButton } from "./FormSubmitButton";
import { useAutosave, AutosaveStatus } from "@/app/hooks/useAutosave";
import { Button } from "@/app/components/forms/Button";
import { showErrorToast, showSuccessToast, showValidationErrorToast } from "@/app/components/feedback";
import { cn } from "@/app/lib/utils";

// SCHEMA ALIGNMENT: Using canonical assembly schema fields from database_schema_updated.md
export interface AssemblyFormData {
  assemblyCode: string;
  name: string;
  description?: string | null | undefined;
  status: 'active' | 'pending_review' | 'design_phase' | 'design_complete' | 'obsolete';
  version: number;
  isTopLevel: boolean;
  manufacturingInstructions?: string | null | undefined;
  estimatedBuildTime?: string | null | undefined;
  productId?: string | null | undefined;
  parentId?: string | null | undefined;
  partsRequired: Array<{
    partId: string;
    quantityRequired: number;
    unitOfMeasure: string;
    children?: Array<{
      partId: string;
      quantityRequired: number;
      unitOfMeasure: string;
      children?: any[];
    }>;
  }>;
}

export interface AssemblyFormContainerProps {
  /**
   * Form title
   */
  title: string;
  
  /**
   * Optional form description
   */
  description?: string;
  
  /**
   * Initial form data
   */
  defaultValues?: Partial<AssemblyFormData>;
  
  /**
   * Validation schema (Zod)
   */
  schema: any;
  
  /**
   * Form submission handler
   */
  onSubmit: (data: AssemblyFormData) => Promise<void>;
  
  /**
   * Form submission error handler
   */
  onError?: (errors: any) => void;
  
  /**
   * Whether the form is in a loading state
   */
  isLoading?: boolean;
  
  /**
   * Whether the form is currently saving
   */
  isSaving?: boolean;
  
  /**
   * Error message to display
   */
  error?: string | null;
  
  /**
   * Success callback after form submission
   */
  onSuccess?: (() => void) | undefined;
  
  /**
   * Cancel/close callback
   */
  onCancel?: (() => void) | undefined;
  
  /**
   * Whether to enable autosave
   */
  enableAutosave?: boolean;
  
  /**
   * Autosave delay in milliseconds
   */
  autosaveDelay?: number;
  
  /**
   * Local storage key for autosave
   */
  localStorageKey?: string;
  
  /**
   * Whether to animate the form container
   */
  animate?: boolean;
  
  /**
   * Additional class names
   */
  className?: string;
  
  /**
   * Maximum width of the form container
   */
  maxWidth?: string;
  
  /**
   * Form content
   */
  children: React.ReactNode;
  
  /**
   * Whether this is a modal form
   */
  isModal?: boolean;
  
  /**
   * Modal close handler
   */
  onModalClose?: (() => void) | undefined;
}

export const AssemblyFormContainer: React.FC<AssemblyFormContainerProps> = ({
  title,
  description,
  defaultValues = {},
  schema,
  onSubmit,
  onError,
  isLoading = false,
  isSaving = false,
  error = null,
  onSuccess,
  onCancel,
  enableAutosave = false,
  autosaveDelay = 2000,
  localStorageKey,
  animate = true,
  className = "",
  maxWidth = "max-w-4xl",
  children,
  isModal = false,
  onModalClose,
}) => {
  // Initialize react-hook-form with Zod validation
  const form = useForm<AssemblyFormData>({
    resolver: zodResolver(schema),
    defaultValues: defaultValues as AssemblyFormData,
    mode: 'onChange',
    criteriaMode: 'all',
  });

  // Autosave functionality
  const {
    status: autosaveStatus,
    lastSaved,
    error: autosaveError,
  } = useAutosave({
    form,
    onSave: async (data) => {
      if (enableAutosave) {
        try {
          await onSubmit(data);
        } catch (error) {
          console.error('Autosave failed:', error);
          throw error;
        }
      }
    },
    delay: autosaveDelay,
    enabled: enableAutosave && !isSaving,
    ...(localStorageKey ? { localStorageKey } : {}),
    minDirtyFields: 2, // Only autosave after meaningful changes
  });

  // Form submission handler
  const handleSubmit: SubmitHandler<AssemblyFormData> = async (data) => {
    try {
      await onSubmit(data);
      showSuccessToast('Assembly saved successfully!');
      onSuccess?.();
    } catch (error) {
      console.error('Form submission error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to save assembly';
      showErrorToast({ error: errorMessage });
      throw error;
    }
  };

  // Form error handler
  const handleError: SubmitErrorHandler<AssemblyFormData> = (errors) => {
    console.error('Form validation errors:', errors);
    showValidationErrorToast(
      Object.keys(errors),
      { title: 'Please fix the validation errors before submitting' }
    );
    onError?.(errors);
  };

  // Handle form cancellation
  const handleCancel = () => {
    if (form.formState.isDirty) {
      const confirmed = window.confirm('You have unsaved changes. Are you sure you want to cancel?');
      if (!confirmed) return;
    }
    
    form.reset();
    onCancel?.();
    onModalClose?.();
  };

  // Footer content with action buttons
  const footerContent = (
    <div className="flex items-center justify-between w-full">
      <div className="flex items-center gap-2">
        {enableAutosave && (
          <div className="text-sm text-muted-foreground">
            {autosaveStatus === 'saving' && 'Saving...'}
            {autosaveStatus === 'saved' && lastSaved && `Saved ${lastSaved.toLocaleTimeString()}`}
            {autosaveStatus === 'error' && (
              <span className="text-destructive flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                Autosave failed
              </span>
            )}
          </div>
        )}
      </div>
      
      <div className="flex items-center gap-2">
        {(onCancel || onModalClose) && (
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={isSaving}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
        )}
        
        <FormSubmitButton
          isLoading={isSaving}
          loadingText="Saving..."
          successText="Saved!"
        >
          <Save className="h-4 w-4 mr-2" />
          Save Assembly
        </FormSubmitButton>
      </div>
    </div>
  );

  // Modal wrapper for modal forms
  const ModalWrapper = ({ children }: { children: React.ReactNode }) => {
    if (!isModal) return <>{children}</>;
    
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50">
        <AnimatePresence>
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="w-full max-w-4xl max-h-[90vh] overflow-auto"
          >
            {children}
          </motion.div>
        </AnimatePresence>
      </div>
    );
  };

  return (
    <ModalWrapper>
      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit, handleError)} className="w-full">
          <EnhancedFormContainer
            title={title}
            description={description}
            isLoading={isLoading}
            error={error}
            animate={animate}
            className={cn("w-full", className)}
            maxWidth={maxWidth}
            footer={footerContent}
            autosaveStatus={autosaveStatus}
            lastSaved={lastSaved}
            autosaveError={autosaveError}
            showAutosaveIndicator={enableAutosave}
          >
            {children}
          </EnhancedFormContainer>
        </form>
      </FormProvider>
    </ModalWrapper>
  );
};
