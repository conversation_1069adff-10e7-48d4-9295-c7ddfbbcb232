"use client";

import React, { useState } from 'react';
import { Button } from '@/app/components/forms/Button';
import { Input } from '@/app/components/forms/Input';
import { Label } from '@/app/components/forms/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/forms/Select';
import { Textarea } from '@/app/components/forms/Textarea/Textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
import { Badge } from '@/app/components/data-display/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/app/components/navigation/dialog';
import { 
  Plus, 
  Edit2, 
  Trash2, 
  MapPin, 
  Package, 
  Weight, 
  Box,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { LocationFormData } from './types';

interface LocationManagementProps {
  locations: LocationFormData[];
  onLocationsChange: (locations: LocationFormData[]) => void;
  disabled?: boolean;
  isLoading?: boolean;
}

interface LocationFormProps {
  location?: LocationFormData | undefined;
  onSave: (location: LocationFormData) => void;
  onCancel: () => void;
  isEdit?: boolean;
}

const LOCATION_TYPES = [
  'Bin',
  'Shelf', 
  'Floor Area',
  'Staging',
  'Production Area',
  'Quality Control',
  'Shipping',
  'Receiving'
] as const;

function LocationForm({ location, onSave, onCancel, isEdit = false }: LocationFormProps) {
  const [formData, setFormData] = useState<LocationFormData>({
    name: location?.name || '',
    description: location?.description || '',
    locationType: location?.locationType || 'Bin',
    capacity: location?.capacity || null,
    isActive: location?.isActive ?? true,
    _id: location?._id || `temp-${Date.now()}`
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) return;
    
    onSave(formData);
  };

  const handleCapacityChange = (field: 'maxWeightKg' | 'volumeM3', value: string) => {
    const numValue = value === '' ? undefined : parseFloat(value);
    setFormData(prev => ({
      ...prev,
      capacity: {
        ...prev.capacity,
        [field]: numValue
      }
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Location Name */}
        <div className="space-y-2">
          <Label htmlFor="location-name" className="flex items-center space-x-1">
            <MapPin className="h-4 w-4" />
            <span>Location Name</span>
            <span className="text-red-500">*</span>
          </Label>
          <Input
            id="location-name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            placeholder="e.g., A1-R2-S3, BIN-001, HARDENING-AREA-1"
            required
          />
        </div>

        {/* Location Type */}
        <div className="space-y-2">
          <Label htmlFor="location-type" className="flex items-center space-x-1">
            <Package className="h-4 w-4" />
            <span>Location Type</span>
            <span className="text-red-500">*</span>
          </Label>
          <Select
            value={formData.locationType}
            onValueChange={(value) => setFormData(prev => ({ 
              ...prev, 
              locationType: value as LocationFormData['locationType']
            }))}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select location type" />
            </SelectTrigger>
            <SelectContent>
              {LOCATION_TYPES.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Description */}
      <div className="space-y-2">
        <Label htmlFor="location-description">Description</Label>
        <Textarea
          id="location-description"
          value={formData.description || ''}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          placeholder="Optional description of this location"
          rows={2}
        />
      </div>

      {/* Capacity */}
      <div className="space-y-2">
        <Label className="flex items-center space-x-1">
          <Weight className="h-4 w-4" />
          <span>Capacity Limits (Optional)</span>
        </Label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-1">
            <Label htmlFor="max-weight" className="text-sm text-muted-foreground">
              Max Weight (kg)
            </Label>
            <Input
              id="max-weight"
              type="number"
              min="0"
              step="0.1"
              value={formData.capacity?.maxWeightKg || ''}
              onChange={(e) => handleCapacityChange('maxWeightKg', e.target.value)}
              placeholder="e.g., 1000"
            />
          </div>
          <div className="space-y-1">
            <Label htmlFor="max-volume" className="text-sm text-muted-foreground flex items-center space-x-1">
              <Box className="h-3 w-3" />
              <span>Max Volume (m³)</span>
            </Label>
            <Input
              id="max-volume"
              type="number"
              min="0"
              step="0.1"
              value={formData.capacity?.volumeM3 || ''}
              onChange={(e) => handleCapacityChange('volumeM3', e.target.value)}
              placeholder="e.g., 50"
            />
          </div>
        </div>
      </div>

      {/* Active Status */}
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="location-active"
          checked={formData.isActive}
          onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
          className="rounded border-gray-300"
        />
        <Label htmlFor="location-active" className="text-sm">
          Location is active and available for use
        </Label>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">
          {isEdit ? 'Update Location' : 'Add Location'}
        </Button>
      </div>
    </form>
  );
}

export default function LocationManagement({
  locations,
  onLocationsChange,
  disabled = false,
  isLoading = false
}: LocationManagementProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingLocation, setEditingLocation] = useState<LocationFormData | undefined>();

  const handleAddLocation = (newLocation: LocationFormData) => {
    const locationWithId = {
      ...newLocation,
      _id: `temp-${Date.now()}` // Temporary ID for new locations
    };
    onLocationsChange([...locations, locationWithId]);
    setIsDialogOpen(false);
  };

  const handleEditLocation = (updatedLocation: LocationFormData) => {
    const updatedLocations = locations.map(loc => 
      loc._id === editingLocation?._id ? updatedLocation : loc
    );
    onLocationsChange(updatedLocations);
    setEditingLocation(undefined);
    setIsDialogOpen(false);
  };

  const handleDeleteLocation = (locationId: string) => {
    const updatedLocations = locations.filter(loc => loc._id !== locationId);
    onLocationsChange(updatedLocations);
  };

  const openEditDialog = (location: LocationFormData) => {
    setEditingLocation(location);
    setIsDialogOpen(true);
  };

  const openAddDialog = () => {
    setEditingLocation(undefined);
    setIsDialogOpen(true);
  };

  const closeDialog = () => {
    setIsDialogOpen(false);
    setEditingLocation(undefined);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label className="text-base font-medium">Warehouse Locations</Label>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button 
              type="button" 
              variant="outline" 
              size="sm" 
              onClick={openAddDialog}
              disabled={disabled}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Location
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl" aria-describedby="location-dialog-description">
            <DialogHeader>
              <DialogTitle>
                {editingLocation ? 'Edit Location' : 'Add New Location'}
              </DialogTitle>
              <div id="location-dialog-description" className="sr-only">
                {editingLocation
                  ? 'Edit the details of an existing warehouse location including name, type, description, and capacity limits.'
                  : 'Add a new location to this warehouse by providing location details such as name, type, description, and capacity limits.'
                }
              </div>
            </DialogHeader>
            <LocationForm
              location={editingLocation}
              onSave={editingLocation ? handleEditLocation : handleAddLocation}
              onCancel={closeDialog}
              isEdit={!!editingLocation}
            />
          </DialogContent>
        </Dialog>
      </div>

      {isLoading ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-8 text-center">
            <MapPin className="h-12 w-12 text-muted-foreground mb-4 animate-pulse" />
            <h3 className="text-lg font-medium text-muted-foreground mb-2">
              Loading locations...
            </h3>
            <p className="text-sm text-muted-foreground mb-4">
              Fetching existing warehouse locations
            </p>
          </CardContent>
        </Card>
      ) : locations.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-8 text-center">
            <MapPin className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium text-muted-foreground mb-2">
              No locations added yet
            </h3>
            <p className="text-sm text-muted-foreground mb-4">
              Add locations to organize inventory within this warehouse
            </p>
            <Button 
              type="button" 
              variant="outline" 
              onClick={openAddDialog}
              disabled={disabled}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add First Location
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 auto-rows-fr">
          {locations.map((location) => (
            <Card key={location._id} className="relative h-fit">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium">
                    {location.name}
                  </CardTitle>
                  <div className="flex items-center space-x-1">
                    {location.isActive ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => openEditDialog(location)}
                      disabled={disabled}
                      className="h-6 w-6 p-0"
                    >
                      <Edit2 className="h-3 w-3" />
                    </Button>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteLocation(location._id!)}
                      disabled={disabled}
                      className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                <Badge variant="secondary" className="w-fit">
                  {location.locationType}
                </Badge>
              </CardHeader>
              <CardContent className="pt-0">
                {location.description && (
                  <p className="text-xs text-muted-foreground mb-2">
                    {location.description}
                  </p>
                )}
                {location.capacity && (
                  <div className="text-xs text-muted-foreground space-y-1">
                    {location.capacity.maxWeightKg && (
                      <div className="flex items-center space-x-1">
                        <Weight className="h-3 w-3" />
                        <span>Max: {location.capacity.maxWeightKg} kg</span>
                      </div>
                    )}
                    {location.capacity.volumeM3 && (
                      <div className="flex items-center space-x-1">
                        <Box className="h-3 w-3" />
                        <span>Max: {location.capacity.volumeM3} m³</span>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
