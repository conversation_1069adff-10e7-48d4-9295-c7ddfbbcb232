import * as z from 'zod';

/**
 * Location types for warehouse management
 * UPDATED: Added for multi-location warehouse support
 */
export interface LocationFormData {
  _id: string;
  name: string;
  description?: string | null | undefined;
  locationType: 'Bin' | 'Shelf' | 'Floor Area' | 'Staging' | 'Production Area' | 'Quality Control' | 'Shipping' | 'Receiving';
  capacity?: {
    maxWeightKg?: number | undefined;
    volumeM3?: number | undefined;
  } | null | undefined;
  isActive: boolean;
}

export const locationFormSchema = z.object({
  _id: z.string(),
  name: z.string().min(1, 'Location name is required').max(100, 'Name must be less than 100 characters'),
  description: z.string().optional().nullable(),
  locationType: z.enum(['Bin', 'Shelf', 'Floor Area', 'Staging', 'Production Area', 'Quality Control', 'Shipping', 'Receiving']),
  capacity: z.object({
    maxWeightKg: z.number().min(0).optional(),
    volumeM3: z.number().min(0).optional(),
  }).optional().nullable(),
  isActive: z.boolean(),
});

/**
 * Interface for WarehouseForm props
 */
export interface WarehouseFormProps {
  /** Submit handler function */
  onSubmit: (data: WarehouseFormData) => void;
  /** Close handler function */
  onClose: () => void;
  /** Initial data for the form (for editing) */
  initialData?: WarehouseFormData | undefined;
  /** Whether the form is for editing or adding a warehouse */
  isEdit?: boolean;
  /** Title to display on the form */
  title?: string;
  /** Whether the form is currently submitting */
  isLoading?: boolean;
  /** Error message to display */
  error?: string | null;
}

/**
 * Schema for validating warehouse form data
 * Uses Zod to define the validation rules
 * Aligns with the API model: location_id, name, location, capacity, manager, contact, isBinTracked
 */
export const warehouseFormSchema = z.object({
  _id: z.string().optional(), // MongoDB ObjectId for editing
  location_id: z.string().min(1, 'Location ID is required').optional(), // Auto-generated if not provided
  name: z.string().min(1, 'Warehouse name is required').max(100, 'Name must be less than 100 characters'),
  location: z.string().min(1, 'Location details are required').max(200, 'Location must be less than 200 characters'),
  capacity: z.number()
    .min(0, 'Capacity cannot be negative')
    .int('Capacity must be a whole number')
    .max(1000000, 'Capacity seems unreasonably large'),
  manager: z.string().min(1, 'Manager name is required').max(100, 'Manager name must be less than 100 characters'),
  contact: z.string().min(1, 'Contact information is required').max(100, 'Contact must be less than 100 characters'),
  isBinTracked: z.boolean(),
  // Additional fields for better UX (not in API model but useful for forms)
  description: z.string().optional().nullable(), // Optional description field
  isActive: z.boolean(), // Status field for UI
  // UPDATED: Added locations management
  locations: z.array(locationFormSchema).optional(), // Array of locations within this warehouse
});

/**
 * Interface for the internal form data structure, derived from the Zod schema
 */
export type WarehouseFormData = z.infer<typeof warehouseFormSchema>;

/**
 * Interface for warehouse data as returned by the API
 * This matches the actual API model structure
 */
export interface WarehouseApiData {
  _id: string;
  location_id: string;
  name: string;
  location: string;
  capacity: number;
  manager: string;
  contact: string;
  isBinTracked: boolean;
  description?: string;
  isActive?: boolean;
  schemaVersion?: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Interface for warehouse display data (for tables and UI)
 * Includes computed fields and formatted data
 */
export interface WarehouseDisplayData extends WarehouseApiData {
  /** Computed status based on various factors */
  status: 'active' | 'inactive';
  /** Formatted capacity string */
  capacityFormatted: string;
  /** Formatted creation date */
  createdAtFormatted: string;
  /** Formatted last update date */
  updatedAtFormatted: string;
}

/**
 * Interface for warehouse table actions
 */
export interface WarehouseTableActions {
  onView?: (warehouse: WarehouseDisplayData) => void;
  onEdit?: (warehouse: WarehouseDisplayData) => void;
  onDelete?: (warehouse: WarehouseDisplayData) => void;
  onCreate?: () => void;
}

/**
 * Interface for warehouse API response
 */
export interface WarehouseApiResponse {
  data: WarehouseApiData[] | WarehouseApiData | null;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  error: string | null;
  meta?: {
    duration: number;
  };
}

/**
 * Interface for warehouse creation request
 * Matches the API expectation for POST requests
 */
export interface CreateWarehouseRequest {
  location_id: string; // Required
  name: string;
  location: string;
  capacity: number;
  manager: string;
  contact: string;
  isBinTracked: boolean;
}

/**
 * Interface for warehouse update request
 * Matches the API expectation for PUT requests
 */
export interface UpdateWarehouseRequest extends Partial<CreateWarehouseRequest> {
  // All fields are optional for updates
}

/**
 * Utility function to transform form data to API request format
 */
export function transformFormDataToApiRequest(formData: WarehouseFormData): CreateWarehouseRequest {
  return {
    location_id: formData.location_id || '',
    name: formData.name,
    location: formData.location,
    capacity: formData.capacity,
    manager: formData.manager,
    contact: formData.contact,
    isBinTracked: formData.isBinTracked,
  };
}

/**
 * Utility function to transform API data to form data format
 */
export function transformApiDataToFormData(apiData: WarehouseApiData): WarehouseFormData {
  return {
    _id: apiData._id,
    location_id: apiData.location_id,
    name: apiData.name,
    location: apiData.location,
    capacity: apiData.capacity,
    manager: apiData.manager,
    contact: apiData.contact,
    isBinTracked: apiData.isBinTracked,
    description: null, // Not in API model
    isActive: true, // Default to active
    locations: [], // Initialize empty locations array - will be fetched separately
  };
}

/**
 * Utility function to transform API data to display data format
 */
export function transformApiDataToDisplayData(apiData: WarehouseApiData): WarehouseDisplayData {
  return {
    ...apiData,
    status: 'active', // Could be computed based on various factors
    capacityFormatted: apiData.capacity ? `${apiData.capacity.toLocaleString()} units` : 'N/A',
    createdAtFormatted: apiData.createdAt ? new Date(apiData.createdAt).toLocaleDateString() : 'N/A',
    updatedAtFormatted: apiData.updatedAt ? new Date(apiData.updatedAt).toLocaleDateString() : 'N/A',
  };
}
