import { z } from 'zod';

/**
 * Product form validation schema using Zod
 * Based on the database schema for products collection
 * Updated to support hierarchical BOM components
 */

/**
 * Product status enum values
 */
export const ProductStatus = {
  ACTIVE: 'active',
  DISCONTINUED: 'discontinued',
  IN_DEVELOPMENT: 'in_development'
} as const;

export type ProductStatus = typeof ProductStatus[keyof typeof ProductStatus];

/**
 * Hierarchical component schema for recursive BOM structure
 */
export const HierarchicalComponentSchema: z.ZodSchema = z.lazy(() => z.object({
  /**
   * Assembly ID - reference to assemblies collection
   */
  assemblyId: z.string()
    .min(1, "Assembly ID is required")
    .regex(/^[0-9a-fA-F]{24}$/, "Invalid assembly ID format"),

  /**
   * Quantity required for this component
   */
  quantityRequired: z.number()
    .min(0.001, "Quantity must be greater than 0")
    .max(999999, "Quantity cannot exceed 999,999"),

  /**
   * Optional array of child components for nesting
   */
  children: z.array(HierarchicalComponentSchema).optional().default([]),
}));

/**
 * Base product form schema with all validation rules
 */
export const BaseProductFormSchema = z.object({
  /**
   * Product code - unique business identifier
   */
  productCode: z.string()
    .min(1, "Product code is required")
    .max(50, "Product code must be 50 characters or less")
    .regex(/^[A-Z0-9-_]+$/, "Product code must contain only uppercase letters, numbers, hyphens, and underscores")
    .trim(),

  /**
   * Product name
   */
  name: z.string()
    .min(1, "Product name is required")
    .max(200, "Product name must be 200 characters or less")
    .trim(),

  /**
   * Product description
   */
  description: z.string()
    .min(1, "Description is required")
    .max(1000, "Description must be 1000 characters or less")
    .trim(),

  /**
   * Category ID - reference to categories collection
   */
  categoryId: z.string()
    .min(1, "Category is required")
    .regex(/^[0-9a-fA-F]{24}$/, "Invalid category ID format"),

  /**
   * Product status
   */
  status: z.enum([ProductStatus.ACTIVE, ProductStatus.DISCONTINUED, ProductStatus.IN_DEVELOPMENT]),

  /**
   * Selling price - must be non-negative
   */
  sellingPrice: z.number()
    .min(0, "Selling price cannot be negative")
    .max(999999.99, "Selling price cannot exceed $999,999.99"),

  /**
   * Hierarchical components array for BOM structure
   */
  components: z.array(HierarchicalComponentSchema)
    .default([])
    .refine((components) => {
      // Validate maximum nesting depth
      const maxDepth = 10;
      const checkDepth = (items: any[], depth: number = 0): boolean => {
        if (depth > maxDepth) return false;
        return items.every(item =>
          !item.children || item.children.length === 0 || checkDepth(item.children, depth + 1)
        );
      };
      return checkDepth(components);
    }, "Component hierarchy cannot exceed 10 levels deep"),

  /**
   * Legacy fields for backward compatibility
   * Assembly ID - optional reference to assemblies collection
   */
  assemblyId: z.string()
    .transform((val) => val === "none" ? null : val)
    .refine((val) => val === null || /^[0-9a-fA-F]{24}$/.test(val), "Invalid assembly ID format")
    .optional()
    .nullable(),

  /**
   * Part ID - optional reference to parts collection
   */
  partId: z.string()
    .transform((val) => val === "none" ? null : val)
    .refine((val) => val === null || /^[0-9a-fA-F]{24}$/.test(val), "Invalid part ID format")
    .optional()
    .nullable(),
});

/**
 * Product form schema for creating new products
 */
export const ProductFormSchema = BaseProductFormSchema;

/**
 * Product form schema for editing existing products
 * Includes ID field and makes certain fields optional
 */
export const EditProductFormSchema = BaseProductFormSchema.extend({
  _id: z.string()
    .regex(/^[0-9a-fA-F]{24}$/, "Invalid product ID format"),
}).partial({
  // Allow partial updates during editing
  productCode: true,
  name: true,
  description: true,
  categoryId: true,
});

/**
 * Partial product form schema for create/edit operations
 * Some fields may be optional during creation
 */
export const PartialProductFormSchema = BaseProductFormSchema.partial({
  status: true,
  assemblyId: true,
  partId: true,
}).extend({
  // Override required fields that must always be present
  productCode: BaseProductFormSchema.shape.productCode,
  name: BaseProductFormSchema.shape.name,
  description: BaseProductFormSchema.shape.description,
  categoryId: BaseProductFormSchema.shape.categoryId,
  sellingPrice: BaseProductFormSchema.shape.sellingPrice,
});

/**
 * Type definitions for form data
 */
export type HierarchicalComponentFormData = z.infer<typeof HierarchicalComponentSchema>;
export type ProductFormData = z.infer<typeof ProductFormSchema>;
export type EditProductFormData = z.infer<typeof EditProductFormSchema>;
export type PartialProductFormData = z.infer<typeof PartialProductFormSchema>;

/**
 * Default values for new product form
 */
export const defaultProductFormValues: ProductFormData = {
  productCode: '',
  name: '',
  description: '',
  categoryId: '',
  status: ProductStatus.ACTIVE,
  sellingPrice: 0,
  components: [],
};

/**
 * Product form field names for type safety
 */
export const ProductFormFields = {
  PRODUCT_CODE: 'productCode',
  NAME: 'name',
  DESCRIPTION: 'description',
  CATEGORY_ID: 'categoryId',
  STATUS: 'status',
  SELLING_PRICE: 'sellingPrice',
  COMPONENTS: 'components',
  // Legacy fields
  ASSEMBLY_ID: 'assemblyId',
  PART_ID: 'partId',
} as const;

/**
 * Validation helper functions
 */
export const validateProductCode = (code: string): boolean => {
  return /^[A-Z0-9-_]+$/.test(code) && code.length > 0 && code.length <= 50;
};

export const validateSellingPrice = (price: number): boolean => {
  return price >= 0 && price <= 999999.99;
};

export const validateObjectId = (id: string): boolean => {
  return /^[0-9a-fA-F]{24}$/.test(id);
};
