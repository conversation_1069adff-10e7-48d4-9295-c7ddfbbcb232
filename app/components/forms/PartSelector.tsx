'use client';

import { Badge } from '@/app/components/data-display/badge';
import { Button } from '@/app/components/forms/Button';
import { Input } from '@/app/components/forms/Input';
import { Label } from '@/app/components/forms/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/forms/Select';
import { Card, CardContent } from '@/app/components/layout/cards/card';
import { useState } from 'react';
import { PartSearch, PartSearchResult } from '@/app/components/search/PartSearch';
import { useAssemblyForm } from './AssemblyFormWrapper';
import { PartRequirement, unitOfMeasureOptions } from './schema';

interface PartSelectorProps {
  onSelect?: (part: PartRequirement) => void;
}

export function PartSelector({ onSelect }: PartSelectorProps) {
  const { addPart } = useAssemblyForm();

  const [selectedPart, setSelectedPart] = useState<PartSearchResult | null>(null);
  const [quantity, setQuantity] = useState<number>(1);
  const [unitOfMeasure, setUnitOfMeasure] = useState<string>('ea');
  const [error, setError] = useState<string | null>(null);

  // Handle part selection from the PartSearch component
  const handlePartSelect = (part: PartSearchResult) => {
    setSelectedPart(part);
    setError(null); // Clear any previous errors
  };
  
  // Add the selected part to the assembly
  const handleAddPart = () => {
    if (!selectedPart) {
      setError('Please select a part first');
      return;
    }
    
    if (quantity <= 0) {
      setError('Quantity must be greater than zero');
      return;
    }
    
    // Create the part requirement object
    const partRequirement: PartRequirement = {
      partId: selectedPart._id,
      name: selectedPart.name,
      description: selectedPart.description || '',
      partNumber: selectedPart.partNumber || '',
      quantityRequired: quantity,
      unitOfMeasure: unitOfMeasure as any, // Type assertion to satisfy TS
      isAssembly: false, // Parts are not assemblies - this is for adding parts to assemblies
      currentStock: selectedPart.inventory?.currentStock || 0,
      stockStatus: (selectedPart.inventory?.currentStock || 0) >= quantity ? 'sufficient' : 'insufficient'
    };
    
    // Add to the assembly
    addPart(partRequirement);
    
    // Call the onSelect callback if provided
    if (onSelect) {
      onSelect(partRequirement);
    }
    
    // Reset the form
    setSelectedPart(null);
    setQuantity(1);
    setUnitOfMeasure('ea');
    setError(null);
  };
  
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Add Part to Assembly</h3>
      
      {/* Part Search using standardized component */}
      <PartSearch
        selectedPart={selectedPart}
        onPartSelect={handlePartSelect}
        placeholder="Search by part name, number, or description"
        label="Search for a part"
        showStockLevels={true}
        minQueryLength={2}
        debounceTime={300}
        maxResults={10}
      />
      
      {/* Selected Part */}
      {selectedPart && (
        <div className="p-3 border rounded-md bg-muted/20">
          <div className="flex justify-between">
            <div>
              <h4 className="font-medium">{selectedPart.name}</h4>
              <div className="text-sm text-muted-foreground">
                {selectedPart.partNumber || selectedPart._id}
              </div>
            </div>
            <Badge
              variant={(selectedPart.inventory?.currentStock || 0) > 0 ? "success" : "destructive"}
            >
              Stock: {selectedPart.inventory?.currentStock || 0}
            </Badge>
          </div>
        </div>
      )}
      
      {/* Quantity and UOM */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="quantity">Quantity Required</Label>
          <Input
            id="quantity"
            type="number"
            min="1"
            value={quantity}
            onChange={(e) => setQuantity(parseInt(e.target.value) || 0)}
            className="mt-1"
          />
        </div>
        
        <div>
          <Label htmlFor="uom">Unit of Measure</Label>
          <Select
            value={unitOfMeasure}
            onValueChange={setUnitOfMeasure}
          >
            <SelectTrigger id="uom" className="mt-1">
              <SelectValue placeholder="Select unit" />
            </SelectTrigger>
            <SelectContent>
              {unitOfMeasureOptions.map((unit) => (
                <SelectItem key={unit} value={unit}>
                  {unit}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      
      {/* Error Message */}
      {error && (
        <div className="text-sm text-destructive">
          {error}
        </div>
      )}
      
      {/* Add Button */}
      <Button
        onClick={handleAddPart}
        disabled={!selectedPart}
        className="w-full"
      >
        Add Part to Assembly
      </Button>
    </div>
  );
} 