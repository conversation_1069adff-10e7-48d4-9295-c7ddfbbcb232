import BatchFormClient from './BatchFormClient';

// Re-export the client component
export { BatchFormClient };

// Server component wrapper for BatchForm
export default function BatchForm(props: React.ComponentProps<typeof BatchFormClient>) {
  return <BatchFormClient {...props} />;
}

// Re-export the batch statuses and transitions
export { BATCH_STATUSES, VALID_STATUS_TRANSITIONS } from './BatchFormClient';
