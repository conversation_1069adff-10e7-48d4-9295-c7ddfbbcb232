'use client';

import { Badge } from '@/app/components/data-display/badge';
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger
} from '@/app/components/feedback/tooltip';
import { useTheme } from '@/app/context/ThemeContext';
import { useStatusColors } from '@/app/hooks/useEnhancedTheme';
import { cn } from '@/app/lib/utils';
import {
    AlertCircle,
    Beaker,
    CheckCircle2,
    Clock,
    Pause,
    XCircle
} from 'lucide-react';
import { BATCH_STATUSES } from '../forms/BatchForm/BatchForm';

interface BatchStatusBadgeProps {
  status: string;
  showLabel?: boolean;
  size?: 'sm' | 'default' | 'lg';
  className?: string;
  showTooltip?: boolean;
}

/**
 * Component to display batch status with appropriate visual indicators
 */
export function BatchStatusBadge({
  status,
  showLabel = true,
  size = 'default',
  className,
  showTooltip = true,
}: BatchStatusBadgeProps) {
  const { theme } = useTheme();
  const { statusColors } = useStatusColors();
  const normalizedStatus = status.toLowerCase().replace(' ', '_');
  
  // Define status configurations using standardized status colors
  const statusConfig = {
    [BATCH_STATUSES.PENDING]: {
      icon: <Clock className={cn("text-warning", size === 'sm' ? 'h-3.5 w-3.5' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4')} />,
      label: 'Pending',
      description: 'Batch is planned but not yet started',
      className: statusColors.warning.full
    },
    [BATCH_STATUSES.IN_PROGRESS]: {
      icon: <Beaker className={cn("text-info", size === 'sm' ? 'h-3.5 w-3.5' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4')} />,
      label: 'In Progress',
      description: 'Batch is currently being produced',
      className: statusColors.info.full
    },
    [BATCH_STATUSES.QUALITY_CHECK]: {
      icon: <AlertCircle className={cn("text-info", size === 'sm' ? 'h-3.5 w-3.5' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4')} />,
      label: 'Quality Check',
      description: 'Batch is undergoing quality inspection',
      className: statusColors.info.full
    },
    [BATCH_STATUSES.ON_HOLD]: {
      icon: <Pause className={cn("text-warning", size === 'sm' ? 'h-3.5 w-3.5' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4')} />,
      label: 'On Hold',
      description: 'Batch production has been temporarily paused',
      className: statusColors.warning.full
    },
    [BATCH_STATUSES.COMPLETED]: {
      icon: <CheckCircle2 className={cn("text-success", size === 'sm' ? 'h-3.5 w-3.5' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4')} />,
      label: 'Completed',
      description: 'Batch has been successfully completed',
      className: statusColors.success.full
    },
    [BATCH_STATUSES.CANCELLED]: {
      icon: <XCircle className={cn("text-destructive", size === 'sm' ? 'h-3.5 w-3.5' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4')} />,
      label: 'Cancelled',
      description: 'Batch has been cancelled',
      className: statusColors.error.full
    },
    // Default fallback
    default: {
      icon: <AlertCircle className={cn("text-muted-foreground", size === 'sm' ? 'h-3.5 w-3.5' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4')} />,
      label: status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' '),
      description: 'Unknown batch status',
      className: "bg-muted text-muted-foreground border-border"
    }
  };

  // Get the appropriate status configuration
  const config = statusConfig[normalizedStatus as keyof typeof statusConfig] || statusConfig.default;

  // Render the badge with optional tooltip
  const badge = (
    <Badge 
      variant="outline" 
      className={cn(
        config.className,
        size === 'sm' ? 'text-xs py-0 px-2' : size === 'lg' ? 'text-sm py-1 px-3' : 'text-xs py-0.5 px-2.5',
        className
      )}
    >
      {config.icon}
      {showLabel && <span className={cn("ml-1", size === 'sm' ? 'text-xs' : '')}>{config.label}</span>}
    </Badge>
  );

  // Wrap in tooltip if needed
  if (showTooltip) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {badge}
          </TooltipTrigger>
          <TooltipContent>
            {config.description}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return badge;
}
