import * as React from "react"
import * as DropdownMenuPrimitive from "@radix-ui/react-dropdown-menu"

/**
 * Props for the DropdownMenu component
 */
export type DropdownMenuProps = React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Root>

/**
 * Props for the DropdownMenuTrigger component
 */
export type DropdownMenuTriggerProps = React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Trigger>

/**
 * Props for the DropdownMenuGroup component
 */
export type DropdownMenuGroupProps = React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Group>

/**
 * Props for the DropdownMenuPortal component
 */
export type DropdownMenuPortalProps = React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Portal>

/**
 * Props for the DropdownMenuSub component
 */
export type DropdownMenuSubProps = React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Sub>

/**
 * Props for the DropdownMenuRadioGroup component
 */
export type DropdownMenuRadioGroupProps = React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioGroup>

/**
 * Props for the DropdownMenuSubTrigger component
 */
export type DropdownMenuSubTriggerProps = React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {
  inset?: boolean
}

/**
 * Props for the DropdownMenuSubContent component
 */
export type DropdownMenuSubContentProps = React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>

/**
 * Props for the DropdownMenuContent component
 */
export type DropdownMenuContentProps = React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>

/**
 * Props for the DropdownMenuItem component
 */
export type DropdownMenuItemProps = React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {
  inset?: boolean
}

/**
 * Props for the DropdownMenuCheckboxItem component
 */
export type DropdownMenuCheckboxItemProps = React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>

/**
 * Props for the DropdownMenuRadioItem component
 */
export type DropdownMenuRadioItemProps = React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>

/**
 * Props for the DropdownMenuLabel component
 */
export type DropdownMenuLabelProps = React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {
  inset?: boolean
}

/**
 * Props for the DropdownMenuSeparator component
 */
export type DropdownMenuSeparatorProps = React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>

/**
 * Props for the DropdownMenuShortcut component
 */
export type DropdownMenuShortcutProps = React.HTMLAttributes<HTMLSpanElement> 