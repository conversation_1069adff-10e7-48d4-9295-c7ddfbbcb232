'use client';

import { Button } from '@/app/components/forms/Button';
import { UnifiedAssemblyForm } from '@/app/components/forms/UnifiedAssemblyForm';
import { ShimmerButton } from '@/app/components/theme/effects';
import { useTheme } from '@/app/context/ThemeContext';
import { AssemblyFormProvider } from '@/app/contexts/AssemblyFormContext';
import { cn } from '@/app/lib/utils';
import { Plus } from 'lucide-react';
import { useState } from 'react';

interface NewAssemblyButtonProps {
  variant?: 'default' | 'outline' | 'ghost' | 'magic';
  size?: 'default' | 'sm' | 'lg';
  className?: string;
}

/**
 * Button to create a new assembly that opens the UnifiedAssemblyForm
 */
export function NewAssemblyButton({ 
  variant = 'default', 
  size = 'default',
  className = ''
}: NewAssemblyButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { theme } = useTheme();

  // Open the modal
  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  // Close the modal
  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  // Render the appropriate button based on variant
  if (variant === 'magic') {
    return (
      <>
        <ShimmerButton
          onClick={handleOpenModal}
          className={`group relative overflow-hidden ${className}`}
          shimmerColor={theme.isDark ? '#ffffff30' : '#ffffff60'}
          background={theme.isDark ? 'var(--dark-bg)' : '#1274F3'}
        >
          <Plus size={16} className="mr-2" />
          <span className="relative z-10">New Assembly</span>
          <div className={cn(
            "absolute inset-0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-300",
            theme.isDark
              ? "bg-gradient-to-r from-brand-blue/30 to-brand-pink/30"
              : "bg-gradient-to-r from-brand-blue/20 to-brand-pink/20"
          )}></div>
        </ShimmerButton>

        {isModalOpen && (
          <AssemblyFormProvider>
            <UnifiedAssemblyForm
              isOpen={isModalOpen}
              onClose={handleCloseModal}
              mode="create"
            />
          </AssemblyFormProvider>
        )}
      </>
    );
  }

  return (
    <>
      <Button
        variant={variant}
        size={size}
        onClick={handleOpenModal}
        className={`${className}`}
      >
        <Plus size={16} className="mr-2" />
        New Assembly
      </Button>

      {isModalOpen && (
        <AssemblyFormProvider>
          <UnifiedAssemblyForm
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            mode="create"
          />
        </AssemblyFormProvider>
      )}
    </>
  );
} 