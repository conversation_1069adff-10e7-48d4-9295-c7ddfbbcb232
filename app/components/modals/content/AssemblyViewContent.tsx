'use client';

import { Layers, Calendar, Package } from 'lucide-react';

import { Badge } from '@/app/components/data-display/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
// BomViewerButton removed - BOM functionality now integrated into BaseViewModal tabs
import { AssemblyStatusBadge } from '@/app/components/status/AssemblyStatusBadge';
import { PartsCountBadge } from '@/app/components/status/PartsCountBadge';
import { Assembly } from '@/app/components/tables/AssembliesTable/types';
import { StandardizedViewContent, StandardizedViewGrid, StandardizedSection } from './StandardizedViewContent';
import { countHierarchicalParts } from '@/app/utils/assemblyDataTransform';
import { HierarchicalPartsList } from '@/app/components/parts/HierarchicalPartsList';

interface AssemblyViewContentProps {
  assembly: Assembly;
  /**
   * Whether this content is being rendered inside a tab
   */
  isInTab?: boolean;
}

/**
 * Content component for displaying assembly details in a view modal
 * Contains all the specific layout and information for assembly viewing
 */
export function AssemblyViewContent({ assembly, isInTab = false }: AssemblyViewContentProps) {
  // Format date for display
  const formatDate = (date: string | Date | undefined) => {
    if (!date) return 'Not set';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Main content sections
  const mainContent = (
    <>
      {/* Basic Information */}
      <StandardizedSection title="Basic Information">
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Assembly Code
              </label>
              <p className="font-mono text-sm bg-muted px-2 py-1 rounded">
                {assembly.assemblyCode || assembly._id}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Status
              </label>
              <div className="mt-1">
                <AssemblyStatusBadge assembly={assembly} />
              </div>
            </div>
          </div>

          {assembly.description && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Description
              </label>
              <p className="text-sm text-foreground mt-1">
                {assembly.description}
              </p>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Created Date
              </label>
              <p className="text-sm text-foreground">
                {formatDate(assembly.createdAt)}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Last Updated
              </label>
              <p className="text-sm text-foreground">
                {formatDate(assembly.updatedAt)}
              </p>
            </div>
          </div>
        </div>
      </StandardizedSection>

      {/* Parts List */}
      <StandardizedSection
        title="Parts List"
        icon={<Layers className="h-5 w-5" />}
      >
        {(assembly.partsRequired && Array.isArray(assembly.partsRequired) && assembly.partsRequired.length > 0) ? (
          <div className="space-y-4">
            <div className="text-center">
              <p className="text-sm text-muted-foreground mb-2">
                This assembly contains {countHierarchicalParts(assembly.partsRequired)} component{countHierarchicalParts(assembly.partsRequired) !== 1 ? 's' : ''}
                {countHierarchicalParts(assembly.partsRequired) > assembly.partsRequired.length &&
                  ` (${assembly.partsRequired.length} root parts, ${countHierarchicalParts(assembly.partsRequired)} total including children)`
                }
              </p>
            </div>

            {/* Hierarchical Parts Display */}
            <HierarchicalPartsList
              parts={assembly.partsRequired}
              maxDepth={3}
              showQuantity={true}
              showStock={true}
            />

            <div className="flex items-center justify-center gap-2 p-3 bg-primary/5 border border-primary/20 rounded-lg mt-4">
              <Layers className="h-4 w-4 text-primary" />
              <p className="text-sm text-primary font-medium">
                View detailed Bill of Materials in the BOM tab above
              </p>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 space-y-4">
            <Package className="h-12 w-12 text-muted-foreground/50" />
            <p className="text-sm text-muted-foreground text-center">
              No parts defined for this assembly
            </p>
          </div>
        )}
      </StandardizedSection>
    </>
  );

  // Sidebar content sections
  const sidebarContent = (
    <>
      {/* Quick Stats */}
      <StandardizedSection title="Quick Stats">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Parts Count</span>
            <div className="flex items-center gap-1">
              <Package className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">
                {countHierarchicalParts(assembly.partsRequired || [])} part{countHierarchicalParts(assembly.partsRequired || []) !== 1 ? 's' : ''}
              </span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Status</span>
            <AssemblyStatusBadge assembly={assembly} size="sm" />
          </div>
          {assembly.partsRequired && Array.isArray(assembly.partsRequired) && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Components</span>
              <Badge variant="secondary">
                {assembly.partsRequired.length} items
              </Badge>
            </div>
          )}
        </div>
      </StandardizedSection>

      {/* Timeline */}
      <StandardizedSection
        title="Timeline"
        icon={<Calendar className="h-5 w-5" />}
      >
        <div className="space-y-3">
          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Created
            </label>
            <p className="text-sm text-foreground">
              {formatDate(assembly.createdAt)}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Last Modified
            </label>
            <p className="text-sm text-foreground">
              {formatDate(assembly.updatedAt)}
            </p>
          </div>
        </div>
      </StandardizedSection>
    </>
  );

  return (
    <StandardizedViewContent isInTab={isInTab}>
      <StandardizedViewGrid
        mainContent={mainContent}
        sidebarContent={sidebarContent}
      />
    </StandardizedViewContent>
  );
}
