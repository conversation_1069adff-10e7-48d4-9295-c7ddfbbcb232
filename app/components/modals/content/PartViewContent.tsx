'use client';

import { Package, Warehouse, DollarSign, Tag, Calendar, BarChart3, TrendingUp, AlertTriangle, MapPin, Loader2 } from 'lucide-react';

import { Badge } from '@/app/components/data-display/badge';
import { StandardizedViewContent, StandardizedViewGrid, StandardizedSection } from './StandardizedViewContent';
import { InventoryColumnData } from '@/app/components/data-display/data-table/column-definitions';
import { usePartInventoryDetails } from '@/app/hooks/usePartInventoryDetails';

interface PartViewContentProps {
  part: InventoryColumnData;
  /**
   * Whether this content is being rendered inside a tab
   */
  isInTab?: boolean;
}

/**
 * Content component for displaying part details in a view modal
 * Contains all the specific layout and information for part viewing
 */
export function PartViewContent({ part, isInTab = false }: PartViewContentProps) {
  // Fetch detailed inventory breakdown for multi-warehouse display
  const { inventoryDetails, isLoading: isLoadingInventory, error: inventoryError } = usePartInventoryDetails(part._id);
  // Format date for display
  const formatDate = (date: string | Date | undefined) => {
    if (!date) return 'Not set';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Format currency
  const formatCurrency = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return 'Not set';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Get status badge variant
  const getStatusVariant = (status: string | undefined) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'warning';
      case 'obsolete': return 'destructive';
      default: return 'secondary';
    }
  };

  // Calculate total stock - prefer detailed inventory data if available
  const getTotalStock = () => {
    if (inventoryDetails) {
      return inventoryDetails.grandTotal;
    }
    if (part.stockLevels) {
      return part.stockLevels.raw + part.stockLevels.hardening +
             part.stockLevels.grinding + part.stockLevels.finished +
             part.stockLevels.rejected;
    }
    return part.currentStock || 0;
  };

  // Get stock levels - prefer detailed inventory data if available
  const getStockLevels = () => {
    if (inventoryDetails) {
      return inventoryDetails.totals;
    }
    return part.stockLevels;
  };

  // Get stock status
  const getStockStatus = () => {
    const stockLevels = getStockLevels();
    const finishedStock = stockLevels?.finished || part.currentStock || 0;
    const reorderLevel = part.reorderLevel || 0;

    if (finishedStock === 0) return { status: 'Out of Stock', variant: 'destructive' as const };
    if (finishedStock <= reorderLevel) return { status: 'Low Stock', variant: 'warning' as const };
    return { status: 'In Stock', variant: 'success' as const };
  };

  const stockStatus = getStockStatus();

  // Main content sections
  const mainContent = (
    <>
      {/* Basic Information */}
      <StandardizedSection title="Basic Information">
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Part Number
              </label>
              <p className="font-mono text-sm bg-muted px-2 py-1 rounded">
                {part.partNumber}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Status
              </label>
              <div className="mt-1">
                <Badge variant={getStatusVariant(part.inventory?.abcClassification)}>
                  {part.inventory?.abcClassification || 'Active'}
                </Badge>
              </div>
            </div>
          </div>

          {part.businessName && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Business Name
              </label>
              <p className="text-sm text-foreground">
                {part.businessName}
              </p>
            </div>
          )}

          {part.description && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Description
              </label>
              <p className="text-sm text-foreground">
                {part.description}
              </p>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Unit of Measure
              </label>
              <p className="text-sm text-foreground">
                {part.unitOfMeasure || 'pcs'}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Reorder Level
              </label>
              <p className="text-sm text-foreground">
                {part.reorderLevel || 'Not set'}
              </p>
            </div>
          </div>
        </div>
      </StandardizedSection>

      {/* Stock Levels */}
      <StandardizedSection
        title="Stock Levels"
        description="Inventory across different manufacturing stages and warehouses"
        icon={<BarChart3 className="h-5 w-5" />}
      >
        {isLoadingInventory ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <span className="ml-2 text-sm text-muted-foreground">Loading inventory details...</span>
          </div>
        ) : inventoryError ? (
          <div className="text-center py-8">
            <AlertTriangle className="h-12 w-12 text-red-500/50 mx-auto mb-4" />
            <p className="text-sm text-red-600 dark:text-red-400">
              Failed to load detailed inventory: {inventoryError}
            </p>
            <p className="text-lg font-semibold mt-2">
              Fallback Stock: {part.currentStock || 0}
            </p>
          </div>
        ) : inventoryDetails && ((inventoryDetails.warehouseCount && inventoryDetails.warehouseCount > 0) || inventoryDetails.warehouses?.length > 0) ? (
          <div className="space-y-6">
            {/* Stock Levels Summary */}
            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-3">Total Across All Warehouses</h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 dark:bg-blue-950/20 p-3 rounded-lg">
                  <div className="text-sm font-medium text-blue-700 dark:text-blue-300">Raw Materials</div>
                  <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                    {inventoryDetails.stockLevelsSummary?.raw || inventoryDetails.totals?.raw || 0}
                  </div>
                </div>
                <div className="bg-orange-50 dark:bg-orange-950/20 p-3 rounded-lg">
                  <div className="text-sm font-medium text-orange-700 dark:text-orange-300">In Hardening</div>
                  <div className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                    {inventoryDetails.stockLevelsSummary?.hardening || inventoryDetails.totals?.hardening || 0}
                  </div>
                </div>
                <div className="bg-purple-50 dark:bg-purple-950/20 p-3 rounded-lg">
                  <div className="text-sm font-medium text-purple-700 dark:text-purple-300">In Grinding</div>
                  <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                    {inventoryDetails.stockLevelsSummary?.grinding || inventoryDetails.totals?.grinding || 0}
                  </div>
                </div>
                <div className="bg-green-50 dark:bg-green-950/20 p-3 rounded-lg">
                  <div className="text-sm font-medium text-green-700 dark:text-green-300">Finished Goods</div>
                  <div className="text-2xl font-bold text-green-900 dark:text-green-100">
                    {inventoryDetails.stockLevelsSummary?.finished || inventoryDetails.totals?.finished || 0}
                  </div>
                </div>
                <div className="bg-red-50 dark:bg-red-950/20 p-3 rounded-lg">
                  <div className="text-sm font-medium text-red-700 dark:text-red-300">Rejected</div>
                  <div className="text-2xl font-bold text-red-900 dark:text-red-100">
                    {inventoryDetails.stockLevelsSummary?.rejected || inventoryDetails.totals?.rejected || 0}
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-950/20 p-3 rounded-lg">
                  <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Total Stock</div>
                  <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {inventoryDetails.totalStockAllWarehouses || inventoryDetails.grandTotal || 0}
                  </div>
                </div>
              </div>
            </div>

            {/* Warehouse Breakdown */}
            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-2">
                <Warehouse className="h-4 w-4" />
                {inventoryDetails.warehouses?.some(w => w.locations?.length > 0) ?
                  'Breakdown by Warehouse & Location' :
                  `Breakdown by Warehouse (${inventoryDetails.warehouseCount || inventoryDetails.warehouses?.length || 0} locations)`}
              </h4>
              <div className="space-y-4">
                {(inventoryDetails.warehouses || inventoryDetails.warehouseBreakdown || []).map((warehouse) => (
                  <div key={warehouse.warehouseId} className="border border-border rounded-lg p-4 bg-card">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <h5 className="font-medium">{warehouse.warehouseName}</h5>
                        {warehouse.warehouseLocation && (
                          <span className="text-sm text-muted-foreground">
                            ({warehouse.warehouseLocation})
                          </span>
                        )}
                      </div>
                      <Badge variant="outline">
                        Total: {warehouse.totalStock || warehouse.totalQuantity || 0}
                      </Badge>
                    </div>

                    {/* New location-based breakdown */}
                    {warehouse.locations && warehouse.locations.length > 0 ? (
                      <div className="space-y-3">
                        {warehouse.locations.map((location) => (
                          <div key={location.locationId} className="border-l-4 border-l-primary/20 pl-4">
                            <div className="flex items-center justify-between mb-2">
                              <h6 className="font-medium text-sm flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                {location.locationName}
                              </h6>
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
                              {location.stockTypes.map((stock) => (
                                <div key={stock.stockType} className="text-center p-2 bg-muted/50 rounded">
                                  <div className="font-semibold">{stock.quantity}</div>
                                  <div className="text-xs text-muted-foreground capitalize">{stock.stockType}</div>
                                  {stock.safetyStockLevel && (
                                    <div className="text-xs text-orange-600">
                                      Safety: {stock.safetyStockLevel}
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      /* Legacy warehouse-level breakdown */
                      <div className="grid grid-cols-5 gap-2">
                        <div className="text-center p-2 bg-blue-50 dark:bg-blue-950/20 rounded">
                          <div className="text-xs text-blue-700 dark:text-blue-300">Raw</div>
                          <div className="font-semibold text-blue-900 dark:text-blue-100">
                            {warehouse.stockLevels?.raw || 0}
                          </div>
                        </div>
                        <div className="text-center p-2 bg-orange-50 dark:bg-orange-950/20 rounded">
                          <div className="text-xs text-orange-700 dark:text-orange-300">Hardening</div>
                          <div className="font-semibold text-orange-900 dark:text-orange-100">
                            {warehouse.stockLevels?.hardening || 0}
                          </div>
                        </div>
                        <div className="text-center p-2 bg-purple-50 dark:bg-purple-950/20 rounded">
                          <div className="text-xs text-purple-700 dark:text-purple-300">Grinding</div>
                          <div className="font-semibold text-purple-900 dark:text-purple-100">
                            {warehouse.stockLevels?.grinding || 0}
                          </div>
                        </div>
                        <div className="text-center p-2 bg-green-50 dark:bg-green-950/20 rounded">
                          <div className="text-xs text-green-700 dark:text-green-300">Finished</div>
                          <div className="font-semibold text-green-900 dark:text-green-100">
                            {warehouse.stockLevels?.finished || 0}
                          </div>
                        </div>
                        <div className="text-center p-2 bg-red-50 dark:bg-red-950/20 rounded">
                          <div className="text-xs text-red-700 dark:text-red-300">Rejected</div>
                          <div className="font-semibold text-red-900 dark:text-red-100">
                            {warehouse.stockLevels?.rejected || 0}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : getStockLevels() ? (
          <div className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 dark:bg-blue-950/20 p-3 rounded-lg">
                <div className="text-sm font-medium text-blue-700 dark:text-blue-300">Raw Materials</div>
                <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                  {getStockLevels()!.raw}
                </div>
              </div>
              <div className="bg-orange-50 dark:bg-orange-950/20 p-3 rounded-lg">
                <div className="text-sm font-medium text-orange-700 dark:text-orange-300">In Hardening</div>
                <div className="text-2xl font-bold text-orange-900 dark:text-orange-100">
                  {getStockLevels()!.hardening}
                </div>
              </div>
              <div className="bg-purple-50 dark:bg-purple-950/20 p-3 rounded-lg">
                <div className="text-sm font-medium text-purple-700 dark:text-purple-300">In Grinding</div>
                <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                  {getStockLevels()!.grinding}
                </div>
              </div>
              <div className="bg-green-50 dark:bg-green-950/20 p-3 rounded-lg">
                <div className="text-sm font-medium text-green-700 dark:text-green-300">Finished Goods</div>
                <div className="text-2xl font-bold text-green-900 dark:text-green-100">
                  {getStockLevels()!.finished}
                </div>
              </div>
              <div className="bg-red-50 dark:bg-red-950/20 p-3 rounded-lg">
                <div className="text-sm font-medium text-red-700 dark:text-red-300">Rejected</div>
                <div className="text-2xl font-bold text-red-900 dark:text-red-100">
                  {getStockLevels()!.rejected}
                </div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-950/20 p-3 rounded-lg">
                <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Total Stock</div>
                <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {getTotalStock()}
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <Package className="h-12 w-12 text-muted-foreground/50 mx-auto mb-4" />
            <p className="text-sm text-muted-foreground">
              Stock level details not available
            </p>
            <p className="text-lg font-semibold mt-2">
              Current Stock: {part.currentStock || 0}
            </p>
          </div>
        )}
      </StandardizedSection>
    </>
  );

  // Sidebar content sections
  const sidebarContent = (
    <>
      {/* Quick Stats */}
      <StandardizedSection title="Quick Stats">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Stock Status</span>
            <Badge variant={stockStatus.variant}>
              {stockStatus.status}
            </Badge>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Available</span>
            <div className="flex items-center gap-1">
              <Package className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">
                {getStockLevels()?.finished || part.currentStock || 0}
              </span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Total Stock</span>
            <span className="font-medium">{getTotalStock()}</span>
          </div>
          {inventoryDetails && inventoryDetails.warehouseCount && inventoryDetails.warehouseCount > 1 && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Warehouses</span>
              <div className="flex items-center gap-1">
                <Warehouse className="h-4 w-4 text-muted-foreground" />
                <span className="font-medium">{inventoryDetails.warehouseCount}</span>
              </div>
            </div>
          )}
        </div>
      </StandardizedSection>

      {/* Supplier Information */}
      {part.supplier && part.supplier.name && (
        <StandardizedSection 
          title="Supplier" 
          icon={<Tag className="h-5 w-5" />}
        >
          <div className="space-y-2">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Supplier Name
              </label>
              <p className="text-sm text-foreground">
                {part.supplier.name}
              </p>
            </div>
          </div>
        </StandardizedSection>
      )}

      {/* Inventory Settings */}
      <StandardizedSection 
        title="Inventory Settings"
        icon={<Warehouse className="h-5 w-5" />}
      >
        <div className="space-y-3">
          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Safety Stock Level
            </label>
            <p className="text-sm text-foreground">
              {part.inventory?.safetyStockLevel || 'Not set'}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Maximum Stock Level
            </label>
            <p className="text-sm text-foreground">
              {part.inventory?.maximumStockLevel || 'Not set'}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">
              ABC Classification
            </label>
            <p className="text-sm text-foreground">
              {part.inventory?.abcClassification || 'Not classified'}
            </p>
          </div>
        </div>
      </StandardizedSection>

      {/* Timeline */}
      <StandardizedSection
        title="Timeline"
        icon={<Calendar className="h-5 w-5" />}
      >
        <div className="space-y-3">
          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Created
            </label>
            <p className="text-sm text-foreground">
              {formatDate(part.createdAt)}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Last Modified
            </label>
            <p className="text-sm text-foreground">
              {formatDate(part.updatedAt)}
            </p>
          </div>
          {part.inventory?.lastStockUpdate && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Last Stock Update
              </label>
              <p className="text-sm text-foreground">
                {formatDate(part.inventory.lastStockUpdate)}
              </p>
            </div>
          )}
        </div>
      </StandardizedSection>
    </>
  );

  return (
    <StandardizedViewContent isInTab={isInTab}>
      <StandardizedViewGrid
        mainContent={mainContent}
        sidebarContent={sidebarContent}
      />
    </StandardizedViewContent>
  );
}
