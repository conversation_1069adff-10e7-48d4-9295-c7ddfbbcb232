'use client';

import { DollarSign, Tag, Calendar, Package, Layers } from 'lucide-react';

import { Badge } from '@/app/components/data-display/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
// BomViewerButton removed - BOM functionality now integrated into BaseViewModal tabs
import { ProductStatusBadge } from '@/app/components/status/ProductStatusBadge';
import { Product } from '@/app/components/tables/ProductsTable/types';
import { StandardizedViewContent, StandardizedViewGrid, StandardizedSection } from './StandardizedViewContent';

interface ProductViewContentProps {
  product: Product;
  /**
   * Whether this content is being rendered inside a tab
   */
  isInTab?: boolean;
}

/**
 * Content component for displaying product details in a view modal
 * Contains all the specific layout and information for product viewing
 */
export function ProductViewContent({ product, isInTab = false }: ProductViewContentProps) {
  // Format price for display
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  // Format date for display
  const formatDate = (date: string | Date | undefined) => {
    if (!date) return 'Not set';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Main content sections
  const mainContent = (
    <>
      {/* Basic Information */}
      <StandardizedSection title="Basic Information">
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Product Code
              </label>
              <p className="font-mono text-sm bg-muted px-2 py-1 rounded">
                {product.productCode}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Status
              </label>
              <div className="mt-1">
                <ProductStatusBadge product={product} />
              </div>
            </div>
          </div>

          {product.description && (
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Description
              </label>
              <p className="text-sm text-foreground mt-1">
                {product.description}
              </p>
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Created Date
              </label>
              <p className="text-sm text-foreground">
                {formatDate(product.createdAt)}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Last Updated
              </label>
              <p className="text-sm text-foreground">
                {formatDate(product.updatedAt)}
              </p>
            </div>
          </div>
        </div>
      </StandardizedSection>

      {/* Pricing Information */}
      <StandardizedSection
        title="Pricing Information"
        icon={<DollarSign className="h-5 w-5" />}
      >
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Cost Price
              </label>
              <p className="text-lg font-semibold text-destructive">
                {formatPrice(product.costPrice ?? 0)}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Selling Price
              </label>
              <p className="text-lg font-semibold text-success">
                {formatPrice(product.sellingPrice)}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Profit Margin
              </label>
              <p className="text-sm font-medium">
                {formatPrice(product.sellingPrice - (product.costPrice ?? 0))}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-muted-foreground">
                Margin %
              </label>
              <p className="text-sm font-medium">
                {((product.sellingPrice - (product.costPrice ?? 0)) / product.sellingPrice * 100).toFixed(1)}%
              </p>
            </div>
          </div>
        </div>
      </StandardizedSection>

      {/* Category Information */}
      {product.category && (
        <StandardizedSection
          title="Category Information"
          icon={<Tag className="h-5 w-5" />}
        >
          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Category
            </label>
            <p className="text-sm text-foreground mt-1">
              {(() => {
                const category = product.category as any;
                if (category && typeof category === 'object' && category.name) {
                  return category.name;
                } else if (typeof category === 'string') {
                  return category;
                } else {
                  return 'Uncategorized';
                }
              })()}
            </p>
          </div>
        </StandardizedSection>
      )}

      {/* Bill of Materials */}
      <StandardizedSection
        title="Bill of Materials"
        description="Hierarchical breakdown of all components in this product"
      >
        <div className="flex flex-col items-center justify-center py-8 space-y-4">
          {(product.components && Array.isArray(product.components) && product.components.length > 0) ? (
            <>
              <div className="text-center">
                <p className="text-sm text-muted-foreground mb-2">
                  This product contains {product.components.length} component{product.components.length !== 1 ? 's' : ''}
                </p>
                <div className="flex items-center justify-center gap-2 p-3 bg-primary/5 border border-primary/20 rounded-lg">
                  <Layers className="h-4 w-4 text-primary" />
                  <p className="text-sm text-primary font-medium">
                    View detailed Bill of Materials in the BOM tab above
                  </p>
                </div>
              </div>
            </>
          ) : (
            <>
              <Package className="h-12 w-12 text-muted-foreground/50" />
              <p className="text-sm text-muted-foreground text-center">
                No components defined for this product
              </p>
            </>
          )}
        </div>
      </StandardizedSection>
    </>
  );

  // Sidebar content sections
  const sidebarContent = (
    <>
      {/* Quick Stats */}
      <StandardizedSection title="Quick Stats">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Status</span>
            <ProductStatusBadge product={product} size="sm" />
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Price</span>
            <span className="font-semibold text-success">
              {formatPrice(product.sellingPrice)}
            </span>
          </div>
          {product.inventory?.currentStock !== undefined && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Stock</span>
              <Badge variant="outline">
                {product.inventory.currentStock} units
              </Badge>
            </div>
          )}
          {product.components && Array.isArray(product.components) && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Components</span>
              <Badge variant="secondary">
                {product.components.length} items
              </Badge>
            </div>
          )}
        </div>
      </StandardizedSection>

      {/* Timeline */}
      <StandardizedSection
        title="Timeline"
        icon={<Calendar className="h-5 w-5" />}
      >
        <div className="space-y-3">
          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Created
            </label>
            <p className="text-sm text-foreground">
              {formatDate(product.createdAt)}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Last Modified
            </label>
            <p className="text-sm text-foreground">
              {formatDate(product.updatedAt)}
            </p>
          </div>
        </div>
      </StandardizedSection>
    </>
  );

  return (
    <StandardizedViewContent isInTab={isInTab}>
      <StandardizedViewGrid
        mainContent={mainContent}
        sidebarContent={sidebarContent}
      />
    </StandardizedViewContent>
  );
}
