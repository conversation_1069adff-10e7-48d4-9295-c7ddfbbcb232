'use client';

import { cn } from '@/app/lib/utils';

interface StandardizedViewContentProps {
  /**
   * The main content to display
   */
  children: React.ReactNode;
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Whether this content is being rendered inside a tab
   * This affects padding and spacing to ensure consistency
   */
  isInTab?: boolean;
}

/**
 * Standardized content wrapper for view modals
 * Ensures consistent layout and spacing whether content is in tabs or direct modal content
 * 
 * This wrapper provides:
 * - Consistent padding and spacing
 * - Proper grid layout support
 * - Unified visual hierarchy
 * - Responsive design patterns
 */
export function StandardizedViewContent({ 
  children, 
  className,
  isInTab = false 
}: StandardizedViewContentProps) {
  return (
    <div className={cn(
      // Base container styles
      "w-full",
      
      // Conditional padding based on context
      // Tabs already have padding, so we adjust accordingly
      isInTab ? "p-6" : "p-0",
      
      // Ensure proper scrolling if content overflows
      "overflow-y-auto",
      
      // Custom classes
      className
    )}>
      {/* Content wrapper with consistent spacing */}
      <div className="space-y-4">
        {children}
      </div>
    </div>
  );
}

/**
 * Standardized grid layout for entity view content
 * Provides consistent 3-column responsive layout
 */
interface StandardizedViewGridProps {
  /**
   * Main content area (left side, 2/3 width on large screens)
   */
  mainContent: React.ReactNode;
  
  /**
   * Sidebar content (right side, 1/3 width on large screens)
   */
  sidebarContent: React.ReactNode;
  
  /**
   * Additional CSS classes
   */
  className?: string;
}

export function StandardizedViewGrid({ 
  mainContent, 
  sidebarContent, 
  className 
}: StandardizedViewGridProps) {
  return (
    <div className={cn(
      "grid grid-cols-1 lg:grid-cols-3 gap-6",
      className
    )}>
      {/* Main Content Area */}
      <div className="lg:col-span-2 space-y-6">
        {mainContent}
      </div>
      
      {/* Sidebar */}
      <div className="space-y-6">
        {sidebarContent}
      </div>
    </div>
  );
}

/**
 * Standardized section wrapper for consistent card styling
 */
interface StandardizedSectionProps {
  /**
   * Section title
   */
  title: string;

  /**
   * Optional section description
   */
  description?: string;

  /**
   * Optional icon for the section
   */
  icon?: React.ReactNode;

  /**
   * Section content
   */
  children: React.ReactNode;

  /**
   * Additional CSS classes for the card
   */
  className?: string;

  /**
   * Whether to use compact styling (smaller padding and spacing)
   */
  compact?: boolean;
}

export function StandardizedSection({
  title,
  description,
  icon,
  children,
  className,
  compact = false
}: StandardizedSectionProps) {
  return (
    <div className={cn(
      "bg-card border border-border rounded-lg shadow-sm",
      className
    )}>
      {/* Header */}
      <div className={cn(
        "border-b border-border",
        compact ? "p-3" : "p-6"
      )}>
        <div className="flex items-center gap-2">
          {icon && (
            <div className="flex-shrink-0">
              {icon}
            </div>
          )}
          <div>
            <h3 className={cn(
              "font-semibold text-foreground",
              compact ? "text-base" : "text-lg"
            )}>
              {title}
            </h3>
            {description && (
              <p className="text-sm text-muted-foreground mt-1">
                {description}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className={cn(
        compact ? "p-3" : "p-6"
      )}>
        {children}
      </div>
    </div>
  );
}
