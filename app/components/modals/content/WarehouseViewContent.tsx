'use client';

import { Building2, MapPin, User, Phone, Package, Calendar, CheckCircle, XCircle, Hash, Warehouse } from 'lucide-react';
import { useState, useEffect } from 'react';

import { Badge } from '@/app/components/data-display/badge';
import { StandardizedViewContent, StandardizedViewGrid, StandardizedSection } from './StandardizedViewContent';
import { WarehouseDisplayData } from '@/app/components/forms/WarehouseForm';
import { LocationFormData } from '@/app/components/forms/WarehouseForm/types';
import { LocationCard } from '@/app/components/cards/LocationCard';

interface WarehouseViewContentProps {
  warehouse: WarehouseDisplayData;
  /**
   * Whether this content is being rendered inside a tab
   */
  isInTab?: boolean;
}

/**
 * Content component for displaying warehouse details in a view modal
 * Contains all the specific layout and information for warehouse viewing
 */
export function WarehouseViewContent({ warehouse, isInTab = false }: WarehouseViewContentProps) {
  // State for warehouse locations
  const [locations, setLocations] = useState<LocationFormData[]>([]);
  const [isLoadingLocations, setIsLoadingLocations] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);

  // Fetch locations when component mounts
  useEffect(() => {
    const fetchLocations = async () => {
      if (!warehouse._id) return;

      setIsLoadingLocations(true);
      setLocationError(null);

      try {
        const response = await fetch(`/api/warehouses/${warehouse._id}/locations`);

        if (!response.ok) {
          throw new Error(`Failed to fetch locations: ${response.status}`);
        }

        const data = await response.json();

        if (data.data && Array.isArray(data.data)) {
          setLocations(data.data);
        } else {
          setLocations([]);
        }
      } catch (error) {
        console.error('Error fetching warehouse locations:', error);
        setLocationError('Failed to load location data');
        setLocations([]);
      } finally {
        setIsLoadingLocations(false);
      }
    };

    fetchLocations();
  }, [warehouse._id]);

  // Format date for display
  const formatDate = (date: string | Date | undefined) => {
    if (!date) return 'Not set';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Format capacity for display
  const formatCapacity = (capacity: number | undefined) => {
    if (!capacity || capacity === 0) return 'Not specified';
    return capacity.toLocaleString();
  };

  // Main content sections
  const mainContent = (
    <>
      {/* Basic Information */}
      <StandardizedSection
        title="Basic Information"
        icon={<Building2 className="h-5 w-5" />}
      >
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Warehouse Name
            </label>
            <p className="text-lg font-semibold text-foreground">
              {warehouse.name}
            </p>
          </div>
          
          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Location ID
            </label>
            <p className="font-mono text-sm text-foreground flex items-center gap-2">
              <Hash className="h-4 w-4 text-muted-foreground" />
              {warehouse.location_id}
            </p>
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Location Details
            </label>
            <p className="text-foreground flex items-center gap-2">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              {warehouse.location || 'Not specified'}
            </p>
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Description
            </label>
            <p className="text-foreground">
              {warehouse.description || 'No description provided'}
            </p>
          </div>
        </div>
      </StandardizedSection>

      {/* Operations Information */}
      <StandardizedSection
        title="Operations"
        icon={<User className="h-5 w-5" />}
      >
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Manager
            </label>
            <p className="text-foreground flex items-center gap-2">
              <User className="h-4 w-4 text-muted-foreground" />
              {warehouse.manager || 'Not assigned'}
            </p>
          </div>
          
          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Contact Information
            </label>
            <p className="text-foreground flex items-center gap-2">
              <Phone className="h-4 w-4 text-muted-foreground" />
              {warehouse.contact || 'Not provided'}
            </p>
          </div>

          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Capacity
            </label>
            <p className="text-foreground flex items-center gap-2">
              <Package className="h-4 w-4 text-muted-foreground" />
              {formatCapacity(warehouse.capacity)} units
            </p>
          </div>
        </div>
      </StandardizedSection>

      {/* Warehouse Locations */}
      <StandardizedSection
        title="Warehouse Locations"
        icon={<MapPin className="h-5 w-5" />}
      >
        {isLoadingLocations ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-sm text-muted-foreground">Loading locations...</div>
          </div>
        ) : locationError ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-sm text-red-600 dark:text-red-400">{locationError}</div>
          </div>
        ) : locations.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {locations.map((location, index) => (
              <LocationCard
                key={location._id || index}
                location={location}
                isReadOnly={true}
              />
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <MapPin className="h-12 w-12 text-muted-foreground/50 mb-3" />
            <p className="text-sm text-muted-foreground mb-1">No locations configured for this warehouse</p>
            <p className="text-xs text-muted-foreground">Edit the warehouse to add specific locations</p>
          </div>
        )}
      </StandardizedSection>
    </>
  );

  // Sidebar content
  const sidebarContent = (
    <>
      {/* Status & Configuration */}
      <StandardizedSection
        title="Status & Configuration"
        icon={<Warehouse className="h-5 w-5" />}
      >
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Status</span>
            {warehouse.isActive !== false ? (
              <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                <CheckCircle className="h-3 w-3 mr-1" />
                Active
              </Badge>
            ) : (
              <Badge variant="secondary" className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100">
                <XCircle className="h-3 w-3 mr-1" />
                Inactive
              </Badge>
            )}
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Bin Tracking</span>
            {warehouse.isBinTracked ? (
              <Badge variant="default" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100">
                <CheckCircle className="h-3 w-3 mr-1" />
                Enabled
              </Badge>
            ) : (
              <Badge variant="outline">
                <XCircle className="h-3 w-3 mr-1" />
                Disabled
              </Badge>
            )}
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Locations</span>
            <div className="flex items-center gap-1">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">
                {isLoadingLocations ? '...' : locations.length}
              </span>
            </div>
          </div>
        </div>
      </StandardizedSection>

      {/* Timeline */}
      <StandardizedSection
        title="Timeline"
        icon={<Calendar className="h-5 w-5" />}
      >
        <div className="space-y-3">
          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Created
            </label>
            <p className="text-sm text-foreground">
              {formatDate(warehouse.createdAt)}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium text-muted-foreground">
              Last Modified
            </label>
            <p className="text-sm text-foreground">
              {formatDate(warehouse.updatedAt)}
            </p>
          </div>
        </div>
      </StandardizedSection>
    </>
  );

  return (
    <StandardizedViewContent isInTab={isInTab}>
      <StandardizedViewGrid
        mainContent={mainContent}
        sidebarContent={sidebarContent}
      />
    </StandardizedViewContent>
  );
}
