'use client';

import {
  FileText,
  Package,
  User,
  Warehouse,
  TrendingUp,
  TrendingDown,
  BarChart3
} from 'lucide-react';
import { format } from 'date-fns';

import { Badge } from '@/app/components/data-display/badge';
import { StandardizedViewContent, StandardizedSection } from './StandardizedViewContent';
import { type InventoryTransactionColumnData } from '@/app/components/data-display/data-table';
import {
  getTransactionTypeIcon,
  getTransactionTypeBadgeVariant,
  getQuantityChangeDisplay,
  getReferenceTypeInfo
} from '@/app/utils/transactionUtils';

interface TransactionViewContentProps {
  transaction: InventoryTransactionColumnData;
  /**
   * Whether this content is being rendered inside a tab
   */
  isInTab?: boolean;
}

/**
 * Get the appropriate icon and color for quantity change (local override for specific styling)
 */
function getLocalQuantityChangeDisplay(quantity: number) {
  if (quantity > 0) {
    return {
      icon: <TrendingUp className="h-4 w-4 text-green-600" />,
      color: 'text-green-600',
      prefix: '+'
    };
  } else if (quantity < 0) {
    return {
      icon: <TrendingDown className="h-4 w-4 text-red-600" />,
      color: 'text-red-600',
      prefix: ''
    };
  } else {
    return {
      icon: <BarChart3 className="h-4 w-4 text-gray-600" />,
      color: 'text-gray-600',
      prefix: ''
    };
  }
}

export function TransactionViewContent({ transaction, isInTab = false }: TransactionViewContentProps) {
  const quantityDisplay = getLocalQuantityChangeDisplay(transaction.quantity);

  return (
    <StandardizedViewContent isInTab={isInTab}>
      <div className="space-y-3">
        {/* Transaction Overview - Compact Header */}
        <div className="bg-card border border-border rounded-lg shadow-sm">
          <div className="p-4 border-b border-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {getTransactionTypeIcon(transaction.transactionType)}
                <div>
                  <h3 className="text-lg font-semibold text-foreground">
                    {transaction.transactionType}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {format(new Date(transaction.transactionDate), 'PPP p')}
                  </p>
                </div>
              </div>
              <Badge variant={getTransactionTypeBadgeVariant(transaction.transactionType)}>
                {transaction.transactionType}
              </Badge>
            </div>
          </div>
          <div className="p-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <div>
                <p className="text-sm text-muted-foreground">Transaction ID</p>
                <p className="font-mono text-sm">{transaction.transactionId || transaction._id}</p>
              </div>
              <div className="flex items-center gap-2">
                {quantityDisplay.icon}
                <div>
                  <p className="text-sm text-muted-foreground">Quantity Change</p>
                  <span className={`font-bold ${quantityDisplay.color}`}>
                    {quantityDisplay.prefix}{transaction.quantity} units
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Item & Warehouse Information - Combined Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
          <StandardizedSection
            title="Item Details"
            icon={<Package className="h-4 w-4" />}
            className="h-fit"
            compact={true}
          >
            <div className="space-y-2">
              <div>
                <p className="text-sm text-muted-foreground">Item Name</p>
                <p className="font-medium text-foreground">{transaction.itemName}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Part Number</p>
                <p className="font-mono text-sm">{transaction.partNumber || transaction.itemId}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Item Type</p>
                <Badge variant="outline">{transaction.itemType}</Badge>
              </div>
            </div>
          </StandardizedSection>

          <StandardizedSection
            title="Warehouse Details"
            icon={<Warehouse className="h-4 w-4" />}
            className="h-fit"
            compact={true}
          >
            <div className="space-y-2">
              <div>
                <p className="text-sm text-muted-foreground">Warehouse Name</p>
                <p className="font-medium text-foreground">{transaction.warehouseName}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Location ID</p>
                <p className="font-mono text-sm">{transaction.warehouseLocationId || transaction.warehouseId}</p>
              </div>
            </div>
          </StandardizedSection>
        </div>

        {/* Stock Movement - Compact */}
        <StandardizedSection
          title="Stock Movement"
          icon={quantityDisplay.icon}
          compact={true}
        >
          <div className="grid grid-cols-2 gap-3">
            <div className="p-3 bg-muted/50 rounded-lg text-center">
              <p className="text-sm text-muted-foreground">Previous Stock</p>
              <p className="font-semibold text-lg">{transaction.previousStock}</p>
            </div>
            <div className="p-3 bg-muted/50 rounded-lg text-center">
              <p className="text-sm text-muted-foreground">New Stock</p>
              <p className="font-semibold text-lg">{transaction.newStock}</p>
            </div>
          </div>
        </StandardizedSection>

        {/* Reference & User Information - Combined Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
          {/* Reference Information */}
          {(transaction.referenceNumber || transaction.referenceType) && (
            <StandardizedSection
              title="Reference Information"
              icon={<FileText className="h-4 w-4" />}
              className="h-fit"
              compact={true}
            >
              <div className="space-y-2">
                {transaction.referenceType && (
                  <div>
                    <p className="text-sm text-muted-foreground">Reference Type</p>
                    <Badge variant="outline">{transaction.referenceType}</Badge>
                  </div>
                )}
                {transaction.referenceNumber && (
                  <div>
                    <p className="text-sm text-muted-foreground">Reference Number</p>
                    <p className="font-mono text-sm">{transaction.referenceNumber}</p>
                  </div>
                )}
                {transaction.reference && (
                  <div>
                    <p className="text-sm text-muted-foreground">Full Reference</p>
                    <p className="text-sm">{transaction.reference}</p>
                  </div>
                )}
              </div>
            </StandardizedSection>
          )}

          {/* User Information */}
          <StandardizedSection
            title="User Information"
            icon={<User className="h-4 w-4" />}
            className="h-fit"
            compact={true}
          >
            <div className="space-y-2">
              <div>
                <p className="text-sm text-muted-foreground">User Name</p>
                <p className="font-medium text-foreground">{transaction.userName}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">User ID</p>
                <p className="font-mono text-sm">{transaction.userId}</p>
              </div>
            </div>
          </StandardizedSection>
        </div>

        {/* Notes */}
        {transaction.notes && transaction.notes !== '-' && (
          <StandardizedSection
            title="Notes"
            icon={<FileText className="h-4 w-4" />}
            compact={true}
          >
            <div className="p-3 bg-muted/50 rounded-lg">
              <p className="text-sm whitespace-pre-wrap">{transaction.notes}</p>
            </div>
          </StandardizedSection>
        )}
      </div>
    </StandardizedViewContent>
  );
}
