'use client';

import { useState } from 'react';
import { Eye } from 'lucide-react';

import { Button } from '@/app/components/forms/Button';
import { BaseViewModal } from './BaseViewModal';
import { type ModalComponentItem } from '@/app/components/bom/EnhancedBomViewer';

interface BaseViewButtonProps {
  /**
   * The title to display in the modal header
   */
  title: string;
  
  /**
   * The subtitle to display under the title
   */
  subtitle?: string;
  
  /**
   * The icon to display in the modal header
   */
  icon: React.ReactNode;
  
  /**
   * The content to display in the modal body
   */
  children: React.ReactNode;
  
  /**
   * Button variant
   */
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | 'success' | 'warning' | 'info';

  /**
   * Button size
   */
  size?: 'default' | 'sm' | 'lg' | 'icon';
  
  /**
   * Additional CSS classes for the button
   */
  className?: string;
  
  /**
   * Custom button content (overrides default Eye icon + "View" text)
   */
  buttonContent?: React.ReactNode;
  
  /**
   * Maximum width of the modal (default: max-w-4xl)
   */
  maxWidth?: string;
  
  /**
   * Additional CSS classes for the modal container
   */
  modalClassName?: string;

  /**
   * BOM (Bill of Materials) data for automatic BOM tab integration
   */
  bomData?: ModalComponentItem[];

  /**
   * Callback for lazy loading assembly components in BOM viewer
   */
  onLoadAssemblyComponents?: (assemblyId: string) => Promise<ModalComponentItem[]>;

  /**
   * Whether to show BOM summary statistics
   */
  showBomSummary?: boolean;

  /**
   * Custom height for BOM tree viewer
   */
  bomViewerHeight?: string;

  /**
   * Whether BOM search is enabled
   */
  enableBomSearch?: boolean;
}

/**
 * Base button component that opens a view modal
 * Provides common functionality for view buttons with customizable content
 */
export function BaseViewButton({
  title,
  subtitle,
  icon,
  children,
  variant = 'default',
  size = 'sm',
  className,
  buttonContent,
  maxWidth,
  modalClassName,
  bomData,
  onLoadAssemblyComponents,
  showBomSummary = true,
  bomViewerHeight = '500px',
  enableBomSearch = true
}: BaseViewButtonProps) {
  const [isOpen, setIsOpen] = useState(false);

  const defaultButtonContent = (
    <>
      <Eye className="h-4 w-4 mr-1" />
      View
    </>
  );

  return (
    <BaseViewModal
      isOpen={isOpen}
      onClose={() => setIsOpen(false)}
      title={title}
      subtitle={subtitle || ''}
      icon={icon}
      maxWidth={maxWidth ?? 'max-w-4xl'}
      className={modalClassName ?? ''}
      {...(bomData && { bomData })}
      {...(onLoadAssemblyComponents && { onLoadAssemblyComponents })}
      showBomSummary={showBomSummary}
      bomViewerHeight={bomViewerHeight}
      enableBomSearch={enableBomSearch}
      trigger={
        <Button
          variant={variant}
          size={size}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setIsOpen(true);
          }}
          className={className ?? ''}
        >
          {buttonContent || defaultButtonContent}
        </Button>
      }
    >
      {children}
    </BaseViewModal>
  );
}
