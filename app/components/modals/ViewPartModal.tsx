'use client';

import { Package } from 'lucide-react';

import { BaseViewModal } from './BaseViewModal';
import { BaseViewButton } from './BaseViewButton';
import { PartViewContent } from './content/PartViewContent';
import { InventoryColumnData } from '@/app/components/data-display/data-table/column-definitions';

interface ViewPartModalProps {
  part: InventoryColumnData;
  isOpen: boolean;
  onClose: () => void;
  trigger?: React.ReactNode;
}

/**
 * Modal component for viewing part details
 * Uses the standardized BaseViewModal pattern
 */
export function ViewPartModal({ part, isOpen, onClose, trigger }: ViewPartModalProps) {
  // Determine if content will be in tab mode (parts don't have BOM data)
  const isInTab = false;

  return (
    <BaseViewModal
      isOpen={isOpen}
      onClose={onClose}
      trigger={trigger}
      title={part.businessName || part.name}
      subtitle="Part Details"
      icon={<Package className="h-5 w-5 text-primary" />}
    >
      <PartViewContent part={part} isInTab={isInTab} />
    </BaseViewModal>
  );
}

interface ViewPartButtonProps {
  part: InventoryColumnData;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | 'success' | 'warning' | 'info';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  /**
   * Custom button content (overrides default content)
   */
  buttonContent?: React.ReactNode;
  /**
   * Children to render inside the button
   */
  children?: React.ReactNode;
}

/**
 * Button component that opens the ViewPartModal
 * Uses the standardized BaseViewButton pattern
 */
export function ViewPartButton({
  part,
  variant = 'default',
  size = 'sm',
  className,
  buttonContent,
  children
}: ViewPartButtonProps) {
  return (
    <BaseViewButton
      title={part.businessName || part.name}
      subtitle="Part Details"
      icon={<Package className="h-5 w-5 text-primary" />}
      variant={variant}
      size={size}
      className={className || ''}
      buttonContent={buttonContent || children}
    >
      <PartViewContent part={part} />
    </BaseViewButton>
  );
}
