'use client';

import { LazyUnifiedAssemblyForm } from '@/app/components/forms/LazyUnifiedAssemblyForm';
import { Button } from '@/app/components/forms/Button';
import { useTheme } from '@/app/context/ThemeContext';
import { Layers, LayoutGrid, Plus } from 'lucide-react';
import { useState } from 'react';

interface AssemblyFormModalProps {
  onSuccess?: () => void;
}

/**
 * Assembly Form Modal Component
 * Provides buttons to open the unified assembly form modal
 * and manages the modal state
 */
export function AssemblyFormModal({ onSuccess }: AssemblyFormModalProps) {
  const { theme } = useTheme();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
  const [assemblyId, setAssemblyId] = useState<string | undefined>(undefined);

  // Open modal for creating a new assembly
  const openCreateModal = () => {
    setModalMode('create');
    setAssemblyId(undefined);
    setIsModalOpen(true);
  };

  // Open modal for editing an existing assembly
  const openEditModal = (id: string) => {
    setModalMode('edit');
    setAssemblyId(id);
    setIsModalOpen(true);
  };

  // Close the modal
  const closeModal = () => {
    setIsModalOpen(false);
  };

  // Handle success - called when assembly is successfully saved
  const handleSuccess = () => {
    if (onSuccess) {
      onSuccess();
    }
  };

  return (
    <>
      {/* Create Assembly Buttons */}
      <div className="space-x-2">
        <Button
          variant="secondary"
          className="rounded-full"
          onClick={openCreateModal}
        >
          <Plus size={16} className="mr-2" />
          <span>Standard Assembly</span>
        </Button>

        {/* Hierarchical Part Entry */}
        <Button
          onClick={openCreateModal}
          className="rounded-full px-4 py-2 text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90"
        >
          <Layers size={16} className="mr-2" />
          <span>Hierarchical Assembly</span>
        </Button>

        {/* Visual Builder */}
        <Button
          variant="outline"
          className="rounded-full"
          onClick={openCreateModal}
        >
          <LayoutGrid size={16} className="mr-2" />
          <span>Visual Builder</span>
        </Button>
      </div>

      {/* Assembly Form Modal - Lazy Loaded */}
      <LazyUnifiedAssemblyForm
        isOpen={isModalOpen}
        onClose={closeModal}
        assemblyId={assemblyId}
        mode={modalMode}
        onSuccess={handleSuccess}
      />
    </>
  );
}

/**
 * Assembly Edit Button Component
 * Standalone button to edit a specific assembly
 */
export function AssemblyEditButton({ assemblyId, onSuccess }: { assemblyId: string, onSuccess?: () => void }) {
  const { theme } = useTheme();
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Enhanced openEditModal function with console logging for debugging
  const openEditModal = () => {
    console.log('Opening edit modal for assembly:', assemblyId);
    // Force any existing modals to close first
    document.body.click();
    setTimeout(() => {
      setIsModalOpen(true);
    }, 50);
  };

  const closeModal = () => {
    console.log('Closing edit modal for assembly:', assemblyId);
    setIsModalOpen(false);
  };

  // Handle success - called when assembly is successfully saved
  const handleSuccess = () => {
    if (onSuccess) {
      onSuccess();
    }
  };

  return (
    <>
      {/* Button with appropriate z-index and flex constraints */}
      <Button
        variant="outline"
        size="sm"
        onClick={openEditModal}
        className="relative z-10 flex-shrink-0 h-9 rounded-md px-3"
      >
        Edit
      </Button>

      {/* Modal with higher z-index - rendered at the root level */}
      <div className="fixed inset-0 z-0 pointer-events-none">
        <LazyUnifiedAssemblyForm
          isOpen={isModalOpen}
          onClose={closeModal}
          assemblyId={assemblyId}
          mode="edit"
          onSuccess={handleSuccess}
        />
      </div>
    </>
  );
}