'use client';

import React, { memo, useMemo } from 'react';
import { Package, Layers } from 'lucide-react';

import { BaseViewModal } from '../BaseViewModal';
import { BaseViewButton } from '../BaseViewButton';
import { getBomTabConfig, type BomTabConfig } from '@/app/components/bom/bomIntegrationHelpers';

/**
 * Generic entity type for modal content
 */
export interface EntityData {
  _id: string;
  name: string;
  [key: string]: any;
}

/**
 * Entity type configuration
 */
interface EntityTypeConfig {
  icon: React.ReactNode;
  subtitle: string;
  bomType: 'product' | 'assembly';
}

/**
 * Entity type configurations
 * Memoized for better performance
 */
const ENTITY_TYPE_CONFIGS: Record<string, EntityTypeConfig> = {
  product: {
    icon: <Package className="h-5 w-5 text-primary" />,
    subtitle: "Product Details",
    bomType: 'product'
  },
  assembly: {
    icon: <Layers className="h-5 w-5 text-primary" />,
    subtitle: "Assembly Details",
    bomType: 'assembly'
  }
};

/**
 * Props for StandardizedEntityModal
 */
interface StandardizedEntityModalProps {
  /**
   * Entity data to display
   */
  entity: EntityData;
  
  /**
   * Type of entity (product, assembly, etc.)
   */
  entityType: keyof typeof ENTITY_TYPE_CONFIGS;
  
  /**
   * Whether the modal is open
   */
  isOpen: boolean;
  
  /**
   * Function to close the modal
   */
  onClose: () => void;
  
  /**
   * Optional trigger element
   */
  trigger?: React.ReactNode;
  
  /**
   * Content to display in the modal
   */
  children: React.ReactNode;
}

/**
 * Standardized modal component for viewing entity details
 * Can be used for products, assemblies, and other entities
 */
export const StandardizedEntityModal = memo(function StandardizedEntityModal({
  entity,
  entityType,
  isOpen,
  onClose,
  trigger,
  children
}: StandardizedEntityModalProps) {
  // Memoize config lookup for better performance
  const config = useMemo(() => {
    const entityConfig = ENTITY_TYPE_CONFIGS[entityType];
    if (!entityConfig) {
      throw new Error(`Unsupported entity type: ${entityType}`);
    }
    return entityConfig;
  }, [entityType]);

  // Memoize BOM configuration for this entity
  const bomConfig: BomTabConfig | null = useMemo(() =>
    getBomTabConfig(entity, { type: config.bomType }),
    [entity, config.bomType]
  );

  // Determine if content will be in tab mode (when BOM data exists)
  const isInTab = bomConfig?.bomData ? true : false;

  return (
    <BaseViewModal
      isOpen={isOpen}
      onClose={onClose}
      trigger={trigger}
      title={entity.name}
      subtitle={config.subtitle}
      icon={config.icon}
      {...(bomConfig || {})}
    >
      {React.cloneElement(children as React.ReactElement<any>, { isInTab })}
    </BaseViewModal>
  );
});

/**
 * Props for StandardizedEntityButton
 */
interface StandardizedEntityButtonProps {
  /**
   * Entity data to display
   */
  entity: EntityData;
  
  /**
   * Type of entity (product, assembly, etc.)
   */
  entityType: keyof typeof ENTITY_TYPE_CONFIGS;
  
  /**
   * Button variant
   */
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | undefined;

  /**
   * Button size
   */
  size?: 'default' | 'sm' | 'lg' | 'icon' | undefined;
  
  /**
   * Custom className
   */
  className?: string | undefined;
  
  /**
   * Custom button content
   */
  buttonContent?: React.ReactNode | undefined;
  
  /**
   * Content to display in the modal
   */
  children: React.ReactNode;
}

/**
 * Standardized button component that opens the StandardizedEntityModal
 * Can be used for products, assemblies, and other entities
 */
export const StandardizedEntityButton = memo(function StandardizedEntityButton({
  entity,
  entityType,
  variant = 'default',
  size = 'sm',
  className,
  buttonContent,
  children
}: StandardizedEntityButtonProps) {
  // Memoize config lookup for better performance
  const config = useMemo(() => {
    const entityConfig = ENTITY_TYPE_CONFIGS[entityType];
    if (!entityConfig) {
      throw new Error(`Unsupported entity type: ${entityType}`);
    }
    return entityConfig;
  }, [entityType]);

  // Memoize BOM configuration for this entity
  const bomConfig: BomTabConfig | null = useMemo(() =>
    getBomTabConfig(entity, { type: config.bomType }),
    [entity, config.bomType]
  );

  return (
    <BaseViewButton
      title={entity.name}
      subtitle={config.subtitle}
      icon={config.icon}
      variant={variant}
      size={size}
      className={className || ''}
      buttonContent={buttonContent}
      {...(bomConfig || {})}
    >
      {children}
    </BaseViewButton>
  );
});

/**
 * Utility function to register new entity types
 */
export function registerEntityType(
  type: string, 
  config: EntityTypeConfig
): void {
  ENTITY_TYPE_CONFIGS[type] = config;
}

/**
 * Utility function to get available entity types
 */
export function getAvailableEntityTypes(): string[] {
  return Object.keys(ENTITY_TYPE_CONFIGS);
}

/**
 * Utility function to check if an entity type is supported
 */
export function isEntityTypeSupported(type: string): boolean {
  return type in ENTITY_TYPE_CONFIGS;
}

/**
 * Hook for using entity type configuration
 */
export function useEntityTypeConfig(entityType: string) {
  const config = ENTITY_TYPE_CONFIGS[entityType];
  
  if (!config) {
    throw new Error(`Unsupported entity type: ${entityType}`);
  }
  
  return config;
}

/**
 * Higher-order component for creating entity-specific modals
 */
export function createEntityModal<T extends EntityData>(
  entityType: keyof typeof ENTITY_TYPE_CONFIGS,
  ContentComponent: React.ComponentType<{ entity: T; isInTab?: boolean }>
) {
  return function EntityModal({ 
    entity, 
    isOpen, 
    onClose, 
    trigger 
  }: {
    entity: T;
    isOpen: boolean;
    onClose: () => void;
    trigger?: React.ReactNode;
  }) {
    return (
      <StandardizedEntityModal
        entity={entity}
        entityType={entityType}
        isOpen={isOpen}
        onClose={onClose}
        trigger={trigger}
      >
        <ContentComponent entity={entity} />
      </StandardizedEntityModal>
    );
  };
}

/**
 * Higher-order component for creating entity-specific buttons
 */
export function createEntityButton<T extends EntityData>(
  entityType: keyof typeof ENTITY_TYPE_CONFIGS,
  ContentComponent: React.ComponentType<{ entity: T }>
) {
  return function EntityButton({ 
    entity, 
    variant, 
    size, 
    className, 
    buttonContent 
  }: {
    entity: T;
    variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | undefined;
    size?: 'default' | 'sm' | 'lg' | 'icon' | undefined;
    className?: string | undefined;
    buttonContent?: React.ReactNode | undefined;
  }) {
    return (
      <StandardizedEntityButton
        entity={entity}
        entityType={entityType}
        variant={variant}
        size={size}
        className={className}
        buttonContent={buttonContent}
      >
        <ContentComponent entity={entity} />
      </StandardizedEntityButton>
    );
  };
}
