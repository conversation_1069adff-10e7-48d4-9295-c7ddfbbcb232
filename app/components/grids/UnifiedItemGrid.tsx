'use client';

import { Package, Layers, Plus } from 'lucide-react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

import { Button } from '@/app/components/forms/Button';
import { UnifiedItemCard, type UnifiedItem, type ItemConfig } from '@/app/components/cards/UnifiedItemCard';
import { DeleteItemAction, DuplicateItemAction, QuickEditItemAction, QuickEditAssemblyAction, QuickEditProductAction } from '@/app/components/actions';
// Define ItemType locally for this grid (only assembly and product)
type GridItemType = 'assembly' | 'product';

// Import existing status badge components
import { AssemblyStatusBadge } from '@/app/components/status/AssemblyStatusBadge';
import { ViewAssemblyButton } from '@/app/components/modals/ViewAssemblyModal';
import { ViewProductButton } from '@/app/components/modals/ViewProductModal';

// Import assembly and product types
import type { Assembly } from '@/app/components/tables/AssembliesTable/types';
import type { Product } from '@/app/components/tables/ProductsTable/types';

// Create wrapper components for status badges
const AssemblyStatusBadgeWrapper = ({ item, size = 'sm' }: { item: any; size?: string }) => (
  <AssemblyStatusBadge assembly={item} size={size as 'sm' | 'default' | 'lg'} />
);

const ProductStatusBadgeWrapper = ({ item, size = 'sm' }: { item: any; size?: string }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'discontinued':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'in_development':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(item.status)}`}>
      {item.status === 'active' ? 'Active' :
       item.status === 'discontinued' ? 'Discontinued' :
       item.status === 'in_development' ? 'In Development' :
       item.status}
    </span>
  );
};

// Create wrapper components for view buttons
const ViewAssemblyButtonWrapper = ({ item, ...props }: { item: any; [key: string]: any }) => (
  <ViewAssemblyButton assembly={item} {...props} />
);

const ViewProductButtonWrapper = ({ item, ...props }: { item: any; [key: string]: any }) => (
  <ViewProductButton product={item} {...props} />
);

export interface UnifiedItemGridProps {
  items: UnifiedItem[];
  itemType: GridItemType;
  onRefresh?: () => void;
  emptyStateConfig?: {
    title: string;
    description: string;
    actionLabel: string;
    actionHref: string;
  };
}

/**
 * Unified grid component that can display both assemblies and products
 */
export function UnifiedItemGrid({ 
  items, 
  itemType, 
  onRefresh,
  emptyStateConfig 
}: UnifiedItemGridProps) {
  const router = useRouter();

  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
    } else {
      router.refresh();
    }
  };

  // Configure item-specific behavior
  const getItemConfig = (type: GridItemType): ItemConfig => {
    switch (type) {
      case 'assembly':
        return {
          type: 'assembly',
          codeField: 'assemblyCode',
          icon: <Layers className="h-5 w-5" />,
          statusBadgeComponent: AssemblyStatusBadgeWrapper,
          viewButtonComponent: ViewAssemblyButtonWrapper,
          editActionComponent: (props) => (
            <QuickEditAssemblyAction
              assembly={props.item as Assembly}
              variant="icon"
              size="sm"
              onSuccess={props.onSuccess}
              className={props.className}
              id={props.id}
            />
          ),
          deleteActionComponent: (props) => (
            <DeleteItemAction
              {...props}
              itemType="assembly"
            />
          ),
          duplicateActionComponent: (props) => (
            <DuplicateItemAction
              {...props}
              itemType="assembly"
            />
          ),
          calculateMetrics: (item) => {
            const assemblyItem = item as Assembly;

            // Recursively count hierarchical parts
            const countHierarchicalParts = (parts: any[] = []): number => {
              return parts.reduce((count, part) => {
                let partCount = 1; // Count the current part
                if (part.children && part.children.length > 0) {
                  partCount += countHierarchicalParts(part.children);
                }
                return count + partCount;
              }, 0);
            };

            const partsCount = countHierarchicalParts(assemblyItem.partsRequired);

            // Calculate inventory status with hierarchical support
            const calculateRecursive = (partsList: any[]): any => {
              return partsList.reduce(
                (acc, part) => {
                  if (!part?.partDetails) return acc;

                  const quantityRequired = part.quantityRequired || 1;
                  const inStock = part.partDetails.inventory?.currentStock || 0;

                  acc.availableForAssembly += Math.min(inStock, quantityRequired);
                  acc.totalRequired += quantityRequired;

                  if (inStock === 0) {
                    acc.outOfStockCount++;
                  } else if (inStock < quantityRequired) {
                    acc.lowStockCount++;
                  }

                  // Process children recursively
                  if (part.children && part.children.length > 0) {
                    const childResult = calculateRecursive(part.children);
                    acc.availableForAssembly += childResult.availableForAssembly;
                    acc.totalRequired += childResult.totalRequired;
                    acc.outOfStockCount += childResult.outOfStockCount;
                    acc.lowStockCount += childResult.lowStockCount;
                  }

                  return acc;
                },
                { availableForAssembly: 0, totalRequired: 0, lowStockCount: 0, outOfStockCount: 0 }
              );
            };

            const parts = assemblyItem.partsRequired || [];
            const inventoryStatus = calculateRecursive(parts);

            const isInStock = inventoryStatus.availableForAssembly >= inventoryStatus.totalRequired;
            const isLowStock = inventoryStatus.lowStockCount > 0 || inventoryStatus.outOfStockCount > 0;

            return {
              primaryCount: partsCount,
              primaryCountLabel: partsCount === 1 ? 'part' : 'parts',
              completionPercentage: inventoryStatus.totalRequired > 0 
                ? Math.round((inventoryStatus.availableForAssembly / inventoryStatus.totalRequired) * 100)
                : 100,
              statusInfo: {
                isInStock,
                isLowStock,
                lowStockCount: inventoryStatus.lowStockCount,
                outOfStockCount: inventoryStatus.outOfStockCount,
              }
            };
          }
        };

      case 'product':
        return {
          type: 'product',
          codeField: 'productCode',
          icon: <Package className="h-5 w-5" />,
          statusBadgeComponent: ProductStatusBadgeWrapper,
          viewButtonComponent: ViewProductButtonWrapper,
          editActionComponent: (props) => (
            <QuickEditProductAction
              product={props.item as Product}
              variant="icon"
              size="sm"
              onSuccess={props.onSuccess}
              className={props.className}
              id={props.id}
            />
          ),
          deleteActionComponent: (props) => (
            <DeleteItemAction
              {...props}
              itemType="product"
            />
          ),
          duplicateActionComponent: (props) => (
            <DuplicateItemAction
              {...props}
              itemType="product"
            />
          ),
          calculateMetrics: (item) => {
            const productItem = item as Product;

            // Recursively count hierarchical assemblies
            const countHierarchicalAssemblies = (assemblies: any[] = []): number => {
              return assemblies.reduce((count, assembly) => {
                let assemblyCount = 1;
                if (assembly.children && assembly.children.length > 0) {
                  assemblyCount += countHierarchicalAssemblies(assembly.children);
                }
                return count + assemblyCount;
              }, 0);
            };

            const assembliesCount = countHierarchicalAssemblies(productItem.components);
            const price = productItem.sellingPrice || 0;

            return {
              primaryCount: assembliesCount,
              primaryCountLabel: assembliesCount === 1 ? 'assembly' : 'assemblies',
              secondaryCount: price,
              secondaryCountLabel: 'Price ($)',
              totalCost: price,
            };
          }
        };

      default:
        throw new Error(`Unsupported item type: ${type}`);
    }
  };

  const config = getItemConfig(itemType);

  // Default empty state configuration
  const defaultEmptyState: Record<GridItemType, { title: string; description: string; actionLabel: string; actionHref: string }> = {
    assembly: {
      title: 'No assemblies found',
      description: 'Get started by creating your first assembly.',
      actionLabel: 'Create Assembly',
      actionHref: '/assemblies/new'
    },
    product: {
      title: 'No products found',
      description: 'Get started by creating your first product.',
      actionLabel: 'Create Product',
      actionHref: '/products/new'
    }
  };

  const emptyState = emptyStateConfig || defaultEmptyState[itemType];

  if (items.length === 0) {
    const IconComponent = itemType === 'assembly' ? Layers : Package;
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <IconComponent className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">{emptyState.title}</h3>
        <p className="text-muted-foreground mb-6 max-w-sm">{emptyState.description}</p>
        <Button asChild>
          <Link href={emptyState.actionHref}>
            <Plus size={16} className="mr-2" />
            {emptyState.actionLabel}
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {items.map((item) => (
        <UnifiedItemCard
          key={item._id}
          item={item}
          config={config}
          onRefresh={handleRefresh}
        />
      ))}
    </div>
  );
}
