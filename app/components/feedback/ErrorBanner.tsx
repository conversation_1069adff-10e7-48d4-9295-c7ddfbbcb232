/**
 * ErrorBanner Component
 * Page-level error banners with navigation options and actions
 * For major page-level errors that require user attention
 */

"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { AlertCircle, AlertTriangle, Info, X, RefreshCw } from 'lucide-react';
import { Button } from '@/app/components/forms/Button';
import { cn } from '@/app/lib/utils';
import { ErrorBannerProps, ErrorSeverity } from '@/app/types/error.types';
import { extractErrorMessage, isTestError, getErrorSeverity } from '@/app/utils/error.utils';

/**
 * ErrorBanner component for page-level error display
 * Provides prominent error messaging with action buttons
 */
export function ErrorBanner({
  error,
  actions = [],
  persistent = false,
  className,
}: ErrorBannerProps) {
  const [dismissed, setDismissed] = React.useState(false);
  const errorMessage = extractErrorMessage(error);
  const isTest = isTestError(error);
  const severity = getErrorSeverity(error);

  // Don't render if dismissed and not persistent
  if (dismissed && !persistent) {
    return null;
  }

  // Get styling based on error severity
  const getVariantStyles = () => {
    if (isTest) {
      return {
        container: 'bg-theme-warning-light border-theme-warning/20 text-theme-warning',
        icon: 'text-theme-warning',
        iconBg: 'bg-theme-warning/10',
      };
    }

    switch (severity) {
      case ErrorSeverity.CRITICAL:
        return {
          container: 'bg-red-50 dark:bg-red-950/20 border-red-200 dark:border-red-800/30 text-red-800 dark:text-red-200',
          icon: 'text-red-600 dark:text-red-400',
          iconBg: 'bg-red-100 dark:bg-red-900/30',
        };
      case ErrorSeverity.WARNING:
        return {
          container: 'bg-theme-warning-light border-theme-warning/20 text-theme-warning',
          icon: 'text-theme-warning',
          iconBg: 'bg-theme-warning/10',
        };
      case ErrorSeverity.INFO:
        return {
          container: 'bg-theme-info-light border-theme-info/20 text-theme-info',
          icon: 'text-theme-info',
          iconBg: 'bg-theme-info/10',
        };
      default:
        return {
          container: 'bg-theme-error-light border-theme-error/20 text-theme-error',
          icon: 'text-theme-error',
          iconBg: 'bg-theme-error/10',
        };
    }
  };

  // Get appropriate icon
  const getIcon = () => {
    if (isTest || severity === ErrorSeverity.WARNING) {
      return <AlertTriangle className="h-5 w-5" />;
    }
    if (severity === ErrorSeverity.INFO) {
      return <Info className="h-5 w-5" />;
    }
    return <AlertCircle className="h-5 w-5" />;
  };

  const styles = getVariantStyles();

  // Default retry action if no actions provided and error is retryable
  const defaultActions = actions.length === 0 ? [
    {
      label: 'Retry',
      action: () => window.location.reload(),
      variant: 'default' as const,
    }
  ] : actions;

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={cn(
        'relative w-full border-l-4 p-4 shadow-sm',
        styles.container,
        className
      )}
      role="alert"
      aria-live="polite"
    >
      <div className="flex items-start gap-3">
        {/* Icon */}
        <div className={cn('flex-shrink-0 rounded-full p-2', styles.iconBg)}>
          <div className={styles.icon}>
            {getIcon()}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-medium mb-1">
            {isTest ? 'Test Error Detected' : 'Error Occurred'}
          </h3>
          
          <p className="text-sm opacity-90 mb-3">
            {errorMessage}
          </p>

          {isTest && (
            <p className="text-xs opacity-75 mb-3">
              This is an intentional test error to verify error banner functionality.
            </p>
          )}

          {/* Actions */}
          {defaultActions.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {defaultActions.map((action, index) => (
                <Button
                  key={index}
                  variant={action.variant || 'outline'}
                  size="sm"
                  onClick={action.action}
                  className="text-xs"
                >
                  {action.label === 'Retry' && <RefreshCw className="h-3 w-3 mr-1" />}
                  {action.label}
                </Button>
              ))}
            </div>
          )}
        </div>

        {/* Dismiss button */}
        {!persistent && (
          <Button
            variant="ghost"
            size="sm"
            className="flex-shrink-0 h-6 w-6 p-0 hover:bg-black/5 dark:hover:bg-white/5"
            onClick={() => setDismissed(true)}
            aria-label="Dismiss error banner"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>
    </motion.div>
  );
}

/**
 * Compact error banner for less prominent errors
 */
export function ErrorBannerCompact({
  error,
  actions = [],
  persistent = false,
  className,
}: ErrorBannerProps) {
  const [dismissed, setDismissed] = React.useState(false);
  const errorMessage = extractErrorMessage(error);
  const isTest = isTestError(error);

  if (dismissed && !persistent) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      className={cn(
        'flex items-center justify-between px-4 py-2 text-sm border-l-4',
        isTest 
          ? 'bg-theme-warning-light border-theme-warning text-theme-warning'
          : 'bg-theme-error-light border-theme-error text-theme-error',
        className
      )}
      role="alert"
    >
      <div className="flex items-center gap-2 flex-1 min-w-0">
        <AlertCircle className="h-4 w-4 flex-shrink-0" />
        <span className="truncate">{errorMessage}</span>
      </div>

      <div className="flex items-center gap-2 ml-3">
        {actions.map((action, index) => (
          <Button
            key={index}
            variant="ghost"
            size="sm"
            onClick={action.action}
            className="h-6 px-2 text-xs"
          >
            {action.label}
          </Button>
        ))}
        
        {!persistent && (
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => setDismissed(true)}
            aria-label="Dismiss"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>
    </motion.div>
  );
}

export default ErrorBanner;
