/**
 * FormErrorDisplay Component
 * Standardized form error handling to replace duplicate form error code
 * Replaces the duplicate error handling in form-container.tsx and ProductForm/FormContainer.tsx
 */

"use client";

import React from 'react';
import { AlertCircle } from 'lucide-react';
import { <PERSON>ert, AlertTitle, AlertDescription } from '@/app/components/data-display/alert';
import { cn } from '@/app/lib/utils';
import { FormErrorDisplayProps } from '@/app/types/error.types';
import { extractErrorMessage, isTestError } from '@/app/utils/error.utils';

/**
 * FormErrorDisplay component for standardized form error handling
 * Replaces duplicate error handling code across form containers
 */
export function FormErrorDisplay({
  error,
  field,
  className,
}: FormErrorDisplayProps) {
  // Don't render if no error
  if (!error) {
    return null;
  }

  const errorMessage = extractErrorMessage(error);
  const isTest = isTestError(error);
  
  // Use warning variant for test errors
  const variant = isTest ? 'warning' : 'destructive';

  // Format error message for display
  const formatMessage = (message: string) => {
    // Handle multi-line error messages (common in form validation)
    const lines = message.split('\n').filter(line => line.trim());
    
    if (lines.length <= 1) {
      return <span>{message}</span>;
    }

    return (
      <div>
        {lines.map((line, index) => (
          <div key={index} className="mb-1 last:mb-0">
            {line}
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className={cn('px-6', className)}>
      <Alert variant={variant} className="mb-4">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>
          {isTest ? 'Test Error' : field ? `${field} Error` : 'Error'}
        </AlertTitle>
        <AlertDescription>
          {formatMessage(errorMessage)}
          
          {isTest && (
            <div className="mt-2 text-xs opacity-75">
              This is an intentional test error to verify form error handling.
            </div>
          )}
        </AlertDescription>
      </Alert>
    </div>
  );
}

/**
 * Inline form error display for field-level errors
 */
export function InlineFormError({
  error,
  field,
  className,
}: FormErrorDisplayProps) {
  if (!error) {
    return null;
  }

  const errorMessage = extractErrorMessage(error);
  const isTest = isTestError(error);

  return (
    <div className={cn('mt-1', className)}>
      <p className={cn(
        'text-sm flex items-center gap-1',
        isTest 
          ? 'text-theme-warning' 
          : 'text-theme-error'
      )}>
        <AlertCircle className="h-3 w-3 flex-shrink-0" />
        <span>{errorMessage}</span>
      </p>
      
      {isTest && (
        <p className="text-xs text-muted-foreground mt-1">
          Test error for validation testing
        </p>
      )}
    </div>
  );
}

/**
 * Form error summary for displaying multiple field errors
 */
export function FormErrorSummary({
  errors,
  className,
}: {
  errors: Record<string, string>;
  className?: string;
}) {
  const errorEntries = Object.entries(errors).filter(([_, message]) => message);
  
  if (errorEntries.length === 0) {
    return null;
  }

  return (
    <div className={cn('px-6', className)}>
      <Alert variant="destructive" className="mb-4">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>
          {errorEntries.length === 1 ? 'Form Error' : 'Form Errors'}
        </AlertTitle>
        <AlertDescription>
          {errorEntries.length === 1 ? (
            <span>{errorEntries[0]?.[1]}</span>
          ) : (
            <ul className="list-disc list-inside space-y-1">
              {errorEntries.map(([field, message]) => (
                <li key={field}>
                  <strong>{field}:</strong> {message}
                </li>
              ))}
            </ul>
          )}
        </AlertDescription>
      </Alert>
    </div>
  );
}

export default FormErrorDisplay;
