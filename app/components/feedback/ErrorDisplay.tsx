"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { AlertCircle, RefreshCw } from 'lucide-react';

interface ErrorDisplayProps {
  error: Error | string;
  onRetry?: () => void;
  message?: string;
  suggestion?: string;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  onRetry,
  message = "An error occurred",
  suggestion = "Please try again later"
}) => {
  const errorMessage = typeof error === 'string' ? error : error.message;
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="w-full p-6 bg-card rounded-lg shadow-md border border-destructive/20"
    >
      <div className="flex flex-col items-center text-center">
        <div className="bg-destructive/10 p-3 rounded-full mb-4">
          <AlertCircle className="h-8 w-8 text-destructive" />
        </div>

        <h3 className="text-xl font-medium text-foreground mb-1">{message}</h3>
        <p className="text-muted-foreground mb-4">{suggestion}</p>

        <div className="bg-destructive/5 p-3 rounded-md w-full max-w-lg mb-6">
          <p className="text-destructive text-sm font-mono">{errorMessage}</p>
        </div>
        
        {onRetry && (
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onRetry}
            className="flex items-center justify-center gap-2 bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg transition-colors"
          >
            <RefreshCw size={16} />
            <span>Retry</span>
          </motion.button>
        )}
      </div>
    </motion.div>
  );
};

export default ErrorDisplay; 