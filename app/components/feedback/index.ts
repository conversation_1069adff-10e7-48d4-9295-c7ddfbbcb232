/**
 * Unified Error Handling Components Export
 * Central export for all standardized error handling components
 */

// Core error display components
export { default as ErrorDisplay } from './ErrorDisplay/ErrorDisplay';
export { default as ErrorDisplayClient } from './ErrorDisplay/ErrorDisplayClient';

// New standardized error components
export { ErrorAlert, ErrorAlertCompact } from './ErrorAlert';
export { ErrorBanner, ErrorBannerCompact } from './ErrorBanner';
export {
  FormErrorDisplay,
  InlineFormError,
  FormErrorSummary
} from './FormErrorDisplay';
export {
  InlineError,
  InlineWarning,
  InlineInfo,
  InlineSuccess,
  InlineMessage
} from './InlineError';
export {
  ErrorRecovery,
  ErrorRecoveryEnhanced,
  ErrorRecoveryCompact,
  ErrorRecoveryInline
} from './ErrorRecovery';
export {
  default as ErrorToast,
  showErrorToast,
  showWarningToast,
  showInfoToast,
  showSuccessToast,
  showLoadingToast,
  showRetryToast,
  showNetworkErrorToast,
  showValidationErrorToast,
  showPermissionErrorToast,
  dismissToast,
  dismissAllToasts,
  showPromiseToast
} from './ErrorToast';

// Error boundary
export { default as ErrorBoundary } from './ErrorBoundary';

// Legacy components (for backward compatibility)
export { default as ErrorDisplayLegacy } from './ErrorDisplay';

// Re-export types for convenience
export type {
  ErrorDisplayProps,
  ErrorAlertProps,
  ErrorBannerProps,
  FormErrorDisplayProps,
  InlineErrorProps,
  ErrorRecoveryProps,
  StandardError,
  ErrorSeverity,
  ErrorCategory,
} from '@/app/types/error.types';

// Re-export hooks
export {
  useErrorHandler,
  useErrorRecovery,
  useErrorToast,
  useFormErrors,
  useAsyncError,
} from '@/app/hooks/useErrorHandler';

// Re-export utilities
export {
  createStandardError,
  normalizeError,
  extractErrorMessage,
  isRetryableError,
  isRecoverableError,
  formatErrorMessage,
  getErrorMessage,
  logError,
} from '@/app/utils/error.utils';

// Other feedback components
export {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger
} from './tooltip';

export { ScrollArea, ScrollBar } from './scroll-area';
export { Separator } from './separator';
export { Slider } from './slider';

