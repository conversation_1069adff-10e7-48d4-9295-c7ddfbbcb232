"use client";

import { createDebouncedSearch } from "@/app/lib/utils";
import { getApiUrl } from "@/app/utils/apiUtils";
import { Loader2, Package, Search, X } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { Input } from "../forms/Input";
import { Label } from "../forms/label";

/**
 * Interface for Part data received from the search API
 * Updated to match the new database schema with stockLevels
 */
export interface PartSearchResult {
  _id: string;
  partNumber: string;
  name: string;
  businessName?: string | null; // Human-readable business name for the part
  description?: string | null;
  inventory?: {
    stockLevels?: {
      raw: number;
      hardening: number;
      grinding: number;
      finished: number;
      rejected: number;
    };
    currentStock?: number; // Backward compatibility - computed as stockLevels.finished
    warehouseId?: string;
  };
  current_stock?: number; // Alternative field name from some APIs
  status?: string;
  unitOfMeasure?: string;
  reorderLevel?: number;
}

/**
 * Props for the standardized PartSearch component
 */
export interface PartSearchProps {
  selectedPartId?: string;
  selectedPart?: PartSearchResult | null;
  warehouseId?: string; // Context for filtering and stock display
  onPartSelect: (part: PartSearchResult) => void;
  placeholder?: string;
  label?: string;
  error?: string;
  disabled?: boolean;
  className?: string;
  // Additional props for enhanced functionality
  showStockLevels?: boolean; // Whether to display detailed stock levels
  minQueryLength?: number; // Minimum characters before search triggers
  debounceTime?: number; // Debounce delay in milliseconds
  maxResults?: number; // Maximum number of results to display
}

/**
 * Standardized Part Search Component
 * 
 * This is the single, consolidated component for searching and selecting parts
 * across the entire application. It replaces EnhancedPartSelector, PartSelector,
 * and the old PartSearch components.
 * 
 * Features:
 * - Real-time search with configurable debouncing
 * - Stock level display with new stockLevels schema support
 * - Warehouse context filtering
 * - Keyboard navigation support
 * - Loading states and comprehensive error handling
 * - Accessibility features
 * - Performance optimized with sub-200ms target response time
 */
export function PartSearch({
  selectedPartId,
  selectedPart,
  warehouseId,
  onPartSelect,
  placeholder = "Search parts by name or number...",
  label = "Part",
  error,
  disabled = false,
  className = "",
  showStockLevels = true,
  minQueryLength = 3,
  debounceTime = 300,
  maxResults = 10,
}: PartSearchProps) {
  // State management
  const [searchQuery, setSearchQuery] = useState("");
  const [suggestions, setSuggestions] = useState<PartSearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);

  // Refs for DOM manipulation and cleanup
  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  /**
   * Fetch parts from the search API
   * Optimized for performance with proper error handling
   */
  const fetchParts = useCallback(async (query: string) => {
    if (query.length < minQueryLength) {
      setSuggestions([]);
      return;
    }

    setIsLoading(true);
    try {
      const searchParams = new URLSearchParams({
        search: query,
        limit: maxResults.toString(),
      });

      // Add warehouse filter if provided
      if (warehouseId) {
        searchParams.append('warehouseId', warehouseId);
      }

      const apiUrl = getApiUrl(`/api/parts/search?${searchParams.toString()}`);
      const response = await fetch(apiUrl);
      
      if (!response.ok) {
        throw new Error(`Search failed: ${response.statusText}`);
      }

      const data = await response.json();
      const parts = data.data || [];
      
      setSuggestions(parts);
    } catch (error) {
      console.error("Error fetching parts:", error);
      setSuggestions([]);
    } finally {
      setIsLoading(false);
    }
  }, [warehouseId, minQueryLength, maxResults]);

  /**
   * Debounced search function
   * Configurable debounce time for optimal performance
   */
  const debouncedSearch = useRef(
    createDebouncedSearch(
      (query: string) => fetchParts(query),
      {
        wait: debounceTime,
        minLength: minQueryLength,
        onEmpty: () => setSuggestions([]),
        onTooShort: () => setSuggestions([]),
      }
    )
  ).current;

  /**
   * Handle input changes with debounced search
   */
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    setFocusedIndex(-1);
    
    if (value.length === 0) {
      setIsOpen(false);
      setSuggestions([]);
    } else {
      setIsOpen(true);
      debouncedSearch(value);
    }
  };

  /**
   * Handle part selection
   */
  const handlePartSelect = (part: PartSearchResult) => {
    onPartSelect(part);
    setSearchQuery(`${part.businessName || part.name} (${part.partNumber})`);
    setIsOpen(false);
    setSuggestions([]);
    setFocusedIndex(-1);
  };

  /**
   * Handle keyboard navigation
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setFocusedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setFocusedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (focusedIndex >= 0 && focusedIndex < suggestions.length) {
          const selectedPart = suggestions[focusedIndex];
          if (selectedPart) {
            handlePartSelect(selectedPart);
          }
        }
        break;
      case 'Escape':
        e.preventDefault();
        setIsOpen(false);
        setFocusedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  /**
   * Get stock display value with new stockLevels support
   */
  const getStockDisplay = (part: PartSearchResult): number => {
    // Priority: stockLevels.finished > currentStock > current_stock > 0
    return part.inventory?.stockLevels?.finished ?? 
           part.inventory?.currentStock ?? 
           part.current_stock ?? 
           0;
  };

  /**
   * Get stock color class based on stock level and reorder point
   */
  const getStockColorClass = (stock: number, reorderLevel?: number): string => {
    if (stock === 0) return 'text-red-600 dark:text-red-400';
    if (reorderLevel && stock <= reorderLevel) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-green-600 dark:text-green-400';
  };

  /**
   * Get stock status indicator
   */
  const getStockStatusIndicator = (stock: number): string => {
    if (stock === 0) return '⚠️';
    if (stock < 10) return '⚡';
    return '✅';
  };

  /**
   * Handle clicks outside component to close suggestions
   */
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setFocusedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  /**
   * Initialize with selected part if provided
   */
  useEffect(() => {
    if (selectedPart) {
      setSearchQuery(`${selectedPart.businessName || selectedPart.name} (${selectedPart.partNumber})`);
    }
  }, [selectedPart]);

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      <div className="space-y-2">
        <Label htmlFor="part-search">{label}</Label>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            ref={inputRef}
            id="part-search"
            type="text"
            value={searchQuery}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={() => {
              if (searchQuery.length >= minQueryLength) {
                setIsOpen(true);
              }
            }}
            placeholder={placeholder}
            disabled={disabled}
            className={`pl-10 pr-10 ${error ? 'border-red-500' : ''}`}
            autoComplete="off"
          />
          {isLoading && (
            <Loader2 className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 animate-spin text-muted-foreground" />
          )}
          {searchQuery && !isLoading && (
            <button
              type="button"
              onClick={() => {
                setSearchQuery("");
                setSuggestions([]);
                setIsOpen(false);
                inputRef.current?.focus();
              }}
              className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground hover:text-foreground"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
        {error && (
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        )}
      </div>

      {/* Suggestions Dropdown */}
      {isOpen && (suggestions.length > 0 || isLoading) && (
        <div
          ref={suggestionsRef}
          className="absolute z-50 mt-1 w-full bg-card dark:bg-card shadow-lg rounded-md border border-border max-h-60 overflow-y-auto"
        >
          {isLoading ? (
            <div className="p-3 flex items-center justify-center">
              <Loader2 className="h-4 w-4 animate-spin mr-2 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Searching parts...</span>
            </div>
          ) : (
            <ul className="py-1">
              {suggestions.map((part, index) => {
                const stock = getStockDisplay(part);
                const stockColorClass = getStockColorClass(stock, part.reorderLevel);
                const stockIndicator = getStockStatusIndicator(stock);
                const isHighlighted = index === focusedIndex;

                return (
                  <li
                    key={part._id}
                    className={`px-3 py-2 cursor-pointer transition-colors ${
                      isHighlighted
                        ? 'bg-accent dark:bg-accent/20'
                        : 'hover:bg-accent/50 dark:hover:bg-accent/10'
                    }`}
                    onClick={() => handlePartSelect(part)}
                    onMouseEnter={() => setFocusedIndex(index)}
                  >
                    <div className="flex justify-between items-center">
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-foreground truncate">
                          {part.businessName || part.name} ({part.partNumber})
                        </div>
                        {part.businessName && (
                          <div className="text-xs text-muted-foreground italic truncate">
                            {part.name}
                          </div>
                        )}
                        {part.description && (
                          <div className="text-xs text-muted-foreground truncate mt-1">
                            {part.description}
                          </div>
                        )}
                      </div>
                      {showStockLevels && (
                        <div className="flex items-center space-x-2 ml-2">
                          <span className={`text-xs font-medium ${stockColorClass}`}>
                            {stockIndicator} {stock} {part.unitOfMeasure || 'pcs'}
                          </span>
                          <Package className="h-3 w-3 text-muted-foreground" />
                        </div>
                      )}
                    </div>
                  </li>
                );
              })}
            </ul>
          )}
        </div>
      )}
    </div>
  );
}

// Export the component as default for easier imports
export default PartSearch;
