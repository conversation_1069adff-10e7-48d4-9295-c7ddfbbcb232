"use client";

import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { cn } from '@/app/lib/utils';
import { useTheme } from '@/app/context/ThemeContext';
import { useState, useMemo } from 'react';
import { DataTableProps } from './types';
import { DataTableToolbar } from './DataTableToolbar';
import { DataTablePagination } from './DataTablePagination';
import { MobileCardView } from './MobileCardView';
import { DataTableSkeleton } from './DataTableSkeleton';
import { DataTableError } from './DataTableError';
import { DataTableEmpty } from './DataTableEmpty';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/app/components/data-display/table';

/**
 * Modern, responsive DataTable component using TanStack Table
 * Features:
 * - Mobile-first responsive design with card fallback
 * - Full accessibility support (WCAG 2.1 AA)
 * - Sorting, filtering, pagination, and search
 * - Customizable density and styling
 * - Server-side data support
 */
export function DataTable<T>({
  data,
  columns,
  enableSorting = true,
  enableFiltering = true,
  enableGlobalSearch = true,
  enablePagination = true,
  enableRowSelection = false,
  enableColumnVisibility = true,
  mobileDisplayMode = 'cards',
  density = 'normal',
  initialSorting = [],
  initialColumnFilters = [],
  initialGlobalFilter = '',
  initialPagination = { pageIndex: 0, pageSize: 50 },
  initialColumnVisibility = {},
  pageSizeOptions = [10, 20, 50, 100, 150],
  manualPagination = false,
  totalRows,
  isLoading = false,
  loadingRows = 5,
  error = null,
  className,
  showCaption = true,
  caption,
  stickyHeader = false,
  maxHeight,
  onSortingChange,
  onColumnFiltersChange,
  onGlobalFilterChange,
  onPaginationChange,
  onRowSelectionChange,
  onColumnVisibilityChange,
  onRowClick,
  renderRowActions,
  renderToolbar,
  renderEmptyState,
  renderLoadingState,
  renderErrorState,
  renderMobileCard,
}: DataTableProps<T>) {
  // Table state
  const [sorting, setSorting] = useState(initialSorting);
  const [columnFilters, setColumnFilters] = useState(initialColumnFilters);
  const [globalFilter, setGlobalFilter] = useState(initialGlobalFilter);
  const [pagination, setPagination] = useState(initialPagination);
  const [rowSelection, setRowSelection] = useState({});
  const [columnVisibility, setColumnVisibility] = useState(initialColumnVisibility);

  // Filter columns for mobile display
  const mobileColumns = useMemo(() => {
    return columns
      .filter(col => !col.hideOnMobile)
      .sort((a, b) => (a.mobilePriority || 999) - (b.mobilePriority || 999));
  }, [columns]);

  // Create table instance
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    ...(enableSorting && { getSortedRowModel: getSortedRowModel() }),
    ...(enableFiltering && { getFilteredRowModel: getFilteredRowModel() }),
    ...(enablePagination && { getPaginationRowModel: getPaginationRowModel() }),
    // Server-side pagination configuration
    ...(manualPagination && {
      manualPagination: true,
      pageCount: totalRows ? Math.ceil(totalRows / pagination.pageSize) : -1,
    }),
    state: {
      sorting,
      columnFilters,
      globalFilter,
      pagination,
      rowSelection,
      columnVisibility,
    },
    onSortingChange: (updater) => {
      const newSorting = typeof updater === 'function' ? updater(sorting) : updater;
      setSorting(newSorting);
      onSortingChange?.(newSorting);
    },
    onColumnFiltersChange: (updater) => {
      const newFilters = typeof updater === 'function' ? updater(columnFilters) : updater;
      setColumnFilters(newFilters);
      onColumnFiltersChange?.(newFilters);
    },
    onGlobalFilterChange: (updater) => {
      const newFilter = typeof updater === 'function' ? updater(globalFilter) : updater;
      setGlobalFilter(newFilter);
      onGlobalFilterChange?.(newFilter);
    },
    onPaginationChange: (updater) => {
      const newPagination = typeof updater === 'function' ? updater(pagination) : updater;
      setPagination(newPagination);
      onPaginationChange?.(newPagination);
    },
    onRowSelectionChange: (updater) => {
      const newSelection = typeof updater === 'function' ? updater(rowSelection) : updater;
      setRowSelection(newSelection);
      onRowSelectionChange?.(newSelection);
    },
    onColumnVisibilityChange: (updater) => {
      const newVisibility = typeof updater === 'function' ? updater(columnVisibility) : updater;
      setColumnVisibility(newVisibility);
      onColumnVisibilityChange?.(newVisibility);
    },
    enableRowSelection,
    enableSorting,
    enableColumnFilters: enableFiltering,
    enableGlobalFilter: enableGlobalSearch,
  });

  // Density classes
  const densityClasses = {
    compact: 'text-sm',
    normal: '',
    comfortable: 'text-base',
  };

  // Error state
  if (error) {
    return renderErrorState ? renderErrorState(error) : <DataTableError error={error} />;
  }

  // Loading state
  if (isLoading) {
    return renderLoadingState ? renderLoadingState() : <DataTableSkeleton rows={loadingRows} />;
  }

  // Empty state
  if (!data.length) {
    return renderEmptyState ? renderEmptyState() : <DataTableEmpty />;
  }

  return (
    <div className={cn('w-full space-y-4', className)}>
      {/* Toolbar */}
      {(enableGlobalSearch || enableColumnVisibility || renderToolbar) && (
        <DataTableToolbar
          table={table}
          enableGlobalSearch={enableGlobalSearch}
          enableColumnVisibility={enableColumnVisibility}
        >
          {renderToolbar?.()}
        </DataTableToolbar>
      )}

      {/* Table Container with Background */}
      <div className="rounded-lg shadow-sm border border-border overflow-hidden">

        {/* Mobile Card View */}
        <div className="block md:hidden p-4">
          <MobileCardView
            data={table.getRowModel().rows.map(row => row.original)}
            columns={mobileColumns}
            {...(onRowClick && { onRowClick })}
            {...(renderRowActions && { renderRowActions })}
            {...(renderMobileCard && { renderMobileCard })}
          />
        </div>

        {/* Desktop Table View */}
        <div className="hidden md:block">
          <div
            className={cn(
              'overflow-hidden',
              stickyHeader && 'overflow-auto',
              maxHeight && `max-h-[${maxHeight}]`
            )}
            style={maxHeight ? { maxHeight } : undefined}
          >
            <Table className={cn(densityClasses[density])}>
              {showCaption && (
                <caption className="sr-only">
                  {caption || `Data table with ${data.length} rows`}
                </caption>
              )}
              <TableHeader className={cn(
                stickyHeader && 'sticky top-0 z-10 bg-background'
              )}>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableHead
                        key={header.id}
                        className={cn(
                          header.column.getCanSort() && 'cursor-pointer select-none',
                          'text-left'
                        )}
                        onClick={header.column.getToggleSortingHandler()}
                        scope="col"
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                    className={cn(
                      onRowClick && 'cursor-pointer hover:bg-muted/50',
                      'transition-colors'
                    )}
                    onClick={() => onRowClick?.(row.original)}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* Pagination */}
        {enablePagination && (
          <div className="p-4 border-t border-border/30">
            <DataTablePagination
              table={table}
              pageSizeOptions={pageSizeOptions}
              totalRows={manualPagination ? totalRows : undefined}
            />
          </div>
        )}
      </div>

      {/* Pagination outside container for mobile */}
      {enablePagination && (
        <div className="block md:hidden">
          <DataTablePagination
            table={table}
            pageSizeOptions={pageSizeOptions}
            totalRows={manualPagination ? totalRows : undefined}
          />
        </div>
      )}
    </div>
  );
}
