"use client";

import { Input } from '@/app/components/forms/Input';
import { Button } from '@/app/components/forms/Button';
import { Search, X } from 'lucide-react';
import { DataTableToolbarProps } from './types';

/**
 * DataTable toolbar with search and column visibility controls
 */
export function DataTableToolbar<T>({
  table,
  enableGlobalSearch = true,
  enableColumnVisibility = true,
  children,
}: DataTableToolbarProps<T>) {
  const isFiltered = table.getState().columnFilters.length > 0 || table.getState().globalFilter;

  return (
    <div className="flex items-center justify-between space-x-4">
      <div className="flex flex-1 items-center space-x-2">
        {/* Global Search */}
        {enableGlobalSearch && (
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search all columns..."
              value={table.getState().globalFilter ?? ''}
              onChange={(e) => table.setGlobalFilter(e.target.value)}
              className="pl-9"
              aria-label="Search table data"
            />
          </div>
        )}

        {/* Clear Filters */}
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={() => {
              table.resetColumnFilters();
              table.setGlobalFilter('');
            }}
            className="h-8 px-2 lg:px-3"
            aria-label="Clear all filters"
          >
            Reset
            <X className="ml-2 h-4 w-4" />
          </Button>
        )}

        {/* Custom toolbar content */}
        {children}
      </div>

      {/* Column Visibility Toggle - Removed as per requirements */}
    </div>
  );
}
