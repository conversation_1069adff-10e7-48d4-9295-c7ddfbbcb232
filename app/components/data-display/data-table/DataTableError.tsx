"use client";

import { Button } from '@/app/components/forms/Button';
import { cn } from '@/app/lib/utils';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface DataTableErrorProps {
  error: Error;
  onRetry?: () => void;
  className?: string;
}

/**
 * Error state component for DataTable
 */
export function DataTableError({
  error,
  onRetry,
  className,
}: DataTableErrorProps) {
  return (
    <div className={cn('flex flex-col items-center justify-center p-8 text-center', className)}>
      <div className="rounded-full bg-destructive/10 p-3 mb-4">
        <AlertTriangle className="h-6 w-6 text-destructive" />
      </div>
      
      <h3 className="text-lg font-semibold mb-2">
        Something went wrong
      </h3>
      
      <p className="text-muted-foreground mb-4 max-w-md">
        {error.message || 'An unexpected error occurred while loading the data.'}
      </p>
      
      {onRetry && (
        <Button onClick={onRetry} variant="outline" className="gap-2">
          <RefreshCw className="h-4 w-4" />
          Try again
        </Button>
      )}
    </div>
  );
}
