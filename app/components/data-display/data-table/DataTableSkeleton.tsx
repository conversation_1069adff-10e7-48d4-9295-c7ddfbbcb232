"use client";

import { Skeleton } from '@/app/components/data-display/skeleton';
import { cn } from '@/app/lib/utils';

interface DataTableSkeletonProps {
  rows?: number;
  columns?: number;
  showHeader?: boolean;
  className?: string;
}

/**
 * Loading skeleton for DataTable
 */
export function DataTableSkeleton({
  rows = 5,
  columns = 4,
  showHeader = true,
  className,
}: DataTableSkeletonProps) {
  return (
    <div className={cn('w-full space-y-4', className)}>
      {/* Toolbar skeleton */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-[250px]" />
        <Skeleton className="h-8 w-[100px]" />
      </div>

      {/* Desktop table skeleton */}
      <div className="hidden md:block">
        <div className="rounded-md border">
          <div className="overflow-hidden">
            {/* <PERSON><PERSON> skeleton */}
            {showHeader && (
              <div className="border-b bg-muted/50 p-4">
                <div className="flex space-x-4">
                  {Array.from({ length: columns }).map((_, i) => (
                    <Skeleton key={i} className="h-4 flex-1" />
                  ))}
                </div>
              </div>
            )}

            {/* Rows skeleton */}
            <div className="divide-y">
              {Array.from({ length: rows }).map((_, i) => (
                <div key={i} className="p-4">
                  <div className="flex space-x-4">
                    {Array.from({ length: columns }).map((_, j) => (
                      <Skeleton key={j} className="h-4 flex-1" />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Mobile cards skeleton */}
      <div className="block md:hidden space-y-3">
        {Array.from({ length: rows }).map((_, i) => (
          <div key={i} className="rounded-lg border p-4 space-y-3">
            {/* Primary info */}
            <div className="space-y-2">
              <Skeleton className="h-3 w-16" />
              <Skeleton className="h-5 w-32" />
              <Skeleton className="h-3 w-20" />
              <Skeleton className="h-4 w-24" />
            </div>

            {/* Secondary info */}
            <div className="grid grid-cols-2 gap-3 pt-2 border-t">
              {Array.from({ length: Math.min(columns - 2, 4) }).map((_, j) => (
                <div key={j} className="space-y-1">
                  <Skeleton className="h-3 w-12" />
                  <Skeleton className="h-4 w-16" />
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Pagination skeleton */}
      <div className="flex items-center justify-between">
        <Skeleton className="h-4 w-[120px]" />
        <div className="flex items-center space-x-2">
          <Skeleton className="h-8 w-[100px]" />
          <div className="flex space-x-1">
            {Array.from({ length: 4 }).map((_, i) => (
              <Skeleton key={i} className="h-8 w-8" />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
