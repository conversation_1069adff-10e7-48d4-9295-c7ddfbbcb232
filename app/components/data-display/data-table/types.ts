/**
 * Type definitions for the DataTable component using TanStack Table
 */

import { ColumnDef, SortingState, ColumnFiltersState, PaginationState } from '@tanstack/react-table';
import { ReactNode } from 'react';

/**
 * Extended column definition with mobile-specific options
 */
export type DataTableColumn<T> = ColumnDef<T> & {
  /** Whether to hide this column on mobile devices */
  hideOnMobile?: boolean;
  /** Priority for mobile display (lower numbers shown first) */
  mobilePriority?: number;
  /** Custom mobile renderer for card view */
  mobileRender?: (value: any, row: T) => ReactNode;
  /** Whether this column should be searchable */
  searchable?: boolean;
  /** Custom filter function */
  filterFn?: (row: T, columnId: string, filterValue: any) => boolean;
  /** Extended meta properties */
  meta?: {
    /** Whether this column should be hidden by default */
    defaultHidden?: boolean;
    [key: string]: any;
  };
}

/**
 * Mobile display modes
 */
export type MobileDisplayMode = 'cards' | 'horizontal-scroll' | 'stacked';

/**
 * Table density options
 */
export type TableDensity = 'compact' | 'normal' | 'comfortable';

/**
 * Props for the DataTable component
 */
export interface DataTableProps<T> {
  /** Data to display in the table */
  data: T[];
  /** Column definitions */
  columns: DataTableColumn<T>[];
  /** Whether to enable sorting */
  enableSorting?: boolean;
  /** Whether to enable filtering */
  enableFiltering?: boolean;
  /** Whether to enable global search */
  enableGlobalSearch?: boolean;
  /** Whether to enable pagination */
  enablePagination?: boolean;
  /** Whether to enable row selection */
  enableRowSelection?: boolean;
  /** Whether to enable column visibility toggle */
  enableColumnVisibility?: boolean;
  /** Mobile display mode */
  mobileDisplayMode?: MobileDisplayMode;
  /** Table density */
  density?: TableDensity;
  /** Initial sorting state */
  initialSorting?: SortingState;
  /** Initial column filters */
  initialColumnFilters?: ColumnFiltersState;
  /** Initial global filter */
  initialGlobalFilter?: string;
  /** Initial pagination state */
  initialPagination?: PaginationState;
  /** Initial column visibility state */
  initialColumnVisibility?: Record<string, boolean>;
  /** Page size options */
  pageSizeOptions?: number[];
  /** Whether to use server-side pagination */
  manualPagination?: boolean;
  /** Total number of rows for server-side pagination */
  totalRows?: number | undefined;
  /** Whether the data is loading */
  isLoading?: boolean;
  /** Loading skeleton rows count */
  loadingRows?: number;
  /** Error state */
  error?: Error | null;
  /** CSS class name for the table container */
  className?: string;
  /** Whether to show table caption for accessibility */
  showCaption?: boolean;
  /** Table caption text */
  caption?: string;
  /** Whether to use sticky header */
  stickyHeader?: boolean;
  /** Maximum height for the table container */
  maxHeight?: string;
  /** Callback for when sorting changes */
  onSortingChange?: (sorting: SortingState) => void;
  /** Callback for when column filters change */
  onColumnFiltersChange?: (filters: ColumnFiltersState) => void;
  /** Callback for when global filter changes */
  onGlobalFilterChange?: (filter: string) => void;
  /** Callback for when pagination changes */
  onPaginationChange?: ((pagination: PaginationState) => void) | undefined;
  /** Callback for when row selection changes */
  onRowSelectionChange?: (selection: Record<string, boolean>) => void;
  /** Callback for when column visibility changes */
  onColumnVisibilityChange?: (visibility: Record<string, boolean>) => void;
  /** Callback for when a row is clicked */
  onRowClick?: (row: T) => void;
  /** Render function for row actions */
  renderRowActions?: (row: T) => ReactNode;
  /** Render function for table toolbar */
  renderToolbar?: () => ReactNode;
  /** Render function for empty state */
  renderEmptyState?: () => ReactNode;
  /** Render function for loading state */
  renderLoadingState?: () => ReactNode;
  /** Render function for error state */
  renderErrorState?: (error: Error) => ReactNode;
  /** Custom mobile card renderer */
  renderMobileCard?: (row: T, index: number) => ReactNode;
}

/**
 * Props for the DataTable toolbar
 */
export interface DataTableToolbarProps<T> {
  /** Table instance */
  table: any;
  /** Whether global search is enabled */
  enableGlobalSearch?: boolean;
  /** Whether column visibility toggle is enabled */
  enableColumnVisibility?: boolean;
  /** Custom toolbar content */
  children?: ReactNode;
}

/**
 * Props for the DataTable pagination
 */
export interface DataTablePaginationProps {
  /** Table instance */
  table: any;
  /** Page size options */
  pageSizeOptions?: number[];
  /** Total number of rows for server-side pagination */
  totalRows?: number | undefined;
}

/**
 * Props for mobile card view
 */
export interface MobileCardProps<T> {
  /** Row data */
  row: T;
  /** Column definitions */
  columns: DataTableColumn<T>[];
  /** Row index */
  index: number;
  /** Click handler */
  onClick?: (() => void) | undefined;
  /** Row actions renderer */
  renderRowActions?: ((row: T) => ReactNode) | undefined;
}
