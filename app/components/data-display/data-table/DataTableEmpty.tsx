"use client";

import { cn } from '@/app/lib/utils';
import { Database } from 'lucide-react';

interface DataTableEmptyProps {
  title?: string;
  description?: string;
  icon?: React.ReactNode;
  className?: string;
  children?: React.ReactNode;
}

/**
 * Empty state component for DataTable
 */
export function DataTableEmpty({
  title = 'No data found',
  description = 'There are no items to display at the moment.',
  icon,
  className,
  children,
}: DataTableEmptyProps) {
  return (
    <div className={cn('flex flex-col items-center justify-center p-8 text-center', className)}>
      <div className="rounded-full bg-muted/50 p-3 mb-4">
        {icon || <Database className="h-6 w-6 text-muted-foreground" />}
      </div>
      
      <h3 className="text-lg font-semibold mb-2">
        {title}
      </h3>
      
      <p className="text-muted-foreground mb-4 max-w-md">
        {description}
      </p>
      
      {children}
    </div>
  );
}
