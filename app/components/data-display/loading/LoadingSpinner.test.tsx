/**
 * @jest-environment jsdom
 */

import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import { 
  LoadingSpinner, 
  LoadingOverlay, 
  LoadingInline, 
  LoadingCard,
  SearchLoadingIndicator 
} from './LoadingSpinner';

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

describe('LoadingSpinner', () => {
  it('renders basic spinner', () => {
    const { container } = render(<LoadingSpinner />);
    expect(container.firstChild).toBeInTheDocument();
  });

  it('renders with message', () => {
    render(<LoadingSpinner message="Loading data..." />);
    expect(screen.getByText('Loading data...')).toBeInTheDocument();
  });

  it('renders with progress', () => {
    render(<LoadingSpinner progress={75} showProgress message="Processing..." />);
    expect(screen.getByText('Processing...')).toBeInTheDocument();
    expect(screen.getByText('75%')).toBeInTheDocument();
  });

  it('applies correct variant classes', () => {
    const { container } = render(<LoadingSpinner variant="accent" size="lg" />);
    expect(container.firstChild).toHaveClass('text-accent-primary');
  });

  it('renders overlay container', () => {
    const { container } = render(<LoadingSpinner container="overlay" />);
    expect(container.firstChild).toHaveClass('fixed', 'inset-0', 'z-50');
  });
});

describe('LoadingOverlay', () => {
  it('renders overlay with default props', () => {
    const { container } = render(<LoadingOverlay />);
    expect(container.firstChild).toHaveClass('fixed', 'inset-0');
  });

  it('renders with custom message', () => {
    render(<LoadingOverlay message="Loading form..." />);
    expect(screen.getByText('Loading form...')).toBeInTheDocument();
  });
});

describe('LoadingInline', () => {
  it('renders inline loading', () => {
    const { container } = render(<LoadingInline />);
    expect(container.firstChild).toHaveClass('flex', 'items-center', 'justify-center', 'gap-2');
  });
});

describe('LoadingCard', () => {
  it('renders card loading', () => {
    const { container } = render(<LoadingCard />);
    expect(container.firstChild).toHaveClass('rounded-lg', 'border', 'bg-card');
  });
});

describe('SearchLoadingIndicator', () => {
  it('renders search loading with default message', () => {
    render(<SearchLoadingIndicator />);
    expect(screen.getByText('Searching...')).toBeInTheDocument();
  });

  it('renders with custom message', () => {
    render(<SearchLoadingIndicator message="Finding parts..." />);
    expect(screen.getByText('Finding parts...')).toBeInTheDocument();
  });
});

describe('Accessibility', () => {
  it('has proper ARIA attributes', () => {
    render(<LoadingSpinner message="Loading..." />);
    const spinner = screen.getByRole('generic');
    expect(spinner).toBeInTheDocument();
  });

  it('respects reduced motion preferences', () => {
    // Mock reduced motion preference
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });

    render(<LoadingSpinner animate={false} />);
    expect(screen.getByRole('generic')).toBeInTheDocument();
  });
});

describe('Theme Integration', () => {
  it('applies theme-aware classes', () => {
    const { container } = render(<LoadingSpinner variant="destructive" />);
    expect(container.firstChild).toHaveClass('text-theme-error');
  });

  it('supports all variant types', () => {
    const variants = ['default', 'muted', 'accent', 'destructive', 'success', 'warning'] as const;
    
    variants.forEach(variant => {
      const { container } = render(<LoadingSpinner variant={variant} />);
      expect(container.firstChild).toBeInTheDocument();
    });
  });

  it('supports all size types', () => {
    const sizes = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'] as const;
    
    sizes.forEach(size => {
      const { container } = render(<LoadingSpinner size={size} />);
      expect(container.firstChild).toBeInTheDocument();
    });
  });
});
