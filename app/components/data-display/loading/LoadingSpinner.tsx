"use client";

import { cn } from "@/app/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";
import { motion, AnimatePresence } from "framer-motion";
import { Loader2 } from "lucide-react";
import React from "react";

/**
 * LoadingSpinner variants using CVA for consistent styling
 */
const loadingSpinnerVariants = cva(
  "flex items-center justify-center",
  {
    variants: {
      variant: {
        default: "text-primary",
        muted: "text-muted-foreground",
        accent: "text-accent-primary",
        destructive: "text-theme-error",
        success: "text-theme-success",
        warning: "text-theme-warning",
      },
      size: {
        xs: "h-3 w-3",
        sm: "h-4 w-4", 
        md: "h-6 w-6",
        lg: "h-8 w-8",
        xl: "h-10 w-10",
        "2xl": "h-12 w-12",
      },
      type: {
        spinner: "",
        pulse: "",
        dots: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
      type: "spinner",
    },
  }
);

/**
 * Container variants for different loading contexts
 */
const containerVariants = cva(
  "flex items-center justify-center",
  {
    variants: {
      container: {
        inline: "gap-2",
        block: "flex-col gap-3 py-4",
        overlay: "fixed inset-0 z-50 bg-background/80 backdrop-blur-sm",
        card: "rounded-lg border bg-card p-6 shadow-sm",
        fullscreen: "min-h-screen",
      },
    },
    defaultVariants: {
      container: "inline",
    },
  }
);

export interface LoadingSpinnerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof loadingSpinnerVariants>,
    VariantProps<typeof containerVariants> {
  /**
   * Loading message to display
   */
  message?: string;
  
  /**
   * Whether to show animated entrance
   */
  animate?: boolean;
  
  /**
   * Whether to show pulse effect around spinner
   */
  showPulse?: boolean;
  
  /**
   * Custom spinner icon
   */
  icon?: React.ReactNode;
  
  /**
   * Progress value (0-100) for progress indicators
   */
  progress?: number;
  
  /**
   * Whether to show progress text
   */
  showProgress?: boolean;
}

/**
 * Standardized LoadingSpinner component that replaces all custom loading implementations
 * 
 * Features:
 * - Multiple variants for different contexts
 * - Consistent theming and animations
 * - Accessibility support
 * - Progress indication
 * - Customizable messages and icons
 * 
 * @example
 * ```tsx
 * // Basic spinner
 * <LoadingSpinner />
 * 
 * // Overlay loading
 * <LoadingSpinner 
 *   container="overlay" 
 *   size="lg" 
 *   message="Loading assembly form..." 
 * />
 * 
 * // Card loading
 * <LoadingSpinner 
 *   container="card" 
 *   variant="muted" 
 *   message="Loading data..." 
 * />
 * ```
 */
export const LoadingSpinner = React.forwardRef<HTMLDivElement, LoadingSpinnerProps>(
  (
    {
      className,
      variant,
      size,
      type,
      container,
      message,
      animate = true,
      showPulse = false,
      icon,
      progress,
      showProgress = false,
      ...props
    },
    ref
  ) => {
    const spinnerIcon = icon || <Loader2 className={cn("animate-spin", loadingSpinnerVariants({ variant, size }))} />;
    
    const content = (
      <div className={cn(containerVariants({ container }), className)} ref={ref} {...props}>
        {/* Spinner with optional pulse effect */}
        <div className="relative flex items-center justify-center">
          {spinnerIcon}
          {showPulse && (
            <div 
              className={cn(
                "absolute inset-0 rounded-full animate-ping opacity-20",
                loadingSpinnerVariants({ variant, size })
              )}
              style={{ backgroundColor: "currentColor" }}
            />
          )}
        </div>
        
        {/* Message */}
        {message && (
          <div className="text-center">
            <p className={cn(
              "font-medium",
              container === "overlay" ? "text-lg" : "text-sm",
              variant === "muted" ? "text-muted-foreground" : "text-foreground"
            )}>
              {message}
            </p>
            
            {/* Progress indicator */}
            {typeof progress === "number" && (
              <div className="mt-2 w-full max-w-xs">
                <div className="h-2 bg-muted rounded-full overflow-hidden">
                  <motion.div
                    className="h-full bg-primary rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
                    transition={{ duration: 0.3 }}
                  />
                </div>
                {showProgress && (
                  <p className="text-xs text-muted-foreground mt-1">
                    {Math.round(progress)}%
                  </p>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    );

    if (!animate) {
      return content;
    }

    return (
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, scale: container === "overlay" ? 0.9 : 1 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: container === "overlay" ? 0.9 : 1 }}
          transition={{ 
            duration: container === "overlay" ? 0.3 : 0.2,
            ease: "easeOut"
          }}
        >
          {content}
        </motion.div>
      </AnimatePresence>
    );
  }
);

LoadingSpinner.displayName = "LoadingSpinner";

/**
 * Specialized loading components for common use cases
 */

/**
 * Loading overlay for forms and modals
 */
export const LoadingOverlay: React.FC<Omit<LoadingSpinnerProps, "container">> = (props) => (
  <LoadingSpinner container="overlay" size="lg" showPulse animate {...props} />
);

/**
 * Inline loading indicator for buttons and small components
 */
export const LoadingInline: React.FC<Omit<LoadingSpinnerProps, "container">> = (props) => (
  <LoadingSpinner container="inline" size="sm" {...props} />
);

/**
 * Card loading state for content areas
 */
export const LoadingCard: React.FC<Omit<LoadingSpinnerProps, "container">> = (props) => (
  <LoadingSpinner container="card" variant="muted" {...props} />
);

/**
 * Fullscreen loading for page transitions
 */
export const LoadingFullscreen: React.FC<Omit<LoadingSpinnerProps, "container">> = (props) => (
  <LoadingSpinner container="fullscreen" size="xl" showPulse animate {...props} />
);

/**
 * Search loading indicator for search results
 */
export const SearchLoadingIndicator: React.FC<{
  message?: string;
  className?: string;
}> = ({ message = "Searching...", className }) => (
  <div className={cn("flex items-center justify-center py-6 text-muted-foreground", className)}>
    <LoadingSpinner
      size="md"
      variant="muted"
      container="inline"
      message={message}
    />
  </div>
);
