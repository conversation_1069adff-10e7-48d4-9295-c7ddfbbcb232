/**
 * Standardized Loading Components
 * 
 * This module provides a comprehensive set of loading components that replace
 * all custom loading implementations across the application.
 * 
 * Components:
 * - LoadingSpinner: Core spinner component with variants
 * - LoadingSkeleton: Skeleton loading for content layouts
 * - Specialized components for common use cases
 * 
 * Usage:
 * ```tsx
 * import { LoadingOverlay, LoadingCard, PageLoadingSkeleton } from '@/app/components/data-display/loading';
 * 
 * // Overlay loading
 * <LoadingOverlay message="Loading assembly form..." />
 * 
 * // Card loading
 * <LoadingCard message="Loading data..." />
 * 
 * // Page loading
 * <PageLoadingSkeleton contentType="grid" />
 * ```
 */

// Core loading components
export {
  LoadingSpinner,
  LoadingOverlay,
  LoadingInline,
  LoadingCard,
  LoadingFullscreen,
  SearchLoadingIndicator,
  type LoadingSpinnerProps,
} from './LoadingSpinner';

export {
  LoadingSkeleton,
  PageLoadingSkeleton,
  ChartLoadingSkeleton,
  FormLoadingSkeleton,
  type LoadingSkeletonProps,
} from './LoadingSkeleton';

// Re-export the basic Skeleton component for backward compatibility
export { Skeleton } from '../skeleton';

/**
 * Migration guide for replacing custom loading implementations:
 * 
 * 1. LazyUnifiedAssemblyForm.tsx LoadingFallback:
 *    Replace with: <LoadingOverlay message="Loading Assembly Form..." />
 * 
 * 2. LazyAssembliesPageWrapper.tsx LoadingFallback:
 *    Replace with: <PageLoadingSkeleton contentType="grid" />
 * 
 * 3. Chart loading components:
 *    Replace with: <ChartLoadingSkeleton title="Chart Name" />
 * 
 * 4. Form loading overlays:
 *    Replace with: <LoadingOverlay size="md" message="Loading..." />
 * 
 * 5. Button loading states:
 *    Replace with: <LoadingInline size="sm" /> (already well-implemented in FormSubmitButton)
 * 
 * 6. Search loading indicators:
 *    Replace with: <LoadingInline variant="muted" message="Searching..." />
 * 
 * 7. Grid/List loading:
 *    Replace with: <LoadingSkeleton variant="grid" count={8} columns={4} />
 */

/**
 * Common loading patterns and their replacements:
 */

// Pattern 1: Overlay with motion animation
// Before: Custom LoadingFallback with fixed positioning and motion
// After: <LoadingOverlay message="Loading..." />

// Pattern 2: Inline loading with Loader2
// Before: <Loader2 className="h-6 w-6 animate-spin" />
// After: <LoadingInline size="md" />

// Pattern 3: Card loading state
// Before: Custom skeleton cards with manual layout
// After: <LoadingSkeleton variant="card" count={3} />

// Pattern 4: Page loading
// Before: Container with custom loading layout
// After: <PageLoadingSkeleton contentType="grid" />

// Pattern 5: Form loading overlay
// Before: Absolute positioned div with backdrop
// After: <LoadingOverlay container="overlay" />

/**
 * Theme integration:
 * All loading components automatically integrate with the theme system:
 * - Respect dark/light mode
 * - Use theme colors for variants
 * - Follow theme animation patterns
 * - Support reduced motion preferences
 */

/**
 * Accessibility features:
 * - ARIA labels for screen readers
 * - Reduced motion support
 * - Proper focus management
 * - Color contrast compliance
 * - Semantic HTML structure
 */
