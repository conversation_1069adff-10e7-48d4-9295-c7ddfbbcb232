'use client';

import { Package, Layers, ChevronRight } from 'lucide-react';
import { Badge } from '@/app/components/data-display/badge';
import { cn } from '@/app/lib/utils';

interface HierarchicalPartsListProps {
  parts: any[];
  maxDepth?: number;
  showQuantity?: boolean;
  showStock?: boolean;
  className?: string;
}

interface PartItemProps {
  part: any;
  level: number;
  maxDepth: number;
  showQuantity: boolean;
  showStock: boolean;
}

/**
 * Component for displaying a single part item with proper indentation
 */
function PartItem({ part, level, maxDepth, showQuantity, showStock }: PartItemProps) {
  if (!part) return null;

  // Get part details - handle both populated and unpopulated partId
  let partDetails = null;
  let partName = 'Unknown Part';
  let partNumber = '';
  let currentStock = 0;

  if (part.partDetails) {
    partDetails = part.partDetails;
  } else if (typeof part.partId === 'object' && part.partId !== null) {
    partDetails = part.partId;
  }

  if (partDetails) {
    // Use businessName if available, fallback to technical name
    partName = partDetails.businessName || partDetails.name || partDetails.partNumber || 'Unknown Part';
    partNumber = partDetails.partNumber || '';
    currentStock = partDetails.inventory?.currentStock || 0;
  } else if (typeof part.partId === 'string') {
    partName = `Part ${part.partId}`;
  }

  const quantity = part.quantityRequired || 1;
  const unitOfMeasure = part.unitOfMeasure || 'ea';
  const hasChildren = part.children && Array.isArray(part.children) && part.children.length > 0;

  return (
    <div className="space-y-1">
      {/* Current part */}
      <div 
        className={cn(
          "flex items-center gap-2 p-2 rounded-md border bg-card",
          level > 0 && "ml-4 border-l-2 border-l-primary/30"
        )}
        style={{ marginLeft: `${level * 16}px` }}
      >
        {/* Indentation indicator */}
        {level > 0 && (
          <ChevronRight className="h-3 w-3 text-muted-foreground flex-shrink-0" />
        )}
        
        {/* Part icon */}
        <div className="flex-shrink-0">
          {hasChildren ? (
            <Layers className="h-4 w-4 text-primary" />
          ) : (
            <Package className="h-4 w-4 text-muted-foreground" />
          )}
        </div>

        {/* Part information */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm truncate">
              {partName}
            </span>
            {partNumber && (
              <Badge variant="outline" className="text-xs font-mono">
                {partNumber}
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-3 mt-1">
            {showQuantity && (
              <span className="text-xs text-muted-foreground">
                Qty: {quantity} {unitOfMeasure}
              </span>
            )}
            {showStock && partDetails && (
              <span className={cn(
                "text-xs",
                currentStock > 0 ? "text-green-600" : "text-red-600"
              )}>
                Stock: {currentStock}
              </span>
            )}
            {hasChildren && (
              <span className="text-xs text-primary">
                {part.children.length} child{part.children.length !== 1 ? 'ren' : ''}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Render children recursively */}
      {hasChildren && level < maxDepth && (
        <div className="space-y-1">
          {part.children.map((child: any, index: number) => (
            <PartItem
              key={`child-${level}-${index}`}
              part={child}
              level={level + 1}
              maxDepth={maxDepth}
              showQuantity={showQuantity}
              showStock={showStock}
            />
          ))}
        </div>
      )}
    </div>
  );
}

/**
 * Component for displaying hierarchical parts list
 * Shows parts with proper indentation and nesting
 */
export function HierarchicalPartsList({
  parts,
  maxDepth = 5,
  showQuantity = true,
  showStock = true,
  className
}: HierarchicalPartsListProps) {
  if (!parts || !Array.isArray(parts) || parts.length === 0) {
    return (
      <div className={cn("flex flex-col items-center justify-center py-8 space-y-4", className)}>
        <Package className="h-12 w-12 text-muted-foreground/50" />
        <p className="text-sm text-muted-foreground text-center">
          No parts found in this assembly
        </p>
      </div>
    );
  }

  return (
    <div className={cn("space-y-2", className)}>
      {parts.map((part, index) => (
        <PartItem
          key={`root-${index}`}
          part={part}
          level={0}
          maxDepth={maxDepth}
          showQuantity={showQuantity}
          showStock={showStock}
        />
      ))}
    </div>
  );
}

/**
 * Compact version for use in cards and small spaces
 */
export function CompactHierarchicalPartsList({
  parts,
  maxItems = 3,
  className
}: {
  parts: any[];
  maxItems?: number;
  className?: string;
}) {
  if (!parts || !Array.isArray(parts) || parts.length === 0) {
    return (
      <p className={cn("text-xs text-muted-foreground", className)}>
        No parts
      </p>
    );
  }

  // Flatten the hierarchical structure for compact display
  const flattenParts = (partsList: any[], level: number = 0): any[] => {
    const flattened: any[] = [];
    
    partsList.forEach(part => {
      flattened.push({ ...part, level });
      
      if (part.children && Array.isArray(part.children)) {
        flattened.push(...flattenParts(part.children, level + 1));
      }
    });
    
    return flattened;
  };

  const flatParts = flattenParts(parts);
  const displayParts = flatParts.slice(0, maxItems);
  const remainingCount = flatParts.length - maxItems;

  return (
    <div className={cn("space-y-1", className)}>
      {displayParts.map((part, index) => {
        const partName = part.partDetails?.businessName || part.partDetails?.name ||
                        (typeof part.partId === 'object' ? (part.partId?.businessName || part.partId?.name) : null) ||
                        'Unknown Part';
        const quantity = part.quantityRequired || 1;
        const partId = part.partDetails?._id ||
                      (typeof part.partId === 'object' ? part.partId?._id : part.partId) ||
                      `part-${index}`;

        return (
          <div key={`${partId}-${index}-${part.level || 0}`} className="flex items-center text-xs">
            <span 
              className="w-2 inline-block" 
              style={{ marginLeft: `${part.level * 8}px` }}
            >
              {part.level > 0 ? '└' : '•'}
            </span>
            <span className="font-medium truncate flex-1">
              {partName}
            </span>
            <span className="text-muted-foreground ml-1">
              ({quantity}x)
            </span>
          </div>
        );
      })}
      
      {remainingCount > 0 && (
        <div className="text-xs text-muted-foreground">
          +{remainingCount} more parts...
        </div>
      )}
    </div>
  );
}
