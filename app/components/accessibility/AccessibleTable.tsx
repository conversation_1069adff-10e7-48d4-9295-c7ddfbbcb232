'use client';

import {
    Table,
    TableBody,
    TableCaption,
    TableFooter,
    TableHeader,
    TableRow
} from '@/app/components/data-display/table';
import { cn } from '@/app/lib/utils';
import React, { useEffect, useRef, useState } from 'react';
import { LiveRegion } from './LiveRegion';
import { VisuallyHidden } from './VisuallyHidden';

export interface AccessibleTableProps extends React.HTMLAttributes<HTMLTableElement> {
  /**
   * Table caption (required for accessibility)
   */
  caption: string;
  
  /**
   * Whether to visually hide the caption
   */
  hideCaption?: boolean;
  
  /**
   * Table summary for screen readers
   */
  summary?: string;
  
  /**
   * Whether the table has a header
   */
  hasHeader?: boolean;
  
  /**
   * Whether the table has a footer
   */
  hasFooter?: boolean;
  
  /**
   * Whether the table is sortable
   */
  isSortable?: boolean;
  
  /**
   * Whether the table is currently being sorted
   */
  isSorting?: boolean;
  
  /**
   * Whether the table data is loading
   */
  isLoading?: boolean;
  
  /**
   * Message to announce when data is loading
   */
  loadingMessage?: string;
  
  /**
   * Message to announce when data is loaded
   */
  loadedMessage?: string;
  
  /**
   * Whether to enable keyboard navigation within the table
   */
  enableKeyboardNavigation?: boolean;
  
  /**
   * Additional class name for the table container
   */
  containerClassName?: string;
  
  /**
   * Additional class name for the table
   */
  tableClassName?: string;
  
  /**
   * Additional class name for the caption
   */
  captionClassName?: string;
  
  /**
   * Children elements
   */
  children: React.ReactNode;
}

/**
 * AccessibleTable component that enhances the standard Table with accessibility features
 * 
 * @param props - Component properties
 * @returns React component
 */
export const AccessibleTable = React.forwardRef<HTMLTableElement, AccessibleTableProps>(
  (props, ref) => {
    const {
      caption,
      hideCaption = false,
      summary,
      hasHeader = true,
      hasFooter = false,
      isSortable = false,
      isSorting = false,
      isLoading = false,
      loadingMessage = 'Loading...',
      loadedMessage = 'Content loaded',
      enableKeyboardNavigation = true,
      containerClassName = '',
      tableClassName = '',
      captionClassName = '',
      children,
    } = props as AccessibleTableProps;

    const tableRef = useRef<HTMLTableElement>(null);
    const [prevLoading, setPrevLoading] = useState(isLoading);
    const [announcement, setAnnouncement] = useState<string | null>(null);
    const [focusedCell, setFocusedCell] = useState<{ row: number; col: number } | null>(null);
    
    // Merge refs
    const mergedRef = (node: HTMLTableElement) => {
      if (typeof ref === 'function') {
        ref(node);
      } else if (ref) {
        ref.current = node;
      }
      tableRef.current = node;
    };
    
    // Announce loading state changes
    useEffect(() => {
      if (prevLoading && !isLoading) {
        setAnnouncement(loadedMessage);
      } else if (!prevLoading && isLoading) {
        setAnnouncement(loadingMessage);
      }
      
      setPrevLoading(isLoading);
      
      // Clear announcement after 3 seconds
      if (announcement) {
        const timer = setTimeout(() => {
          setAnnouncement(null);
        }, 3000);

        return () => clearTimeout(timer);
      }

      return undefined;
    }, [isLoading, prevLoading, loadingMessage, loadedMessage, announcement]);
    
    // Handle keyboard navigation
    const handleKeyDown = (e: React.KeyboardEvent<HTMLTableElement | HTMLTableRowElement | HTMLTableCellElement>) => {
      if (!enableKeyboardNavigation || !focusedCell) return;
      
      const table = tableRef.current;
      if (!table) return;
      
      const rows = Array.from(table.querySelectorAll('tr'));
      const currentRow = rows[focusedCell.row];
      if (!currentRow) return;
      
      const cells = Array.from(currentRow.querySelectorAll('th, td'));
      const currentCell = cells[focusedCell.col];
      if (!currentCell) return;
      
      let newRow = focusedCell.row;
      let newCol = focusedCell.col;
      
      switch (e.key) {
        case 'ArrowUp':
          newRow = Math.max(hasHeader ? 1 : 0, focusedCell.row - 1);
          break;
        case 'ArrowDown':
          newRow = Math.min(rows.length - 1, focusedCell.row + 1);
          break;
        case 'ArrowLeft':
          newCol = Math.max(0, focusedCell.col - 1);
          break;
        case 'ArrowRight':
          newCol = Math.min(cells.length - 1, focusedCell.col + 1);
          break;
        case 'Home':
          if (e.ctrlKey) {
            newRow = hasHeader ? 1 : 0;
            newCol = 0;
          } else {
            newCol = 0;
          }
          break;
        case 'End':
          if (e.ctrlKey) {
            newRow = rows.length - 1;
            newCol = cells.length - 1;
          } else {
            newCol = cells.length - 1;
          }
          break;
        default:
          return;
      }
      
      // Prevent default behavior
      e.preventDefault();
      
      // Update focused cell
      setFocusedCell({ row: newRow, col: newCol });
      
      // Focus the new cell
      const newRowElement = rows[newRow];
      if (!newRowElement) return;
      
      const newCells = Array.from(newRowElement.querySelectorAll('th, td'));
      const newCellElement = newCells[newCol] as HTMLElement;
      if (!newCellElement) return;
      
      newCellElement.focus();
    };
    
    // Handle cell focus
    const handleCellFocus = (rowIndex: number, colIndex: number) => {
      setFocusedCell({ row: rowIndex, col: colIndex });
    };
    
    // Helper function to clone elements with proper typing
    const cloneWithProps = (
      element: React.ReactElement,
      props: React.HTMLAttributes<HTMLElement>,
      ...children: React.ReactNode[]
    ) => {
      return React.cloneElement(element, props, ...children);
    };

    // Add keyboard navigation attributes to children
    const enhanceChildrenWithKeyboardNav = (nodes: React.ReactNode): React.ReactNode => {
      if (!nodes) return null;
      
      return React.Children.map(nodes, (child, rowIndex) => {
        if (!React.isValidElement(child)) return child;
        
        // Handle TableHeader, TableBody, TableFooter
        if (child.type === TableHeader || child.type === TableBody || child.type === TableFooter) {
          const enhancedChildren = enhanceChildrenWithKeyboardNav(
            (child.props as { children?: React.ReactNode }).children
          );
          // Safely spread only valid props
          const safeProps = typeof child.props === 'object' && child.props !== null 
            ? { ...child.props }
            : {};
            
          return cloneWithProps(
            child,
            {
              ...safeProps,
              children: enhancedChildren
            }
          );
        }
        
        // Handle TableRow
        if (child.type === TableRow) {
          const rowProps = child.props as React.HTMLAttributes<HTMLTableRowElement> & {
            children?: React.ReactNode;
          };
          
          const rowChildren = React.Children.map(rowProps.children, (cell, colIndex) => {
            if (!React.isValidElement(cell)) return cell;
            
            const cellProps = cell.props as React.TdHTMLAttributes<HTMLTableCellElement>;
            
            return cloneWithProps(
              cell,
              {
                ...cellProps,
                tabIndex: focusedCell?.row === rowIndex && focusedCell?.col === colIndex ? 0 : -1,
                onFocus: (e: React.FocusEvent<HTMLTableCellElement>) => {
                  cellProps.onFocus?.(e);
                  handleCellFocus(rowIndex, colIndex);
                },
                onKeyDown: (e: React.KeyboardEvent<HTMLTableCellElement>) => {
                  handleKeyDown(e as any);
                  cellProps.onKeyDown?.(e);
                }
              }
            );
          });
          
          return cloneWithProps(
            child,
            {
              ...rowProps,
              onKeyDown: (e: React.KeyboardEvent<HTMLTableRowElement>) => {
                handleKeyDown(e as any);
                rowProps.onKeyDown?.(e);
              }
            },
            rowChildren
          );
        }
        
        return child;
      });
    };
    
    const enhancedChildren = enableKeyboardNavigation
      ? enhanceChildrenWithKeyboardNav(children)
      : children;
    
    return (
      <div className={cn("relative w-full overflow-auto", containerClassName)}>
        <Table
          ref={mergedRef}
          className={cn("w-full caption-bottom text-sm", tableClassName)}
          role="grid"
          aria-busy={isLoading}
          aria-describedby={summary ? `${caption}-summary` : undefined}
          onKeyDown={handleKeyDown}
          {...props}
        >
          {summary && (
            <VisuallyHidden id={`${caption}-summary`}>
              {summary}
            </VisuallyHidden>
          )}
          
          <TableCaption
            className={cn(
              hideCaption && "sr-only",
              captionClassName
            )}
          >
            {caption}
          </TableCaption>
          
          {enhancedChildren}
        </Table>
        
        {/* Announce loading state changes */}
        {announcement && (
          <LiveRegion politeness="assertive">
            {announcement}
          </LiveRegion>
        )}
      </div>
    );
  }
);

AccessibleTable.displayName = 'AccessibleTable';

export default AccessibleTable;
