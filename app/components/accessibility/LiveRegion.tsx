'use client';

import React from 'react';
import { cn } from '@/app/lib/utils';

interface LiveRegionProps {
  children?: React.ReactNode;
  politeness?: 'polite' | 'assertive';
  atomic?: boolean;
  relevant?: 'additions' | 'removals' | 'text' | 'all';
  className?: string;
}

/**
 * LiveRegion component for announcing dynamic content changes to screen readers
 * 
 * @param props - Component properties
 * @returns React component
 */
export function LiveRegion({
  children,
  politeness = 'polite',
  atomic = true,
  relevant = 'additions',
  className,
  ...props
}: LiveRegionProps & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      aria-live={politeness}
      aria-atomic={atomic}
      aria-relevant={relevant}
      className={cn(
        children ? "" : "sr-only",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

export default LiveRegion;
