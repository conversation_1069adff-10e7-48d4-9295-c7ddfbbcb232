'use client';

import React from 'react';
import { cn } from '@/app/lib/utils';

interface VisuallyHiddenProps {
  children: React.ReactNode;
  className?: string;
  as?: React.ElementType;
}

/**
 * VisuallyHidden component for content that should be available to screen readers
 * but not visible on screen
 * 
 * @param props - Component properties
 * @returns React component
 */
export function VisuallyHidden({
  children,
  className,
  as: Component = 'span',
  ...props
}: VisuallyHiddenProps & React.HTMLAttributes<HTMLElement>) {
  return (
    <Component
      className={cn("sr-only", className)}
      {...props}
    >
      {children}
    </Component>
  );
}

export default VisuallyHidden;
