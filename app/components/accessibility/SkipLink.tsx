'use client';

import React from 'react';
import { cn } from '@/app/lib/utils';

interface SkipLinkProps {
  className?: string;
  targetId?: string;
  label?: string;
}

/**
 * SkipLink component for keyboard users to bypass navigation
 * 
 * @param props - Component properties
 * @returns React component
 */
export function SkipLink({
  className,
  targetId = "main-content",
  label = "Skip to main content"
}: SkipLinkProps) {
  return (
    <a
      href={`#${targetId}`}
      className={cn(
        "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50",
        "focus:bg-background focus:p-4 focus:rounded-md focus:outline-none focus:ring-2 focus:ring-ring",
        "dark:focus:bg-dark-element dark:focus:text-dark-text-primary dark:focus:ring-dark-accent",
        className
      )}
    >
      {label}
    </a>
  );
}

export default SkipLink;
