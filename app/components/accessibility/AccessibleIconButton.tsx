'use client';

import { VisuallyHidden } from '@/app/components/accessibility/VisuallyHidden';
import { Button, ButtonProps } from '@/app/components/forms/Button';
import { cn } from '@/app/lib/utils';
import { getAriaAttributes, shouldReduceAnimations } from '@/app/utils/accessibility';
import React from 'react';

export interface AccessibleIconButtonProps extends Omit<ButtonProps, 'size'> {
  /**
   * Icon to display in the button
   */
  icon: React.ReactNode;
  
  /**
   * Accessible label for the button (required for icon-only buttons)
   */
  accessibleLabel: string;
  
  /**
   * Size of the button
   */
  size?: 'sm' | 'md' | 'lg';
  
  /**
   * Whether the button controls an expandable element
   */
  isExpanded?: boolean;
  
  /**
   * Whether the button is in a pressed state (for toggle buttons)
   */
  isPressed?: boolean;
  
  /**
   * ID of the element controlled by this button
   */
  controls?: string;
  
  /**
   * Whether to show a visible focus indicator even when using mouse
   */
  alwaysShowFocus?: boolean;
  
  /**
   * Whether to reduce motion for animations
   */
  reduceMotion?: boolean;
  
  /**
   * Whether the button is disabled but should remain focusable
   */
  isFocusableWhenDisabled?: boolean;
}

/**
 * AccessibleIconButton component for icon-only buttons with proper accessibility
 * 
 * @param props - Component properties
 * @returns React component
 */
export const AccessibleIconButton = React.forwardRef<HTMLButtonElement, AccessibleIconButtonProps>(
  ({
    icon,
    accessibleLabel,
    size = 'md',
    isExpanded,
    isPressed,
    controls,
    alwaysShowFocus = false,
    reduceMotion: propReduceMotion,
    isFocusableWhenDisabled = false,
    className,
    disabled,
    ...props
  }, ref) => {
    // Determine if we should reduce motion based on props or system preferences
    const reduceMotion = propReduceMotion ?? shouldReduceAnimations();
    
    // Get ARIA attributes based on button state
    const ariaAttributes = getAriaAttributes('button', {
      isExpanded,
      isPressed,
      controls,
      label: accessibleLabel,
      isDisabled: disabled && isFocusableWhenDisabled
    });
    
    // Handle disabled but focusable state
    const isDisabled = disabled && !isFocusableWhenDisabled;
    const tabIndex = disabled && isFocusableWhenDisabled ? 0 : undefined;
    
    // Map size to button size
    const buttonSize = size === 'sm' ? 'icon-sm' : size === 'lg' ? 'icon-lg' : 'icon';
    
    // Enhanced class names for focus visibility
    const enhancedClassName = cn(
      'rounded-full flex items-center justify-center',
      size === 'sm' ? 'h-8 w-8' : size === 'lg' ? 'h-12 w-12' : 'h-10 w-10',
      alwaysShowFocus && 'focus:ring-2 focus:ring-theme-focus focus:ring-offset-2',
      reduceMotion && 'transition-none animate-none',
      className
    );
    
    const buttonProps: any = {
      variant: "ghost",
      size: buttonSize as any,
      className: enhancedClassName,
      tabIndex,
      ...ariaAttributes,
      ...props
    };

    if (isDisabled !== undefined) {
      buttonProps.disabled = isDisabled;
    }

    return (
      <Button
        ref={ref}
        {...buttonProps}
      >
        {icon}
        <VisuallyHidden>{accessibleLabel}</VisuallyHidden>
      </Button>
    );
  }
);

AccessibleIconButton.displayName = 'AccessibleIconButton';

export default AccessibleIconButton;
