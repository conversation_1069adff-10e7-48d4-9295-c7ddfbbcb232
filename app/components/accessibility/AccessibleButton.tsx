'use client';

import { VisuallyHidden } from '@/app/components/accessibility/VisuallyHidden';
import { Button, ButtonProps, buttonVariants } from '@/app/components/forms/Button';
import { cn } from '@/app/lib/utils';
import { getAriaAttributes, shouldReduceAnimations } from '@/app/utils/accessibility';
import { Slot } from '@radix-ui/react-slot';
import React from 'react';

export interface AccessibleButtonProps extends ButtonProps {
  /**
   * Accessible label for the button (required for icon-only buttons)
   */
  accessibleLabel?: string;
  
  /**
   * Whether the button controls an expandable element
   */
  isExpanded?: boolean;
  
  /**
   * Whether the button is in a pressed state (for toggle buttons)
   */
  isPressed?: boolean;
  
  /**
   * ID of the element controlled by this button
   */
  controls?: string;
  
  /**
   * Whether to show a visible focus indicator even when using mouse
   */
  alwaysShowFocus?: boolean;
  
  /**
   * Whether to reduce motion for animations
   */
  reduceMotion?: boolean;
  
  /**
   * Whether the button is disabled but should remain focusable
   */
  isFocusableWhenDisabled?: boolean;
}

/**
 * AccessibleButton component that enhances the standard Button with accessibility features
 * 
 * @param props - Component properties
 * @returns React component
 */
export const AccessibleButton = React.forwardRef<HTMLButtonElement, AccessibleButtonProps>(
  ({
    accessibleLabel,
    isExpanded,
    isPressed,
    controls,
    alwaysShowFocus = false,
    reduceMotion: propReduceMotion,
    isFocusableWhenDisabled = false,
    className,
    children,
    disabled,
    asChild = false,
    ...props
  }, ref) => {
    // Determine if we should reduce motion based on props or system preferences
    const reduceMotion = propReduceMotion ?? shouldReduceAnimations();
    
    // Get ARIA attributes based on button state
    const ariaAttributes = getAriaAttributes('button', {
      isExpanded,
      isPressed,
      controls,
      label: accessibleLabel,
      isDisabled: disabled && isFocusableWhenDisabled
    });
    
    // Handle disabled but focusable state
    const isDisabled = disabled && !isFocusableWhenDisabled;
    const tabIndex = disabled && isFocusableWhenDisabled ? 0 : undefined;
    
    // Determine if we need a visually hidden label
    const needsVisuallyHiddenLabel = accessibleLabel && 
      (!children || typeof children !== 'string' || React.Children.count(children) === 0);
    
    // Enhanced class names for focus visibility
    const enhancedClassName = cn(
      alwaysShowFocus && 'focus:ring-2 focus:ring-theme-focus focus:ring-offset-2',
      reduceMotion && 'transition-none animate-none',
      className
    );
    
    // If using asChild, we need to handle the rendering differently
    if (asChild) {
      return (
        <Slot
          className={cn(
            buttonVariants({ className: enhancedClassName, ...props }), 
            isFocusableWhenDisabled && disabled ? 'opacity-50 pointer-events-none' : ''
          )}
          ref={ref}
          aria-disabled={isDisabled}
          tabIndex={isDisabled && !isFocusableWhenDisabled ? -1 : tabIndex}
          {...ariaAttributes}
          {...props}
          style={{
            ...(isDisabled && { pointerEvents: 'none' }),
            ...(props as any).style
          }}
        >
          {children}
          {needsVisuallyHiddenLabel && <VisuallyHidden>{accessibleLabel}</VisuallyHidden>}
        </Slot>
      );
    }
    
    const buttonProps: any = {
      className: enhancedClassName,
      tabIndex,
      ...ariaAttributes,
      ...props
    };

    if (isDisabled !== undefined) {
      buttonProps.disabled = isDisabled;
    }

    return (
      <Button
        ref={ref}
        {...buttonProps}
      >
        {children}
        {needsVisuallyHiddenLabel && <VisuallyHidden>{accessibleLabel}</VisuallyHidden>}
      </Button>
    );
  }
);

AccessibleButton.displayName = 'AccessibleButton';

export default AccessibleButton;
