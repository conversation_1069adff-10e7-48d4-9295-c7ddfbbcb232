// Main Components Index - Following Next.js Best Practices
// Centralized exports for all component categories

// Form Components
export * from './forms';

// Layout Components
export * from './layout';

// Navigation Components
export * from './navigation';

// Data Display Components
export * from './data-display';

// Theme Components
export * from './theme';

// Feedback Components
export * from './feedback';

// Feature Components
export * from './features';

// Status Components
export * from './status';

// Accessibility Components
export * from './accessibility';

// Action Components
// export * from './actions'; // TODO: Fix actions index

// Table Components
// export * from './tables'; // TODO: Fix tables index

// Card Components
// export * from './cards'; // TODO: Fix cards index

// Chart Components
// export * from './charts'; // TODO: Fix charts index

// Dialog Components
// export * from './dialogs'; // TODO: Fix dialogs index

// Search Components
// export * from './search'; // TODO: Fix search index

// Control Components
// export * from './controls'; // TODO: Fix controls index

// Demo Components
// export * from './demos'; // TODO: Fix demos index

// Detail Components
export * from './details';

// Inventory Components
export * from './inventory';

// Log Components
export * from './logs';

// Modal Components
// export * from './modals'; // TODO: Fix modals index

// Assembly Components
// export * from './assemblies'; // TODO: Fix assemblies index

// Bootstrap Components
// export * from './bootstrap'; // TODO: Fix bootstrap index

