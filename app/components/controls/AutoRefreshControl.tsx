'use client';

import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/app/components/feedback/tooltip';
import { Button } from '@/app/components/forms/Button';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/app/components/forms/Select';
import { useAssemblies } from '@/app/contexts/AssembliesContext';
import { cn } from '@/app/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import { Clock, Power, PowerOff, RefreshCw } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';

interface AutoRefreshControlProps {
  className?: string;
}

/**
 * Unified refresh control component with smart button and dropdown for auto-refresh settings
 */
export function AutoRefreshControl({ className }: AutoRefreshControlProps) {
  const {
    refreshAssemblies: refreshAssembliesOriginal,
    isLoading,
    lastUpdated,
    isAutoRefreshEnabled,
    autoRefreshInterval,
    toggleAutoRefresh: toggleAutoRefreshOriginal,
    setAutoRefreshInterval: setAutoRefreshIntervalOriginal,
  } = useAssemblies();

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Memoize the refresh function to prevent unnecessary re-renders
  const refreshAssemblies = useCallback(() => {
    refreshAssembliesOriginal();
  }, [refreshAssembliesOriginal]);

  // Memoize the toggle function
  const toggleAutoRefresh = useCallback(() => {
    toggleAutoRefreshOriginal();
  }, [toggleAutoRefreshOriginal]);

  // Memoize the interval setter
  const setAutoRefreshInterval = useCallback((interval: number) => {
    setAutoRefreshIntervalOriginal(interval);
  }, [setAutoRefreshIntervalOriginal]);

  // Format the last updated time - memoize to prevent re-renders
  const lastUpdatedText = useMemo(() => {
    return lastUpdated
      ? `Last updated ${formatDistanceToNow(lastUpdated, { addSuffix: true })}`
      : 'Not yet updated';
  }, [lastUpdated]);

  // Available refresh intervals
  const refreshIntervals = useMemo(() => [
    { value: 5000, label: '5 seconds' },
    { value: 10000, label: '10 seconds' },
    { value: 30000, label: '30 seconds' },
    { value: 60000, label: '1 minute' },
    { value: 300000, label: '5 minutes' },
  ], []);

  // Convert to string once to avoid repeated conversions
  const autoRefreshIntervalString = useMemo(() =>
    autoRefreshInterval.toString(),
    [autoRefreshInterval]
  );

  // Smart button text based on auto-refresh state
  const buttonText = useMemo(() => {
    if (isAutoRefreshEnabled) {
      const intervalLabel = refreshIntervals.find(i => i.value === autoRefreshInterval)?.label || '30s';
      return `Refresh (Auto: ${intervalLabel})`;
    }
    return 'Refresh';
  }, [isAutoRefreshEnabled, autoRefreshInterval, refreshIntervals]);

  // Tooltip text based on current state
  const tooltipText = useMemo(() => {
    if (isLoading) return 'Refreshing data...';
    if (isAutoRefreshEnabled) {
      return `Manual refresh (Auto-refresh enabled every ${refreshIntervals.find(i => i.value === autoRefreshInterval)?.label})`;
    }
    return 'Manually refresh data';
  }, [isLoading, isAutoRefreshEnabled, autoRefreshInterval, refreshIntervals]);

  return (
    <div className={cn("flex items-center space-x-2", className)}>
      {/* Main refresh button with smart text and visual indicators */}
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              onClick={refreshAssemblies}
              disabled={isLoading}
              className={cn(
                "h-8 px-3",
                isAutoRefreshEnabled && "border-green-200 bg-green-50 text-green-700 hover:bg-green-100"
              )}
            >
              <RefreshCw
                size={14}
                className={cn(
                  "mr-2",
                  isLoading && "animate-spin",
                  isAutoRefreshEnabled && "text-green-600"
                )}
              />
              <span className="text-sm">{buttonText}</span>
              {isAutoRefreshEnabled && (
                <div className="ml-2 w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{tooltipText}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {/* Auto-refresh toggle button */}
      <Button
        variant="ghost"
        size="sm"
        className="flex items-center space-x-2 h-8 px-2"
        onClick={toggleAutoRefresh}
      >
        {isAutoRefreshEnabled ? (
          <Power size={16} className="text-green-600" />
        ) : (
          <PowerOff size={16} className="text-muted-foreground" />
        )}
        <span className="text-sm">Auto</span>
      </Button>

      {/* Interval selector - only show when auto-refresh is enabled */}
      {isAutoRefreshEnabled && (
        <div className="flex items-center space-x-2">
          <Clock size={14} className="text-muted-foreground" />
          <Select
            value={autoRefreshIntervalString}
            onValueChange={(value) => setAutoRefreshInterval(parseInt(value))}
          >
            <SelectTrigger className="h-8 w-[100px]">
              <SelectValue placeholder="Interval" />
            </SelectTrigger>
            <SelectContent>
              {refreshIntervals.map((interval) => (
                <SelectItem key={interval.value} value={interval.value.toString()}>
                  {interval.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Last updated info */}
      <div className="text-xs text-muted-foreground flex items-center">
        <Clock size={12} className="mr-1" />
        {lastUpdatedText}
      </div>
    </div>
  );
}
