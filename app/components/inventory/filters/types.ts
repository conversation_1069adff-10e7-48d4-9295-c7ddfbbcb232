/**
 * Types and utilities for inventory filtering
 */

export interface RangeFilter {
  min: number;
  max: number;
  enabled: boolean;
}

export interface ReorderLevelFilter {
  value: number;
  belowOnly: boolean;
  enabled: boolean;
}

export interface MultiSelectFilter<T extends string = string> {
  values: T[];
  enabled: boolean;
}

export interface FilterState {
  stockQuantity: RangeFilter;
  reorderLevel: ReorderLevelFilter;
  supplier: MultiSelectFilter;
  category: MultiSelectFilter;
  location: MultiSelectFilter;
}

export const DEFAULT_FILTER_STATE: FilterState = {
  stockQuantity: {
    min: 0,
    max: 1000,
    enabled: false
  },
  reorderLevel: {
    value: 10,
    belowOnly: true,
    enabled: false
  },
  supplier: {
    values: [],
    enabled: false
  },
  category: {
    values: [],
    enabled: false
  },
  location: {
    values: [],
    enabled: false
  }
};

/**
 * Count the number of active filters
 */
export const countActiveFilters = (filters: FilterState): number => {
  let count = 0;
  
  if (filters.stockQuantity.enabled) count++;
  if (filters.reorderLevel.enabled) count++;
  if (filters.supplier.enabled && filters.supplier.values.length > 0) count++;
  if (filters.category.enabled && filters.category.values.length > 0) count++;
  if (filters.location.enabled && filters.location.values.length > 0) count++;
  
  return count;
};

/**
 * Check if any filters are active
 */
export const hasActiveFilters = (filters: FilterState): boolean => {
  return countActiveFilters(filters) > 0;
};

/**
 * Reset all filters to their default state
 */
export const resetFilters = (): FilterState => {
  return { ...DEFAULT_FILTER_STATE };
};

/**
 * Get the localStorage key for filters
 */
export const getFilterStorageKey = (userId: string = 'default'): string => {
  return `inventory_filters_${userId}`;
};

/**
 * Save filters to localStorage
 */
export const saveFiltersToStorage = (filters: FilterState, userId: string = 'default'): void => {
  try {
    localStorage.setItem(getFilterStorageKey(userId), JSON.stringify(filters));
  } catch (error) {
    console.error('Failed to save filters to localStorage:', error);
  }
};

/**
 * Load filters from localStorage
 */
export const loadFiltersFromStorage = (userId: string = 'default'): FilterState | null => {
  try {
    const storedFilters = localStorage.getItem(getFilterStorageKey(userId));
    if (storedFilters) {
      return JSON.parse(storedFilters) as FilterState;
    }
  } catch (error) {
    console.error('Failed to load filters from localStorage:', error);
  }
  return null;
};
