"use client";

import { useTheme } from '@/app/context/ThemeContext';
import { LogisticsInfo } from '@/app/types';
import { motion } from 'framer-motion';
import { ArrowRight } from 'lucide-react';
import React from 'react';

// Extended logistics info interface for the map component
interface ExtendedLogisticsInfo extends LogisticsInfo {
  warehouses?: Array<{
    id: string;
    name: string;
    location: string;
    inventory: number;
  }>;
  routes?: Array<{
    from: string;
    to: string;
    status: string;
    items: number;
    eta: string;
  }>;
  delayedItems: Array<{
    _id: string;
    id: string;
    productCode: string;
    name: string;
    description: string;
    categoryId: string;
    status: 'active' | 'discontinued' | 'in_development';
    sellingPrice: number;
    currentStock?: number;
    reorderLevel?: number;
    createdAt: Date;
    updatedAt: Date;
  }>;
}

/**
 * Component that displays a logistics map with shipping routes and warehouse information
 */
const LogisticsMap: React.FC<{ logisticsInfo?: LogisticsInfo }> = ({ logisticsInfo }) => {
  const { theme } = useTheme();
  
  // Mock data for logistics map
  const mockData: ExtendedLogisticsInfo = {
    inTransit: logisticsInfo?.inTransit || 95,
    delivered: logisticsInfo?.delivered || 1250,
    delayed: logisticsInfo?.delayed || 18,
    total: logisticsInfo?.total || 1363,
    pending: logisticsInfo?.pending || 25,
    totalShipments: logisticsInfo?.totalShipments || 1388,
    warehouses: logisticsInfo?.warehouses || [
      { id: 'w1', name: 'Main Warehouse', location: 'New York', inventory: 1250 },
      { id: 'w2', name: 'West Distribution', location: 'Los Angeles', inventory: 875 },
      { id: 'w3', name: 'Central Storage', location: 'Chicago', inventory: 620 },
      { id: 'w4', name: 'Southern Hub', location: 'Atlanta', inventory: 430 },
    ],
    routes: logisticsInfo?.routes || [
      { from: 'w1', to: 'w2', status: 'active', items: 45, eta: '2 days' },
      { from: 'w1', to: 'w3', status: 'active', items: 32, eta: '1 day' },
      { from: 'w2', to: 'w4', status: 'delayed', items: 18, eta: '4 days' },
      { from: 'w3', to: 'w4', status: 'completed', items: 27, eta: 'Delivered' },
    ],
    delayedItems: logisticsInfo?.delayedItems || [
      {
        _id: 'p1',
        id: 'p1',
        productCode: 'PROD-CB-A',
        name: 'Circuit Board A',
        description: 'Electronic circuit board component',
        categoryId: 'electronics-cat-id',
        status: 'active' as const,
        sellingPrice: 150.00,
        currentStock: 5,
        reorderLevel: 10,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        _id: 'p2',
        id: 'p2',
        productCode: 'PROD-PS-001',
        name: 'Power Supply',
        description: 'Electronic power supply unit',
        categoryId: 'electronics-cat-id',
        status: 'active' as const,
        sellingPrice: 200.00,
        currentStock: 3,
        reorderLevel: 8,
        createdAt: new Date(),
        updatedAt: new Date()
      },
    ]
  };
  
  // Get warehouse by ID
  const getWarehouse = (id: string) => {
    return mockData.warehouses?.find((w: any) => w.id === id);
  };
  
  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-blue-500 dark:text-blue-400';
      case 'delayed':
        return 'text-red-500 dark:text-red-400';
      case 'completed':
        return 'text-green-500 dark:text-green-400';
      default:
        return 'text-gray-500 dark:text-gray-400';
    }
  };
  
  // Get status background color
  const getStatusBgColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-info/10 text-info';
      case 'delayed':
        return 'bg-destructive/10 text-destructive';
      case 'completed':
        return 'bg-success/10 text-success';
      default:
        return 'bg-muted/50 text-muted-foreground';
    }
  };
  
  return (
    <div className="bg-card rounded-lg shadow-sm p-4">
      <h3 className="text-lg font-medium text-foreground mb-4">Logistics Map</h3>
      
      {/* Warehouses */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-3">Warehouses</h4>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
          {mockData.warehouses?.map((warehouse: any, index: number) => (
            <motion.div
              key={warehouse.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-gray-50 dark:bg-gray-750 rounded-lg p-3 border border-gray-200 dark:border-gray-700"
            >
              <div className="font-medium text-gray-800 dark:text-gray-200 mb-1">{warehouse.name}</div>
              <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">{warehouse.location}</div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500 dark:text-gray-400">Inventory</span>
                <span className="text-sm font-medium text-gray-800 dark:text-gray-200">{warehouse.inventory} items</span>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
      
      {/* Shipping Routes */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-3">Shipping Routes</h4>
        <div className="space-y-3">
          {mockData.routes?.map((route: any, index: number) => {
            const fromWarehouse = getWarehouse(route.from);
            const toWarehouse = getWarehouse(route.to);
            
            return (
              <motion.div
                key={`${route.from}-${route.to}`}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-gray-50 dark:bg-gray-750 rounded-lg p-3 border border-gray-200 dark:border-gray-700"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <span className="font-medium text-gray-800 dark:text-gray-200">{fromWarehouse?.name}</span>
                    <ArrowRight className={`mx-2 ${getStatusColor(route.status)}`} size={16} />
                    <span className="font-medium text-gray-800 dark:text-gray-200">{toWarehouse?.name}</span>
                  </div>
                  <div className={`text-xs px-2 py-1 rounded-full ${getStatusBgColor(route.status)}`}>
                    {route.status.charAt(0).toUpperCase() + route.status.slice(1)}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="text-gray-500 dark:text-gray-400">
                    Items: <span className="text-gray-700 dark:text-gray-300">{route.items}</span>
                  </div>
                  <div className="text-gray-500 dark:text-gray-400">
                    ETA: <span className="text-gray-700 dark:text-gray-300">{route.eta}</span>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
      
      {/* Delayed Items */}
      {mockData.delayedItems.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-3">Delayed Items</h4>
          <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-3 border border-red-100 dark:border-red-800/30">
            <div className="text-sm text-red-700 dark:text-red-400 mb-2">
              The following items are delayed in transit:
            </div>
            <ul className="space-y-1">
              {mockData.delayedItems.map((item: any) => (
                <li key={item.id} className="text-sm text-red-600 dark:text-red-400 flex items-center">
                  <span className="w-1.5 h-1.5 bg-red-500 rounded-full mr-2"></span>
                  {item.name} (Category: {item.categoryId})
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default LogisticsMap;
