"use client";

import { useAppContext } from '@/app/context/AppContext';
import React, { useEffect, useState } from 'react';
// import { checkSupabaseConfig } from '@/app/lib/supabase'; // Disabled for production deployment

// Mock Supabase config check for production deployment
const checkSupabaseConfig = () => ({
  isConfigured: false,
  error: 'Supabase is disabled for production deployment',
  details: null
});

const SupabaseSetup: React.FC = () => {
  const { refreshData } = useAppContext();
  const [showSetup, setShowSetup] = useState(false);
  const [supabaseUrl, setSupabaseUrl] = useState('');
  const [supabaseKey, setSupabaseKey] = useState('');
  const [configStatus, setConfigStatus] = useState<{
    isConfigured: boolean;
    error: string | null;
    details?: any;
  } | null>(null);

  useEffect(() => {
    // Check if Supabase is configured
    const config = checkSupabaseConfig();
    setConfigStatus(config);

    // Show setup if not configured
    setShowSetup(!config.isConfigured);

    // Load from window.ENV if available
    if (typeof window !== 'undefined' && window.ENV?.SUPABASE_URL) {
      setSupabaseUrl(window.ENV.SUPABASE_URL);
    }
  }, []);

  const saveConfig = () => {
    if (!supabaseUrl || !supabaseKey) {
      alert('Please enter both Supabase URL and Anon Key');
      return;
    }

    try {
      // Save to window.ENV for immediate use
      if (typeof window !== 'undefined') {
        window.ENV = window.ENV || {};
        window.ENV.SUPABASE_URL = supabaseUrl;
        window.ENV.SUPABASE_ANON_KEY = supabaseKey;

        // Save to localStorage for persistence
        localStorage.setItem('env_config', JSON.stringify({
          SUPABASE_URL: supabaseUrl,
          SUPABASE_ANON_KEY: supabaseKey
        }));
      }

      // Re-check configuration
      const config = checkSupabaseConfig();
      setConfigStatus(config);

      if (config.isConfigured) {
        setShowSetup(false);
        refreshData(); // Refresh data with new configuration
        alert('Supabase configuration saved successfully!');
      } else {
        alert(`Configuration saved but still has issues: ${config.error}`);
      }
    } catch (error) {
      alert(`Error saving configuration: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  if (!showSetup) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md">
        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Supabase Connection Setup</h2>

        {configStatus?.error && (
          <div className="mb-4 p-3 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded">
            <p className="font-medium">Configuration Error</p>
            <p className="text-sm">{configStatus.error}</p>
          </div>
        )}

        <div className="mb-4">
          <label className="block mb-2 text-gray-700 dark:text-gray-300">Supabase URL</label>
          <input
            type="text"
            value={supabaseUrl}
            onChange={(e) => setSupabaseUrl(e.target.value)}
            className="w-full p-2 border rounded dark:bg-gray-700 dark:border-gray-600 text-gray-800 dark:text-gray-200"
            placeholder="https://your-project.supabase.co"
          />
        </div>

        <div className="mb-4">
          <label className="block mb-2 text-gray-700 dark:text-gray-300">Supabase Anon Key</label>
          <input
            type="text"
            value={supabaseKey}
            onChange={(e) => setSupabaseKey(e.target.value)}
            className="w-full p-2 border rounded dark:bg-gray-700 dark:border-gray-600 text-gray-800 dark:text-gray-200"
            placeholder="your-anon-key"
          />
        </div>

        <div className="flex justify-between">
          <button
            onClick={() => setShowSetup(false)}
            className="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded"
          >
            Cancel
          </button>
          <button
            onClick={saveConfig}
            className="px-4 py-2 bg-blue-500 text-white rounded"
          >
            Save Configuration
          </button>
        </div>

        <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
          <p>
            You can find your Supabase URL and Anon Key in your Supabase project dashboard
            under Project Settings &gt; API.
          </p>
        </div>
      </div>
    </div>
  );
};

export default SupabaseSetup;