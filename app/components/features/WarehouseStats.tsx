"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/app/components/layout/cards/card';
import { Badge } from '@/app/components/data-display/badge';
import { Building2, Package, MapPin, Activity } from 'lucide-react';
import React from 'react';

/**
 * Interface for warehouse statistics
 */
export interface WarehouseStatsData {
  total: number;
  active: number;
  inactive: number;
  totalCapacity: number;
  binTrackedCount: number;
}

/**
 * Props for WarehouseStats component
 */
interface WarehouseStatsProps {
  stats: WarehouseStatsData;
  isLoading?: boolean;
  className?: string;
}

/**
 * WarehouseStats component displays key warehouse metrics in cards
 */
export function WarehouseStats({ stats, isLoading = false, className = "" }: WarehouseStatsProps) {
  const statCards = [
    {
      title: "Total Warehouses",
      value: stats.total,
      icon: Building2,
      description: "All warehouse locations",
      color: "text-blue-600 dark:text-blue-400"
    },
    {
      title: "Active Warehouses",
      value: stats.active,
      icon: Activity,
      description: "Currently operational",
      color: "text-green-600 dark:text-green-400"
    },
    {
      title: "Total Capacity",
      value: stats.totalCapacity.toLocaleString(),
      icon: Package,
      description: "Combined storage capacity",
      color: "text-purple-600 dark:text-purple-400"
    },
    {
      title: "Bin Tracking",
      value: stats.binTrackedCount,
      icon: MapPin,
      description: "Warehouses with bin tracking",
      color: "text-orange-600 dark:text-orange-400"
    }
  ];

  if (isLoading) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
        {[1, 2, 3, 4].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                <div className="h-4 bg-muted rounded w-24"></div>
              </CardTitle>
              <div className="h-4 w-4 bg-muted rounded"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted rounded w-16 mb-1"></div>
              <div className="h-3 bg-muted rounded w-32"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
      {statCards.map((card, index) => {
        const Icon = card.icon;
        return (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {card.title}
              </CardTitle>
              <Icon className={`h-4 w-4 ${card.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
              <p className="text-xs text-muted-foreground">
                {card.description}
              </p>
              {card.title === "Active Warehouses" && stats.inactive > 0 && (
                <div className="flex items-center space-x-2 mt-2">
                  <Badge variant="outline" className="text-xs">
                    {stats.inactive} inactive
                  </Badge>
                </div>
              )}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}

export default WarehouseStats;
