'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/app/components/forms/Button';
import { Input } from '@/app/components/forms/Input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/forms/Select';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
import { Label } from '@/app/components/forms/label';
import { Textarea } from '@/app/components/forms/Textarea/Textarea';
import { toast } from 'sonner';
import { StockMovementRequest } from '@/app/services/stockmovement.service';

interface Part {
  _id: string;
  partNumber: string;
  name: string;
  businessName?: string;
  inventory?: {
    stockLevels?: {
      raw: number;
      hardening: number;
      grinding: number;
      finished: number;
      rejected: number;
    };
    warehouseId?: string;
  };
}

interface Warehouse {
  _id: string;
  warehouseCode: string;
  name: string;
}

interface Location {
  _id: string;
  name: string;
  warehouseId: string;
  locationType: string;
  isActive: boolean;
}

interface StockMovementFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const MOVEMENT_TYPES = {
  purchase_receipt: {
    label: 'Purchase Receipt',
    description: 'Receive goods from supplier',
    fromRequired: false,
    toRequired: true,
    defaultToStockType: 'raw'
  },
  sales_shipment: {
    label: 'Sales Shipment',
    description: 'Ship finished goods to customer',
    fromRequired: true,
    toRequired: false,
    defaultFromStockType: 'finished'
  },
  internal_transfer: {
    label: 'Internal Transfer',
    description: 'Move between warehouses or stock types',
    fromRequired: true,
    toRequired: true
  },
  scrap_disposal: {
    label: 'Scrap Disposal',
    description: 'Dispose rejected parts',
    fromRequired: true,
    toRequired: true,
    defaultFromStockType: 'rejected',
    defaultToStockType: 'scrap'
  },
  process_move: {
    label: 'Process Move',
    description: 'Move between manufacturing stages',
    fromRequired: true,
    toRequired: true
  },
  adjustment: {
    label: 'Stock Adjustment',
    description: 'Manual stock correction',
    fromRequired: false,
    toRequired: false
  }
};

const STOCK_TYPES = [
  { value: 'raw', label: 'Raw Materials' },
  { value: 'hardening', label: 'In Hardening' },
  { value: 'grinding', label: 'In Grinding' },
  { value: 'finished', label: 'Finished Goods' },
  { value: 'rejected', label: 'Rejected Parts' },
  { value: 'scrap', label: 'Scrap' }
];

export function StockMovementForm({ isOpen, onClose, onSuccess }: StockMovementFormProps) {
  const [parts, setParts] = useState<Part[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [locations, setLocations] = useState<Location[]>([]);
  const [fromLocations, setFromLocations] = useState<Location[]>([]);
  const [toLocations, setToLocations] = useState<Location[]>([]);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<Partial<StockMovementRequest>>({
    movementType: 'purchase_receipt',
    quantity: 1,
    transactionDate: new Date(),
    partId: '', // Initialize partId field
    userId: '6751b8b8e5b8b8b8b8b8b8b8' // Default user ID - should be replaced with actual user session
  });

  // Fetch parts and warehouses on component mount and reset form
  useEffect(() => {
    if (isOpen) {
      fetchParts();
      fetchWarehouses();
      // Reset form when opening
      setFormData({
        movementType: 'purchase_receipt',
        quantity: 1,
        transactionDate: new Date(),
        partId: '',
        userId: '6751b8b8e5b8b8b8b8b8b8b8' // Default user ID
      });

    }
  }, [isOpen]);

  const fetchParts = async () => {
    try {
      const response = await fetch('/api/parts?limit=100');
      const result = await response.json();
      if (result.data) {
        setParts(result.data);
      }
    } catch (error) {
      console.error('Error fetching parts:', error);
      toast.error('Failed to load parts');
    }
  };

  const fetchWarehouses = async () => {
    try {
      const response = await fetch('/api/warehouses');
      const result = await response.json();
      if (result.data) {
        setWarehouses(result.data);
      }
    } catch (error) {
      console.error('Error fetching warehouses:', error);
      toast.error('Failed to load warehouses');
    }
  };

  const loadLocationsForWarehouse = async (warehouseId: string): Promise<Location[]> => {
    try {
      const response = await fetch(`/api/warehouses/${warehouseId}/locations`);
      const data = await response.json();
      if (data.success) {
        return data.data || [];
      }
      return [];
    } catch (error) {
      console.error('Error loading locations for warehouse:', error);
      return [];
    }
  };

  const handleFromWarehouseChange = async (warehouseId: string) => {
    const locations = await loadLocationsForWarehouse(warehouseId);
    setFromLocations(locations);
    setFormData(prev => ({
      ...prev,
      from: { ...prev.from!, warehouseId, locationId: '' }
    }));
  };

  const handleToWarehouseChange = async (warehouseId: string) => {
    const locations = await loadLocationsForWarehouse(warehouseId);
    setToLocations(locations);
    setFormData(prev => ({
      ...prev,
      to: { ...prev.to!, warehouseId, locationId: '' }
    }));
  };

  const selectedPart = parts.find(p => p._id === formData.partId);
  const movementConfig = MOVEMENT_TYPES[formData.movementType as keyof typeof MOVEMENT_TYPES];

  // Auto-populate warehouse based on part's existing inventory
  useEffect(() => {
    if (selectedPart?.inventory?.warehouseId && !formData.from?.warehouseId && !formData.to?.warehouseId) {
      const warehouseId = selectedPart.inventory.warehouseId;
      
      if (movementConfig?.fromRequired) {
        setFormData(prev => ({
          ...prev,
          from: {
            warehouseId,
            stockType: (movementConfig as any).defaultFromStockType || 'finished'
          }
        }));
      }

      if (movementConfig?.toRequired) {
        setFormData(prev => ({
          ...prev,
          to: {
            warehouseId,
            stockType: (movementConfig as any).defaultToStockType || 'raw'
          }
        }));
      }
    }
  }, [selectedPart, formData.movementType]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Validate required fields
      if (!formData.partId) {
        toast.error('Please select a part');
        setLoading(false);
        return;
      }
      if (!formData.movementType) {
        toast.error('Please select a movement type');
        setLoading(false);
        return;
      }
      if (!formData.quantity || formData.quantity <= 0) {
        toast.error('Please enter a valid quantity');
        setLoading(false);
        return;
      }
      if (!formData.userId) {
        toast.error('User ID is required');
        setLoading(false);
        return;
      }

      const requestData: StockMovementRequest = {
        ...formData,
        transactionDate: formData.transactionDate ? new Date(formData.transactionDate) : new Date()
      } as StockMovementRequest;

      console.log('Submitting stock movement request:', requestData);

      const response = await fetch('/api/inventory/move', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      const result = await response.json();
      console.log('Stock movement response:', result);

      if (result.success) {
        toast.success(result.data.message || 'Stock movement completed successfully');
        onSuccess();
        onClose();
        // Reset form
        setFormData({
          movementType: 'purchase_receipt',
          quantity: 1,
          transactionDate: new Date(),
          partId: '',
          userId: '6751b8b8e5b8b8b8b8b8b8b8'
        });
      } else {
        toast.error(result.error || 'Failed to execute stock movement');
      }
    } catch (error) {
      console.error('Error executing stock movement:', error);
      toast.error('Failed to execute stock movement');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle>Stock Movement</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Movement Type */}
            <div>
              <Label htmlFor="movementType">Movement Type</Label>
              <Select
                value={formData.movementType || 'purchase_receipt'}
                onValueChange={(value) => setFormData(prev => ({
                  ...prev,
                  movementType: value as any,
                  from: null,
                  to: null
                }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select movement type" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(MOVEMENT_TYPES).map(([key, config]) => (
                    <SelectItem key={key} value={key}>
                      <div>
                        <div className="font-medium">{config.label}</div>
                        <div className="text-sm text-muted-foreground">{config.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Part Selection */}
            <div>
              <Label htmlFor="partId">Part</Label>
              <Select
                value={formData.partId || ''}
                onValueChange={(value) => {
                  setFormData(prev => ({ ...prev, partId: value }));
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select part" />
                </SelectTrigger>
                <SelectContent>
                  {parts.map((part) => (
                    <SelectItem key={part._id} value={part._id}>
                      <div>
                        <div className="font-medium">{part.partNumber}</div>
                        <div className="text-sm text-muted-foreground">
                          {part.name} {part.businessName && `(${part.businessName})`}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Show current stock levels for selected part */}
            {selectedPart?.inventory?.stockLevels && (
              <div className="bg-muted p-3 rounded-md">
                <div className="text-sm font-medium mb-2">Current Stock Levels:</div>
                <div className="grid grid-cols-5 gap-2 text-xs">
                  <div>Raw: {selectedPart.inventory.stockLevels.raw}</div>
                  <div>Hardening: {selectedPart.inventory.stockLevels.hardening}</div>
                  <div>Grinding: {selectedPart.inventory.stockLevels.grinding}</div>
                  <div>Finished: {selectedPart.inventory.stockLevels.finished}</div>
                  <div>Rejected: {selectedPart.inventory.stockLevels.rejected}</div>
                </div>
              </div>
            )}

            {/* Quantity */}
            <div>
              <Label htmlFor="quantity">Quantity</Label>
              <Input
                type="number"
                min="1"
                value={formData.quantity}
                onChange={(e) => setFormData(prev => ({ ...prev, quantity: parseInt(e.target.value) }))}
                required
              />
            </div>

            {/* From Location (if required) */}
            {movementConfig?.fromRequired && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>From Warehouse</Label>
                    <Select
                      value={formData.from?.warehouseId || ''}
                      onValueChange={handleFromWarehouseChange}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select warehouse" />
                      </SelectTrigger>
                      <SelectContent>
                        {warehouses.map((warehouse) => (
                          <SelectItem key={warehouse._id} value={warehouse._id}>
                            {warehouse.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>From Stock Type</Label>
                    <Select
                      value={formData.from?.stockType || ''}
                      onValueChange={(value) => setFormData(prev => ({
                        ...prev,
                        from: { ...prev.from!, stockType: value as any }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select stock type" />
                      </SelectTrigger>
                      <SelectContent>
                        {STOCK_TYPES.filter(st => st.value !== 'scrap').map((stockType) => (
                          <SelectItem key={stockType.value} value={stockType.value}>
                            {stockType.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* From Location Dropdown */}
                {formData.from?.warehouseId && fromLocations.length > 0 && (
                  <div>
                    <Label>From Location</Label>
                    <Select
                      value={formData.from?.locationId || ''}
                      onValueChange={(value) => setFormData(prev => ({
                        ...prev,
                        from: { ...prev.from!, locationId: value }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select specific location" />
                      </SelectTrigger>
                      <SelectContent>
                        {fromLocations.map((location) => (
                          <SelectItem key={location._id} value={location._id}>
                            {location.name} ({location.locationType})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>
            )}

            {/* To Location (if required) */}
            {movementConfig?.toRequired && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>To Warehouse</Label>
                    <Select
                      value={formData.to?.warehouseId || ''}
                      onValueChange={handleToWarehouseChange}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select warehouse" />
                      </SelectTrigger>
                      <SelectContent>
                        {warehouses.map((warehouse) => (
                          <SelectItem key={warehouse._id} value={warehouse._id}>
                            {warehouse.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>To Stock Type</Label>
                    <Select
                      value={formData.to?.stockType || ''}
                      onValueChange={(value) => setFormData(prev => ({
                        ...prev,
                        to: { ...prev.to!, stockType: value as any }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select stock type" />
                      </SelectTrigger>
                      <SelectContent>
                        {STOCK_TYPES.map((stockType) => (
                          <SelectItem key={stockType.value} value={stockType.value}>
                            {stockType.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* To Location Dropdown */}
                {formData.to?.warehouseId && toLocations.length > 0 && (
                  <div>
                    <Label>To Location</Label>
                    <Select
                      value={formData.to?.locationId || ''}
                      onValueChange={(value) => setFormData(prev => ({
                        ...prev,
                        to: { ...prev.to!, locationId: value }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select specific location" />
                      </SelectTrigger>
                      <SelectContent>
                        {toLocations.map((location) => (
                          <SelectItem key={location._id} value={location._id}>
                            {location.name} ({location.locationType})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>
            )}

            {/* Reference Information */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="referenceNumber">Reference Number</Label>
                <Input
                  value={formData.referenceNumber || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, referenceNumber: e.target.value }))}
                  placeholder="PO-001, WO-123, etc."
                />
              </div>
              <div>
                <Label htmlFor="referenceType">Reference Type</Label>
                <Select
                  value={formData.referenceType || ''}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, referenceType: value as any }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select reference type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PurchaseOrder">Purchase Order</SelectItem>
                    <SelectItem value="WorkOrder">Work Order</SelectItem>
                    <SelectItem value="SalesOrder">Sales Order</SelectItem>
                    <SelectItem value="StockAdjustment">Stock Adjustment</SelectItem>
                    <SelectItem value="ProcessOrder">Process Order</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Transaction Date */}
            <div>
              <Label htmlFor="transactionDate">Transaction Date</Label>
              <Input
                type="date"
                value={formData.transactionDate ? formData.transactionDate.toISOString().split('T')[0] : new Date().toISOString().split('T')[0]}
                onChange={(e) => setFormData(prev => ({ ...prev, transactionDate: new Date(e.target.value) }))}
                required
              />
            </div>

            {/* Notes */}
            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                value={formData.notes || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Additional notes about this movement..."
                rows={3}
              />
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-2 pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Processing...' : 'Execute Movement'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
