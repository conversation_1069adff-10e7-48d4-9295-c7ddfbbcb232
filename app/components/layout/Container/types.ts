import React from 'react';

/**
 * Props for the Container component
 */
export interface ContainerProps {
  /** Content to be displayed within the container */
  children: React.ReactNode;
  /** Optional maximum width class for the container */
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full' | 'none';
  /** Optional padding classes */
  padding?: string;
  /** Optional className for custom styling */
  className?: string;
  /** Optional flag to center the container */
  centered?: boolean;
} 