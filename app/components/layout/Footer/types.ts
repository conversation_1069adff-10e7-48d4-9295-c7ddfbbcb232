import React from 'react';

/**
 * Props for the Footer component
 */
export interface FooterProps {
  /** Optional company name to display in the footer */
  companyName?: string;
  /** Optional copyright year, defaults to current year if not provided */
  copyrightYear?: number;
  /** Optional additional links to display in the footer */
  links?: FooterLink[];
  /** Optional className for custom styling */
  className?: string;
}

/**
 * Interface for footer links
 */
export interface FooterLink {
  /** Text to display for the link */
  label: string;
  /** URL for the link */
  href: string;
  /** Whether to open the link in a new tab */
  external?: boolean;
} 