import { ReactNode } from 'react';

/**
 * Props for the ActionCard component
 */
export interface ActionCardProps {
  /** Icon displayed next to the label */
  icon?: ReactNode;
  /** Text label for the action */
  label: string;
  /** Click handler for the action */
  onClick?: () => void;
  /** Color theme for the action */
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'orange' | 'gray';
  /** Additional CSS classes */
  className?: string;
}

/**
 * Color class mapping for different themes using theme variables
 */
export const colorClasses = {
  blue: 'text-info',
  green: 'text-success',
  red: 'text-destructive',
  yellow: 'text-warning',
  purple: 'text-primary',
  orange: 'text-warning',
  gray: 'text-muted-foreground'
};