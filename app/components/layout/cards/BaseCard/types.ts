import { ReactNode } from 'react';

/**
 * Props for the BaseCard component
 */
export interface BaseCardProps {
  /** Title displayed at the top of the card */
  title?: string;
  /** Subtitle displayed below the title */
  subtitle?: string;
  /** Icon displayed in the top-right corner */
  icon?: ReactNode;
  /** Color theme for the card */
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple' | 'orange' | 'gray';
  /** Click handler for the entire card */
  onClick?: () => void;
  /** Click handler for the view details link */
  onViewDetails?: () => void;
  /** Text for the view details link */
  viewDetailsText?: string;
  /** Whether the card is featured (has a colored border) */
  isFeatured?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Card content */
  children: ReactNode;
  /** Whether to animate the card */
  animate?: boolean;
}

/**
 * Color classes using semantic color variables for consistent styling
 */
export const colorClasses = {
  blue: {
    accent: 'text-info',
    light: 'bg-info/10',
    bar: 'bg-info',
    icon: 'text-info',
    gradient: 'from-info/10 to-info/5',
    border: 'border-info/20'
  },
  green: {
    accent: 'text-success',
    light: 'bg-success/10',
    bar: 'bg-success',
    icon: 'text-success',
    gradient: 'from-success/10 to-success/5',
    border: 'border-success/20'
  },
  red: {
    accent: 'text-destructive',
    light: 'bg-destructive/10',
    bar: 'bg-destructive',
    icon: 'text-destructive',
    gradient: 'from-destructive/10 to-destructive/5',
    border: 'border-destructive/20'
  },
  yellow: {
    accent: 'text-warning',
    light: 'bg-warning/10',
    bar: 'bg-warning',
    icon: 'text-warning',
    gradient: 'from-warning/10 to-warning/5',
    border: 'border-warning/20'
  },
  purple: {
    accent: 'text-purple',
    light: 'bg-purple/10',
    bar: 'bg-purple',
    icon: 'text-purple',
    gradient: 'from-purple/10 to-purple/5',
    border: 'border-purple/20'
  },
  orange: {
    accent: 'text-orange',
    light: 'bg-orange/10',
    bar: 'bg-orange',
    icon: 'text-orange',
    gradient: 'from-orange/10 to-orange/5',
    border: 'border-orange/20'
  },
  gray: {
    accent: 'text-muted-foreground',
    light: 'bg-muted',
    bar: 'bg-muted-foreground',
    icon: 'text-muted-foreground',
    gradient: 'from-muted-foreground/10 to-muted-foreground/5',
    border: 'border-muted-foreground/20'
  }
};