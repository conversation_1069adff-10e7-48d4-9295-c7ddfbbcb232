"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { ChevronRight } from 'lucide-react';
import { useTheme } from '@/app/context/ThemeContext';
import { BaseCardProps, colorClasses } from './types';

/**
 * Client component version of BaseCard that handles interactivity
 */
const BaseCardClient: React.FC<BaseCardProps> = ({
  title,
  subtitle,
  icon,
  color = 'blue',
  onClick,
  onViewDetails,
  viewDetailsText = 'View Details',
  isFeatured = false,
  className = '',
  children,
  animate = true
}) => {
  const { theme } = useTheme();

  // Get the color classes for the selected color
  const selectedColor = colorClasses[color] || colorClasses.blue;

  // Define animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4, ease: "easeOut" }
    },
    hover: {
      y: -5,
      scale: 1.02,
      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
      transition: { duration: 0.2 }
    }
  };

  const CardComponent = animate ? motion.div : 'div';
  const animationProps = animate ? {
    initial: "hidden",
    animate: "visible",
    whileHover: "hover",
    variants: cardVariants
  } : {};

  const featuredBorderClass = isFeatured
    ? `border-2 ${selectedColor.border}`
    : 'border border-gray-100 dark:border-border';

  return (
    <CardComponent
      {...animationProps}
      className={`bg-card backdrop-blur-sm shadow-md border border-border
        rounded-xl overflow-hidden transition-all duration-200
        ${onClick ? 'cursor-pointer hover:bg-accent/50' : ''}
        ${featuredBorderClass}
        ${className}`}
      onClick={onClick}
    >
      <div className="p-6">
        {(title || icon) && (
          <div className="flex justify-between items-center mb-4">
            {title && <h3 className="text-lg font-semibold text-card-foreground">{title}</h3>}
            {icon && (
              <div className={`h-10 w-10 rounded-full ${selectedColor.light} flex items-center justify-center`}>
                <div className={selectedColor.icon}>{icon}</div>
              </div>
            )}
          </div>
        )}

        {subtitle && (
          <div className="text-sm text-gray-500 dark:text-text-secondary mb-3">{subtitle}</div>
        )}

        {children}

        {onViewDetails && (
          <div className="mt-4 flex justify-end">
            <motion.div
              whileHover={{ x: 5 }}
              whileTap={{ scale: 0.98 }}
              className={`${selectedColor.accent} flex items-center text-sm font-medium cursor-pointer`}
              onClick={(e) => {
                e.stopPropagation();
                onViewDetails();
              }}
            >
              {viewDetailsText} <ChevronRight size={16} className="ml-1" />
            </motion.div>
          </div>
        )}
      </div>
    </CardComponent>
  );
};

export default BaseCardClient; 