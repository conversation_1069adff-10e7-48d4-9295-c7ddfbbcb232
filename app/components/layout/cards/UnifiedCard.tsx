"use client";

import * as React from "react";
import { ChevronRight } from "lucide-react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/app/lib/utils";
import { useTheme } from "@/app/context/ThemeContext";
import { useEnhancedTheme } from "@/app/hooks/useEnhancedTheme";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './card';
import { LazyMotionWrapper } from './animations/LazyMotionWrapper';
import { AnimationType } from './animations/cardAnimationVariants';

// CVA variants for the unified card
const unifiedCardVariants = cva(
  // Base styles - standardized shadow system for visual consistency
  "relative rounded-xl border transition-all duration-300 ease-in-out",
  {
    variants: {
      variant: {
        default: "bg-card text-card-foreground shadow-sm hover:shadow-md",
        base: "bg-card text-card-foreground shadow-sm hover:shadow-md",
        action: "bg-card text-card-foreground shadow-sm hover:shadow-md cursor-pointer border-2 hover:border-primary/50 hover:scale-[1.02]",
        status: "bg-card text-card-foreground shadow-sm hover:shadow-md",
        elevated: "bg-card text-card-foreground shadow-lg hover:shadow-xl",
        interactive: "bg-card text-card-foreground shadow-sm hover:shadow-md cursor-pointer hover:bg-accent/5",
        outline: "border-2 border-border bg-transparent shadow-sm hover:shadow-md hover:bg-accent/5",
        ghost: "border-transparent bg-transparent hover:bg-accent/10 hover:shadow-sm"
      },
      size: {
        sm: "p-3",
        md: "p-4",
        lg: "p-6",
        xl: "p-8"
      },
      color: {
        default: "",
        blue: "border-info/30",
        green: "border-success/30",
        red: "border-destructive/30",
        yellow: "border-warning/30",
        purple: "border-primary/30",
        orange: "border-warning/40",
        gray: "border-muted-foreground/30"
      }
    },
    defaultVariants: {
      variant: "default",
      size: "md",
      color: "default"
    }
  }
);

// Props interface extending CVA variants
export interface UnifiedCardProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'color'>,
    VariantProps<typeof unifiedCardVariants> {
  
  // Base card props
  title?: string;
  subtitle?: string;
  icon?: React.ReactNode;
  
  // Action card props
  label?: string;
  
  // Status card props
  data?: Record<string, number>;
  mainStat?: {
    value: number;
    label: string;
  };
  
  // Common props
  onViewDetails?: () => void;
  viewDetailsText?: string;
  isFeatured?: boolean;
  animate?: boolean;
  animation?: AnimationType;
}

export const UnifiedCard = React.forwardRef<HTMLDivElement, UnifiedCardProps>(
  ({ 
    className, 
    variant, 
    size, 
    color, 
    title, 
    subtitle, 
    icon, 
    label, 
    data, 
    mainStat, 
    onViewDetails, 
    viewDetailsText = "View Details", 
    isFeatured = false, 
    animate = true, 
    animation = 'subtle',
    children, 
    onClick,
    ...props 
  }, ref) => {
    const { theme } = useTheme();
    const { themeClasses } = useEnhancedTheme();

    // Color theme classes using semantic variables
    const getColorClasses = (colorVariant: string) => {
      const colorMap = {
        blue: 'text-info',
        green: 'text-success',
        red: 'text-destructive',
        yellow: 'text-warning',
        purple: 'text-primary',
        orange: 'text-warning',
        gray: 'text-muted-foreground'
      };
      return colorMap[colorVariant as keyof typeof colorMap] || '';
    };

    // Render different variants
    const renderContent = () => {
      switch (variant) {
        case 'base':
          return (
            <>
              {/* Header with icon, title, subtitle */}
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    {icon && (
                      <div className={cn("flex-shrink-0", getColorClasses(color || 'default'))}>
                        {icon}
                      </div>
                    )}
                    <div>
                      {title && (
                        <CardTitle className="text-lg font-semibold">
                          {title}
                        </CardTitle>
                      )}
                      {subtitle && (
                        <CardDescription className="text-sm text-muted-foreground mt-1">
                          {subtitle}
                        </CardDescription>
                      )}
                    </div>
                  </div>
                  {isFeatured && (
                    <div className="bg-primary text-primary-foreground px-2 py-1 rounded-full text-xs font-medium">
                      Featured
                    </div>
                  )}
                </div>
              </CardHeader>
              
              {/* Content */}
              <CardContent className="pt-0">
                {children}
              </CardContent>
              
              {/* Footer with view details */}
              {onViewDetails && (
                <CardFooter className="pt-0">
                  <button
                    onClick={onViewDetails}
                    className={cn(
                      "flex items-center text-sm font-medium transition-colors",
                      getColorClasses(color || 'default'),
                      "hover:underline"
                    )}
                  >
                    {viewDetailsText}
                    <ChevronRight size={14} className="ml-1" />
                  </button>
                </CardFooter>
              )}
            </>
          );
          
        case 'action':
          return (
            <div className="flex items-center justify-center space-x-2 py-2">
              {children || (
                <>
                  {icon && (
                    <div className={getColorClasses(color || 'default')}>
                      {icon}
                    </div>
                  )}
                  {label && (
                    <span className="font-medium text-sm">
                      {label}
                    </span>
                  )}
                </>
              )}
            </div>
          );
          
        case 'status':
          return (
            <>
              {/* Header */}
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {icon && (
                      <div className={getColorClasses(color || 'default')}>
                        {icon}
                      </div>
                    )}
                    {title && (
                      <CardTitle className="text-base font-semibold">
                        {title}
                      </CardTitle>
                    )}
                  </div>
                </div>
              </CardHeader>
              
              {/* Main stat */}
              {mainStat && (
                <CardContent className="pt-0 pb-3">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-foreground">
                      {mainStat.value.toLocaleString()}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {mainStat.label}
                    </div>
                  </div>
                </CardContent>
              )}
              
              {/* Data visualization */}
              {data && (
                <CardContent className="pt-0">
                  <div className="space-y-2">
                    {Object.entries(data).map(([key, value]) => {
                      const total = Object.values(data).reduce((sum, val) => sum + val, 0);
                      const percentage = total > 0 ? (value / total) * 100 : 0;
                      
                      return (
                        <div key={key} className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">{key}</span>
                          <div className="flex items-center space-x-2">
                            <div className="w-16 bg-muted rounded-full h-2">
                              <div
                                className={cn(
                                  "h-2 rounded-full transition-all duration-300",
                                  color === 'blue' && "bg-info",
                                  color === 'green' && "bg-success",
                                  color === 'red' && "bg-destructive",
                                  color === 'yellow' && "bg-warning",
                                  color === 'purple' && "bg-primary",
                                  color === 'orange' && "bg-warning",
                                  color === 'gray' && "bg-muted-foreground",
                                  !color && "bg-primary"
                                )}
                                style={{ width: `${percentage}%` }}
                              />
                            </div>
                            <span className="font-medium w-8 text-right">{value}</span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              )}
              
              {/* Additional content */}
              {children && (
                <CardContent className="pt-0">
                  {children}
                </CardContent>
              )}
            </>
          );
          
        default:
          return children;
      }
    };

    return (
      <LazyMotionWrapper
        ref={ref}
        className={cn(unifiedCardVariants({ variant, size, color: color as any }), className)}
        onClick={onClick}
        animate={animate}
        animation={animation}
        {...Object.fromEntries(Object.entries(props).filter(([key]) => key !== 'style' && key !== 'color'))}
      >
        <Card className="border-0 shadow-none bg-transparent">
          {renderContent()}
        </Card>
      </LazyMotionWrapper>
    );
  }
);

UnifiedCard.displayName = "UnifiedCard";

export default UnifiedCard;
