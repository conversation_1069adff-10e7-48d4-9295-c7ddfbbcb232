# Card Components

This directory contains all card-related components for the Trend_IMS application.

## Components Overview

### Modern Unified Components
- **`UnifiedCard.tsx`** - Main unified card component with CVA variants
- **`card-compat.tsx`** - Compatibility wrappers for backward compatibility
- **`card.tsx`** - Enhanced base card component with CVA variants

### Legacy Components (Deprecated)
- **`BaseCard/`** - Legacy base card component (use UnifiedCard variant="base" instead)
- **`ActionCard/`** - Legacy action card component (use UnifiedCard variant="action" instead)  
- **`StatusCard/`** - Legacy status card component (use UnifiedCard variant="status" instead)

## Quick Start

### Using UnifiedCard (Recommended)

```tsx
import { UnifiedCard } from '@/app/components/layout';

// Basic card
<UnifiedCard>
  <h3>Card Title</h3>
  <p>Card content</p>
</UnifiedCard>

// Base card with title and icon
<UnifiedCard
  variant="base"
  title="Inventory Overview"
  icon={<Package />}
  color="blue"
>
  <p>Card content</p>
</UnifiedCard>

// Action card
<UnifiedCard
  variant="action"
  label="Add Product"
  icon={<Plus />}
  onClick={() => handleAdd()}
/>

// Status card with data visualization
<UnifiedCard
  variant="status"
  title="Order Status"
  mainStat={{ value: 89, label: 'Active Orders' }}
  data={{
    'Pending': 25,
    'Processing': 40,
    'Shipped': 20,
    'Delivered': 4
  }}
/>
```

### Using Compatibility Wrappers

```tsx
import { BaseCardCompat, ActionCardCompat, StatusCardCompat } from '@/app/components/layout';

// Drop-in replacements for legacy components
<BaseCardCompat title="Title" icon={<Icon />}>
  Content
</BaseCardCompat>

<ActionCardCompat label="Add Item" onClick={handler} />

<StatusCardCompat 
  title="Status"
  mainStat={{ value: 100, label: 'Total' }}
/>
```

## Available Variants

### UnifiedCard Variants
- `default` - Basic card with hover effects
- `base` - Enhanced card with title, subtitle, icon support
- `action` - Button-like card for actions
- `status` - Data visualization card with progress bars
- `elevated` - Enhanced shadow for prominence
- `interactive` - Clickable with hover states
- `outline` - Border-only styling
- `ghost` - Transparent background

### Size Options
- `sm` - Small padding (12px)
- `md` - Medium padding (16px) - Default
- `lg` - Large padding (24px)
- `xl` - Extra large padding (32px)

### Color Themes
- `blue` - Information/primary actions
- `green` - Success states
- `red` - Error/destructive actions
- `yellow` - Warning states
- `purple` - Primary brand color
- `orange` - Secondary warning
- `gray` - Neutral/disabled states

### Animation Options
- `none` - No animations
- `subtle` - Gentle hover effects (default)
- `bounce` - More pronounced movement
- `glow` - Glowing shadow effects

## Migration Guide

### From BaseCard
```tsx
// Before
import { BaseCard } from '@/app/components/layout';
<BaseCard title="Title" icon={<Icon />}>Content</BaseCard>

// After
import { UnifiedCard } from '@/app/components/layout';
<UnifiedCard variant="base" title="Title" icon={<Icon />}>Content</UnifiedCard>
```

### From ActionCard
```tsx
// Before
import { ActionCard } from '@/app/components/layout';
<ActionCard label="Add" onClick={handler} />

// After
import { UnifiedCard } from '@/app/components/layout';
<UnifiedCard variant="action" label="Add" onClick={handler} />
```

### From StatusCard
```tsx
// Before
import { StatusCard } from '@/app/components/layout';
<StatusCard title="Status" mainStat={{ value: 100, label: 'Total' }} />

// After
import { UnifiedCard } from '@/app/components/layout';
<UnifiedCard variant="status" title="Status" mainStat={{ value: 100, label: 'Total' }} />
```

## TypeScript Support

All components include comprehensive TypeScript definitions:

```tsx
import type { UnifiedCardProps } from '@/app/components/layout';

const MyCard: React.FC<UnifiedCardProps> = (props) => {
  return <UnifiedCard {...props} />;
};
```

## Accessibility

All card components include:
- Proper ARIA attributes for interactive elements
- Keyboard navigation support
- Screen reader compatibility
- Focus management
- Color contrast compliance (WCAG 2.1 AA)

## Performance

- **Memoized Components**: Optimized re-rendering
- **Lazy Loading**: Framer Motion animations loaded on demand
- **Tree Shaking**: Only used variants are included in bundle
- **TypeScript**: Compile-time optimization

## Examples

See the UnifiedCardDemo component in `app/components/demos/UnifiedCardDemo.tsx` for comprehensive examples of all variants and features.

## Contributing

When contributing to card components:
1. Maintain backward compatibility
2. Add comprehensive TypeScript types
3. Include accessibility considerations
4. Update documentation and examples
5. Add appropriate tests

For detailed documentation, see [docs/unified-card-component.md](../../../../docs/unified-card-component.md).

## Component Files

- **`UnifiedCard.tsx`** - Main unified card component with CVA variants
- **`card-compat.tsx`** - Compatibility wrappers for backward compatibility
- **`card.tsx`** - Enhanced base card component with CVA variants
- **`README.md`** - This documentation file
- **`BaseCard/`** - Legacy BaseCard component (deprecated)
- **`ActionCard/`** - Legacy ActionCard component (deprecated)
- **`StatusCard/`** - Legacy StatusCard component (deprecated)
