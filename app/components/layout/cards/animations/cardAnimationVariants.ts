/**
 * Animation variants for UnifiedCard components
 * Separated into its own module for better tree shaking and lazy loading
 */

// Animation variants for Framer Motion
export const cardAnimationVariants = {
  none: {},
  subtle: {
    hover: { scale: 1.02, transition: { duration: 0.2 } },
    tap: { scale: 0.98 }
  },
  bounce: {
    hover: { scale: 1.05, transition: { type: "spring", stiffness: 400, damping: 10 } },
    tap: { scale: 0.95 }
  },
  glow: {
    hover: { 
      scale: 1.02, 
      boxShadow: "0 10px 25px rgba(0,0,0,0.15)",
      transition: { duration: 0.3 }
    },
    tap: { scale: 0.98 }
  }
};

export type AnimationType = keyof typeof cardAnimationVariants;

/**
 * Get animation variants for a specific animation type
 * This function enables lazy loading of animation variants
 */
export const getAnimationVariants = (animation: AnimationType) => {
  return cardAnimationVariants[animation] || cardAnimationVariants.none;
};
