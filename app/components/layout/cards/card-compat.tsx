"use client";

import * as React from "react";
import { UnifiedCard, type UnifiedCardProps } from './UnifiedCard';

/**
 * Compatibility wrapper for BaseCard
 * Maintains the original BaseCard API while using UnifiedCard internally
 */
export interface BaseCardCompatProps extends Omit<UnifiedCardProps, 'variant'> {
  // BaseCard specific props are already included in UnifiedCardProps
}

export const BaseCardCompat = React.forwardRef<HTMLDivElement, BaseCardCompatProps>(
  (props, ref) => {
    return <UnifiedCard ref={ref} variant="base" {...props} />;
  }
);

BaseCardCompat.displayName = "BaseCardCompat";

/**
 * Compatibility wrapper for ActionCard  
 * Maintains the original ActionCard API while using UnifiedCard internally
 */
export interface ActionCardCompatProps extends Omit<UnifiedCardProps, 'variant'> {
  // ActionCard specific props are already included in UnifiedCardProps
}

export const ActionCardCompat = React.forwardRef<HTMLDivElement, ActionCardCompatProps>(
  (props, ref) => {
    return <UnifiedCard ref={ref} variant="action" {...props} />;
  }
);

ActionCardCompat.displayName = "ActionCardCompat";

/**
 * Compatibility wrapper for StatusCard
 * Maintains the original StatusCard API while using UnifiedCard internally  
 */
export interface StatusCardCompatProps extends Omit<UnifiedCardProps, 'variant'> {
  // StatusCard specific props are already included in UnifiedCardProps
}

export const StatusCardCompat = React.forwardRef<HTMLDivElement, StatusCardCompatProps>(
  (props, ref) => {
    return <UnifiedCard ref={ref} variant="status" {...props} />;
  }
);

StatusCardCompat.displayName = "StatusCardCompat";

/**
 * Enhanced Card component with CVA variants
 * This is an enhanced version of the base Card component
 */
export interface EnhancedCardProps extends UnifiedCardProps {
  // All props are inherited from UnifiedCardProps
}

export const EnhancedCard = React.forwardRef<HTMLDivElement, EnhancedCardProps>(
  (props, ref) => {
    return <UnifiedCard ref={ref} {...props} />;
  }
);

EnhancedCard.displayName = "EnhancedCard";

// Re-export the original Card components for full backward compatibility
export { default as BaseCard } from './BaseCard';
export { default as ActionCard } from './ActionCard'; 
export { default as StatusCard } from './StatusCard';
export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent } from './card';
