"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Home,
  Package,
  Truck,
  FileText,
  Menu,
  LayoutGrid,
  Settings,
  LogOut,
  Users,
  BarChart2,
  ShoppingCart,
  Clipboard,
  RotateCcw,
  UserCog,
  Warehouse,
  Layers,
  Upload,
  Database,
  ChevronDown,
  ChevronRight,
  ChevronLeft,
  ChevronsRight,
  Box,
  AlertCircle
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAppContext } from '@/app/context/AppContext';
import { useTheme } from '@/app/context/ThemeContext';
import { useEnhancedTheme } from '@/app/hooks/useEnhancedTheme';
import { cn } from '@/app/lib/utils';
import { NavItem, SidebarProps } from './types';

/**
 * Client component for the Sidebar
 * Handles all interactive elements, animations, and state
 */
const SidebarClient: React.FC<SidebarProps> = ({ navItems: propNavItems, initialExpanded }) => {
  const pathname = usePathname();
  const { sidebarExpanded, setSidebarExpanded } = useAppContext();
  const { theme } = useTheme();
  const { themeClasses } = useEnhancedTheme();
  const [isMobile, setIsMobile] = useState(false);
  const [expandedCategory, setExpandedCategory] = useState<string | null>(null);

  // Use the default nav items if none are provided
  const defaultNavItems: NavItem[] = [
    { icon: <Home size={20} />, path: '/', label: 'Dashboard', category: 'Overview' },
    { icon: <BarChart2 size={20} />, path: '/analytics', label: 'Analytics', category: 'Overview' },

    { icon: <Package size={20} />, path: '/inventory', label: 'Inventory', badge: 5, category: 'Inventory' },
    { icon: <Box size={20} />, path: '/assemblies', label: 'Assemblies', category: 'Inventory' },
    { icon: <Upload size={20} />, path: '/product-import', label: 'Import Products', category: 'Inventory' },
    { icon: <Layers size={20} />, path: '/products', label: 'Products', category: 'Inventory' },
    { icon: <LayoutGrid size={20} />, path: '/categories', label: 'Categories', category: 'Inventory' },

    { icon: <ShoppingCart size={20} />, path: '/purchase-orders', label: 'Purchase Orders', badge: 2, category: 'Orders' },
    { icon: <Clipboard size={20} />, path: '/work-orders', label: 'Work Orders', category: 'Orders' },

    { icon: <RotateCcw size={20} />, path: '/inventory-transactions', label: 'Transactions', category: 'Operations' },
    { icon: <Truck size={20} />, path: '/logistics', label: 'Logistics', category: 'Operations' },
    { icon: <Warehouse size={20} />, path: '/warehouses', label: 'Warehouses', category: 'Operations' },
    { icon: <Layers size={20} />, path: '/batch-tracking', label: 'Batch Tracking', category: 'Operations' },

    { icon: <FileText size={20} />, path: '/reports', label: 'Reports', category: 'Administration' },
    { icon: <Users size={20} />, path: '/suppliers', label: 'Suppliers', category: 'Administration' },
    { icon: <UserCog size={20} />, path: '/user-management', label: 'Users', category: 'Administration' },
    { icon: <AlertCircle size={20} />, path: '/monitoring', label: 'System Monitoring', category: 'Administration' }
  ];

  const navItemsToUse = propNavItems || defaultNavItems;

  // Check window size on mount and when it changes
  useEffect(() => {
    const checkWindowSize = () => {
      setIsMobile(window.innerWidth < 768);

      // Auto-collapse sidebar on mobile
      if (window.innerWidth < 768 && sidebarExpanded) {
        setSidebarExpanded(false);
      }
    };

    // Initial check
    checkWindowSize();

    // Add event listener
    window.addEventListener('resize', checkWindowSize);

    // Cleanup
    return () => window.removeEventListener('resize', checkWindowSize);
  }, [sidebarExpanded, setSidebarExpanded]);

  // Set initial expanded state if provided
  useEffect(() => {
    if (initialExpanded !== undefined && initialExpanded !== sidebarExpanded) {
      setSidebarExpanded(initialExpanded);
    }
  }, [initialExpanded, setSidebarExpanded, sidebarExpanded]);

  const toggleSidebar = () => {
    setSidebarExpanded(!sidebarExpanded);
  };

  const toggleCategory = (category: string) => {
    if (expandedCategory === category) {
      setExpandedCategory(null);
    } else {
      setExpandedCategory(category);
    }
  };

  // Get all unique categories
  const categories = Array.from(new Set(navItemsToUse.map(item => item.category)));

  return (
    <motion.div
      initial={{ x: -100, opacity: 0 }}
      animate={{
        x: 0,
        opacity: 1,
        width: sidebarExpanded ? (isMobile ? '85%' : 240) : 68
      }}
      transition={{ duration: 0.3 }}
      className={cn(
        'h-screen flex flex-col py-6 shadow-lg overflow-hidden',
        'bg-card/95 border-r border-border',
        isMobile && sidebarExpanded ? 'fixed z-50' : 'relative'
      )}
    >
      <div className={`flex justify-between w-full ${sidebarExpanded ? 'px-4 mb-6' : 'px-3 mb-6'}`}>
        <motion.div
          whileHover={{ scale: 1.05 }}
          transition={{ duration: 0.2 }}
          className="flex items-center"
        >
          {/* Logo with floating animation */}
          <motion.div
            whileHover={{ rotate: 180 }}
            animate={{ y: [0, -5, 0] }}
            transition={{
              duration: 4,
              repeat: Infinity,
              repeatType: "reverse"
            }}
            className="w-10 h-10 rounded-full flex items-center justify-center bg-gradient-to-br from-primary/80 to-primary"
          >
            <div className="w-6 h-3 bg-primary-foreground rounded-sm"></div>
          </motion.div>
          <AnimatePresence>
            {sidebarExpanded && (
              <motion.div
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -10 }}
                transition={{ duration: 0.2 }}
                className="ml-3 text-foreground font-semibold text-lg"
              >
                Inventory
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* Subtle collapse button when expanded */}
        {sidebarExpanded && (
          <motion.div
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
            onClick={toggleSidebar}
            className={cn("p-2 cursor-pointer", themeClasses.interactive, themeClasses.text.secondary)}
            aria-label="Collapse sidebar"
          >
            <ChevronLeft size={16} />
          </motion.div>
        )}
      </div>

      {/* Subtle expand button for collapsed state - only show when collapsed */}
      {!sidebarExpanded && (
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={toggleSidebar}
          className={cn(
            "mb-4 w-10 h-7 mx-auto flex items-center justify-center cursor-pointer",
            themeClasses.interactive,
            themeClasses.text.secondary
          )}
          aria-label="Expand sidebar"
        >
          <ChevronsRight size={16} />
        </motion.div>
      )}

      <div className={`flex-1 w-full ${sidebarExpanded ? 'px-4' : 'px-3'} overflow-y-auto overflow-x-hidden custom-scrollbar`}>
        <div className={`flex flex-col ${sidebarExpanded ? 'space-y-1' : 'space-y-3'}`}>
          {/* Show categorized navigation when expanded */}
          {sidebarExpanded ? (
            categories.map((category) => (
              <div key={category} className="w-full">
                <motion.div
                  whileHover={{ backgroundColor: 'hsl(var(--accent) / 0.5)' }}
                  initial={{ backgroundColor: 'transparent' }}
                  transition={{ duration: 0.2 }}
                  onClick={() => toggleCategory(category!)}
                  className={cn(
                    "flex items-center justify-between px-3 py-2 text-xs font-medium uppercase cursor-pointer rounded-xl",
                    themeClasses.text.secondary
                  )}
                  role="button"
                  aria-expanded={expandedCategory === category}
                  aria-controls={`category-${category}`}
                >
                  <span>{category}</span>
                  <motion.div
                    animate={{ rotate: expandedCategory === category ? 180 : 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <ChevronDown size={14} />
                  </motion.div>
                </motion.div>

                <AnimatePresence>
                  {(expandedCategory === category || expandedCategory === null) && (
                    <motion.div
                      id={`category-${category}`}
                      initial={{ height: 0, opacity: 0 }}
                      animate={{
                        height: expandedCategory === category ? "auto" : 0,
                        opacity: expandedCategory === category ? 1 : 0,
                        marginBottom: expandedCategory === category ? 8 : 0
                      }}
                      exit={{ height: 0, opacity: 0, marginBottom: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="mt-1 ml-3 flex flex-col space-y-1">
                        {navItemsToUse
                          .filter(item => item.category === category)
                          .map((item) => (
                            <Link href={item.path} key={item.path} className="block w-full">
                              <motion.div
                                whileHover={{
                                  backgroundColor: pathname === item.path
                                    ? 'hsl(var(--accent) / 0.8)'
                                    : 'hsl(var(--accent) / 0.5)'
                                }}
                                transition={{ duration: 0.2 }}
                                className={cn(
                                  "flex items-center justify-between py-2 px-3 rounded-lg text-sm font-medium",
                                  pathname === item.path
                                    ? "bg-accent text-accent-foreground"
                                    : cn(themeClasses.text.secondary, "hover:text-foreground")
                                )}
                              >
                                <div className="flex items-center">
                                  <div className="w-5 h-5 flex items-center justify-center">
                                    {item.icon}
                                  </div>
                                  <span className="ml-3 text-sm font-medium">{item.label}</span>
                                </div>

                                {item.badge && (
                                  <span className="px-2 py-0.5 text-xs rounded-full bg-muted text-muted-foreground">
                                    {item.badge}
                                  </span>
                                )}
                              </motion.div>
                            </Link>
                          ))}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ))
          ) : (
            // Show just icons when collapsed
            navItemsToUse.map((item) => (
              <Link href={item.path} key={item.path} className="block w-full">
                <motion.div
                  whileHover={{
                    scale: 1.02,
                    backgroundColor: pathname === item.path
                      ? 'hsl(var(--accent) / 0.8)'
                      : 'hsl(var(--accent) / 0.5)'
                  }}
                  whileTap={{ scale: 0.98 }}
                  className={cn(
                    "mt-2 flex py-2 px-3 rounded-xl",
                    pathname === item.path
                      ? "bg-accent text-accent-foreground"
                      : cn(themeClasses.interactive, themeClasses.text.secondary)
                  )}
                >
                  <div className="w-5 h-5 flex items-center justify-center">
                    {item.icon}
                  </div>

                  {/* Badge indicator */}
                  {item.badge && (
                    <div className="absolute -top-1 -right-1 w-4 h-4 flex items-center justify-center">
                      <span className="w-4 h-4 flex items-center justify-center text-[10px] rounded-full bg-muted text-muted-foreground">
                        {item.badge}
                      </span>
                    </div>
                  )}
                </motion.div>

                {/* Tooltip for nav items when collapsed */}
                <div className="absolute left-16 mt-[-32px] px-2 py-1 w-auto bg-gray-900 text-white text-xs font-medium rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity z-50 pointer-events-none">
                  {item.label}
                </div>
              </Link>
            ))
          )}
        </div>
      </div>

      {/* Footer actions */}
      <div className={`mt-auto ${sidebarExpanded ? 'px-4' : 'px-3'}`}>
        <Link href="/settings" className="block w-full">
          <motion.div
            whileHover={{
              backgroundColor: 'hsl(var(--accent) / 0.8)',
              scale: 1.02
            }}
            whileTap={{ scale: 0.98 }}
            className={cn(
              "flex items-center py-2 rounded-xl mb-2",
              sidebarExpanded ? 'justify-start px-3' : 'justify-center',
              pathname === '/settings'
                ? "bg-accent text-accent-foreground"
                : cn(themeClasses.text.secondary, "hover:text-foreground")
            )}
          >
            <Settings size={18} />
            <AnimatePresence>
              {sidebarExpanded && (
                <motion.span
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  transition={{ duration: 0.2 }}
                  className="ml-3 text-sm font-medium"
                >
                  Settings
                </motion.span>
              )}
            </AnimatePresence>
          </motion.div>
        </Link>
      </div>
    </motion.div>
  );
};

export default SidebarClient;