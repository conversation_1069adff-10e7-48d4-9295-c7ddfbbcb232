"use client";

import React, { useState, use<PERSON>emo, ReactNode } from 'react';
import { Button } from '@/app/components/forms/Button';
import { Input } from '@/app/components/forms/Input';
import { DataTable } from '@/app/components/data-display/data-table/DataTable';
import { DataTableColumn } from '@/app/components/data-display/data-table/types';
import { cn } from '@/app/lib/utils';
import { Search, Table, LayoutGrid, List } from 'lucide-react';

/**
 * View mode options for the standardized table
 */
export type ViewMode = 'table' | 'grid' | 'detailed';

/**
 * View option configuration
 */
export interface ViewOption {
  id: ViewMode;
  label: string;
  icon: ReactNode;
}

/**
 * Props for the StandardizedTable component
 */
export interface StandardizedTableProps<T> {
  /** Data to display */
  data: T[];
  /** Column definitions for table view */
  columns: DataTableColumn<T>[];
  /** Search placeholder text */
  searchPlaceholder?: string;
  /** Available view modes */
  viewOptions?: ViewOption[];
  /** Default view mode */
  defaultViewMode?: ViewMode;
  /** Whether to show search input */
  enableSearch?: boolean;
  /** Whether to show view mode toggle */
  enableViewToggle?: boolean;
  /** Custom filter controls to render */
  renderFilters?: () => ReactNode;
  /** Custom actions to render in toolbar */
  renderActions?: () => ReactNode;
  /** Custom grid component for grid view */
  GridComponent?: React.ComponentType<{ data: T[]; searchTerm: string }>;
  /** Custom detailed component for detailed view */
  DetailedComponent?: React.ComponentType<{ data: T[]; searchTerm: string }>;
  /** Additional DataTable props */
  tableProps?: Partial<React.ComponentProps<typeof DataTable>>;
  /** Container className */
  className?: string;
  /** Callback when view mode changes */
  onViewModeChange?: (mode: ViewMode) => void;
  /** Callback when search term changes */
  onSearchChange?: (term: string) => void;
}

/**
 * Default view options
 */
const DEFAULT_VIEW_OPTIONS: ViewOption[] = [
  { id: 'table', label: 'Table', icon: <Table className="h-4 w-4" /> },
  { id: 'grid', label: 'Grid', icon: <LayoutGrid className="h-4 w-4" /> },
  { id: 'detailed', label: 'Detailed', icon: <List className="h-4 w-4" /> },
];

/**
 * StandardizedTable component - A unified table wrapper with consistent controls
 * 
 * Features:
 * - Unified search functionality
 * - View mode toggle (table/grid/detailed)
 * - Professional styling without bg-card wrappers
 * - Configurable toolbar with custom actions and filters
 * - Consistent UI/UX across all data tables
 */
export function StandardizedTable<T>({
  data,
  columns,
  searchPlaceholder = "Search...",
  viewOptions = DEFAULT_VIEW_OPTIONS,
  defaultViewMode = 'table',
  enableSearch = true,
  enableViewToggle = true,
  renderFilters,
  renderActions,
  GridComponent,
  DetailedComponent,
  tableProps = {},
  className,
  onViewModeChange,
  onSearchChange,
}: StandardizedTableProps<T>) {
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<ViewMode>(defaultViewMode);

  // Filter data based on search term - only for client-side search
  // For server-side pagination, search should be handled by the parent component
  const filteredData = useMemo(() => {
    // If using manual pagination, don't filter here - let the server handle it
    if (tableProps?.manualPagination) {
      return data;
    }

    if (!searchTerm.trim()) return data;

    const searchLower = searchTerm.toLowerCase();
    return data.filter((item: any) => {
      // Search through all string properties of the item
      return Object.values(item).some((value) => {
        if (typeof value === 'string') {
          return value.toLowerCase().includes(searchLower);
        }
        if (typeof value === 'number') {
          return value.toString().includes(searchLower);
        }
        return false;
      });
    });
  }, [data, searchTerm, tableProps?.manualPagination]);

  // Handle search change
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    onSearchChange?.(value);
  };

  // Handle view mode change
  const handleViewModeChange = (mode: ViewMode) => {
    setViewMode(mode);
    onViewModeChange?.(mode);
  };

  // Get available view options (filter out unavailable ones)
  const availableViewOptions = useMemo(() => {
    return viewOptions.filter(option => {
      return !((option.id === 'grid' && !GridComponent) || (option.id === 'detailed' && !DetailedComponent));
    });
  }, [viewOptions, GridComponent, DetailedComponent]);

  return (
    <div className={cn('w-full space-y-4', className)}>
      {/* Standardized Toolbar - Matching batch-tracking design */}
      {(enableSearch || enableViewToggle || renderFilters || renderActions) && (
        <div className="flex items-center justify-between space-x-4">
          {/* Left side - Search and Filters */}
          <div className="flex flex-1 items-center space-x-2">
            {/* Search Input */}
            {enableSearch && (
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  type="text"
                  placeholder={searchPlaceholder}
                  className="pl-9"
                  value={searchTerm}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  aria-label="Search table data"
                />
              </div>
            )}

            {/* Custom Filters */}
            {renderFilters?.()}
          </div>

          {/* Right side - View Toggle and Actions */}
          <div className="flex items-center gap-2">
            {/* Custom Actions */}
            {renderActions?.()}

            {/* View Mode Toggle */}
            {enableViewToggle && availableViewOptions.length > 1 && (
              <div className="flex items-center border border-border rounded-md">
                {availableViewOptions.map((option, index) => (
                  <Button
                    key={option.id}
                    variant={viewMode === option.id ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => handleViewModeChange(option.id)}
                    className={cn(
                      "h-8 px-3",
                      index === 0 && availableViewOptions.length > 1 && "rounded-r-none border-r border-border",
                      index > 0 && index < availableViewOptions.length - 1 && "rounded-none border-r border-border",
                      index === availableViewOptions.length - 1 && index > 0 && "rounded-l-none"
                    )}
                    title={option.label}
                  >
                    {option.icon}
                  </Button>
                ))}
              </div>
            )}


          </div>
        </div>
      )}

      {/* Content Container with Professional Styling - Matching batch-tracking design */}
      <div className="rounded-lg shadow-md dark:shadow-gray-900/30 overflow-hidden">
        {/* Render appropriate view based on mode */}
        {viewMode === 'table' && (
          <DataTable
            data={filteredData}
            columns={columns as any}
            enableSorting={true}
            enableFiltering={true}
            enableGlobalSearch={false} // Disabled since we have our own search
            enablePagination={true}
            enableColumnVisibility={false} // Disabled column visibility
            mobileDisplayMode="cards"
            density="normal"
            initialPagination={{ pageIndex: 0, pageSize: 20 }} // Default values - smaller page size for better UX
            pageSizeOptions={[10, 20, 50, 100, 150]}
            {...tableProps} // tableProps will override defaults above
          />
        )}

        {viewMode === 'grid' && GridComponent && (
          <div className="p-4">
            <GridComponent data={filteredData} searchTerm={searchTerm} />
          </div>
        )}

        {viewMode === 'detailed' && DetailedComponent && (
          <div className="p-4">
            <DetailedComponent data={filteredData} searchTerm={searchTerm} />
          </div>
        )}
      </div>
    </div>
  );
}
