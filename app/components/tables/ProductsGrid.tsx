'use client';

import { UnifiedItemGrid } from '@/app/components/grids/UnifiedItemGrid';
import { Product } from '@/app/components/tables/ProductsTable/types';

interface ProductsGridProps {
  products: Product[];
}

/**
 * Grid view for products using the unified grid component
 */
export function ProductsGrid({ products }: ProductsGridProps) {
  return (
    <UnifiedItemGrid
      items={products}
      itemType="product"
      emptyStateConfig={{
        title: 'No products found',
        description: 'Get started by creating your first product.',
        actionLabel: 'Create Product',
        actionHref: '/products/new'
      }}
    />
  );
}
