"use client";

import {
  DataTable,
} from "@/app/components/data-display/data-table";
import { WarehouseDisplayData, WarehouseApiResponse, transformApiDataToDisplayData } from "@/app/components/forms/WarehouseForm/types";
import { getApiUrl } from '@/app/utils/apiUtils';
import {
    AlertTriangle,
    RefreshCw,
} from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { WarehouseTableProps, WarehouseColumnData, WarehouseTableActionsForDataTable } from "./types";
import { createWarehouseSimpleColumns, createWarehouseComplexColumns } from "./columns";

/**
 * Client component for the WarehouseTable
 * Handles data fetching, transformation, and rendering
 */
export default function WarehouseTableClient({
  simple = false,
  className = "",
  initialData = [],
  fetchData = true,
  onWarehouseClick,
  onWarehouseEdit,
  onWarehouseDelete,
  onWarehouseView,
  isLoading: externalLoading = false,
  error: externalError = null,
  searchTerm = "",
  actions = {}
}: WarehouseTableProps) {
  const [warehouses, setWarehouses] = useState<WarehouseDisplayData[]>(initialData);
  const [isLoading, setIsLoading] = useState(externalLoading || fetchData);
  const [error, setError] = useState<string | null>(externalError);

  // Fetch warehouses from API
  const fetchWarehouses = async () => {
    if (!fetchData) return;

    try {
      setIsLoading(true);
      setError(null);

      const url = getApiUrl('/api/warehouses');
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`Failed to fetch warehouses: ${response.status} ${response.statusText}`);
      }

      const data: WarehouseApiResponse = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      if (data.data && Array.isArray(data.data)) {
        const transformedWarehouses = data.data.map(transformApiDataToDisplayData);
        setWarehouses(transformedWarehouses);
      } else {
        setWarehouses([]);
      }
    } catch (err) {
      console.error('Error fetching warehouses:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to load warehouses';
      setError(errorMessage);
      setWarehouses([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    if (fetchData) {
      fetchWarehouses();
    }
  }, [fetchData]);

  // Update external loading state
  useEffect(() => {
    setIsLoading(externalLoading || (fetchData && warehouses.length === 0));
  }, [externalLoading, fetchData, warehouses.length]);

  // Update external error state
  useEffect(() => {
    setError(externalError);
  }, [externalError]);

  // Transform warehouse data for table display
  const tableData: WarehouseColumnData[] = useMemo(() => {
    let filteredWarehouses = warehouses;

    // Apply search filter if provided
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filteredWarehouses = warehouses.filter(warehouse =>
        (warehouse.name || '').toLowerCase().includes(searchLower) ||
        (warehouse.location_id || '').toLowerCase().includes(searchLower) ||
        (warehouse.location || '').toLowerCase().includes(searchLower) ||
        (warehouse.manager || '').toLowerCase().includes(searchLower) ||
        (warehouse.contact || '').toLowerCase().includes(searchLower)
      );
    }

    return filteredWarehouses.map(warehouse => ({
      _id: warehouse._id,
      location_id: warehouse.location_id,
      name: warehouse.name,
      location: warehouse.location,
      capacity: warehouse.capacity,
      capacityFormatted: warehouse.capacityFormatted,
      manager: warehouse.manager,
      contact: warehouse.contact,
      isBinTracked: warehouse.isBinTracked,
      status: warehouse.status,
      createdAt: warehouse.createdAt,
      updatedAt: warehouse.updatedAt,
      createdAtFormatted: warehouse.createdAtFormatted,
      updatedAtFormatted: warehouse.updatedAtFormatted,
    }));
  }, [warehouses, searchTerm]);

  // Create table actions
  const tableActions: WarehouseTableActionsForDataTable = useMemo(() => {
    const actions: WarehouseTableActionsForDataTable = {};

    if (onWarehouseView || onWarehouseClick) {
      actions.onView = (warehouse: WarehouseColumnData) => {
        const originalWarehouse = warehouses.find(w => w._id === warehouse._id);
        if (originalWarehouse) {
          if (onWarehouseView) {
            onWarehouseView(originalWarehouse);
          } else if (onWarehouseClick) {
            onWarehouseClick(originalWarehouse);
          }
        }
      };
    }

    if (onWarehouseEdit) {
      actions.onEdit = (warehouse: WarehouseColumnData) => {
        const originalWarehouse = warehouses.find(w => w._id === warehouse._id);
        if (originalWarehouse) {
          onWarehouseEdit(originalWarehouse);
        }
      };
    }

    if (onWarehouseDelete) {
      actions.onDelete = (warehouse: WarehouseColumnData) => {
        const originalWarehouse = warehouses.find(w => w._id === warehouse._id);
        if (originalWarehouse) {
          onWarehouseDelete(originalWarehouse);
        }
      };
    }

    return actions;
  }, [warehouses, onWarehouseView, onWarehouseClick, onWarehouseEdit, onWarehouseDelete]);

  // Create columns based on simple/complex mode
  const columns = useMemo(() => {
    return simple
      ? createWarehouseSimpleColumns(tableActions) as any
      : createWarehouseComplexColumns(tableActions) as any;
  }, [simple, tableActions]);

  // Handle error state
  if (error && !isLoading) {
    return (
      <div className={`flex flex-col items-center justify-center h-64 ${className}`}>
        <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
        <h3 className="text-lg font-semibold text-foreground mb-2">Error Loading Warehouses</h3>
        <p className="text-sm text-muted-foreground mb-4 text-center max-w-md">
          {error}
        </p>
        <button
          onClick={fetchWarehouses}
          className="flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
        >
          <RefreshCw className="h-4 w-4" />
          <span>Retry</span>
        </button>
      </div>
    );
  }

  // Render the DataTable
  return (
    <div className={className}>
      <DataTable
        data={tableData}
        columns={columns}
        enableSorting={true}
        enableFiltering={!simple}
        enableGlobalSearch={!simple && !searchTerm} // Disable global search if simple mode or external search is provided
        enablePagination={true}
        enableColumnVisibility={!simple}
        mobileDisplayMode="cards"
        isLoading={isLoading}
        error={error ? new Error(error) : null}
        onRowClick={(row) => {
          // Convert back to WarehouseDisplayData for callback compatibility
          const warehouse = warehouses.find(w => w._id === row._id);
          if (warehouse && onWarehouseClick) {
            onWarehouseClick(warehouse);
          }
        }}
        initialPagination={{ pageIndex: 0, pageSize: simple ? 10 : 20 }}
        pageSizeOptions={simple ? [5, 10, 20] : [10, 20, 50, 100]}
        caption={`${simple ? 'Simple' : 'Detailed'} warehouses table with ${tableData.length} items`}
      />
    </div>
  );
}
