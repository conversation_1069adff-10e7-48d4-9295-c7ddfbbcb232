"use client";

import { Badge } from '@/app/components/data-display/badge';
import { Button } from '@/app/components/forms/Button';
import { ColumnDef } from '@tanstack/react-table';
import { ArrowUpDown, Building2, Edit, Eye, MapPin, Package, Phone, Trash2, User } from 'lucide-react';
import { WarehouseColumnData, WarehouseTableActionsForDataTable } from './types';

/**
 * Create warehouse table columns for simple view
 */
export function createWarehouseSimpleColumns(
  actions: WarehouseTableActionsForDataTable = {}
): ColumnDef<WarehouseColumnData>[] {
  return [
    {
      accessorKey: 'location_id',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="h-auto p-0 font-semibold"
        >
          Location ID
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Building2 className="h-4 w-4 text-muted-foreground" />
          <span className="font-mono text-sm">{row.original.location_id}</span>
        </div>
      ),
    },
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="h-auto p-0 font-semibold"
        >
          Warehouse Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="font-medium">{row.original.name}</div>
      ),
    },
    {
      accessorKey: 'location',
      header: 'Location',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2 max-w-[200px]">
          <MapPin className="h-4 w-4 text-muted-foreground flex-shrink-0" />
          <span className="truncate text-sm">{row.original.location}</span>
        </div>
      ),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = row.original.status;
        return (
          <Badge variant={status === 'active' ? 'default' : 'secondary'}>
            {status}
          </Badge>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      enableSorting: false,
      enableHiding: false,
      cell: ({ row }) => {
        const warehouse = row.original;

        return (
          <div className="flex items-center gap-1">
            {/* View Action */}
            {actions.onView && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => actions.onView!(warehouse)}
                title="View warehouse details"
              >
                <Eye className="h-4 w-4" />
              </Button>
            )}

            {/* Edit Action */}
            {actions.onEdit && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => actions.onEdit!(warehouse)}
                title="Edit warehouse"
              >
                <Edit className="h-4 w-4" />
              </Button>
            )}

            {/* Delete Action */}
            {actions.onDelete && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => actions.onDelete!(warehouse)}
                title="Delete warehouse"
                className="text-destructive hover:text-destructive"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        );
      },
    },
  ];
}

/**
 * Create warehouse table columns for complex/detailed view
 */
export function createWarehouseComplexColumns(
  actions: WarehouseTableActionsForDataTable = {}
): ColumnDef<WarehouseColumnData>[] {
  return [
    {
      accessorKey: 'location_id',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="h-auto p-0 font-semibold"
        >
          Location ID
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      enableHiding: false, // Always show location ID
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Building2 className="h-4 w-4 text-muted-foreground" />
          <span className="font-mono text-sm">{row.original.location_id}</span>
        </div>
      ),
    },
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="h-auto p-0 font-semibold"
        >
          Warehouse Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      enableHiding: false, // Always show warehouse name
      cell: ({ row }) => (
        <div className="font-medium">{row.original.name}</div>
      ),
    },
    {
      accessorKey: 'location',
      header: 'Location',
      enableHiding: true, // Allow hiding location
      cell: ({ row }) => (
        <div className="flex items-center space-x-2 max-w-[250px]">
          <MapPin className="h-4 w-4 text-muted-foreground flex-shrink-0" />
          <span className="truncate text-sm">{row.original.location}</span>
        </div>
      ),
    },
    {
      accessorKey: 'manager',
      header: 'Manager',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <User className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{row.original.manager}</span>
        </div>
      ),
    },
    {
      accessorKey: 'contact',
      header: 'Contact',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2 max-w-[150px]">
          <Phone className="h-4 w-4 text-muted-foreground flex-shrink-0" />
          <span className="truncate text-sm">{row.original.contact}</span>
        </div>
      ),
    },
    {
      accessorKey: 'capacityFormatted',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="h-auto p-0 font-semibold"
        >
          Capacity
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Package className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-mono">{row.original.capacityFormatted}</span>
        </div>
      ),
      sortingFn: (rowA, rowB) => {
        // Sort by actual capacity number, not formatted string
        return rowA.original.capacity - rowB.original.capacity;
      },
    },
    {
      accessorKey: 'isBinTracked',
      header: 'Bin Tracking',
      cell: ({ row }) => {
        const isBinTracked = row.original.isBinTracked;
        return (
          <Badge variant={isBinTracked ? 'default' : 'outline'}>
            {isBinTracked ? 'Enabled' : 'Disabled'}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = row.original.status;
        return (
          <Badge variant={status === 'active' ? 'default' : 'secondary'}>
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'createdAtFormatted',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          className="h-auto p-0 font-semibold"
        >
          Created
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <span className="text-sm text-muted-foreground">
          {row.original.createdAtFormatted}
        </span>
      ),
      sortingFn: (rowA, rowB) => {
        // Sort by actual date, not formatted string
        return new Date(rowA.original.createdAt).getTime() - new Date(rowB.original.createdAt).getTime();
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      enableSorting: false,
      enableHiding: false,
      cell: ({ row }) => {
        const warehouse = row.original;

        return (
          <div className="flex items-center gap-1">
            {/* View Action */}
            {actions.onView && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => actions.onView!(warehouse)}
                title="View warehouse details"
              >
                <Eye className="h-4 w-4" />
              </Button>
            )}

            {/* Edit Action */}
            {actions.onEdit && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => actions.onEdit!(warehouse)}
                title="Edit warehouse"
              >
                <Edit className="h-4 w-4" />
              </Button>
            )}

            {/* Delete Action */}
            {actions.onDelete && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => actions.onDelete!(warehouse)}
                title="Delete warehouse"
                className="text-destructive hover:text-destructive"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        );
      },
    },
  ];
}
