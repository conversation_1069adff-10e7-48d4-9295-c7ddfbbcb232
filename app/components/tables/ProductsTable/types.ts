import { HierarchicalComponent } from '@/app/types';

/**
 * Type definitions for ProductsTable component
 */

/**
 * Interface for Product data in the table
 */
export interface Product {
  _id: string;
  id: string; // Required for frontend compatibility, can be mapped from _id
  productCode: string;
  name: string;
  description: string;
  categoryId: string;
  status: 'active' | 'discontinued' | 'in_development';
  sellingPrice: number;
  components?: HierarchicalComponent[]; // New hierarchical components array
  // Legacy fields for backward compatibility
  assemblyId?: string | null;
  partId?: string | null;
  createdAt: Date;
  updatedAt: Date;
  // Additional properties for table compatibility
  currentStock?: number;
  reorderLevel?: number;
  supplierManufacturer?: string;
  costPrice?: number; // Added for ProductViewContent
  category?: string; // Added for ProductViewContent
  inventory?: {
    currentStock: number;
    warehouseId?: string;
    safetyStockLevel?: number;
    maximumStockLevel?: number;
    averageDailyUsage?: number;
    abcClassification?: string;
    lastStockUpdate?: Date | null;
  };
}

/**
 * Props for the ProductsTable component
 */
export interface ProductsTableProps {
  products: Product[];
  simple?: boolean; // For simple mode without dropdown actions
  onDeletePart?: (id: string) => Promise<void>; // Optional callback for when a part is deleted
}