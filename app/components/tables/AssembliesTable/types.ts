/**
 * Type definitions for AssembliesTable component
 */

/**
 * Interface for Part data
 */
export interface Part {
  _id: string;
  partNumber?: string; // Added to align with updated schema and other Part types
  name: string;
  description?: string | null;
  technicalSpecs?: string | null; // Changed from technical_specs
  isManufactured?: boolean; // Changed from is_manufactured
  reorderLevel?: number | null; // Changed from reorder_level
  status?: string;
  inventory?: {
    currentStock: number;
    warehouseId: string; // Added: Reference to warehouses._id
    safetyStockLevel: number; // Added
    maximumStockLevel: number; // Added
    averageDailyUsage: number; // Added
    abcClassification: string; // Added
    lastStockUpdate?: string | Date | null; // Changed from lastCountDate, added null option
    // location?: string | null; // Kept for potential legacy use, but warehouseId is primary
  };
  costData?: {
    unitCost: number;
    currency?: string;
    lastUpdated?: string | Date | null;
  };
  supplierId?: string | null; // Added to align
  unitOfMeasure?: string; // Added to align
  costPrice?: number; // Added to align
  categoryId?: string | null; // Added to align
  createdAt?: string | Date;
  updatedAt?: string | Date;
}

/**
 * Interface for Assembly data in the table, aligned with both old and new schema
 */
export interface Assembly {
  _id: string; // MongoDB ObjectId as string
  assemblyCode: string; // Unique code for the assembly (now required)
  name: string;
  description?: string | null;
  productId?: string | null; // Reference to Product ObjectId as string
  parentId?: string | null; // Reference to parent Assembly ObjectId as string
  isTopLevel?: boolean;
  
  // Properly aligned with the database schema
  partsRequired?: {
    partId: string; // MongoDB ObjectId as string for the part
    partDetails?: Part; // Populated part details, if requested from API
    quantityRequired: number; // Changed from quantity to quantityRequired to match DB schema
    unitOfMeasure?: string; // Made optional to align with Part interface
  }[];
  
  version?: number; // Changed to number to align with DB schema (Int32)
  status?: 'active' | 'pending_review' | 'in_production' | 'obsolete';
  manufacturingInstructions?: string | null;
  estimatedBuildTime?: string | null;
  
  // Fields kept for backward compatibility or UI purposes
  type?: 'standard' | 'custom' | 'kit';
  manufacturing_lead_time?: number | null;
  cost?: number | null;
  images?: { url: string; alt_text?: string | null; }[];
  attributes?: { name: string; value: string; }[];
  notes?: string | null;
  createdAt?: string | Date;
  updatedAt?: string | Date;
}

/**
 * Props for the AssembliesTable component
 */
export interface AssembliesTableProps {
  assemblies: Assembly[];
  simple?: boolean; // For simple mode without dropdown actions
}
