import { type ModalComponentItem } from './EnhancedBomViewer';
import { type Assembly } from '@/app/components/tables/AssembliesTable/types';
import { type Product } from '@/app/components/tables/ProductsTable/types';
import { getPartById } from '@/app/api-client/part.api';

/**
 * BOM configuration interface for BaseViewModal integration
 */
export interface BomTabConfig {
  bomData: ModalComponentItem[];
  onLoadAssemblyComponents?: (assemblyId: string) => Promise<ModalComponentItem[]>;
  showBomSummary?: boolean;
  bomViewerHeight?: string;
  enableBomSearch?: boolean;
}

/**
 * Options for BOM tab configuration
 */
export interface BomTabConfigOptions {
  type?: 'assembly' | 'product' | 'auto';
  onLoadAssemblyComponents?: (assemblyId: string) => Promise<ModalComponentItem[]>;
  showSummary?: boolean;
  height?: string;
  enableSearch?: boolean;
}

/**
 * Transform assembly data to BOM format for BaseViewModal integration
 * Now supports hierarchical parts with children
 */
export function transformAssemblyToBomData(assembly: Assembly | any): ModalComponentItem[] {
  console.log('[BOM Transform] Input assembly:', JSON.stringify(assembly, null, 2));

  if (!assembly) {
    console.log('[BOM Transform] No assembly provided');
    return [];
  }

  // Handle different assembly data structures
  const components: ModalComponentItem[] = [];

  /**
   * Recursively process parts and their children
   */
  const processPartsRecursively = (parts: any[], level: number = 0, parentId: string | null = null) => {
    parts.forEach((partRequired: any, index: number) => {
      console.log(`[BOM Transform] Processing part at level ${level}, index ${index + 1}:`, partRequired);

      let partData = null;

      // Check for partDetails first (new structure)
      if (partRequired.partDetails) {
        console.log(`[BOM Transform] Using partDetails for part at level ${level}, index ${index + 1}`);
        partData = partRequired.partDetails;
      }
      // Fallback to partId (legacy structure or populated partId)
      else if (partRequired.partId) {
        console.log(`[BOM Transform] Using partId for part at level ${level}, index ${index + 1}`);
        partData = partRequired.partId;
      }

      if (partData) {
        let componentItem: ModalComponentItem;

        // Handle both populated and non-populated part references
        if (typeof partData === 'string') {
          console.log(`[BOM Transform] Part at level ${level}, index ${index + 1} is unpopulated ObjectId string:`, partData);
          // partData is not populated, just an ObjectId string
          componentItem = {
            part: {
              _id: partData,
              name: 'Unknown Part',
              current_stock: 0,
              is_assembly: false
            },
            quantity: partRequired.quantityRequired || 1,
            level: level,
            parentId: parentId,
            partId: partData
          };
        } else {
          // Ensure name is always a string, not an object
          const partName = typeof partData.name === 'object'
            ? (partData.name.name || partData.name._id || 'Unknown Part')
            : (partData.name || 'Unknown Part');

          console.log(`[BOM Transform] Part at level ${level}, index ${index + 1} is populated object:`, partName);
          // partData is populated with full part object
          componentItem = {
            part: {
              _id: partData._id || partData,
              name: partName,
              partNumber: partData.partNumber,
              description: partData.description,
              category: partData.category,
              type: partData.type,
              current_stock: partData.inventory?.currentStock || partData.current_stock || 0,
              is_assembly: partData.is_assembly || false
            },
            quantity: partRequired.quantityRequired || 1,
            level: level,
            parentId: parentId,
            partId: partData._id || partData
          };
        }

        // Add the component to the list
        components.push(componentItem);

        // Process children recursively if they exist
        if (partRequired.children && Array.isArray(partRequired.children) && partRequired.children.length > 0) {
          console.log(`[BOM Transform] Processing ${partRequired.children.length} children for part at level ${level}, index ${index + 1}`);
          processPartsRecursively(partRequired.children, level + 1, componentItem.partId);
        }
      } else {
        console.log(`[BOM Transform] Part at level ${level}, index ${index + 1} has no valid part data`);
      }
    });
  };

  // Check for partsRequired (primary structure based on actual schema)
  if (assembly.partsRequired && Array.isArray(assembly.partsRequired)) {
    console.log('[BOM Transform] Processing partsRequired array:', assembly.partsRequired.length, 'items');
    processPartsRecursively(assembly.partsRequired, 0, null);
  }

  console.log('[BOM Transform] Returning components:', components.length, components);
  return components;
}

/**
 * Enhanced async transform product data to BOM format with real part details
 * Products can have BOM data from their associated assembly, direct components, or hierarchical components
 */
export async function transformProductToBomDataAsync(product: Product | any): Promise<ModalComponentItem[]> {
  console.log('[BOM Transform Product] Input product:', JSON.stringify(product, null, 2));

  if (!product) {
    console.log('[BOM Transform Product] No product provided');
    return [];
  }

  const components: ModalComponentItem[] = [];

  // Priority 1: Check for hierarchical components (new structure)
  if (product.components && Array.isArray(product.components)) {
    console.log('[BOM Transform Product] Processing hierarchical components:', product.components.length);
    // Process hierarchical components recursively with real part data
    const hierarchicalComponents = await processHierarchicalComponentsAsync(product.components, 0, null);
    console.log('[BOM Transform Product] Processed hierarchical components:', hierarchicalComponents.length);
    components.push(...hierarchicalComponents);
  } else {
    console.log('[BOM Transform Product] No hierarchical components found');
  }

  console.log('[BOM Transform Product] Final components count:', components.length);

  // Debug: Log the final BOM data structure to see inventory information
  console.log('[BOM Transform Product] Final BOM data with inventory:');
  components.forEach((comp, index) => {
    console.log(`[BOM Transform Product] Component ${index + 1}: ${comp.part.name} - current_stock: ${comp.part.current_stock}, is_assembly: ${comp.part.is_assembly}`);
  });

  return components;
}

/**
 * Transform product data to BOM format for BaseViewModal integration (synchronous version)
 * Products can have BOM data from their associated assembly, direct components, or hierarchical components
 */
export function transformProductToBomData(product: Product | any, partsData?: any[]): ModalComponentItem[] {
  if (!product) {
    return [];
  }

  const components: ModalComponentItem[] = [];

  // Priority 1: Check for hierarchical components (new structure)
  if (product.components && Array.isArray(product.components)) {
    // Process hierarchical components recursively
    const hierarchicalComponents = processHierarchicalComponents(product.components, 0, null, false, partsData);
    components.push(...hierarchicalComponents);
  }
  // Priority 2: Check for direct components array (legacy transformation from product service)
  else if (product.components && Array.isArray(product.components)) {
    console.log('[BOM Transform] Processing product components array:', product.components.length, 'items');

    product.components.forEach((component: any, index: number) => {
      console.log(`[BOM Transform] Processing component ${index + 1}:`, component);

      if (component.part) {
        // Ensure name is always a string, not an object
        const partName = typeof component.part.name === 'object'
          ? (component.part.name.name || component.part.name._id || 'Unknown Part')
          : (component.part.name || 'Unknown Part');

        console.log(`[BOM Transform] Component ${index + 1} name:`, partName);

        components.push({
          part: {
            _id: component.part._id || component.partId,
            name: partName,
            partNumber: component.part.partNumber,
            description: component.part.description,
            category: component.part.category,
            type: component.part.type,
            current_stock: component.part.current_stock || 0,
            is_assembly: component.part.is_assembly || false
          },
          quantity: component.quantity || 1,
          level: 0,
          parentId: null,
          partId: component.part._id || component.partId
        });
      }
    });
  }
  // Priority 3: Check for assembly-based BOM data (assemblyId with partsRequired)
  else if (product.assemblyId && typeof product.assemblyId === 'object' && product.assemblyId.partsRequired) {
    // Use assembly transformation for consistency
    const assemblyComponents = transformAssemblyToBomData(product.assemblyId);
    components.push(...assemblyComponents);
  }

  return components;
}

/**
 * Async helper function to fetch part details by ID with enhanced error handling
 */
async function fetchPartDetails(partId: string): Promise<any | null> {
  try {
    console.log(`[BOM Transform] Fetching part details for ID: ${partId}`);

    // Validate partId
    if (!partId || typeof partId !== 'string') {
      console.warn(`[BOM Transform] Invalid part ID: ${partId}`);
      return null;
    }

    // Try direct API call first
    try {
      const response = await fetch(`/api/parts/${partId}`);
      if (response.ok) {
        const result = await response.json();
        if (result.data) {
          console.log(`[BOM Transform] Successfully fetched part via API: ${result.data.name} (${result.data.partNumber})`);
          return result.data;
        }
      }
      console.warn(`[BOM Transform] API call failed or returned no data for part ${partId}`);
    } catch (apiError) {
      console.warn(`[BOM Transform] API call failed for part ${partId}:`, apiError);
    }

    // Fallback to getPartById function
    try {
      const part = await getPartById(partId);
      if (part) {
        console.log(`[BOM Transform] Successfully fetched part via getPartById: ${part.name} (${part.partNumber})`);
        return part;
      } else {
        console.warn(`[BOM Transform] Part not found via getPartById for ID: ${partId}`);
        return null;
      }
    } catch (getPartError) {
      console.error(`[BOM Transform] getPartById failed for ${partId}:`, getPartError);
      return null;
    }
  } catch (error) {
    console.error(`[BOM Transform] Failed to fetch part details for ${partId}:`, error);
    return null;
  }
}

/**
 * Enhanced async function to process hierarchical components with real part data
 */
async function processHierarchicalComponentsAsync(
  components: any[],
  level: number = 0,
  parentId: string | null = null,
  isProcessingChildren: boolean = false
): Promise<ModalComponentItem[]> {
  console.log(`[BOM Process Hierarchical] Processing ${components.length} components at level ${level}, parentId: ${parentId}, isProcessingChildren: ${isProcessingChildren}`);

  const processedComponents: ModalComponentItem[] = [];

  for (const component of components) {
    console.log(`[BOM Process Hierarchical] Processing component:`, JSON.stringify(component, null, 2));

    // For children, use the parentId directly; for components, generate a componentId
    const componentId = isProcessingChildren ? parentId : `${parentId || 'root'}-${components.indexOf(component)}`;
    console.log(`[BOM Process Hierarchical] Generated componentId: ${componentId}`);

    // Handle direct part components (children with only partId)
    if (component.partId && !component.assemblyId && isProcessingChildren) {
      console.log(`[BOM Process Hierarchical] Processing child part component with partId:`, typeof component.partId, component.partId);

      // Extract part ID string and existing part data correctly
      const partIdString = typeof component.partId === 'object' ? component.partId._id : component.partId;
      const existingPartData = typeof component.partId === 'object' ? component.partId : null;



      // Create part object with corrected fallback logic (matching sync version)
      let partObject;
      if (existingPartData) {
        // Use existing populated part data directly
        const partName = typeof existingPartData.name === 'object'
          ? (existingPartData.name.name || existingPartData.name._id || 'Unknown Part')
          : (existingPartData.name || 'Unknown Part');

        partObject = {
          _id: existingPartData._id || partIdString,
          name: partName,
          partNumber: existingPartData.partNumber || existingPartData.sku || 'N/A',
          description: existingPartData.description || '',
          current_stock: existingPartData.current_stock || existingPartData.inventory?.currentStock || 0,
          is_assembly: existingPartData.is_assembly || false
        };
        console.log(`[BOM Process Hierarchical] Using existing part data for child ${partName} (${partObject.partNumber})`);
      } else {
        // Try to fetch part details if no existing data
        console.log(`[BOM Process Hierarchical] Attempting to fetch part details for string partId: ${partIdString}`);
        const fetchedPartDetails = await fetchPartDetails(partIdString);
        console.log(`[BOM Process Hierarchical] Fetch result:`, fetchedPartDetails);

        if (fetchedPartDetails) {
          // Use fetched part details
          partObject = {
            _id: fetchedPartDetails._id,
            name: fetchedPartDetails.name,
            partNumber: fetchedPartDetails.partNumber,
            description: fetchedPartDetails.description || '',
            current_stock: fetchedPartDetails.inventory?.currentStock || 0,
            is_assembly: fetchedPartDetails.is_assembly || false
          };
          console.log(`[BOM Process Hierarchical] Fetched part details for child ${fetchedPartDetails.name} (${fetchedPartDetails.partNumber})`);
        } else {
          // Last resort fallback
          partObject = {
            _id: partIdString,
            name: 'Unknown Child Part',
            partNumber: 'N/A',
            description: 'Child part details unavailable',
            current_stock: 0,
            is_assembly: false
          };
          console.warn(`[BOM Process Hierarchical] Using fallback for child part ID: ${partIdString}`);
        }
      }

      processedComponents.push({
        part: partObject,
        quantity: component.quantityRequired || 1,
        level: level + 1,
        parentId: parentId,
        partId: partIdString
      });

      // Process children recursively if they exist
      if (component.children && component.children.length > 0) {
        console.log(`[BOM Process Hierarchical] Processing ${component.children.length} children for child part ${partObject.name}`);
        const childComponents = await processHierarchicalComponentsAsync(
          component.children,
          level + 2,
          partIdString,
          true
        );
        processedComponents.push(...childComponents);
      }

      continue; // Skip to next component
    }

    // Handle assembly components (components with assemblyId)
    if (component.assemblyId) {
      console.log(`[BOM Process Hierarchical] Processing assembly component with assemblyId`);
      const assembly = component.assemblyId;

      if (assembly && assembly.partsRequired) {
        console.log(`[BOM Process Hierarchical] Processing assembly with ${assembly.partsRequired.length} parts`);

        // First, get the assembly name
        const assemblyName = typeof assembly.name === 'object'
          ? (assembly.name.name || assembly.name._id || 'Unknown Assembly')
          : (assembly.name || 'Unknown Assembly');

        // First, recursively process the assembly's parts as children
        console.log(`[BOM Process Hierarchical] Processing ${assembly.partsRequired.length} parts as children of assembly`);
        const assemblyPartsAsComponents = assembly.partsRequired.map((partRequired: any) => ({
          partId: partRequired.partId,
          quantityRequired: partRequired.quantityRequired,
          children: partRequired.children || []
        }));

        const childComponents = await processHierarchicalComponentsAsync(
          assemblyPartsAsComponents,
          level + 1,
          assembly._id || componentId,
          true // Flag to indicate this is processing children, not components
        );
        processedComponents.push(...childComponents);

        // Now calculate assembly availability based on the processed child parts
        let assemblyAvailability = Number.MAX_SAFE_INTEGER; // Start with max, will be reduced by limiting parts

        // Process child parts to calculate assembly availability
        if (assembly.partsRequired && assembly.partsRequired.length > 0) {
          for (const childPart of assembly.partsRequired) {
            const childQuantityRequired = childPart.quantityRequired || 1;

            // Try to find the child part in the processed components to get its stock
            let childStock = 0;
            const childPartId = typeof childPart.partId === 'string' ? childPart.partId : childPart.partId?._id;

            // Look for the child part in the components we just processed
            const childComponent = childComponents.find(comp => comp.part._id === childPartId);
            if (childComponent) {
              childStock = childComponent.part.current_stock || 0;
            }

            // Calculate how many assemblies we can make with this child part
            const assembliesFromThisPart = Math.floor(childStock / childQuantityRequired);
            assemblyAvailability = Math.min(assemblyAvailability, assembliesFromThisPart);
          }
        }

        // If no parts or calculation failed, set to 0
        if (assemblyAvailability === Number.MAX_SAFE_INTEGER || assemblyAvailability < 0) {
          assemblyAvailability = 0;
        }

        const assemblyObject = {
          _id: assembly._id || componentId,
          name: assemblyName,
          partNumber: assembly.assemblyCode || assembly.sku || 'N/A',
          description: assembly.description || '',
          current_stock: assemblyAvailability, // Calculate based on child parts availability
          is_assembly: true
        };

        console.log(`[BOM Process Hierarchical] Adding assembly parent node: ${assemblyName} with availability: ${assemblyAvailability}`);
        processedComponents.push({
          part: assemblyObject,
          quantity: component.quantityRequired || 1,
          level: level,
          parentId: parentId,
          partId: assembly._id || componentId
        });
      } else {
        // Assembly not populated, create placeholder
        const assemblyName = typeof assembly === 'object' ? (assembly.name || 'Unknown Assembly') : 'Unknown Assembly';
        processedComponents.push({
          part: {
            _id: typeof assembly === 'string' ? assembly : assembly._id,
            name: assemblyName,
            is_assembly: true
          },
          quantity: component.quantityRequired || 1,
          level,
          parentId,
          partId: typeof assembly === 'string' ? assembly : assembly._id
        });
      }
    }

    // Handle direct part components (children that are just part references)
    if (component.partId && !component.assemblyId) {
      // Extract part ID and existing part data
      const partId = component.partId;
      const existingPartData = typeof component.part === 'object' ? component.part : null;

      // Try to fetch part details asynchronously
      const partDetails = await fetchPartDetails(partId);

      // Create part object with enhanced fallback logic
      let partObject;
      if (partDetails) {
        // Use fetched part details
        partObject = {
          _id: partDetails._id,
          name: partDetails.name,
          partNumber: partDetails.partNumber,
          description: partDetails.description || '',
          current_stock: partDetails.inventory?.currentStock || 0,
          is_assembly: partDetails.is_assembly || false
        };
        console.log(`[BOM Transform] Fetched part details for ${partDetails.name} (${partDetails.partNumber})`);
      } else if (existingPartData) {
        // Use existing populated part data as fallback
        const partName = typeof existingPartData.name === 'object'
          ? (existingPartData.name.name || existingPartData.name._id || 'Unknown Part')
          : (existingPartData.name || 'Unknown Part');

        partObject = {
          _id: existingPartData._id || partId,
          name: partName,
          partNumber: existingPartData.partNumber || existingPartData.sku || 'N/A',
          description: existingPartData.description || '',
          current_stock: existingPartData.current_stock || 0,
          is_assembly: existingPartData.is_assembly || false
        };
        console.log(`[BOM Transform] Using existing part data for ${partName} (${partObject.partNumber})`);
      } else {
        // Last resort fallback
        partObject = {
          _id: partId,
          name: 'Unknown Part',
          partNumber: 'N/A',
          description: 'Part details unavailable',
          current_stock: 0,
          is_assembly: false
        };
        console.warn(`[BOM Transform] Using fallback for part ID: ${partId}`);
      }

      processedComponents.push({
        part: partObject,
        quantity: component.quantityRequired || 1,
        level: level + 1,
        parentId: parentId, // Use the actual parentId, not the generated componentId
        partId: partId
      });
    }

    // Recursively process children at component level
    if (component.children && component.children.length > 0) {
      const childComponents = await processHierarchicalComponentsAsync(component.children, level + 1, componentId);
      processedComponents.push(...childComponents);
    }
  }

  return processedComponents;
}

/**
 * Helper function to process hierarchical components recursively (synchronous version)
 */
function processHierarchicalComponents(
  components: any[],
  level: number = 0,
  parentId: string | null = null,
  isProcessingChildren: boolean = false,
  partsData?: any[]
): ModalComponentItem[] {
  const processedComponents: ModalComponentItem[] = [];

  components.forEach((component: any, index: number) => {
    // For children, use the parentId directly; for components, generate a componentId
    const componentId = isProcessingChildren ? parentId : `${parentId || 'root'}-${index}`;

    // Handle direct part components (children with only partId)
    if (component.partId && !component.assemblyId && isProcessingChildren) {
      console.log(`[BOM Process Hierarchical Sync] Processing child part component with partId:`, typeof component.partId, component.partId);

      // Extract part ID string and existing part data correctly
      const partIdString = typeof component.partId === 'object' ? component.partId._id : component.partId;
      const existingPartData = typeof component.partId === 'object' ? component.partId : null;

      // Create part object with fallback logic (synchronous version)
      let partObject;
      if (existingPartData) {
        // Use existing populated part data
        const partName = typeof existingPartData.name === 'object'
          ? (existingPartData.name.name || existingPartData.name._id || 'Unknown Part')
          : (existingPartData.name || 'Unknown Part');

        partObject = {
          _id: existingPartData._id || partIdString,
          name: partName,
          partNumber: existingPartData.partNumber || existingPartData.sku || 'N/A',
          description: existingPartData.description || '',
          current_stock: existingPartData.current_stock || 0,
          is_assembly: existingPartData.is_assembly || false
        };
        console.log(`[BOM Process Hierarchical Sync] Using existing part data for child ${partName} (${partObject.partNumber})`);
      } else {
        // Try to find part in AppContext parts data first
        console.log(`[BOM Process Hierarchical Sync] Attempting to find part in AppContext for ID: ${partIdString}`);

        let foundPart = null;
        if (partsData && partsData.length > 0) {
          foundPart = partsData.find(part => part._id === partIdString || part.id === partIdString);
          console.log(`[BOM Process Hierarchical Sync] Found part in AppContext:`, !!foundPart, foundPart?.name);
        }

        if (foundPart) {
          // Use part data from AppContext
          partObject = {
            _id: foundPart._id || foundPart.id,
            name: foundPart.name || 'Unknown Part',
            partNumber: foundPart.partNumber || 'N/A',
            description: foundPart.description || '',
            current_stock: foundPart.inventory?.currentStock || foundPart.current_stock || 0,
            is_assembly: foundPart.is_assembly || false
          };
          console.log(`[BOM Process Hierarchical Sync] Using AppContext part data for ${foundPart.name} (${foundPart.partNumber})`);
        } else {
          // Fall back to "Unknown Child Part"
          partObject = {
            _id: partIdString,
            name: 'Unknown Child Part',
            partNumber: 'N/A',
            description: 'Child part details unavailable',
            current_stock: 0,
            is_assembly: false
          };
          console.warn(`[BOM Process Hierarchical Sync] Using fallback for child part ID: ${partIdString}`);
        }
      }

      processedComponents.push({
        part: partObject,
        quantity: component.quantityRequired || 1,
        level: level + 1,
        parentId: parentId,
        partId: partIdString
      });

      // Process children recursively if they exist
      if (component.children && component.children.length > 0) {
        const childComponents = processHierarchicalComponents(
          component.children,
          level + 2,
          partIdString,
          true,
          partsData
        );
        processedComponents.push(...childComponents);
      }

      return; // Skip to next component
    }

    // Handle assembly component
    if (component.assemblyId) {
      const assembly = component.assemblyId;

      // If assembly is populated, process its parts
      if (typeof assembly === 'object' && assembly.partsRequired) {
        // First, get the assembly name
        const assemblyName = typeof assembly.name === 'object'
          ? (assembly.name.name || assembly.name._id || 'Unknown Assembly')
          : (assembly.name || 'Unknown Assembly');

        // First, recursively process the assembly's parts as children
        const assemblyPartsAsComponents = assembly.partsRequired.map((partRequired: any) => ({
          partId: partRequired.partId,
          quantityRequired: partRequired.quantityRequired,
          children: partRequired.children || []
        }));

        const childComponents = processHierarchicalComponents(
          assemblyPartsAsComponents,
          level + 1,
          assembly._id || componentId,
          true, // Flag to indicate this is processing children, not components
          partsData
        );
        processedComponents.push(...childComponents);

        // Now calculate assembly availability based on the processed child parts
        let assemblyAvailability = Number.MAX_SAFE_INTEGER; // Start with max, will be reduced by limiting parts

        // Process child parts to calculate assembly availability
        if (assembly.partsRequired && assembly.partsRequired.length > 0) {
          for (const childPart of assembly.partsRequired) {
            const childQuantityRequired = childPart.quantityRequired || 1;

            // Try to find the child part in the processed components to get its stock
            let childStock = 0;
            const childPartId = typeof childPart.partId === 'string' ? childPart.partId : childPart.partId?._id;

            // Look for the child part in the components we just processed
            const childComponent = childComponents.find(comp => comp.part._id === childPartId);
            if (childComponent) {
              childStock = childComponent.part.current_stock || 0;
            }

            // Calculate how many assemblies we can make with this child part
            const assembliesFromThisPart = Math.floor(childStock / childQuantityRequired);
            assemblyAvailability = Math.min(assemblyAvailability, assembliesFromThisPart);
          }
        }

        // If no parts or calculation failed, set to 0
        if (assemblyAvailability === Number.MAX_SAFE_INTEGER || assemblyAvailability < 0) {
          assemblyAvailability = 0;
        }

        const assemblyObject = {
          _id: assembly._id || componentId,
          name: assemblyName,
          partNumber: assembly.assemblyCode || assembly.sku || 'N/A',
          description: assembly.description || '',
          current_stock: assemblyAvailability, // Calculate based on child parts availability
          is_assembly: true
        };

        processedComponents.push({
          part: assemblyObject,
          quantity: component.quantityRequired || 1,
          level: level,
          parentId: parentId,
          partId: assembly._id || componentId
        });
      } else {
        // Assembly not populated, create placeholder
        const assemblyName = typeof assembly === 'object' ? (assembly.name || 'Unknown Assembly') : 'Unknown Assembly';
        processedComponents.push({
          part: {
            _id: typeof assembly === 'string' ? assembly : assembly._id,
            name: assemblyName,
            is_assembly: true
          },
          quantity: component.quantityRequired || 1,
          level,
          parentId,
          partId: typeof assembly === 'string' ? assembly : assembly._id
        });
      }
    }

    // Handle direct part components (children that are just part references)
    if (component.partId && !component.assemblyId) {
      // This is a direct part reference, create a component for it
      // Note: We need to fetch the part details from the parts context or API
      processedComponents.push({
        part: {
          _id: component.partId,
          name: `Part ${component.partId}`, // Placeholder - should be fetched from parts data
          partNumber: component.partId,
          description: '',
          current_stock: 0,
          is_assembly: false
        },
        quantity: component.quantityRequired || 1,
        level: level + 1,
        parentId: parentId, // Use the actual parentId, not the generated componentId
        partId: component.partId
      });
    }

    // Recursively process children at component level
    if (component.children && component.children.length > 0) {
      const childComponents = processHierarchicalComponents(component.children, level + 1, componentId, false, partsData);
      processedComponents.push(...childComponents);
    }
  });

  return processedComponents;
}

/**
 * Generic BOM data transformer that handles various data structures
 */
export function transformToBomData(data: Assembly | Product | any, type: 'assembly' | 'product' | 'auto' = 'auto', partsData?: any[]): ModalComponentItem[] {
  if (!data) return [];

  // Auto-detect type if not specified
  if (type === 'auto') {
    if (data.partsRequired || (data.components && data.is_assembly)) {
      type = 'assembly';
    } else if (data.assemblies || (data.components && !data.is_assembly)) {
      type = 'product';
    } else {
      // Default to assembly if unclear
      type = 'assembly';
    }
  }

  switch (type) {
    case 'assembly':
      return transformAssemblyToBomData(data);
    case 'product':
      return transformProductToBomData(data, partsData);
    default:
      return [];
  }
}

/**
 * Create a lazy loading function for assembly components
 */
export function createAssemblyComponentLoader(
  apiEndpoint: string = '/api/assemblies'
): (assemblyId: string) => Promise<ModalComponentItem[]> {
  return async (assemblyId: string) => {
    try {
      const response = await fetch(`${apiEndpoint}/${assemblyId}?includeParts=true`);
      if (!response.ok) {
        throw new Error(`Failed to load assembly: ${response.statusText}`);
      }
      
      const assembly = await response.json();
      return transformAssemblyToBomData(assembly);
    } catch (error) {
      console.error('Failed to load assembly components:', error);
      throw error;
    }
  };
}

/**
 * Utility to check if an item has BOM data that should be displayed
 */
export function shouldShowBomTab(data: Assembly | Product | any): boolean {
  if (!data) return false;
  
  const bomData = transformToBomData(data);
  return bomData.length > 0;
}

/**
 * Enhanced async BOM tab configuration that fetches real part data
 */
export async function getBomTabConfigAsync(
  data: Assembly | Product | any,
  options: BomTabConfigOptions = {}
): Promise<BomTabConfig | null> {
  let bomData: ModalComponentItem[] = [];

  // Auto-detect type if not specified
  let type = options.type || 'auto';
  if (type === 'auto') {
    if (data.partsRequired || (data.components && data.is_assembly)) {
      type = 'assembly';
    } else if (data.assemblies || (data.components && !data.is_assembly)) {
      type = 'product';
    } else {
      type = 'assembly';
    }
  }

  // Use async transformation for products to get real part data
  if (type === 'product') {
    bomData = await transformProductToBomDataAsync(data);
  } else {
    bomData = transformToBomData(data, type);
  }

  if (bomData.length === 0) {
    return null;
  }

  return {
    bomData,
    onLoadAssemblyComponents: options.onLoadAssemblyComponents || createAssemblyComponentLoader(),
    showBomSummary: options.showSummary ?? true,
    bomViewerHeight: options.height ?? '500px',
    enableBomSearch: options.enableSearch ?? true
  } as BomTabConfig;
}

/**
 * Get BOM tab configuration for BaseViewModal (synchronous version)
 */
export function getBomTabConfig(
  data: Assembly | Product | any,
  options: BomTabConfigOptions & { partsData?: any[] } = {}
): BomTabConfig | null {
  const bomData = transformToBomData(data, options.type, options.partsData);

  if (bomData.length === 0) {
    return null;
  }

  return {
    bomData,
    onLoadAssemblyComponents: options.onLoadAssemblyComponents || createAssemblyComponentLoader(),
    showBomSummary: options.showSummary ?? true,
    bomViewerHeight: options.height ?? '500px',
    enableBomSearch: options.enableSearch ?? true
  } as BomTabConfig;
}

export default {
  transformAssemblyToBomData,
  transformProductToBomData,
  transformProductToBomDataAsync,
  transformToBomData,
  createAssemblyComponentLoader,
  shouldShowBomTab,
  getBomTabConfig,
  getBomTabConfigAsync
};
