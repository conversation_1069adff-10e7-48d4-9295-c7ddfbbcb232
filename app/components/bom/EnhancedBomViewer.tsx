'use client';

import { Badge } from '@/app/components/data-display/badge';
import { cn } from '@/app/lib/utils';
import {
    buildHierarchicalBomData,
    transformBomDataToTree,
    type BomTreeData,
    type BomTreeItem,
    type ModalComponentItem
} from '@/app/utils/bomDataTransform';
import {
    AlertCircle,
    Check,
    ChevronDown,
    ChevronRight,
    Layers,
    Package,
    Search,
    X
} from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
    StaticTreeDataProvider,
    Tree,
    TreeItemIndex,
    UncontrolledTreeEnvironment
} from 'react-complex-tree';

/**
 * Props for EnhancedBomViewer component
 */
interface EnhancedBomViewerProps {
  /**
   * Components to display in the BOM viewer
   */
  components: ModalComponentItem[];
  
  /**
   * Initial expansion level
   */
  level?: number;
  
  /**
   * Whether components should be initially expanded
   */
  initiallyExpanded?: boolean;
  
  /**
   * Parent ID for hierarchical display
   */
  parentId?: string | null;
  
  /**
   * Enable search functionality
   */
  enableSearch?: boolean;
  
  /**
   * Custom height for the tree container (default: 500px)
   */
  height?: string;
  
  /**
   * Callback for lazy loading assembly components
   */
  onLoadAssemblyComponents?: (assemblyId: string) => Promise<ModalComponentItem[]>;
  
  /**
   * Loading state
   */
  isLoading?: boolean;
  
  /**
   * Error state
   */
  error?: string | null;
  
  /**
   * Custom className
   */
  className?: string;
}

/**
 * Enhanced BOM Viewer Component with React Complex Tree
 * Provides sophisticated hierarchical visualization with expand/collapse,
 * search capabilities, and maintains all existing BOM features
 */
export function EnhancedBomViewer({
  components,
  level = 0,
  initiallyExpanded = true,
  parentId = null,
  enableSearch = true,
  height = '500px',
  onLoadAssemblyComponents,
  isLoading = false,
  error = null,
  className = ''
}: EnhancedBomViewerProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedItems, setExpandedItems] = useState<TreeItemIndex[]>([]);
  const [focusedItem, setFocusedItem] = useState<TreeItemIndex>();
  const [selectedItems, setSelectedItems] = useState<TreeItemIndex[]>([]);
  const [assemblyCache, setAssemblyCache] = useState<Record<string, ModalComponentItem[]>>({});
  const [loadingNodes, setLoadingNodes] = useState<Record<string, boolean>>({});

  // Transform components to hierarchical structure
  const hierarchicalComponents = useMemo(() => {
    if (!components || components.length === 0) return [];
    return buildHierarchicalBomData(components);
  }, [components]);

  // Transform to tree data format
  const treeData = useMemo(() => {
    if (!hierarchicalComponents || hierarchicalComponents.length === 0) {
      return { root: { index: 'root', isFolder: true, children: [], data: null } as any };
    }

    // Determine the correct root ID based on the components' parentId
    const hasRootComponents = hierarchicalComponents.some(c => c.parentId === "root-0");
    const rootId = hasRootComponents ? "root-0" : "root";

    return transformBomDataToTree(hierarchicalComponents, rootId);
  }, [hierarchicalComponents]);

  // Determine the correct root item ID for the Tree component
  const rootItemId = useMemo(() => {
    if (!hierarchicalComponents || hierarchicalComponents.length === 0) {
      return 'root';
    }

    // Check if we have components with parentId "root-0"
    const hasRootComponents = hierarchicalComponents.some(c => c.parentId === "root-0");
    return hasRootComponents ? "root-0" : "root";
  }, [hierarchicalComponents]);

  // Filter tree data based on search term
  const filteredTreeData = useMemo(() => {
    if (!searchTerm.trim()) return treeData;
    
    const filtered: BomTreeData = {};
    const searchLower = searchTerm.toLowerCase();
    
    Object.entries(treeData).forEach(([key, item]) => {
      if (key === 'root') {
        filtered[key] = item;
        return;
      }
      
      const bomItem = item as BomTreeItem;
      const matchesSearch = 
        bomItem.data.part.name.toLowerCase().includes(searchLower) ||
        bomItem.data.part.partNumber?.toLowerCase().includes(searchLower) ||
        bomItem.data.part.description?.toLowerCase().includes(searchLower) ||
        bomItem.data.part.category?.toLowerCase().includes(searchLower);
      
      if (matchesSearch) {
        filtered[key] = item;
      }
    });
    
    return filtered;
  }, [treeData, searchTerm]);

  // Initialize expanded state
  useEffect(() => {
    if (initiallyExpanded && Object.keys(treeData).length > 1) {
      const initialExpanded: TreeItemIndex[] = [];
      Object.entries(treeData).forEach(([key, item]) => {
        if (key !== 'root' && (item as BomTreeItem).isFolder) {
          initialExpanded.push(key);
        }
      });
      // Also expand root if it has children
      if (treeData.root && (treeData.root as BomTreeItem).children.length > 0) {
        initialExpanded.push('root');
      }
      setExpandedItems(initialExpanded);
    }
  }, [treeData, initiallyExpanded]);

  /**
   * Get stock status badge
   */
  const getStockBadge = useCallback((currentStock: number, requiredQuantity: number) => {
    const isInStock = currentStock >= requiredQuantity;
    const isLowStock = currentStock > 0 && currentStock < requiredQuantity;
    
    if (isInStock) {
      return (
        <Badge variant="success" className="text-xs">
          <Check className="h-3 w-3 mr-1" />
          In Stock
        </Badge>
      );
    } else if (isLowStock) {
      return (
        <Badge variant="warning" className="text-xs">
          <AlertCircle className="h-3 w-3 mr-1" />
          Low Stock
        </Badge>
      );
    } else {
      return (
        <Badge variant="destructive" className="text-xs">
          <X className="h-3 w-3 mr-1" />
          Out of Stock
        </Badge>
      );
    }
  }, []);

  /**
   * Handle lazy loading of assembly components
   */
  const handleLoadAssemblyComponents = useCallback(async (assemblyId: string) => {
    if (!onLoadAssemblyComponents || assemblyCache[assemblyId] || loadingNodes[assemblyId]) {
      return;
    }

    setLoadingNodes(prev => ({ ...prev, [assemblyId]: true }));

    try {
      const components = await onLoadAssemblyComponents(assemblyId);
      setAssemblyCache(prev => ({ ...prev, [assemblyId]: components }));
    } catch (error) {
      console.error('Failed to load assembly components:', error);
    } finally {
      setLoadingNodes(prev => ({ ...prev, [assemblyId]: false }));
    }
  }, [onLoadAssemblyComponents, assemblyCache, loadingNodes]);

  /**
   * Handle tree item expansion with lazy loading
   */
  const handleItemExpansion = useCallback(async (itemId: TreeItemIndex, isExpanded: boolean) => {
    if (isExpanded && typeof itemId === 'string') {
      const bomItem = (treeData as any)[itemId as string];
      if (bomItem?.data?.part?.is_assembly && bomItem.data.part._id) {
        await handleLoadAssemblyComponents(bomItem.data.part._id);
      }
    }
  }, [treeData, handleLoadAssemblyComponents]);

  /**
   * Custom tree item renderer
   */
  const renderTreeItem = useCallback(({ item, depth, children, title, arrow, context }: any) => {
    if (item.index === 'root') {
      return <div>{children}</div>;
    }

    const bomItem = item as BomTreeItem;
    const part = bomItem.data.part;
    const quantity = bomItem.data.quantity;
    const isLoadingNode = loadingNodes[part._id];

    // Use the component's level property instead of depth for proper hierarchical indentation
    const componentLevel = bomItem.data.level || 0;
    const indentLevel = componentLevel;



    return (
      <div className="relative">
        {/* Tree connecting lines for hierarchical structure */}
        {componentLevel > 0 && (
          <div className="absolute left-0 top-0 h-8 flex items-center pointer-events-none">
            {/* Single L-shaped connector positioned at the correct level */}
            <div
              className="custom-tree-connector border-l-2 border-b-2 border-border"
              style={{
                width: '12px',
                height: '16px',
                marginLeft: `${(componentLevel - 1) * 20 + 8}px`,
                borderBottomLeftRadius: '3px'
              }}
            />
          </div>
        )}

        <div
          className={cn(
            "relative flex items-center py-2 px-3 transition-colors cursor-pointer min-h-[32px]",
            "hover:bg-muted/30"
            // Remove all custom selection/focus styles to let react-complex-tree handle it
          )}
          style={{
            marginLeft: `${componentLevel * 20}px`,
            zIndex: 1,
            position: 'relative',
            border: 'none',
            outline: 'none'
          }}
        >
          {/* Expand/Collapse Arrow */}
          <div className="flex items-center mr-3 flex-shrink-0">
            {bomItem.isFolder ? (
              <div className="w-4 h-4 flex items-center justify-center">
                {isLoadingNode ? (
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current" />
                ) : (
                  arrow
                )}
              </div>
            ) : (
              <div className="w-4 h-4" />
            )}
          </div>

          {/* Part Type Icon */}
          <div className="mr-3 flex-shrink-0">
            {part.is_assembly ? (
              <Layers className="h-4 w-4 text-primary" />
            ) : (
              <Package className="h-4 w-4 text-muted-foreground" />
            )}
          </div>

          {/* Part Information */}
          <div className="flex-1 min-w-0 mr-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-foreground break-words leading-tight">
                  {part.businessName || part.name}
                </h4>
                {part.businessName && (
                  <p className="text-xs text-muted-foreground break-words mt-0.5 italic">
                    {part.name}
                  </p>
                )}
                {part.partNumber && (
                  <p className="text-xs text-muted-foreground break-words mt-0.5">
                    {part.partNumber}
                  </p>
                )}
                {part.description && (
                  <p className="text-xs text-muted-foreground break-words mt-0.5 line-clamp-2">
                    {part.description}
                  </p>
                )}
              </div>

              <div className="flex items-center gap-2 flex-shrink-0">
                {part.category && (
                  <Badge variant="secondary" className="text-xs px-2 py-0.5 whitespace-nowrap">
                    {part.category}
                  </Badge>
                )}
                <span className="text-xs font-medium text-foreground whitespace-nowrap">
                  Qty: {quantity}
                </span>
                {getStockBadge(part.current_stock || 0, quantity)}
              </div>
            </div>
          </div>
        </div>

        {children}
      </div>
    );
  }, [getStockBadge, loadingNodes]);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8 text-muted-foreground">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-current mr-3" />
        Loading BOM data...
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn("flex items-center justify-center py-8 text-destructive", className)}>
        <AlertCircle className="h-5 w-5 mr-2" />
        {error}
      </div>
    );
  }

  // Empty state
  if (!components || components.length === 0) {
    return (
      <div className={cn("py-8 text-center text-muted-foreground", className)}>
        <Layers className="h-12 w-12 mx-auto mb-4 opacity-50" />
        <p>No components found for this assembly.</p>
      </div>
    );
  }

  return (
    <div className={cn("space-y-3", className)}>
      {/* Search Bar */}
      {enableSearch && (
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search components..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-10 py-2 text-sm rounded-md border border-input bg-background text-foreground placeholder:text-muted-foreground focus:border-ring focus:ring-1 focus:ring-ring transition-colors"
          />
          {searchTerm && (
            <button
              onClick={() => setSearchTerm('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 p-0.5 rounded-sm text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
              aria-label="Clear search"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
      )}

      {/* CSS Overrides to hide react-complex-tree default connecting lines */}
      <style jsx>{`
        .rct-tree-container :global(.rct-tree-item-li::before),
        .rct-tree-container :global(.rct-tree-item-li::after),
        .rct-tree-container :global(.rct-tree-item-title-container::before),
        .rct-tree-container :global(.rct-tree-item-title-container::after),
        .rct-tree-container :global(.rct-tree-item::before),
        .rct-tree-container :global(.rct-tree-item::after),
        .rct-tree-container :global([class*="tree-line"]),
        .rct-tree-container :global([class*="connecting-line"]),
        .rct-tree-container :global([class*="tree-connector"]) {
          display: none !important;
          visibility: hidden !important;
          opacity: 0 !important;
          border: none !important;
          background: none !important;
          content: none !important;
        }

        /* Hide any pseudo-elements that might be creating lines */
        .rct-tree-container :global(*::before),
        .rct-tree-container :global(*::after) {
          border-left: none !important;
          border-right: none !important;
          border-top: none !important;
          border-bottom: none !important;
          background-image: none !important;
          background: transparent !important;
        }

        /* Ensure our custom connecting lines are visible */
        .rct-tree-container .custom-tree-connector {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
        }
      `}</style>

      {/* Tree View */}
      <div
        className="rct-tree-container"
        style={{ height }}
      >
        <UncontrolledTreeEnvironment
          dataProvider={new StaticTreeDataProvider(filteredTreeData)}
          getItemTitle={(item) => (item.data as any)?.displayName || 'Unknown'}
          viewState={{
            'bom-tree': {
              expandedItems,
              selectedItems,
              focusedItem: focusedItem,
            } as any,
          }}
          renderItemTitle={renderTreeItem}
          renderItemArrow={({ item, context }: { item: any; context: any }) => {
            if (!(item as BomTreeItem).isFolder) return null;
            return context.isExpanded ? (
              <ChevronDown className="h-4 w-4 transition-transform text-muted-foreground hover:text-foreground" />
            ) : (
              <ChevronRight className="h-4 w-4 transition-transform text-muted-foreground hover:text-foreground" />
            );
          }}
          canDragAndDrop={false}
          canDropOnFolder={false}
          canReorderItems={false}
        >
          <Tree treeId="bom-tree" rootItem={rootItemId} />
        </UncontrolledTreeEnvironment>
      </div>

      {/* Summary */}
      <div className="text-xs text-muted-foreground pt-2 border-t border-border">
        {searchTerm ? (
          <p>
            Showing {Object.keys(filteredTreeData).length - 1} of {Object.keys(treeData).length - 1} components
            {searchTerm && ` matching "${searchTerm}"`}
          </p>
        ) : (
          <p>Total components: {Object.keys(treeData).length - 1}</p>
        )}
      </div>
    </div>
  );
}

/**
 * Utility function to check if components have BOM data
 */
export function hasBomData(components: ModalComponentItem[] | undefined | null): boolean {
  return Array.isArray(components) && components.length > 0;
}

/**
 * Utility function to get BOM summary statistics
 */
export function getBomSummary(components: ModalComponentItem[]) {
  if (!components || components.length === 0) {
    return {
      totalComponents: 0,
      totalAssemblies: 0,
      totalParts: 0,
      outOfStockItems: 0,
      lowStockItems: 0
    };
  }

  let totalAssemblies = 0;
  let totalParts = 0;
  let outOfStockItems = 0;
  let lowStockItems = 0;

  components.forEach(component => {
    if (component.part.is_assembly) {
      totalAssemblies++;
    } else {
      totalParts++;
    }

    const currentStock = component.part.current_stock || 0;
    const requiredQuantity = component.quantity;

    if (currentStock === 0) {
      outOfStockItems++;
    } else if (currentStock < requiredQuantity) {
      lowStockItems++;
    }
  });

  return {
    totalComponents: components.length,
    totalAssemblies,
    totalParts,
    outOfStockItems,
    lowStockItems
  };
}

/**
 * Utility function to flatten hierarchical BOM data for export
 */
export function flattenBomData(components: ModalComponentItem[]): ModalComponentItem[] {
  const flattened: ModalComponentItem[] = [];

  function processComponent(component: ModalComponentItem, level: number = 0) {
    flattened.push({
      ...component,
      level
    });
  }

  components.forEach(component => processComponent(component));
  return flattened;
}

/**
 * Export the component and types
 */
export type { EnhancedBomViewerProps, ModalComponentItem };
export default EnhancedBomViewer;
