'use client';

import { motion } from 'framer-motion';
import { CheckCircle, ChevronRight, Layers, Package, XCircle } from 'lucide-react';
import { useMemo } from 'react';

// UI Components
import { Badge } from '@/app/components/data-display/badge';
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/app/components/feedback/tooltip';
import { Card, CardContent, CardFooter, CardHeader } from '@/app/components/layout/cards/card';

// Types and Utils
import { useTheme } from '@/app/context/ThemeContext';
import { cn } from '@/app/lib/utils';

// ============================================================================
// Type Definitions
// ============================================================================

/**
 * Base interface for items that can be displayed in the unified card
 */
export interface BaseItem {
  _id: string;
  name: string;
  description?: string | null;
  status?: string;
  createdAt?: Date | string;
  updatedAt?: Date | string;
}

/**
 * Assembly-specific item interface
 */
export interface AssemblyItem extends BaseItem {
  assemblyCode: string;
  status?: 'active' | 'pending_review' | 'in_production' | 'obsolete';
  partsRequired?: Array<{
    partId: string;
    partDetails?: {
      name: string;
      partNumber?: string;
      inventory?: { currentStock: number };
      costData?: { unitCost: number };
    };
    quantityRequired: number;
    unitOfMeasure?: string;
    children?: Array<{
      partId: string;
      partDetails?: {
        name: string;
        partNumber?: string;
        inventory?: { currentStock: number };
        costData?: { unitCost: number };
      };
      quantityRequired: number;
      unitOfMeasure?: string;
      children?: any[];
    }>;
  }>;
  version?: number;
  isTopLevel?: boolean;
}

/**
 * Product-specific item interface
 */
export interface ProductItem extends BaseItem {
  productCode: string;
  status?: 'active' | 'discontinued' | 'in_development';
  sellingPrice: number;
  components?: Array<{
    assemblyId: string;
    assemblyDetails?: {
      name: string;
      assemblyCode?: string;
      partsRequired?: any[];
    };
    quantityRequired: number;
    children?: any[];
  }>;
  categoryId?: string;
  inventory?: {
    currentStock: number;
    warehouseId?: string;
  };
}

/**
 * Union type for all supported item types
 */
export type UnifiedItem = AssemblyItem | ProductItem;

/**
 * Item type discriminator
 */
export type ItemType = 'assembly' | 'product';

/**
 * Configuration for item-specific behavior
 */
export interface ItemConfig {
  type: ItemType;
  codeField: 'assemblyCode' | 'productCode';
  icon: React.ReactNode;
  statusBadgeComponent: React.ComponentType<any>;
  viewButtonComponent: React.ComponentType<any>;
  editActionComponent?: React.ComponentType<any>;
  deleteActionComponent?: React.ComponentType<any>;
  duplicateActionComponent?: React.ComponentType<any>;
  calculateMetrics: (item: UnifiedItem) => ItemMetrics;
}

/**
 * Calculated metrics for display
 */
export interface ItemMetrics {
  primaryCount: number;
  primaryCountLabel: string;
  secondaryCount?: number;
  secondaryCountLabel?: string;
  totalCost?: number;
  completionPercentage?: number;
  statusInfo?: {
    isInStock?: boolean;
    isLowStock?: boolean;
    lowStockCount?: number;
    outOfStockCount?: number;
  };
}

/**
 * Props for the UnifiedItemCard component
 */
export interface UnifiedItemCardProps {
  item: UnifiedItem;
  config: ItemConfig;
  onRefresh?: () => void;
  className?: string;
}

// ============================================================================
// Helper Functions
// ============================================================================

/**
 * Recursive component to render hierarchical parts with tree structure
 */
interface HierarchicalPartProps {
  part: {
    partId: string;
    partDetails?: {
      name: string;
      partNumber?: string;
      inventory?: { currentStock: number };
      costData?: { unitCost: number };
    };
    quantityRequired: number;
    unitOfMeasure?: string;
    children?: any[];
  };
  level: number;
  isLast: boolean;
  parentConnectors: boolean[];
}

const HierarchicalPart: React.FC<HierarchicalPartProps> = ({ part, level, isLast, parentConnectors }) => {
  const hasChildren = part.children && part.children.length > 0;

  return (
    <>
      <div className="flex items-start gap-1 text-sm">
        {/* Tree structure connectors */}
        <div className="flex items-center" style={{ minWidth: `${level * 16}px` }}>
          {parentConnectors.map((showConnector, index) => (
            <div key={index} className="w-4 flex justify-center">
              {showConnector && (
                <div className="w-px h-5 bg-muted-foreground/30"></div>
              )}
            </div>
          ))}
          {level > 0 && (
            <div className="w-4 h-5 flex items-center justify-start">
              <div className="flex">
                <div className={`w-2 border-l border-b border-muted-foreground/30 ${isLast ? 'h-2.5' : 'h-5'}`}></div>
                <div className="w-2 border-b border-muted-foreground/30"></div>
              </div>
            </div>
          )}
        </div>

        {/* Part content */}
        <div className="flex-1 min-w-0">
          <span className={cn(
            isPartUnavailable(part) ? "text-red-500" : "text-foreground"
          )}>
            {part.partDetails?.name || 'Unknown Part'}
          </span>
          {part.partDetails?.partNumber && (
            <span className={cn(
              "ml-1",
              isPartUnavailable(part) ? "text-red-400" : "text-muted-foreground"
            )}>
              ({part.partDetails.partNumber})
            </span>
          )}
          <span className={cn(
            "ml-1",
            isPartUnavailable(part) ? "text-red-400" : "text-muted-foreground"
          )}>
            × {part.quantityRequired}
          </span>
        </div>
      </div>

      {/* Render children recursively */}
      {hasChildren && part.children!.map((child, index) => (
        <HierarchicalPart
          key={index}
          part={child}
          level={level + 1}
          isLast={index === part.children!.length - 1}
          parentConnectors={[...parentConnectors, !isLast]}
        />
      ))}
    </>
  );
};

/**
 * Recursive component to render hierarchical assemblies for products
 */
interface HierarchicalAssemblyProps {
  assembly: {
    assemblyId: string;
    assemblyDetails?: {
      name: string;
      assemblyCode?: string;
      partsRequired?: any[];
    };
    quantityRequired: number;
    children?: any[];
  };
  level: number;
  isLast: boolean;
  parentConnectors: boolean[];
}

const HierarchicalAssembly: React.FC<HierarchicalAssemblyProps> = ({ assembly, level, isLast, parentConnectors }) => {
  const hasChildren = assembly.children && assembly.children.length > 0;

  return (
    <>
      <div className="flex items-start gap-1 text-sm">
        {/* Tree structure connectors */}
        <div className="flex items-center" style={{ minWidth: `${level * 16}px` }}>
          {parentConnectors.map((showConnector, index) => (
            <div key={index} className="w-4 flex justify-center">
              {showConnector && (
                <div className="w-px h-5 bg-muted-foreground/30"></div>
              )}
            </div>
          ))}
          {level > 0 && (
            <div className="w-4 h-5 flex items-center justify-start">
              <div className="flex">
                <div className={`w-2 border-l border-b border-muted-foreground/30 ${isLast ? 'h-2.5' : 'h-5'}`}></div>
                <div className="w-2 border-b border-muted-foreground/30"></div>
              </div>
            </div>
          )}
        </div>

        {/* Assembly content */}
        <div className="flex-1 min-w-0">
          <span className={cn(
            isAssemblyUnavailable(assembly.assemblyDetails) ? "text-red-500" : "text-foreground"
          )}>
            {assembly.assemblyDetails?.name || 'Unknown Assembly'}
          </span>
          {assembly.assemblyDetails?.assemblyCode && (
            <span className={cn(
              "ml-1",
              isAssemblyUnavailable(assembly.assemblyDetails) ? "text-red-400" : "text-muted-foreground"
            )}>
              ({assembly.assemblyDetails.assemblyCode})
            </span>
          )}
          <span className={cn(
            "ml-1",
            isAssemblyUnavailable(assembly.assemblyDetails) ? "text-red-400" : "text-muted-foreground"
          )}>
            × {assembly.quantityRequired}
          </span>
        </div>
      </div>

      {/* Render children recursively */}
      {hasChildren && assembly.children!.map((child, index) => (
        <HierarchicalAssembly
          key={index}
          assembly={child}
          level={level + 1}
          isLast={index === assembly.children!.length - 1}
          parentConnectors={[...parentConnectors, !isLast]}
        />
      ))}
    </>
  );
};

/**
 * Recursively count hierarchical parts
 */
const countHierarchicalParts = (parts: AssemblyItem['partsRequired'] = []): number => {
  return parts.reduce((count, part) => {
    let partCount = 1; // Count the current part
    if (part.children && part.children.length > 0) {
      partCount += countHierarchicalParts(part.children);
    }
    return count + partCount;
  }, 0);
};

/**
 * Recursively count hierarchical assemblies
 */
const countHierarchicalAssemblies = (assemblies: ProductItem['components'] = []): number => {
  return assemblies.reduce((count, assembly) => {
    let assemblyCount = 1;
    if (assembly.children && assembly.children.length > 0) {
      assemblyCount += countHierarchicalAssemblies(assembly.children);
    }
    return count + assemblyCount;
  }, 0);
};

/**
 * Check if a part is out of stock or has insufficient stock
 */
const isPartUnavailable = (part: any): boolean => {
  if (!part?.partDetails) return true;
  const quantityRequired = part.quantityRequired || 1;
  const inStock = part.partDetails.inventory?.currentStock || 0;
  return inStock < quantityRequired;
};

/**
 * Sort parts to show unavailable parts first, then available parts
 */
const sortPartsByAvailability = (parts: any[]): any[] => {
  const sortRecursive = (partsList: any[]): any[] => {
    const sorted = [...partsList].sort((a, b) => {
      const aUnavailable = isPartUnavailable(a);
      const bUnavailable = isPartUnavailable(b);

      // Unavailable parts come first
      if (aUnavailable && !bUnavailable) return -1;
      if (!aUnavailable && bUnavailable) return 1;
      return 0;
    });

    // Recursively sort children
    return sorted.map(part => ({
      ...part,
      children: part.children && part.children.length > 0
        ? sortRecursive(part.children)
        : part.children
    }));
  };

  return sortRecursive(parts);
};

/**
 * Calculate inventory status for assemblies (with hierarchical support)
 */
const calculateAssemblyInventoryStatus = (parts: AssemblyItem['partsRequired'] = []) => {
  const calculateRecursive = (partsList: any[]): any => {
    return partsList.reduce(
      (acc, part) => {
        if (!part?.partDetails) return acc;

        const quantityRequired = part.quantityRequired || 1;
        const inStock = part.partDetails.inventory?.currentStock || 0;

        acc.availableForAssembly += Math.min(inStock, quantityRequired);
        acc.totalRequired += quantityRequired;

        if (inStock === 0) {
          acc.outOfStockCount++;
        } else if (inStock < quantityRequired) {
          acc.lowStockCount++;
        }

        // Process children recursively
        if (part.children && part.children.length > 0) {
          const childResult = calculateRecursive(part.children);
          acc.availableForAssembly += childResult.availableForAssembly;
          acc.totalRequired += childResult.totalRequired;
          acc.outOfStockCount += childResult.outOfStockCount;
          acc.lowStockCount += childResult.lowStockCount;
        }

        return acc;
      },
      { availableForAssembly: 0, totalRequired: 0, lowStockCount: 0, outOfStockCount: 0 }
    );
  };

  const result = calculateRecursive(parts);

  return {
    ...result,
    isInStock: result.availableForAssembly >= result.totalRequired,
    isLowStock: result.lowStockCount > 0 || result.outOfStockCount > 0,
    percentage: result.totalRequired > 0 ? Math.round((result.availableForAssembly / result.totalRequired) * 100) : 0
  };
};

/**
 * Check if an assembly is unavailable (has any parts that are out of stock or insufficient)
 */
const isAssemblyUnavailable = (assembly: any): boolean => {
  if (!assembly?.partsRequired || !Array.isArray(assembly.partsRequired)) return true;

  const inventoryStatus = calculateAssemblyInventoryStatus(assembly.partsRequired);
  return !inventoryStatus.isInStock;
};

/**
 * Sort assemblies to show unavailable assemblies first, then available assemblies
 */
const sortAssembliesByAvailability = (assemblies: any[]): any[] => {
  return [...assemblies].sort((a, b) => {
    const aUnavailable = isAssemblyUnavailable(a);
    const bUnavailable = isAssemblyUnavailable(b);

    // Unavailable assemblies come first
    if (aUnavailable && !bUnavailable) return -1;
    if (!aUnavailable && bUnavailable) return 1;
    return 0;
  });
};

/**
 * Calculate inventory status for products (across Product → Assemblies → Parts hierarchy)
 */
const calculateProductInventoryStatus = (components: ProductItem['components'] = []) => {
  let totalAvailableForProduct = 0;
  let totalRequiredForProduct = 0;
  let totalOutOfStockAssemblies = 0;
  let totalLowStockAssemblies = 0;

  for (const component of components) {
    if (!component?.assemblyDetails?.partsRequired) continue;

    const quantityRequired = component.quantityRequired || 1;
    const assemblyInventoryStatus = calculateAssemblyInventoryStatus(component.assemblyDetails.partsRequired);

    // Calculate how many of this assembly can be made
    const availableAssemblies = assemblyInventoryStatus.totalRequired > 0
      ? Math.floor(assemblyInventoryStatus.availableForAssembly / assemblyInventoryStatus.totalRequired * quantityRequired)
      : quantityRequired;

    totalAvailableForProduct += Math.min(availableAssemblies, quantityRequired);
    totalRequiredForProduct += quantityRequired;

    if (!assemblyInventoryStatus.isInStock) {
      totalOutOfStockAssemblies++;
    } else if (assemblyInventoryStatus.isLowStock) {
      totalLowStockAssemblies++;
    }
  }

  return {
    availableForProduct: totalAvailableForProduct,
    totalRequired: totalRequiredForProduct,
    outOfStockCount: totalOutOfStockAssemblies,
    lowStockCount: totalLowStockAssemblies,
    isInStock: totalAvailableForProduct >= totalRequiredForProduct,
    isLowStock: totalLowStockAssemblies > 0 || totalOutOfStockAssemblies > 0,
    percentage: totalRequiredForProduct > 0 ? Math.round((totalAvailableForProduct / totalRequiredForProduct) * 100) : 0
  };
};



// ============================================================================
// Main Component
// ============================================================================

/**
 * Unified Item Card Component
 * Renders both assembly and product cards with consistent design and behavior
 */
export function UnifiedItemCard({ item, config, onRefresh, className }: UnifiedItemCardProps) {
  const { theme } = useTheme();

  // Calculate metrics based on item type
  const metrics = useMemo(() => config.calculateMetrics(item), [item, config]);

  // Get the code value based on configuration
  const itemCode = item[config.codeField as keyof UnifiedItem] as string;

  // Determine if item has valid data
  const hasValidData = useMemo(() => {
    if (config.type === 'assembly') {
      const assemblyItem = item as AssemblyItem;
      return Array.isArray(assemblyItem.partsRequired) && assemblyItem.partsRequired.length > 0;
    } else {
      const productItem = item as ProductItem;
      return Array.isArray(productItem.components) && productItem.components.length > 0;
    }
  }, [item, config.type]);

  return (
    <div className="group h-full">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        whileHover={{ y: -5, transition: { duration: 0.2 } }}
        className="h-full relative"
      >
        {/* Shine border effect with theme-based colors */}
        <div className={cn(
          "absolute -inset-0.5 rounded-xl opacity-0 group-hover:opacity-100 blur transition duration-1000 group-hover:duration-200",
          "bg-gradient-to-r theme-gradient-border"
        )} />
        
        <Card className={cn(
          "h-full relative overflow-hidden transition-all duration-300 border",
          "group-hover:shadow-lg group-hover:border-primary/20",
          "bg-card/95 hover:shadow-primary/5 backdrop-blur-sm",
          className
        )}
        onMouseMove={(e) => {
          const rect = e.currentTarget.getBoundingClientRect();
          const x = e.clientX - rect.left;
          const y = e.clientY - rect.top;
          e.currentTarget.style.setProperty('--x', `${x}px`);
          e.currentTarget.style.setProperty('--y', `${y}px`);
        }}>
          <CardHeader className="pb-3">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <motion.h3
                  className={cn(
                    "text-lg font-semibold line-clamp-1 mb-1",
                    theme.isLight ? "text-gray-800" : "theme-gradient-text"
                  )}
                >
                  {item.name}
                </motion.h3>
                <Badge variant="outline" className={cn("font-mono text-xs", theme.isLight ? "border-border bg-muted text-foreground" : "")}>
                  {itemCode}
                </Badge>
              </div>
              <div className="flex flex-col items-end gap-2">
                <config.statusBadgeComponent item={item} size="sm" />
                {/* Inventory Status for Assemblies */}
                {config.type === 'assembly' && hasValidData && (
                  <div className="text-right">
                    {(() => {
                      const assemblyItem = item as AssemblyItem;
                      const inventoryStatus = calculateAssemblyInventoryStatus(assemblyItem.partsRequired);

                      return (
                        <div className="flex flex-col items-end gap-1">
                          <div className="flex items-center gap-1">
                            {inventoryStatus.isInStock ? (
                              <CheckCircle size={14} className="text-green-500" />
                            ) : (
                              <XCircle size={14} className="text-red-500" />
                            )}
                            <span className={cn(
                              "text-xs font-medium",
                              inventoryStatus.isInStock ? "text-green-600" : "text-red-600"
                            )}>
                              {inventoryStatus.isInStock ? "All Parts Available" : `${inventoryStatus.outOfStockCount + inventoryStatus.lowStockCount} Parts Missing`}
                            </span>
                          </div>
                          {/* Progress Bar positioned between status text and percentage */}
                          <div className="flex items-center gap-2 w-full">
                            <div className="flex-1 bg-muted rounded-full h-1.5">
                              <div
                                className={cn(
                                  "h-1.5 rounded-full transition-all duration-300",
                                  inventoryStatus.percentage >= 100 ? "bg-green-500" :
                                  inventoryStatus.percentage >= 75 ? "bg-yellow-500" :
                                  inventoryStatus.percentage >= 50 ? "bg-orange-500" : "bg-red-500"
                                )}
                                style={{ width: `${inventoryStatus.percentage}%` }}
                              />
                            </div>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {inventoryStatus.percentage}% Available
                          </div>
                        </div>
                      );
                    })()}
                  </div>
                )}

                {/* Inventory Status for Products */}
                {config.type === 'product' && hasValidData && (
                  <div className="text-right">
                    {(() => {
                      const productItem = item as ProductItem;
                      const inventoryStatus = calculateProductInventoryStatus(productItem.components);

                      return (
                        <div className="flex flex-col items-end gap-1">
                          <div className="flex items-center gap-1">
                            {inventoryStatus.isInStock ? (
                              <CheckCircle size={14} className="text-green-500" />
                            ) : (
                              <XCircle size={14} className="text-red-500" />
                            )}
                            <span className={cn(
                              "text-xs font-medium",
                              inventoryStatus.isInStock ? "text-green-600" : "text-red-600"
                            )}>
                              {inventoryStatus.isInStock ? "All Assemblies Available" : `${inventoryStatus.outOfStockCount + inventoryStatus.lowStockCount} Assemblies Missing`}
                            </span>
                          </div>
                          {/* Progress Bar positioned between status text and percentage */}
                          <div className="flex items-center gap-2 w-full">
                            <div className="flex-1 bg-muted rounded-full h-1.5">
                              <div
                                className={cn(
                                  "h-1.5 rounded-full transition-all duration-300",
                                  inventoryStatus.percentage >= 100 ? "bg-green-500" :
                                  inventoryStatus.percentage >= 75 ? "bg-yellow-500" :
                                  inventoryStatus.percentage >= 50 ? "bg-orange-500" : "bg-red-500"
                                )}
                                style={{ width: `${inventoryStatus.percentage}%` }}
                              />
                            </div>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {inventoryStatus.percentage}% Available
                          </div>
                        </div>
                      );
                    })()}
                  </div>
                )}
              </div>
            </div>
          </CardHeader>

          <CardContent className="pb-3">
            {/* Parts Preview Section for Assemblies */}
            {config.type === 'assembly' && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-muted-foreground">
                    {metrics.primaryCountLabel}:
                  </span>
                  <span className="text-sm font-medium">{metrics.primaryCount}</span>
                </div>



                {hasValidData ? (
                  <div className="space-y-0.5">
                    {(() => {
                      const assemblyItem = item as AssemblyItem;
                      const parts = assemblyItem.partsRequired || [];

                      // Sort parts to show unavailable parts first
                      const sortedParts = sortPartsByAvailability(parts);

                      // Optimal display: Show up to 6 parts for better UI/UX (considering card height and readability)
                      const maxDisplayParts = 4;
                      let displayedCount = 0;
                      const partsToShow: any[] = [];

                      const collectParts = (partsList: any[], level: number = 0, parentConnectors: boolean[] = []): void => {
                        for (let i = 0; i < partsList.length; i++) {
                          const part = partsList[i];
                          if (displayedCount >= maxDisplayParts) break;

                          const isLastInGroup = i === partsList.length - 1;
                          partsToShow.push({
                            ...part,
                            level,
                            isLast: isLastInGroup,
                            parentConnectors: [...parentConnectors]
                          });
                          displayedCount++;

                          if (part.children && part.children.length > 0 && displayedCount < maxDisplayParts) {
                            // Add connector info for children: show vertical line if not last item
                            const newParentConnectors = [...parentConnectors, !isLastInGroup];
                            collectParts(part.children, level + 1, newParentConnectors);
                          }
                        }
                      };

                      collectParts(sortedParts);

                      // No need to mark last items again since we handle it during collection

                      const totalParts = countHierarchicalParts(parts);
                      const remainingCount = totalParts - displayedCount;

                      return (
                        <>
                          {partsToShow.map((part, index) => (
                            <HierarchicalPart
                              key={index}
                              part={part}
                              level={part.level}
                              isLast={part.isLast}
                              parentConnectors={part.parentConnectors || []}
                            />
                          ))}
                          {remainingCount > 0 && (
                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                              <span className="ml-1">+{remainingCount} more part{remainingCount !== 1 ? 's' : ''}</span>
                            </div>
                          )}
                        </>
                      );
                    })()}
                  </div>
                ) : (
                  <div className="flex items-center gap-2 py-2">
                    <Layers className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">No parts defined</span>
                  </div>
                )}
              </div>
            )}

            {/* Product Assemblies Section */}
            {config.type === 'product' && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-muted-foreground">
                    {metrics.primaryCountLabel}:
                  </span>
                  <span className="text-sm font-medium">{metrics.primaryCount}</span>
                </div>

                {metrics.totalCost !== undefined && metrics.totalCost > 0 && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Total Cost</span>
                    <span className="font-medium">${metrics.totalCost.toFixed(2)}</span>
                  </div>
                )}

                {hasValidData ? (
                  <div className="space-y-0.5">
                    {(() => {
                      const productItem = item as ProductItem;
                      const assemblies = productItem.components || [];

                      // Sort assemblies to show unavailable assemblies first
                      const sortedAssemblies = sortAssembliesByAvailability(assemblies);

                      // Optimal display: Show up to 6 assemblies for better UI/UX
                      const maxDisplayAssemblies = 6;
                      let displayedCount = 0;
                      const assembliesToShow: any[] = [];

                      const collectAssemblies = (assembliesList: any[], level: number = 0, parentConnectors: boolean[] = []): void => {
                        for (let i = 0; i < assembliesList.length; i++) {
                          const assembly = assembliesList[i];
                          if (displayedCount >= maxDisplayAssemblies) break;

                          const isLastInGroup = i === assembliesList.length - 1;
                          assembliesToShow.push({
                            ...assembly,
                            level,
                            isLast: isLastInGroup,
                            parentConnectors: [...parentConnectors]
                          });
                          displayedCount++;

                          if (assembly.children && assembly.children.length > 0 && displayedCount < maxDisplayAssemblies) {
                            // Add connector info for children: show vertical line if not last item
                            const newParentConnectors = [...parentConnectors, !isLastInGroup];
                            collectAssemblies(assembly.children, level + 1, newParentConnectors);
                          }
                        }
                      };

                      collectAssemblies(sortedAssemblies);

                      // No need to mark last items again since we handle it during collection

                      const totalAssemblies = countHierarchicalAssemblies(assemblies);
                      const remainingCount = totalAssemblies - displayedCount;

                      return (
                        <>
                          {assembliesToShow.map((assembly, index) => (
                            <HierarchicalAssembly
                              key={index}
                              assembly={assembly}
                              level={assembly.level}
                              isLast={assembly.isLast}
                              parentConnectors={assembly.parentConnectors || []}
                            />
                          ))}
                          {remainingCount > 0 && (
                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                              <span className="ml-1">+{remainingCount} more assembl{remainingCount !== 1 ? 'ies' : 'y'}</span>
                            </div>
                          )}
                        </>
                      );
                    })()}
                  </div>
                ) : (
                  <div className="flex items-center gap-2 py-2">
                    <Package className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">No assemblies defined</span>
                  </div>
                )}
              </div>
            )}
          </CardContent>

          <CardFooter className="pt-2 px-4 flex justify-between items-center border-t overflow-hidden relative">
            {/* Action buttons on the left */}
            <div className="flex gap-1 flex-shrink-0 relative z-20">
              {config.deleteActionComponent && (
                <config.deleteActionComponent
                  item={item}
                  variant="icon"
                  size="sm"
                  onSuccess={onRefresh || undefined}
                  className="relative z-20"
                />
              )}

              {config.duplicateActionComponent && (
                <config.duplicateActionComponent
                  item={item}
                  variant="icon"
                  size="sm"
                  onSuccess={onRefresh || undefined}
                  className="relative z-20"
                />
              )}

              {config.editActionComponent && (
                <config.editActionComponent
                  item={item}
                  variant="icon"
                  size="sm"
                  onSuccess={onRefresh || undefined}
                  className="relative z-20"
                />
              )}
            </div>

            {/* View Button */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <config.viewButtonComponent
                    item={item}
                    variant="default"
                    size="sm"
                    className="group/btn relative overflow-hidden z-10"
                  >
                    <span className="relative z-10 flex items-center">
                      View
                      <ChevronRight size={16} className="ml-1 relative z-10" />
                    </span>
                    <div className={cn(
                      "absolute inset-0 translate-x-[-100%] group-hover/btn:translate-x-[100%] transition-transform duration-300",
                      "bg-gradient-to-r theme-gradient-bg"
                    )}></div>
                  </config.viewButtonComponent>
                </TooltipTrigger>
                <TooltipContent>
                  View {config.type === 'assembly' ? 'Assembly' : 'Product'} Details
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
}
