import { NextRequest, NextResponse } from 'next/server';
import { 
  createLocation, 
  fetchLocations,
  CreateLocationDto,
  LocationQueryOptions
} from '@/app/services/location.service';
import { successResponse, errorResponse } from '@/app/lib/api-response';
import { logApiRequest } from '@/app/services/logging';
import withErrorHandling from '@/app/middlewares/withErrorHandling';

const ROUTE_PATH = '/api/locations';
const MAX_LIMIT = 100;

/**
 * GET /api/locations - Fetch locations with pagination and filtering
 */
async function handleGET(request: NextRequest) {
  const startTime = Date.now();
  const { searchParams } = new URL(request.url);

  logApiRequest('GET', ROUTE_PATH, {
    query: Object.fromEntries(searchParams.entries())
  });

  try {
    // Parse query parameters
    const page = Math.max(1, parseInt(searchParams.get('page') || '1'));
    const limit = Math.min(MAX_LIMIT, Math.max(1, parseInt(searchParams.get('limit') || '20')));
    const warehouseId = searchParams.get('warehouseId') || undefined;
    const isActive = searchParams.get('isActive') ? searchParams.get('isActive') === 'true' : undefined;
    const locationType = searchParams.get('locationType') || undefined;
    const sortField = searchParams.get('sortField') || 'name';
    const sortOrder = searchParams.get('sortOrder') === 'desc' ? -1 : 1;

    // Build search filter
    const filter: Record<string, any> = {};
    const search = searchParams.get('search');
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    const options: LocationQueryOptions = {
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter,
      ...(warehouseId && { warehouseId }),
      ...(isActive !== undefined && { isActive }),
      ...(locationType && { locationType })
    };

    const result = await fetchLocations(options);
    const duration = Date.now() - startTime;

    return successResponse(
      result.locations,
      'Locations fetched successfully',
      {
        duration,
        pagination: result.pagination
      }
    );

  } catch (error: any) {
    const duration = Date.now() - startTime;
    return errorResponse(
      'FETCH_LOCATIONS_ERROR',
      error.message || 'Failed to fetch locations',
      [{ duration }],
      500
    );
  }
}

/**
 * POST /api/locations - Create a new location
 */
async function handlePOST(request: NextRequest) {
  const startTime = Date.now();

  logApiRequest('POST', ROUTE_PATH);

  try {
    const body = await request.json();

    // Validate required fields
    const requiredFields = ['warehouseId', 'name', 'locationType'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return errorResponse("API_ERROR", `Missing required field: ${field}`, [{ duration: Date.now() - startTime  }], 400);
      }
    }

    // Validate locationType
    const validLocationTypes = [
      'Bin', 'Shelf', 'Floor Area', 'Staging', 
      'Production Area', 'Quality Control', 'Shipping', 'Receiving'
    ];
    if (!validLocationTypes.includes(body.locationType)) {
      return errorResponse(
        'INVALID_LOCATION_TYPE',
        `Invalid location type. Must be one of: ${validLocationTypes.join(', ')}`,
        [],
        400
      );
    }

    const locationData: CreateLocationDto = {
      warehouseId: body.warehouseId,
      name: body.name,
      description: body.description || null,
      locationType: body.locationType,
      capacity: body.capacity || null,
      isActive: body.isActive ?? true
    };

    const newLocation = await createLocation(locationData);
    const duration = Date.now() - startTime;

    return successResponse(
      newLocation,
      'Location created successfully',
      { duration }
    );

  } catch (error: any) {
    const duration = Date.now() - startTime;
    
    // Handle specific error types
    if (error.message.includes('already exists')) {
      return errorResponse("API_ERROR", error.message, [{ duration }], 409);
    }
    if (error.message.includes('not found')) {
      return errorResponse("API_ERROR", error.message, [{ duration }], 404);
    }
    if (error.message.includes('Invalid')) {
      return errorResponse("API_ERROR", error.message, [{ duration }], 400);
    }

    return errorResponse("API_ERROR", error.message || 'Failed to create location', [{ duration }], 500);
  }
}

// Apply error handling middleware
export const GET = withErrorHandling(handleGET, ROUTE_PATH);
export const POST = withErrorHandling(handlePOST, ROUTE_PATH);
