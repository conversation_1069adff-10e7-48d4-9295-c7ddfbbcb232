import { NextRequest, NextResponse } from 'next/server';
// Import service functions and error handler from the new category service
import { createCategory, getAllCategories, handleMongoDBError } from '@/app/services/category.service';

// Maximum allowed limit per request to prevent overloading
const MAX_LIMIT = 500;

/**
 * GET handler for fetching categories with pagination, sorting, and filtering
 * @param request - The incoming request
 * @returns JSON response with categories data
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/categories - Fetching categories');
    const url = new URL(request.url);

    // --- Parsing Query Parameters ---
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    let limit = parseInt(url.searchParams.get('limit') || '20', 10);
    limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

    const sortField = url.searchParams.get('sortField') || 'name'; // Default sort field
    const sortOrderParam = url.searchParams.get('sortOrder') || 'asc';
    const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

    // --- Parsing includeParts and partsLimit --- 
    const includePartsParam = url.searchParams.get('includeParts');
    const includeParts = includePartsParam === 'true';
    let partsLimit = parseInt(url.searchParams.get('partsLimit') || '10', 10);
    partsLimit = Math.min(partsLimit, 50); // Max 50 parts per category to prevent overload

    // --- Building Filter Object ---
    const filter: any = {};
    
    // Name filter
    const nameFilter = url.searchParams.get('name');
    if (nameFilter) {
      filter.name = new RegExp(nameFilter, 'i'); // Case-insensitive regex search
    }
    
    // Parent category filter
    const parentCategoryFilter = url.searchParams.get('parentCategory');
    if (parentCategoryFilter) {
      if (parentCategoryFilter === 'null') {
        // Filter for top-level categories (no parent)
        filter.parentCategory = null;
      } else {
        // Filter for categories with specific parent
        filter.parentCategory = parentCategoryFilter;
      }
    }

    // --- Prepare Options for Service Function ---
    const options = {
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter,
      includeParts, // Add includeParts to options
      partsLimit    // Add partsLimit to options
    };

    console.log(`[API] Calling getAllCategories service with options: ${JSON.stringify(options)}`);

    // --- Call Service Function ---
    const result = await getAllCategories(options);

    const duration = Date.now() - startTime;
    console.log(`[API] Service getAllCategories completed in ${duration}ms`);

    // --- Return Response ---
    return NextResponse.json({
      data: result?.categories,
      pagination: result?.pagination,
      error: null,
      meta: { duration }
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/categories (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * POST handler for adding a new category
 * @param request - The incoming request with category data
 * @returns JSON response with the newly created category
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] POST /api/categories - Creating new category');
    const categoryData = await request.json() as {
      name?: string;
      description?: string;
      parentCategory?: string;
      [key: string]: any;
    };

    // Basic validation
    if (!categoryData || typeof categoryData !== 'object') {
      return NextResponse.json({ data: null, error: 'Invalid category data provided' }, { status: 400 });
    }

    // Validate required fields based on the category schema
    if (!categoryData.name) {
      return NextResponse.json({ data: null, error: 'Missing required field: name' }, { status: 400 });
    }

    // Optional fields validation
    if (categoryData.parentCategory && typeof categoryData.parentCategory !== 'string') {
      return NextResponse.json({ data: null, error: 'Parent category must be a valid ID string' }, { status: 400 });
    }

    console.log(`[API] Calling createCategory service with data: ${JSON.stringify(categoryData)}`);

    // Create DTO object with only the required fields
    const createCategoryDto = {
      name: categoryData.name,
      description: categoryData.description || null,
      parentCategory: categoryData.parentCategory || null
    };

    // Call the createCategory service function
    const savedCategory = await createCategory(createCategoryDto);

    const duration = Date.now() - startTime;
    console.log(`[API] Service createCategory completed in ${duration}ms`);

    return NextResponse.json({ data: savedCategory, error: null, meta: { duration } }, { status: 201 }); // 201 Created

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in POST /api/categories (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
