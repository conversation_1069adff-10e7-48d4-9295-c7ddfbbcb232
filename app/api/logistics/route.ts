import { NextRequest, NextResponse } from 'next/server';
// Import service functions and error handler
import { addDelivery, fetchDeliveries, handleMongoDBError } from '@/app/services/mongodb';

// Maximum allowed limit per request to prevent overloading
const MAX_LIMIT = 500;

// Define the expected request body interface
interface DeliveryRequestBody {
  referenceType: 'purchase_order' | 'work_order' | 'transfer';
  referenceId: string;
  status: 'scheduled' | 'in_transit' | 'delivered' | 'cancelled';
  scheduledDate: Date;
  actualDate?: Date;
  carrier?: string;
  trackingNumber?: string;
  notes?: string;
}

/**
 * GET handler for fetching deliveries with pagination, sorting, and filtering
 * @param request - The incoming request
 * @returns JSON response with deliveries data
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/logistics - Fetching deliveries');
    const url = new URL(request.url);

    // --- Parsing Query Parameters ---
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    let limit = parseInt(url.searchParams.get('limit') || '20', 10);
    limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

    const sortField = url.searchParams.get('sortField') || 'scheduledDate'; // Default sort field
    const sortOrderParam = url.searchParams.get('sortOrder') || 'desc';
    const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

    // --- Building Filter Object ---
    const filter: any = {};
    
    // Status filter
    const statusFilter = url.searchParams.get('status');
    if (statusFilter) {
      filter.status = statusFilter;
    }
    
    // Reference type filter
    const referenceTypeFilter = url.searchParams.get('referenceType');
    if (referenceTypeFilter) {
      filter.referenceType = referenceTypeFilter;
    }
    
    // Date range filter for scheduledDate
    const startDate = url.searchParams.get('startDate');
    const endDate = url.searchParams.get('endDate');
    if (startDate || endDate) {
      filter.scheduledDate = {};
      if (startDate) {
        filter.scheduledDate.$gte = new Date(startDate);
      }
      if (endDate) {
        filter.scheduledDate.$lte = new Date(endDate);
      }
    }

    // --- Prepare Options for Service Function ---
    const options = {
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter,
    };

    console.log(`[API] Calling fetchDeliveries service with options: ${JSON.stringify(options)}`);

    // --- Call Service Function ---
    const result = await fetchDeliveries(options);

    const duration = Date.now() - startTime;
    console.log(`[API] Service fetchDeliveries completed in ${duration}ms`);

    // --- Return Response ---
    return NextResponse.json({
      data: result?.deliveries,
      pagination: result?.pagination,
      error: null,
      meta: { duration }
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/logistics (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * POST handler for adding a new delivery
 * @param request - The incoming request with delivery data
 * @returns JSON response with the newly created delivery
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] POST /api/logistics - Creating new delivery');
    const deliveryData = await request.json() as DeliveryRequestBody;

    // Basic validation
    if (!deliveryData || typeof deliveryData !== 'object') {
      return NextResponse.json({ data: null, error: 'Invalid delivery data provided' }, { status: 400 });
    }
    
    // Validate required fields based on the delivery schema
    if (!deliveryData.referenceType) {
      return NextResponse.json({ data: null, error: 'Missing required field: referenceType' }, { status: 400 });
    }
    
    if (!deliveryData.referenceId) {
      return NextResponse.json({ data: null, error: 'Missing required field: referenceId' }, { status: 400 });
    }
    
    if (!deliveryData.status) {
      return NextResponse.json({ data: null, error: 'Missing required field: status' }, { status: 400 });
    }
    
    if (!deliveryData.scheduledDate) {
      return NextResponse.json({ data: null, error: 'Missing required field: scheduledDate' }, { status: 400 });
    }

    console.log(`[API] Calling addDelivery service with data: ${JSON.stringify(deliveryData)}`);

    // Call the addDelivery service function
    const savedDelivery = await addDelivery(deliveryData);

    const duration = Date.now() - startTime;
    console.log(`[API] Service addDelivery completed in ${duration}ms`);

    return NextResponse.json({ data: savedDelivery, error: null, meta: { duration } }, { status: 201 }); // 201 Created

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in POST /api/logistics (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
