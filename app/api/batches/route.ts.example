import { NextRequest, NextResponse } from 'next/server';
// Import service functions and error handler
import { fetchBatches, createBatch, handleMongoDBError } from '@/app/services/mongodb';

// Maximum allowed limit per request to prevent overloading
const MAX_LIMIT = 500;

/**
 * GET handler for fetching batch tracking data with pagination, sorting, and filtering
 * @param request - The incoming request
 * @returns JSON response with batches data and pagination information
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/batches - Fetching batches');
    const url = new URL(request.url);

    // --- Parsing Query Parameters ---
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    let limit = parseInt(url.searchParams.get('limit') || '20', 10);
    limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

    const sortField = url.searchParams.get('sortField') || 'createdAt'; // Default sort field
    const sortOrderParam = url.searchParams.get('sortOrder') || 'desc';
    const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

    // --- Building Filter Object ---
    const filter: any = {};
    
    // Status filter
    const statusFilter = url.searchParams.get('status');
    if (statusFilter) {
      filter.status = statusFilter;
    }
    
    // Product ID filter
    const productIdFilter = url.searchParams.get('productId');
    if (productIdFilter) {
      filter.productId = productIdFilter;
    }
    
    // Batch number filter (case-insensitive regex search)
    const batchNumberFilter = url.searchParams.get('batchNumber');
    if (batchNumberFilter) {
      filter.batchNumber = new RegExp(batchNumberFilter, 'i');
    }

    // --- Prepare Options for Service Function ---
    const options = {
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter,
    };

    console.log(`[API] Calling fetchBatches service with options: ${JSON.stringify(options)}`);

    // --- Call Service Function ---
    const result = await fetchBatches(options);

    const duration = Date.now() - startTime;
    console.log(`[API] Service fetchBatches completed in ${duration}ms`);

    // --- Return Response ---
    return NextResponse.json({
      data: result?.batches,
      pagination: result?.pagination,
      error: null,
      meta: { duration }
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/batches (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * POST handler for creating a new batch
 * @param request - The incoming request with batch data
 * @returns JSON response with the newly created batch
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] POST /api/batches - Creating new batch');
    const batchData = await request.json();

    // Basic validation
    if (!batchData || typeof batchData !== 'object') {
      return NextResponse.json({ data: null, error: 'Invalid batch data provided' }, { status: 400 });
    }
    
    // Validate required fields based on the batch schema
    const requiredFields = ['batchNumber', 'productId', 'quantity'];
    const missingFields = requiredFields.filter(field => !batchData[field]);
    
    if (missingFields.length > 0) {
      return NextResponse.json(
        { data: null, error: `Missing required fields: ${missingFields.join(', ')}` },
        { status: 400 }
      );
    }

    console.log(`[API] Calling createBatch service with data: ${JSON.stringify(batchData)}`);

    // --- Call Service Function ---
    const newBatch = await createBatch(batchData);

    const duration = Date.now() - startTime;
    console.log(`[API] Service createBatch completed in ${duration}ms`);

    // --- Return Response ---
    return NextResponse.json(
      { data: newBatch, error: null, meta: { duration } },
      { status: 201 } // Created
    );

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in POST /api/batches (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}