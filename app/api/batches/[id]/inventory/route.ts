import { NextRequest, NextResponse } from 'next/server';
import { getBatchInventorySummary } from '@/app/services/batchInventory';
import { handleMongoDBError } from '@/app/services/mongodb';

interface RouteParams {
  id: string; // This 'id' from the route corresponds to the batch ID
}

/**
 * GET handler for fetching inventory summary for a specific batch
 * @param request - The incoming request
 * @param context - Route context containing the batch ID (Promise in Next.js 15)
 * @returns JSON response with the batch inventory summary
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<RouteParams> }
) {
  const startTime = Date.now();
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;
    console.log(`[API] GET /api/batches/${id}/inventory - Fetching batch inventory summary`);

    // Get batch inventory summary
    const summary = await getBatchInventorySummary(id);

    const duration = Date.now() - startTime;
    console.log(`[API] Batch inventory summary query completed in ${duration}ms`);

    return NextResponse.json({
      data: summary,
      error: null,
      meta: { duration }
    });
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/batches/[id]/inventory (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
