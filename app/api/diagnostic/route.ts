import connectToDatabase from '@/app/lib/mongodb';
import { Part } from '@/app/models';
import mongoose from 'mongoose';
import { NextRequest, NextResponse } from 'next/server';
import { env } from '../../utils/env';

/**
 * GET handler for comprehensive diagnostic information
 * This endpoint does NOT use withDatabase middleware to test raw environment access
 */
export async function GET(request: NextRequest) {
  const diagnosticData: any = {
    timestamp: new Date().toISOString(),
    environment: {
      nodeEnv: env.NODE_ENV,
      platform: typeof process !== 'undefined' ? process.platform : 'unknown',
      nodeVersion: typeof process !== 'undefined' ? process.version : 'unknown',
    },
    environmentVariables: {
      // Check all MongoDB-related environment variables
      MONGODB_URI: env.MONGODB_URI ? 'SET (hidden)' : 'NOT SET',
      MONGODB_URI_PROD: env.MONGODB_URI_PROD ? 'SET (hidden)' : 'NOT SET',
      MONGODB_URI_DEV: env.MONGODB_URI_DEV ? 'SET (hidden)' : 'NOT SET',
      MONGODB_DB_NAME: env.MONGODB_DB_NAME || 'NOT SET',
      // Show which URI would be used based on NODE_ENV
      selectedUri: (() => {
        if (env.NODE_ENV === 'production') {
          return env.MONGODB_URI_PROD || env.MONGODB_URI ? 'MONGODB_URI_PROD or MONGODB_URI' : 'NONE';
        } else {
          return env.MONGODB_URI_DEV || env.MONGODB_URI ? 'MONGODB_URI_DEV or MONGODB_URI' : 'NONE';
        }
      })(),
      // Show all environment variables that contain 'MONGODB'
      allMongodbVars: Object.keys(process.env).filter(key => key.includes('MONGODB')),
    },
    database: {
      connectionState: 'not_attempted',
      error: null,
      partsCount: 0,
      samplePart: null
    }
  };

  // Try to connect to database and get diagnostic info
  try {
    console.log("[API Diagnostic] Starting database connection test");

    // Try to connect to database
    const dbConnection = await connectToDatabase();
    console.log("[API Diagnostic] Database connection established");

    // Update connection state
    const connectionState = mongoose.connection.readyState;
    const connectionStateText = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting',
      99: 'uninitialized'
    }[connectionState] || 'unknown';

    diagnosticData.database.connectionState = connectionStateText;
    diagnosticData.database.host = mongoose.connection.host;
    diagnosticData.database.port = mongoose.connection.port;
    diagnosticData.database.name = mongoose.connection.name;

    // Try to query the parts collection
    try {
      const partsCount = await Part.countDocuments();
      console.log(`[API Diagnostic] Found ${partsCount} parts in database`);
      diagnosticData.database.partsCount = partsCount;

      // Get a sample part to verify we can read data
      const samplePart = await Part.findOne().lean();
      if (samplePart) {
        diagnosticData.database.samplePart = {
          id: samplePart._id.toString(),
          name: (samplePart as any).name || 'No name field',
          hasData: true
        };
      }
    } catch (queryError) {
      console.error("[API Diagnostic] Query error:", queryError);
      diagnosticData.database.queryError = queryError instanceof Error ? queryError.message : 'Unknown query error';
    }

  } catch (error) {
    console.error("[API Diagnostic] Connection error:", error);
    diagnosticData.database.error = error instanceof Error ? error.message : 'Unknown connection error';
    diagnosticData.database.connectionState = 'error';
  }

  // Always return 200 OK for diagnostic endpoint so we can see the data
  return NextResponse.json({
    success: diagnosticData.database.connectionState === 'connected',
    ...diagnosticData
  }, {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache, no-store, must-revalidate'
    }
  });
}
