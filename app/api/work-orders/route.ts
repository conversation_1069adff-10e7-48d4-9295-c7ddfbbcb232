import { NextRequest, NextResponse } from 'next/server';
// Import OPTIMIZED service functions and error handler
import { createWorkOrder, getAllWorkOrders, handleMongoDBError } from '@/app/services/workorder.service';

// Maximum allowed limit per request
const MAX_LIMIT = 500;

/**
 * GET handler for fetching work orders with pagination, sorting, and filtering
 * @param request - The incoming request
 * @returns JSON response with work orders data
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/work-orders - Fetching work orders');
    const url = new URL(request.url);

    // --- Parsing Query Parameters ---
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    let limit = parseInt(url.searchParams.get('limit') || '20', 10);
    limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

    // --- Parse Sort Parameters ---
    const sortField = url.searchParams.get('sortField') || 'createdAt';
    const sortOrder = url.searchParams.get('sortOrder') === 'asc' ? 1 : -1;
    const sort = { [sortField]: sortOrder };

    // --- Parse Filter Parameters ---
    const filter: any = {};
    
    // Status filter
    const status = url.searchParams.get('status');
    if (status && status !== 'all') {
      filter.status = status;
    }
    
    // Priority filter
    const priority = url.searchParams.get('priority');
    if (priority && priority !== 'all') {
      filter.priority = priority;
    }
    
    // Assigned to filter
    const assignedTo = url.searchParams.get('assignedTo');
    if (assignedTo) {
      filter.assignedTo = assignedTo;
    }
    
    // Search term (for woNumber or part ID)
    const searchTerm = url.searchParams.get('search');
    if (searchTerm) {
      const searchRegex = { $regex: searchTerm, $options: 'i' };
      filter.$or = [
        { woNumber: searchRegex },
        { partIdToManufacture: searchRegex }
      ];
    }

    // --- Prepare Options for Service Function ---
    const options = {
      page,
      limit,
      sort,
      filter,
    };

    console.log(`[API] Calling getAllWorkOrders service with options: ${JSON.stringify(options)}`);

    // --- Call Service Function ---
    const result: any = await getAllWorkOrders(options as any);
    const workOrders = result.data;
    const pagination = result.pagination;

    const duration = Date.now() - startTime;
    console.log(`[API] Service getAllWorkOrders completed in ${duration}ms`);

    // --- Return Response ---
    return NextResponse.json({
      data: workOrders,
      pagination,
      error: null,
      meta: { duration }
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/work-orders (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * POST handler for adding a new work order
 * @param request - The incoming request with work order data
 * @returns JSON response with the newly created work order
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] POST /api/work-orders - Creating new work order');
    const workOrderData = await request.json() as any;

    // Basic validation
    if (!workOrderData || typeof workOrderData !== 'object') {
      return NextResponse.json({ data: null, error: 'Invalid work order data provided' }, { status: 400 });
    }

    // Validate required fields
    if (!workOrderData.quantity) {
      return NextResponse.json({ data: null, error: 'Quantity is required' }, { status: 400 });
    }

    if (!workOrderData.assignedTo) {
      return NextResponse.json({ data: null, error: 'Assigned user is required' }, { status: 400 });
    }

    // Validate that at least one of assemblyId, partIdToManufacture, or productId is provided
    if (!workOrderData.assemblyId && !workOrderData.partIdToManufacture && !workOrderData.productId) {
      return NextResponse.json(
        { data: null, error: 'At least one of assemblyId, partIdToManufacture, or productId must be provided' },
        { status: 400 }
      );
    }

    console.log(`[API] Calling createWorkOrder service with data: ${JSON.stringify(workOrderData)}`);

    // Call the createWorkOrder service function
    const savedWorkOrder = await createWorkOrder(workOrderData);

    const duration = Date.now() - startTime;
    console.log(`[API] Service createWorkOrder completed in ${duration}ms`);

    return NextResponse.json({ data: savedWorkOrder, error: null, meta: { duration } }, { status: 201 }); // 201 Created

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in POST /api/work-orders (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
