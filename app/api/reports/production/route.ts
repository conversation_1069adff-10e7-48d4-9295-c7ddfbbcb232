import { NextRequest, NextResponse } from 'next/server';
import { generateProductionReport } from '@/app/services/reports';
import { successResponse, errorResponse } from '@/app/lib/api-response';

/**
 * GET handler for generating production report
 * @param request - The incoming request with query parameters
 * @returns JSON response with production report data
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/reports/production - Generating production report');
    const searchParams = request.nextUrl.searchParams;
    
    // Parse query parameters
    const statusFilter = searchParams.get('status') || null;
    
    // Parse date range if provided
    let dateRange = null;
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    
    if (startDate && endDate) {
      dateRange = { startDate, endDate };
    }
    
    // Generate the report with the specified options
    const reportData = await generateProductionReport({
      statusFilter,
      dateRange
    });
    
    const duration = Date.now() - startTime;
    console.log(`[API] Production report generated in ${duration}ms`);
    
    return successResponse(
      reportData,
      'Production report generated successfully',
      { duration }
    );
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/reports/production (${duration}ms):`, error);
    
    return errorResponse(
      'REPORT_ERROR',
      error instanceof Error ? error.message : 'An unknown error occurred',
      [],
      500
    );
  }
}
