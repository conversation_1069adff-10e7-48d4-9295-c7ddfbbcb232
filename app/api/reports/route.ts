import { NextRequest, NextResponse } from 'next/server';
import { getReportTypes } from '@/app/services/reports';
import { successResponse, errorResponse } from '@/app/lib/api-response';
import { logApiRequest, logError } from '@/app/services/logging';

/**
 * GET handler for fetching available report types
 * @param request - The incoming request
 * @returns JSON response with available report types
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    // Log API request
    await logApiRequest('GET', '/api/reports', null, true);

    // Get all available report types
    const reportTypes = getReportTypes();

    const duration = Date.now() - startTime;
    console.log(`[API] Reports list fetched in ${duration}ms`);

    return successResponse(
      reportTypes,
      'Available report types retrieved successfully',
      { duration }
    );
  } catch (error) {
    const duration = Date.now() - startTime;
    await logError('API', `Error in GET /api/reports (${duration}ms)`, error);

    return errorResponse(
      'REPORT_ERROR',
      error instanceof Error ? error.message : 'An unknown error occurred',
      [],
      500
    );
  }
}
