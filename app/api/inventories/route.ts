import { NextRequest, NextResponse } from 'next/server';
import { InventoriesService } from '../../services/inventories.service';
import { shouldUseV3Service, ServiceType } from '../../lib/migration-feature-flags';

/**
 * INVENTORIES API V3 - NEW DEDICATED COLLECTION ENDPOINT
 * 
 * This endpoint provides direct access to the new inventories collection
 * with enhanced filtering and querying capabilities.
 */

// Helper function to generate request ID
function generateRequestId(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// Request context extraction
function extractRequestContext(request: NextRequest) {
  const userId = request.headers.get('x-user-id') || undefined;
  const requestId = request.headers.get('x-request-id') || generateRequestId();
  
  return { userId, requestId };
}

// Enhanced error handling
function handleError(error: unknown, operation: string, context: any) {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  console.error(`[InventoriesAPI] ${operation} error:`, error);
  
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  return NextResponse.json(
    {
      success: false,
      error: isDevelopment ? errorMessage : 'Internal server error',
      operation,
      requestId: context.requestId,
      timestamp: new Date().toISOString()
    },
    { status: 500 }
  );
}

// Success response wrapper
function successResponse(data: any, metadata: any = {}) {
  return NextResponse.json({
    success: true,
    data,
    metadata: {
      ...metadata,
      timestamp: new Date().toISOString(),
      apiVersion: 'v3'
    }
  });
}

/**
 * GET /api/inventories - Query inventory records from the new inventories collection
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  const context = extractRequestContext(request);
  
  try {
    const url = new URL(request.url);
    
    // Extract query parameters
    const partId = url.searchParams.get('partId');
    const warehouseId = url.searchParams.get('warehouseId');
    const stockType = url.searchParams.get('stockType');
    const minQuantity = url.searchParams.get('minQuantity');
    const maxQuantity = url.searchParams.get('maxQuantity');
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const skip = parseInt(url.searchParams.get('skip') || '0');

    // Build filter object
    const filter: any = {};
    
    if (partId) {
      filter.partId = partId;
    }
    
    if (warehouseId) {
      filter.warehouseId = warehouseId;
    }
    
    if (stockType) {
      filter.stockType = stockType;
    }
    
    if (minQuantity || maxQuantity) {
      filter.quantity = {};
      if (minQuantity) {
        filter.quantity.$gte = parseInt(minQuantity);
      }
      if (maxQuantity) {
        filter.quantity.$lte = parseInt(maxQuantity);
      }
    }

    console.log('[InventoriesAPI] Querying inventories with filter:', filter);

    // Query the inventories collection
    const inventoryRecords = await InventoriesService.queryInventory(filter);
    
    // Apply pagination
    const paginatedRecords = inventoryRecords.slice(skip, skip + limit);
    
    const queryTime = Date.now() - startTime;
    
    return successResponse(paginatedRecords, {
      pagination: {
        total: inventoryRecords.length,
        limit,
        skip,
        hasMore: skip + limit < inventoryRecords.length,
        currentPage: Math.floor(skip / limit) + 1,
        totalPages: Math.ceil(inventoryRecords.length / limit)
      },
      performance: {
        queryTime: `${queryTime}ms`,
        recordsReturned: paginatedRecords.length,
        totalRecords: inventoryRecords.length
      },
      filter
    });

  } catch (error: unknown) {
    return handleError(error, 'queryInventories', context);
  }
}

/**
 * POST /api/inventories - Create or update inventory record
 */
export async function POST(request: NextRequest) {
  const context = extractRequestContext(request);
  
  try {
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = ['partId', 'warehouseId', 'stockType', 'quantity'];
    for (const field of requiredFields) {
      if (!body[field] && body[field] !== 0) {
        return NextResponse.json(
          { 
            success: false, 
            error: `Missing required field: ${field}`,
            requestId: context.requestId
          },
          { status: 400 }
        );
      }
    }

    // Validate stock type
    const validStockTypes = ['raw', 'hardening', 'grinding', 'finished', 'rejected'];
    if (!validStockTypes.includes(body.stockType)) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Invalid stock type. Must be one of: ${validStockTypes.join(', ')}`,
          requestId: context.requestId
        },
        { status: 400 }
      );
    }

    // Create or update inventory record
    const result = await InventoriesService.upsertInventory({
      partId: body.partId,
      warehouseId: body.warehouseId,
      stockType: body.stockType,
      quantity: body.quantity,
      safetyStockLevel: body.safetyStockLevel,
      maximumStockLevel: body.maximumStockLevel,
      averageDailyUsage: body.averageDailyUsage,
      abcClassification: body.abcClassification
    });

    return successResponse(result, {
      operation: 'createOrUpdateInventory',
      requestId: context.requestId
    });

  } catch (error: unknown) {
    return handleError(error, 'createOrUpdateInventory', context);
  }
}

/**
 * PUT /api/inventories - Bulk update inventory records
 */
export async function PUT(request: NextRequest) {
  const context = extractRequestContext(request);
  
  try {
    const body = await request.json();
    
    if (!Array.isArray(body.updates)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Request body must contain an "updates" array',
          requestId: context.requestId
        },
        { status: 400 }
      );
    }

    const results = [];
    
    for (const update of body.updates) {
      try {
        const result = await InventoriesService.upsertInventory(update);
        results.push({ success: true, result });
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        results.push({ success: false, error: errorMessage, update });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const errorCount = results.filter(r => !r.success).length;

    return successResponse(results, {
      operation: 'bulkUpdateInventory',
      summary: {
        total: results.length,
        successful: successCount,
        failed: errorCount
      },
      requestId: context.requestId
    });

  } catch (error: unknown) {
    return handleError(error, 'bulkUpdateInventory', context);
  }
}

/**
 * DELETE /api/inventories - Delete inventory records
 */
export async function DELETE(request: NextRequest) {
  const context = extractRequestContext(request);
  
  try {
    const url = new URL(request.url);
    const partId = url.searchParams.get('partId');
    const warehouseId = url.searchParams.get('warehouseId');
    const stockType = url.searchParams.get('stockType');

    if (!partId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'partId parameter is required',
          requestId: context.requestId
        },
        { status: 400 }
      );
    }

    // Build filter for deletion
    const filter: any = { partId };
    if (warehouseId) filter.warehouseId = warehouseId;
    if (stockType) filter.stockType = stockType;

    // Note: This would require implementing a delete method in InventoriesService
    // For now, return a placeholder response
    return NextResponse.json(
      {
        success: false,
        error: 'Delete operation not yet implemented',
        requestId: context.requestId,
        note: 'This endpoint will be implemented after migration completion'
      },
      { status: 501 }
    );

  } catch (error: unknown) {
    return handleError(error, 'deleteInventory', context);
  }
}
