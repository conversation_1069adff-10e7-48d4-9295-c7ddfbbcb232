import { NextRequest, NextResponse } from 'next/server';
import { searchSuppliers, handleMongoDBError } from '@/app/services/supplier.service';

// Maximum allowed limit per request to prevent overloading
const MAX_LIMIT = 500;

/**
 * GET handler for searching suppliers with pagination, sorting, and filtering
 * @param request - The incoming request
 * @returns JSON response with suppliers data
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/suppliers/search - Searching suppliers');
    const url = new URL(request.url);

    // Get search query
    const query = url.searchParams.get('query') || '';

    // Pagination parameters
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    let limit = parseInt(url.searchParams.get('limit') || '20', 10);
    limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

    // Sorting parameters
    const sortField = url.searchParams.get('sortField') || 'name'; // Default sort field
    const sortOrderParam = url.searchParams.get('sortOrder') || 'asc';
    const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

    // Additional filters
    const filter: any = {};
    
    // Filter by active status if provided
    const activeFilter = url.searchParams.get('is_active');
    if (activeFilter !== null) {
      filter.is_active = activeFilter === 'true';
    }

    // Filter by specialty if provided
    const specialty = url.searchParams.get('specialty');
    if (specialty) {
      filter.specialty = { $in: [specialty] };
    }

    // Filter by rating range if provided
    const minRating = url.searchParams.get('minRating');
    const maxRating = url.searchParams.get('maxRating');
    if (minRating || maxRating) {
      filter.rating = {};
      if (minRating) filter.rating.$gte = parseFloat(minRating);
      if (maxRating) filter.rating.$lte = parseFloat(maxRating);
    }

    // Prepare options for service function
    const options = {
      query,
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter,
    };

    console.log(`[API] Calling searchSuppliers service with options: ${JSON.stringify(options)}`);

    // Call service function
    const result = await searchSuppliers(options);

    const duration = Date.now() - startTime;
    console.log(`[API] Service searchSuppliers completed in ${duration}ms with ${result.suppliers.length} results`);

    // Return response
    return NextResponse.json({
      data: result.suppliers,
      pagination: result.pagination,
      error: null,
      meta: { duration, query }
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error in GET /api/suppliers/search (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
} 