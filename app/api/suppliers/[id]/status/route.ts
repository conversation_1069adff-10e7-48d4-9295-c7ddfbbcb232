import { handleMongoDBError, updateSupplierActiveStatus } from '@/app/services/supplier.service';
import { NextRequest, NextResponse } from 'next/server';

interface RouteParams {
  id: string; // This 'id' from the route corresponds to the supplier_id field
}

interface SupplierStatusRequestBody {
  is_active: boolean;
}

/**
 * PATCH handler for updating a supplier's active status
 * @param request - The incoming request with active status in the body
 * @param params - Route parameters including the supplier ID
 * @returns JSON response with updated supplier data or error
 */
export async function PATCH(
  request: NextRequest,
  context: { params: Promise<RouteParams> }
) {
  const startTime = Date.now();
  const { id: supplierId } = await context.params;
  try {
    console.log(`[API] PATCH /api/suppliers/${supplierId}/status - Updating supplier status`);
    
    const body = await request.json() as SupplierStatusRequestBody;
    
    // Validate the request body
    if (body.is_active === undefined || typeof body.is_active !== 'boolean') {
      return NextResponse.json(
        { data: null, error: 'is_active must be a boolean value', meta: { duration: Date.now() - startTime } },
        { status: 400 }
      );
    }
    
    const isActive = body.is_active;
    
    // Call the service function to update the supplier's active status
    const updatedSupplier = await updateSupplierActiveStatus(supplierId, isActive);
    
    const duration = Date.now() - startTime;
    
    if (!updatedSupplier) {
      console.log(`[API] Supplier ${supplierId} not found for status update (${duration}ms)`);
      return NextResponse.json(
        { data: null, error: `Supplier with ID ${supplierId} not found`, meta: { duration } },
        { status: 404 }
      );
    }
    
    console.log(`[API] Updated supplier ${supplierId} status to ${isActive} successfully (${duration}ms)`);
    return NextResponse.json({
      data: updatedSupplier,
      error: null,
      meta: { duration }
    });
    
  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error updating supplier ${supplierId} status (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
