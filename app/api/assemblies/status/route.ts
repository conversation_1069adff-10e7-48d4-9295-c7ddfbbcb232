import { NextRequest } from 'next/server';
import { getAssembliesByStatus, handleMongoDBError } from '@/app/services/assembly.service';
import { successResponse } from '@/app/lib/api-response';
import { InvalidParameterError } from '@/app/lib/errors';
import { logApiRequest } from '@/app/services/logging';
import withErrorHandling from '@/app/middlewares/withErrorHandling';

// Maximum allowed limit per request
const MAX_LIMIT = 100;
const ROUTE_PATH = '/api/assemblies/status';

/**
 * GET handler for filtering assemblies by status
 * @param request - The incoming request
 * @returns JSON response with filtered assemblies
 */
async function handleGET(request: NextRequest) {
  const startTime = Date.now();
  
  // Log API request
  await logApiRequest('GET', ROUTE_PATH, request.nextUrl.searchParams, true);
  
  console.log('[API] GET /api/assemblies/status - Filtering assemblies by status');
  const url = new URL(request.url);

  // Get status parameter
  const status = url.searchParams.get('status');
  
  if (!status) {
    throw new InvalidParameterError('Status parameter is required');
  }
  
  // Validate status
  const validStatuses = ['active', 'inactive', 'prototype', 'obsolete'];
  if (!validStatuses.includes(status)) {
    throw new InvalidParameterError(
      `Invalid status. Must be one of: ${validStatuses.join(', ')}`,
      [{ field: 'status', message: 'Invalid status value' }]
    );
  }
  
  // --- Pagination Parameters ---
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  let limit = parseInt(url.searchParams.get('limit') || '20', 10);
  limit = Math.min(limit, MAX_LIMIT); // Enforce max limit
  
  // --- Sorting Parameters ---
  const sortField = url.searchParams.get('sortField') || 'name'; // Default sort by name
  const sortOrderParam = url.searchParams.get('sortOrder') || 'asc';
  const sortOrder = sortOrderParam === 'asc' ? 1 : -1;
  
  // Additional filters
  const filter: any = {};
  
  // Type filter (standard, custom, kit)
  const type = url.searchParams.get('type');
  if (type) filter.type = type;
  
  // Assembly ID exact match
  const assemblyId = url.searchParams.get('assembly_id');
  if (assemblyId) filter.assembly_id = assemblyId;
  
  console.log(`[API] Filtering assemblies by status: ${status}`);
  
  // Prepare options
  const options = {
    page,
    limit,
    sort: { [sortField]: sortOrder },
    filter
  };
  
  // Get assemblies by status
  const result = await getAssembliesByStatus(status as 'active' | 'inactive' | 'prototype' | 'obsolete', options);
  
  const duration = Date.now() - startTime;
  console.log(`[API] Filter by status completed in ${duration}ms, found ${result.assemblies.length} results`);
  
  // Return results with pagination metadata
  return successResponse(
    result.assemblies,
    `Found ${result.pagination.totalCount} assemblies with status: ${status}`,
    {
      duration,
      pagination: result.pagination
    }
  );
}

// Apply the withErrorHandling middleware to our handler
export const GET = withErrorHandling(handleGET, ROUTE_PATH); 