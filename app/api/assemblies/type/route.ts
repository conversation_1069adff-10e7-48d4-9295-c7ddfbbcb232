import { NextRequest } from 'next/server';
import { getAssembliesByType, handleMongoDBError } from '@/app/services/assembly.service';
import { successResponse } from '@/app/lib/api-response';
import { InvalidParameterError } from '@/app/lib/errors';
import { logApiRequest } from '@/app/services/logging';
import withErrorHandling from '@/app/middlewares/withErrorHandling';

// Maximum allowed limit per request
const MAX_LIMIT = 100;
const ROUTE_PATH = '/api/assemblies/type';

/**
 * GET handler for filtering assemblies by type
 * @param request - The incoming request
 * @returns JSON response with filtered assemblies
 */
async function handleGET(request: NextRequest) {
  const startTime = Date.now();
  
  // Log API request
  await logApiRequest('GET', ROUTE_PATH, request.nextUrl.searchParams, true);
  
  console.log('[API] GET /api/assemblies/type - Filtering assemblies by type');
  const url = new URL(request.url);

  // Get type parameter
  const type = url.searchParams.get('type');
  
  if (!type) {
    throw new InvalidParameterError('Type parameter is required');
  }
  
  // Validate type
  const validTypes = ['standard', 'custom', 'kit'];
  if (!validTypes.includes(type)) {
    throw new InvalidParameterError(
      `Invalid type. Must be one of: ${validTypes.join(', ')}`,
      [{ field: 'type', message: 'Invalid type value' }]
    );
  }
  
  // --- Pagination Parameters ---
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  let limit = parseInt(url.searchParams.get('limit') || '20', 10);
  limit = Math.min(limit, MAX_LIMIT); // Enforce max limit
  
  // --- Sorting Parameters ---
  const sortField = url.searchParams.get('sortField') || 'name'; // Default sort by name
  const sortOrderParam = url.searchParams.get('sortOrder') || 'asc';
  const sortOrder = sortOrderParam === 'asc' ? 1 : -1;
  
  // Additional filters
  const filter: any = {};
  
  // Status filter
  const status = url.searchParams.get('status');
  if (status) filter.status = status;
  
  // Assembly ID exact match
  const assemblyId = url.searchParams.get('assembly_id');
  if (assemblyId) filter.assembly_id = assemblyId;
  
  console.log(`[API] Filtering assemblies by type: ${type}`);
  
  // Prepare options
  const options = {
    page,
    limit,
    sort: { [sortField]: sortOrder },
    filter
  };
  
  // Get assemblies by type
  const result = await getAssembliesByType(type as 'standard' | 'custom' | 'kit', options);
  
  const duration = Date.now() - startTime;
  console.log(`[API] Filter by type completed in ${duration}ms, found ${result.assemblies.length} results`);
  
  // Return results with pagination metadata
  return successResponse(
    result.assemblies,
    `Found ${result.pagination.totalCount} assemblies with type: ${type}`,
    {
      duration,
      pagination: result.pagination
    }
  );
}

// Apply the withErrorHandling middleware to our handler
export const GET = withErrorHandling(handleGET, ROUTE_PATH); 