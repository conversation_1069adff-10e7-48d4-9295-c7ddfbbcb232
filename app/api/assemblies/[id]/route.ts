import { errorResponse, successResponse } from '@/app/lib/api-response';
import withErrorHandling from '@/app/middlewares/withErrorHandling';
import {
    deleteAssembly,
    deleteAssemblyByAssemblyCode,
    getAssemblyByAssemblyCode,
    getAssemblyById,
    updateAssembly,
    updateAssemblyByAssemblyCode
} from '@/app/services/assembly.service';
import { logApiRequest } from '@/app/services/logging';
import mongoose from 'mongoose';
import { NextRequest } from 'next/server';

const ROUTE_PATH = '/api/assemblies/[id]';

/**
 * Helper function to extract the ID parameter from the request URL
 */
function extractIdFromRequest(request: NextRequest): string {
  const pathname = request.nextUrl.pathname;
  const segments = pathname.split('/');
  return segments[segments.length - 1] || '';
}

/**
 * GET handler for retrieving a specific assembly by ID
 */
async function handleGET(request: NextRequest) {
  const id = extractIdFromRequest(request);
  const startTime = Date.now();

  // Log API request
  await logApiRequest('GET', `${ROUTE_PATH} - ${id}`, null, true);
  
  console.log(`[API] GET /api/assemblies/${id} - Fetching assembly`);

  // Check if URL has includeParts parameter
  const url = new URL(request.url);
  const includeParts = url.searchParams.get('includeParts') === 'true';

  // Verify ID format - try as MongoDB ObjectId first
  if (mongoose.Types.ObjectId.isValid(id)) {
    const assembly = await getAssemblyById(id, includeParts);
    
    if (!assembly) {
      return errorResponse(`Assembly with ID ${id} not found`, '404');
    }
    
    const duration = Date.now() - startTime;
    console.log(`[API] Assembly retrieved by ObjectID in ${duration}ms`);
    
    return successResponse(assembly, 'Assembly retrieved successfully', { duration });
  } else {
    // If not a valid ObjectId, try as an assembly code
    console.log(`[API] ID ${id} is not a valid ObjectId, trying as assembly code`);
    const assembly = await getAssemblyByAssemblyCode(id, includeParts);
    
    if (!assembly) {
      return errorResponse(`Assembly with code ${id} not found`, '404');
    }
    
    const duration = Date.now() - startTime;
    console.log(`[API] Assembly retrieved by assemblyCode in ${duration}ms`);
    
    return successResponse(assembly, 'Assembly retrieved successfully', { duration });
  }
}

/**
 * PUT handler for updating a specific assembly by ID
 */
import { CanonicalUpdateAssemblyDto } from '@/app/services/assembly.service'; // Import the DTO

async function handlePUT(request: NextRequest) {
  const id = extractIdFromRequest(request);
  const startTime = Date.now();
  
  await logApiRequest('PUT', `${ROUTE_PATH} - ${id}`, null, true);
  
  console.log(`[API] PUT /api/assemblies/${id} - Updating assembly`);
  
  const requestBody = await request.json();
  
  // Basic validation for the request body structure
  if (!requestBody || typeof requestBody !== 'object' || Array.isArray(requestBody)) {
    return errorResponse('Invalid update data provided: request body is missing, not an object, or is an array.', '400');
  }

  // ⚠️ TEMPORARY AUTHENTICATION BYPASS ⚠️
  // TODO: Re-enable when authentication is implemented
  //
  // The following validation was temporarily disabled because authentication is not yet implemented.
  // When authentication is ready:
  // 1. Uncomment the validation below
  // 2. Ensure the frontend sends updatedBy field with authenticated user ID
  // 3. Test with actual user sessions
  //
  // if (!requestBody.updatedBy || typeof requestBody.updatedBy !== 'string') {
  //   return errorResponse('Invalid update data: `updatedBy` (string) is required.', '400');
  // }

  // Cast to CanonicalUpdateAssemblyDto. Detailed validation is handled by the service layer.
  const updateDto = requestBody as CanonicalUpdateAssemblyDto;

  // The service layer (updateAssembly/updateAssemblyByAssemblyCode) will perform detailed validation 
  // against CanonicalUpdateAssemblyDto properties (e.g., types, formats, business logic like part existence).
  // It also handles ensuring that assemblyCode is not changed through this DTO.
  
  let updatedAssembly = null;
  
  // Try as MongoDB ObjectId first
  if (mongoose.Types.ObjectId.isValid(id)) {
    console.log(`[API] Updating assembly using ObjectId: ${id}`);
    updatedAssembly = await updateAssembly(id, updateDto);
  } else {
    // If not a valid ObjectId, try as an assembly code
    console.log(`[API] ID ${id} is not a valid ObjectId, updating using assembly code`);
    updatedAssembly = await updateAssemblyByAssemblyCode(id, updateDto);
  }
  
  // The service functions now throw errors with statusCode if assembly not found or on validation failure.
  // The withErrorHandling middleware should catch these and format the response.
  // So, an explicit check for !updatedAssembly might be redundant if service always throws.
  // However, keeping it for robustness in case a service path could still return null on 'not found'.
  if (!updatedAssembly) {
    // This path should ideally not be hit if service throws correctly.
    return errorResponse(`Assembly with ID/code ${id} not found or update failed.`, '404');
  }
  
  const duration = Date.now() - startTime;
  console.log(`[API] Assembly update completed in ${duration}ms for ID/code: ${id}`);
  return successResponse(updatedAssembly, 'Assembly updated successfully', { duration });
}

/**
 * PATCH handler for updating a specific assembly by ID
 * This is an alias to the PUT handler for compatibility with frontend requests
 */
async function handlePATCH(request: NextRequest) {
  // Call the PUT handler implementation
  return handlePUT(request);
}

/**
 * DELETE handler for removing a specific assembly by ID
 */
async function handleDELETE(request: NextRequest) {
  const id = extractIdFromRequest(request);
  const startTime = Date.now();
  
  // Log API request
  await logApiRequest('DELETE', `${ROUTE_PATH} - ${id}`, null, true);
  
  console.log(`[API] DELETE /api/assemblies/${id} - Deleting assembly`);
  
  let result = false;
  
  // Try as MongoDB ObjectId first
  if (mongoose.Types.ObjectId.isValid(id)) {
    console.log(`[API] Deleting assembly using ObjectId: ${id}`);
    result = await deleteAssembly(id);
  } else {
    // If not a valid ObjectId, try as an assembly code
    console.log(`[API] ID ${id} is not a valid ObjectId, deleting using assembly code`);
    result = await deleteAssemblyByAssemblyCode(id);
  }
  
  if (!result) {
    return errorResponse(`Assembly with ID/code ${id} not found or cannot be deleted`, '404');
  }
  
  const duration = Date.now() - startTime;
  return successResponse(null, 'Assembly deleted successfully', { duration });
}

// Apply error handling middleware
export const GET = withErrorHandling(handleGET, ROUTE_PATH);
export const PUT = withErrorHandling(handlePUT, ROUTE_PATH);
export const PATCH = withErrorHandling(handlePATCH, ROUTE_PATH);
export const DELETE = withErrorHandling(handleDELETE, ROUTE_PATH);
