import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';
import connectToDatabase from '@/app/lib/mongodb';
import Part from '@/app/models/part.model';

export async function POST(request: NextRequest) {
  try {
    console.log('[API] POST /api/parts/seed - Seeding parts collection');
    
    // Connect to MongoDB
    await connectToDatabase();
    
    // Sample data based on the provided example
    const sampleData = {
      partNumber: "DL23.108", 
      name: "Spacer Ring", 
      description: "", 
      technicalSpecs: "", 
      isManufactured: true, 
      reorderLevel: 20, 
      status: "active", 
      inventory: { 
        currentStock: 169, 
        warehouseId: new mongoose.Types.ObjectId("65f000000000000000000001"), 
        safetyStockLevel: 10, 
        maximumStockLevel: 60, 
        averageDailyUsage: 0.5, 
        abcClassification: "A", 
        lastStockUpdate: new Date("2025-02-28T21:20:28.000Z")
      }, 
      supplierId: new mongoose.Types.ObjectId("681f796ad6a21248b8ec7600"), 
      unitOfMeasure: "pcs", 
      costPrice: 8.9, 
      categoryId: new mongoose.Types.ObjectId("65f000020000000000000001"), 
      createdAt: new Date("2025-02-28T21:20:28.000Z"), 
      updatedAt: new Date("2025-02-28T21:20:28.000Z")
    };

    // Check if the part already exists
    const existingPart = await Part.findOne({ partNumber: sampleData.partNumber });
    if (existingPart) {
      // Update existing part
      const updatedPart = await Part.findOneAndUpdate(
        { partNumber: sampleData.partNumber },
        sampleData,
        { new: true }
      );
      return NextResponse.json({
        data: updatedPart,
        message: 'Part updated successfully',
        success: true
      });
    }

    // Create new part
    const newPart = new Part(sampleData);
    await newPart.save();

    return NextResponse.json({
      data: newPart,
      message: 'Part created successfully',
      success: true
    });
  } catch (error: any) {
    console.error('[API] Error seeding parts:', error);
    return NextResponse.json(
      { data: null, error: error.message, success: false },
      { status: 500 }
    );
  }
}
