import { NextRequest, NextResponse } from 'next/server';
import { StockMovementService, StockMovementRequest } from '@/app/services/stockmovement.service';
import { handleMongoDBError } from '@/app/lib/mongodb';

/**
 * POST /api/inventory/move
 * 
 * Execute atomic stock movements using the new event-sourced transaction system.
 * This endpoint handles all types of inventory movements:
 * - Purchase receipts (external → raw stock)
 * - Internal transfers (between warehouses/stock types)
 * - Sales shipments (finished stock → external)
 * - Scrap disposal (rejected stock → scrap)
 * - Process moves (between manufacturing states)
 * - Manual adjustments
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    const data = await request.json() as StockMovementRequest;
    
    // Validate required fields
    if (!data.partId || !data.movementType || !data.quantity || !data.userId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields: partId, movementType, quantity, userId' 
        },
        { status: 400 }
      );
    }
    
    // Execute the atomic stock movement
    const result = await StockMovementService.executeMovement(data);
    
    const duration = Date.now() - startTime;
    
    return NextResponse.json({
      success: true,
      data: {
        transaction: result.transaction,
        updatedPart: result.updatedPart,
        message: result.message
      },
      meta: {
        duration,
        movementType: data.movementType,
        quantity: data.quantity
      }
    }, { status: 201 });
    
  } catch (error: any) {
    console.error('Error executing stock movement:', error);
    const errorMessage = handleMongoDBError(error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: errorMessage || 'Failed to execute stock movement',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/inventory/move
 * 
 * Get information about available movement types and their requirements.
 * This is a helper endpoint for frontend forms.
 */
export async function GET() {
  return NextResponse.json({
    success: true,
    data: {
      movementTypes: {
        purchase_receipt: {
          description: 'External purchase receipt',
          fromRequired: false,
          toRequired: true,
          defaultToStockType: 'raw',
          businessRule: 'External source → Raw stock'
        },
        sales_shipment: {
          description: 'Sales shipment to customer',
          fromRequired: true,
          toRequired: false,
          defaultFromStockType: 'finished',
          businessRule: 'Finished stock → External customer'
        },
        internal_transfer: {
          description: 'Transfer between warehouses or stock types',
          fromRequired: true,
          toRequired: true,
          businessRule: 'Internal location → Internal location'
        },
        scrap_disposal: {
          description: 'Dispose rejected parts as scrap',
          fromRequired: true,
          toRequired: true,
          defaultFromStockType: 'rejected',
          defaultToStockType: 'scrap',
          businessRule: 'Rejected stock → Scrap'
        },
        process_move: {
          description: 'Move parts between manufacturing processes',
          fromRequired: true,
          toRequired: true,
          businessRule: 'Manufacturing state → Manufacturing state'
        },
        adjustment: {
          description: 'Manual stock adjustment',
          fromRequired: false,
          toRequired: false,
          businessRule: 'Manual correction of stock levels'
        }
      },
      stockTypes: [
        { value: 'raw', label: 'Raw Materials' },
        { value: 'hardening', label: 'In Hardening Process' },
        { value: 'grinding', label: 'In Grinding Process' },
        { value: 'finished', label: 'Finished Goods' },
        { value: 'rejected', label: 'Rejected Parts' },
        { value: 'scrap', label: 'Scrap (Disposal)' }
      ]
    }
  });
}
