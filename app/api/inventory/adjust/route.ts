import { successResponse } from '@/app/lib/api-response';
import { InvalidParameterError } from '@/app/lib/errors';
import withErrorHandling from '@/app/middlewares/withErrorHandling';
import {
    adjustInventoryQuantity,
    getInventoryById
} from '@/app/services/inventory.service';
import { logApiRequest } from '@/app/services/logging';
import mongoose from 'mongoose';
import { NextRequest, NextResponse } from 'next/server';

const ROUTE_PATH = '/api/inventory/adjust';

/**
 * POST handler for adjusting inventory quantities
 * @param request - The incoming request with adjustment data
 * @returns JSON response with the updated inventory record
 */
async function handlePOST(request: NextRequest) {
  const startTime = Date.now();
  
  // Log API request
  await logApiRequest('POST', ROUTE_PATH, null, true);
  
  console.log('[API] POST /api/inventory/adjust - Adjusting inventory quantity');
  const adjustmentData = await request.json() as {
    id?: string;
    adjustment_quantity?: number;
    reason?: string;
    [key: string]: any;
  };

  // Basic validation
  if (!adjustmentData || typeof adjustmentData !== 'object') {
    throw new InvalidParameterError('Invalid adjustment data provided');
  }
  
  // Validate required fields
  const requiredFields = ['id', 'adjustment_quantity', 'reason'];
  const missingFields = requiredFields.filter(field => !(field in adjustmentData));
  if (missingFields.length > 0) {
    throw new InvalidParameterError(`Missing required fields: ${missingFields.join(', ')}`, 
      missingFields.map(field => ({ field, message: 'This field is required' }))
    );
  }
  
  // Validate ID format
  const { id } = adjustmentData;
  if (!id || !mongoose.Types.ObjectId.isValid(id)) {
    throw new InvalidParameterError(
      `Invalid inventory record ID format: ${id}`,
      [{ field: 'id', message: 'Invalid ID format' }]
    );
  }

  // Type assertion after validation
  const validId = id as string;
  
  // Check if inventory record exists
  const inventoryRecord = await getInventoryById(validId);
  if (!inventoryRecord) {
    return NextResponse.json(
      { data: null, error: `Inventory record with ID ${id} not found` },
      { status: 404 }
    );
  }
  
  // Validate adjustment_quantity is a number
  if (typeof adjustmentData.adjustment_quantity !== 'number') {
    throw new InvalidParameterError(
      'adjustment_quantity must be a number',
      [{ field: 'adjustment_quantity', message: 'Must be a number' }]
    );
  }

  console.log(`[API] Calling adjustInventoryQuantity service with data: ${JSON.stringify(adjustmentData)}`);

  // Call the service function
  const updatedInventory = await adjustInventoryQuantity(validId, {
    adjustment_quantity: adjustmentData.adjustment_quantity,
    reason: adjustmentData.reason as string,
    reference_number: adjustmentData.reference_number,
    notes: adjustmentData.notes
  });

  const duration = Date.now() - startTime;
  console.log(`[API] Service adjustInventoryQuantity completed in ${duration}ms`);

  if (!updatedInventory) {
    return NextResponse.json(
      { data: null, error: `Failed to adjust inventory record with ID ${id}` },
      { status: 500 }
    );
  }

  return successResponse(
    updatedInventory,
    'Inventory quantity adjusted successfully',
    { 
      duration,
      previous_quantity: inventoryRecord.quantity_on_hand,
      new_quantity: updatedInventory.quantity_on_hand,
      adjustment: adjustmentData.adjustment_quantity
    }
  );
}

// Apply the withErrorHandling middleware to our handler
export const POST = withErrorHandling(handlePOST, ROUTE_PATH); 