import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose'; // Added for session
import { handleMongoDBError } from '@/app/lib/mongodb';
import * as inventoryService from '@/app/services/inventory.service';
import * as partService from '@/app/services/part.service'; // Added
import * as inventoryTransactionService from '@/app/services/inventorytransaction.service';
import { InvalidParameterError } from '@/app/lib/errors'; // Added for validation
import { getTotalCurrentStock } from '@/app/utils/inventoryAggregation';

// Updated interface
interface StockUpdateRequest {
  itemId: string; // Renamed from partId for clarity
  itemType: 'Part' | 'Assembly' | 'Product'; // Added
  warehouseId: string;
  quantityChange: number;
  transactionType?: string; // Renamed from 'type'
  notes?: string;
  userId: string;
  referenceId?: string;
  referenceModel?: string;
}

/**
 * POST handler for updating stock levels
 * Uses transactional logic to ensure stock and transaction records are updated atomically
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const session = await mongoose.startSession();
  session.startTransaction();

  try {
    console.log('[API] POST /api/inventory/update-stock - Updating stock level');
    const data = await request.json() as StockUpdateRequest;

    // Validate required fields
    if (!data.itemId || !data.itemType || !data.warehouseId || data.quantityChange === undefined || !data.userId) {
      throw new InvalidParameterError('Missing required fields: itemId, itemType, warehouseId, quantityChange, userId are required.');
    }

    if (!['Part', 'Assembly', 'Product'].includes(data.itemType)) {
      throw new InvalidParameterError(`Invalid itemType: ${data.itemType}. Must be 'Part', 'Assembly', or 'Product'.`);
    }

    if (typeof data.quantityChange !== 'number') {
        throw new InvalidParameterError('quantityChange must be a number.');
    }

    const resolvedTransactionType = data.transactionType || (data.quantityChange > 0 ? 'stock_in_adjustment' : 'stock_out_adjustment');

    let updatedInventoryData;
    let newStockLevelForTransaction: number | undefined;

    if (data.itemType === 'Part') {
      updatedInventoryData = await partService.adjustStockLevelByDelta(
        data.itemId,
        data.warehouseId,
        data.quantityChange,
        { session } 
      );
      if (!updatedInventoryData) {
        throw new Error(`Failed to update stock for Part ID ${data.itemId}.`);
      }
      // Get current stock using v3 schema aggregation
      newStockLevelForTransaction = await getTotalCurrentStock(data.itemId);
    } else { // 'Assembly' or 'Product'
      updatedInventoryData = await inventoryService.updateStockLevel(
        data.itemId,
        data.itemType as 'Assembly' | 'Product',
        data.warehouseId,
        data.quantityChange,
        { session }
      );
      if (!updatedInventoryData || typeof updatedInventoryData.quantity_on_hand !== 'number') {
        throw new Error(`Failed to update stock for ${data.itemType} ID ${data.itemId} or retrieve new stock level.`);
      }
      newStockLevelForTransaction = updatedInventoryData.quantity_on_hand;
    }

    const previousStockLevel = newStockLevelForTransaction - data.quantityChange;

    const recordedTransaction = await inventoryTransactionService.recordTransaction(
      data.itemId,
      data.itemType as 'Part' | 'Assembly' | 'Product',
      data.warehouseId,
      resolvedTransactionType,
      data.quantityChange,
      previousStockLevel,
      data.userId,
      newStockLevelForTransaction,
      { session }
    );

    await session.commitTransaction();
    
    const duration = Date.now() - startTime;
    console.log(`[API] Stock update completed in ${duration}ms`);

    return NextResponse.json({
      success: true,
      data: {
        inventory: updatedInventoryData,
        transaction: recordedTransaction
      },
      meta: { duration }
    });

  } catch (error: any) {
    await session.abortTransaction();
    const duration = Date.now() - startTime;
    console.error(`[API] Error in POST /api/inventory/update-stock (${duration}ms):`, error);
    
    if (error instanceof InvalidParameterError) {
        return NextResponse.json(
            { success: false, error: error.message, details: error.details },
            { status: 400 }
        );
    }
    
    // Attempt to handle MongoDB specific errors, otherwise use generic error message
    let errorMessage = error.message || 'Failed to update stock';
    if (typeof handleMongoDBError === 'function' && error.name && (error.name.includes('Mongo') || error.code)) {
        errorMessage = handleMongoDBError(error) || errorMessage;
    }

    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: error.status || 500 }
    );
  } finally {
    session.endSession();
  }
} 