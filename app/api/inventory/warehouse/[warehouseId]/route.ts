import { NextRequest, NextResponse } from 'next/server';
import { 
  getInventoryByWarehouse,
  handleMongoDBError 
} from '@/app/services/inventory.service';
import mongoose from 'mongoose';
import { successResponse } from '@/app/lib/api-response';
import { logApiRequest } from '@/app/services/logging';
import withErrorHandling from '@/app/middlewares/withErrorHandling';
import { IInventoryLevel } from '@/app/models/inventory.model'; // Corrected import to use the interface

interface RouteParams {
  warehouseId: string;
}

const ROUTE_PATH = '/api/inventory/warehouse/[warehouseId]';

// Maximum allowed limit per request
const MAX_LIMIT = 100;

/**
 * GET handler for fetching all inventory in a specific warehouse
 * @param request - The incoming request
 * @param params - Route parameters including the warehouse ID
 * @returns JSON response with warehouse inventory data
 */
async function handleGET(
  request: NextRequest,
  { params }: { params: RouteParams }
) {
  const warehouseId = params.warehouseId;
  const startTime = Date.now();
  
  // Log API request
  await logApiRequest('GET', `${ROUTE_PATH.replace('[warehouseId]', warehouseId)}`, request.nextUrl.searchParams, true);
  
  console.log(`[API] GET /api/inventory/warehouse/${warehouseId} - Fetching warehouse inventory`);

  if (!mongoose.Types.ObjectId.isValid(warehouseId)) {
    return NextResponse.json(
      { data: null, error: `Invalid warehouse ID format: ${warehouseId}` },
      { status: 400 }
    );
  }

  // --- Parsing Query Parameters ---
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  let limit = parseInt(url.searchParams.get('limit') || '20', 10);
  limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

  const sortField = url.searchParams.get('sortField') || 'last_stock_update';
  const sortOrderParam = url.searchParams.get('sortOrder') || 'desc';
  const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

  // --- Building Filter Object ---
  const filter: any = {};
  
  // Filter by item type
  const itemTypeQueryParam = url.searchParams.get('itemType');

  if (itemTypeQueryParam === 'Part') {
    // If explicitly querying for Parts, block as Part inventory is embedded
    console.log(`[API] Attempted to GET 'Part' inventory via /api/inventory/warehouse/${warehouseId}?itemType=Part`);
    return successResponse(
      [], 
      `Querying Part inventory via this endpoint is not supported. Part inventory is embedded in Part documents. Please query parts directly and check their inventory.warehouseId.`, 
      {
        duration: Date.now() - startTime,
        pagination: { totalCount: 0, totalPages: 1, currentPage: page, limit }
      }
    );
  }
  
  // If itemTypeQueryParam is 'Assembly' or 'Product', apply it to the filter for inventory.service
  if (itemTypeQueryParam) {
    filter.item_type = itemTypeQueryParam;
  }
  // If itemTypeQueryParam is not provided, the filter.item_type remains unset,
  // so inventory.service will fetch all types. We will filter 'Part' types out later.

  // Filter by stock status
  const stockStatus = url.searchParams.get('stockStatus');
  if (stockStatus) filter.stockStatus = stockStatus;
  
  // Filter by location in warehouse
  const location = url.searchParams.get('location');
  if (location) filter.location_in_warehouse = new RegExp(location, 'i');
  
  // Quantity filters
  const minQuantity = url.searchParams.get('minQuantity');
  const maxQuantity = url.searchParams.get('maxQuantity');
  
  if (minQuantity !== null || maxQuantity !== null) {
    filter.quantity_on_hand = {};
    
    if (minQuantity !== null) {
      filter.quantity_on_hand.$gte = parseInt(minQuantity, 10);
    }
    
    if (maxQuantity !== null) {
      filter.quantity_on_hand.$lte = parseInt(maxQuantity, 10);
    }
  }
  
  // Low stock filter
  const lowStock = url.searchParams.get('lowStock') === 'true';
  if (lowStock) {
    filter.lowStock = true;
  }

  // --- Prepare options for service function ---
  const options = {
    page,
    limit,
    sort: { [sortField]: sortOrder },
    filter,
  };

  console.log(`[API] Calling getInventoryByWarehouse service with options: ${JSON.stringify(options)}`);

  let result = await getInventoryByWarehouse(warehouseId, options);
  const duration = Date.now() - startTime;
  console.log(`[API] Service getInventoryByWarehouse completed in ${duration}ms. Initial count: ${result.inventoryRecords.length}`);

  let finalInventoryRecords = result.inventoryRecords;
  let finalPagination = result.pagination;
  let responseMessage = `Inventory records for warehouse ${warehouseId} retrieved successfully`;

  // If no specific itemType was requested (meaning all types were fetched from inventory_levels),
  // filter out 'Part' records as they are legacy in this context.
  if (!itemTypeQueryParam) {
    const preFilterCount = result.inventoryRecords.length;
    finalInventoryRecords = result.inventoryRecords.filter((record: IInventoryLevel) => record.item_type !== 'Part');
    const postFilterCount = finalInventoryRecords.length;
    const partsFilteredOut = preFilterCount - postFilterCount;

    if (partsFilteredOut > 0) {
      console.log(`[API] Filtered out ${partsFilteredOut} 'Part' type records from warehouse ${warehouseId} results.`);
      responseMessage = `Inventory records for warehouse ${warehouseId} retrieved (excluding ${partsFilteredOut} 'Part' records, which are managed directly with Part entities).`;
      
      // Adjust pagination if records were removed
      // This is a simplified adjustment; true pagination would require re-querying or more complex logic
      // if the filtered items significantly change the page content of a paginated request.
      // For now, we adjust totalCount and assume the current page's content is what remains after filtering.
      const newTotalCount = result.pagination.totalCount - partsFilteredOut;
      finalPagination = {
        ...result.pagination,
        totalCount: newTotalCount > 0 ? newTotalCount : 0,
        // totalPages might need recalculation if we want it to be perfectly accurate:
        // totalPages: Math.ceil(newTotalCount / limit) 
        // However, this could be misleading if the current page becomes empty or sparsely populated.
        // For simplicity, we'll keep the original totalPages or adjust based on newTotalCount if it's simpler.
        totalPages: Math.ceil((newTotalCount > 0 ? newTotalCount : 0) / limit) || 1,
      };
    }
  }

  return successResponse(
    finalInventoryRecords,
    responseMessage,
    { 
      duration,
      pagination: finalPagination
    }
  );
}

// Apply the withErrorHandling middleware to our handler
export const GET = withErrorHandling(
  (request: NextRequest) => handleGET(request, { 
    params: { warehouseId: request.nextUrl.pathname.split('/').slice(-1)[0] || '' } 
  }),
  ROUTE_PATH
); 