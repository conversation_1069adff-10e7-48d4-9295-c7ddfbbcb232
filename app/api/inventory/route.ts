import { successResponse } from '@/app/lib/api-response';
import { InvalidParameterError } from '@/app/lib/errors';
import withErrorHandling from '@/app/middlewares/withErrorHandling';
import {
  createInventory,
  getAllInventory,
  getLowStockInventory,
  searchInventory
} from '@/app/services/inventory.service';
import { InventoriesService } from '@/app/services/inventories.service';
import { shouldUseV3Service, ServiceType } from '@/app/lib/migration-feature-flags';
import { logApiRequest } from '@/app/services/logging';
import { InventoryRequestBody } from '@/app/types/form.types';
import { Types } from 'mongoose';
import { NextRequest } from 'next/server';

// Helper function to generate request ID
function generateRequestId(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// Maximum allowed limit per request
const MAX_LIMIT = 100;
const ROUTE_PATH = '/api/inventory';

/**
 * GET handler for fetching inventory records with pagination, sorting, and filtering
 * @param request - The incoming request
 * @returns JSON response with inventory data
 */
async function handleGET(request: NextRequest) {
  const startTime = Date.now();

  // Log API request
  await logApiRequest('GET', ROUTE_PATH, request.nextUrl.searchParams, true);

  console.log('[API] GET /api/inventory - Fetching inventory records with dual-service architecture');
  const url = new URL(request.url);

  // Extract request context for dual-service architecture
  const userId = request.headers.get('x-user-id');
  const requestId = request.headers.get('x-request-id') || generateRequestId();
  const context = {
    ...(userId && { userId }),
    requestId
  };

  // --- Parsing Query Parameters ---
  const page = parseInt(url.searchParams.get('page') || '1', 10);
  let limit = parseInt(url.searchParams.get('limit') || '20', 10);
  limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

  const sortField = url.searchParams.get('sortField') || 'last_stock_update'; // Default sort by last update
  const sortOrderParam = url.searchParams.get('sortOrder') || 'desc';
  const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

  // --- Search and Filter Parameters ---
  const query = url.searchParams.get('query') || '';
  const itemType = url.searchParams.get('itemType');
  const warehouseId = url.searchParams.get('warehouseId');
  const lowStock = url.searchParams.get('lowStock') === 'true';
  const stockStatus = url.searchParams.get('stockStatus'); // out_of_stock, low_stock, in_stock, overstocked

  // --- Building Filter Object ---
  const filter: any = {};

  if (itemType) {
    if (itemType === 'Part') {
      // Prevent querying 'Part' inventory from this endpoint as it uses inventory_levels
      // which is considered legacy for Parts.
      // Part inventory is embedded and should be queried via part-specific endpoints or a dedicated service.
      return successResponse(
        [],
        'Querying Part inventory via this endpoint is not supported. Please use part-specific data sources.',
        {
          duration: Date.now() - startTime,
          pagination: { totalCount: 0, totalPages: 1, currentPage: page, limit }
        }
      );
    }
    filter.item_type = itemType;
  }
  if (warehouseId && Types.ObjectId.isValid(warehouseId)) {
    filter.warehouse_id = new Types.ObjectId(warehouseId);
  }

  // Add low stock filter
  if (lowStock) {
    filter.lowStock = true;
  }

  // Add stock status filter
  if (stockStatus) {
    filter.stockStatus = stockStatus;
  }

  // Quantity range filters
  const minQuantity = url.searchParams.get('minQuantity');
  const maxQuantity = url.searchParams.get('maxQuantity');

  if (minQuantity !== null || maxQuantity !== null) {
    filter.quantity_on_hand = {};

    if (minQuantity !== null) {
      filter.quantity_on_hand.$gte = parseInt(minQuantity, 10);
    }

    if (maxQuantity !== null) {
      filter.quantity_on_hand.$lte = parseInt(maxQuantity, 10);
    }
  }

  // --- Specialized Endpoints ---

  // Special endpoint for low stock inventory
  if (url.searchParams.get('lowStockOnly') === 'true') {
    console.log('[API] Retrieving low stock inventory...');

    const options = {
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter
    };

    const result = await getLowStockInventory(options);

    const duration = Date.now() - startTime;
    console.log(`[API] Low stock inventory retrieved in ${duration}ms`);

    return successResponse(
      result.inventoryRecords,
      'Low stock inventory retrieved successfully',
      {
        duration,
        pagination: result.pagination
      }
    );
  }

  // If search query is provided, use the search function
  if (query && query.trim().length > 0) {
    console.log(`[API] Searching inventory with query: ${query}`);

    const options = {
      query,
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter
    };

    const result = await searchInventory(options);

    const duration = Date.now() - startTime;
    console.log(`[API] Inventory search completed in ${duration}ms`);

    return successResponse(
      result.inventoryRecords,
      'Inventory search results retrieved successfully',
      {
        duration,
        pagination: result.pagination
      }
    );
  }

  // --- Standard Inventory Listing with Dual-Service Architecture ---
  const useV3 = shouldUseV3Service(ServiceType.INVENTORY_SERVICE, context);

  let result;
  if (useV3) {
    console.log(`[API] Using V3 InventoriesService for inventory listing`);

    // Convert filter parameters for V3 service
    const v3Filter: any = {};
    if (filter.warehouse_id) {
      v3Filter.warehouseId = filter.warehouse_id;
    }
    if (filter.stockStatus) {
      v3Filter.stockStatus = filter.stockStatus;
    }
    if (filter.quantity_on_hand) {
      v3Filter.quantity = filter.quantity_on_hand;
    }

    const inventoryRecords = await InventoriesService.queryInventory(v3Filter);

    result = {
      inventoryRecords: inventoryRecords.slice((page - 1) * limit, page * limit),
      pagination: {
        totalCount: inventoryRecords.length,
        page,
        limit,
        totalPages: Math.ceil(inventoryRecords.length / limit)
      }
    };
  } else {
    console.log(`[API] Using legacy inventory service`);
    const options = {
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter,
    };

    result = await getAllInventory(options);
  }

  const duration = Date.now() - startTime;
  console.log(`[API] Service getAllInventory completed in ${duration}ms`);

  return successResponse(
    result.inventoryRecords,
    'Inventory records retrieved successfully',
    {
      duration,
      pagination: result.pagination
    }
  );
}

/**
 * POST handler for creating a new inventory record
 * @param request - The incoming request with inventory data
 * @returns JSON response with the newly created inventory record
 */
async function handlePOST(request: NextRequest) {
  const startTime = Date.now();

  // Log API request
  await logApiRequest('POST', ROUTE_PATH, null, true);

  console.log('[API] POST /api/inventory - Creating new inventory record');
  const inventoryData = await request.json() as InventoryRequestBody;

  // Basic validation
  if (!inventoryData || typeof inventoryData !== 'object') {
    throw new InvalidParameterError('Invalid inventory data provided');
  }

  // Validate required fields
  const requiredFields = ['item_id', 'item_type', 'warehouse_id', 'quantity_on_hand'];
  const missingFields = requiredFields.filter(field => !(field in inventoryData));
  if (missingFields.length > 0) {
    throw new InvalidParameterError(`Missing required fields: ${missingFields.join(', ')}`,
      missingFields.map(field => ({ field, message: 'This field is required' }))
    );
  }

  // Validate item_type
  const validItemTypes = ['Part', 'Assembly', 'Product'];
  if (!validItemTypes.includes(inventoryData.item_type)) {
    throw new InvalidParameterError(
      `Invalid item_type. Must be one of: ${validItemTypes.join(', ')}`,
      [{ field: 'item_type', message: 'Invalid item type value' }]
    );
  }

  // Prevent creating 'Part' inventory through this general endpoint
  if (inventoryData.item_type === 'Part') {
    throw new InvalidParameterError(
      'Cannot create Part inventory via this endpoint. Part inventory is managed directly with the Part entity.',
      [{ field: 'item_type', message: 'Part inventory is managed with the Part entity.' }]
    );
  }

  console.log(`[API] Calling createInventory service with data: ${JSON.stringify(inventoryData)}`);

  // Call the service function
  const savedInventory = await createInventory(inventoryData);

  const duration = Date.now() - startTime;
  console.log(`[API] Service createInventory completed in ${duration}ms`);

  return successResponse(
    savedInventory,
    'Inventory record created successfully',
    { duration },
    201
  );
}

// Apply the withErrorHandling middleware to our handlers
export const GET = withErrorHandling(handleGET, ROUTE_PATH);
export const POST = withErrorHandling(handlePOST, ROUTE_PATH);
