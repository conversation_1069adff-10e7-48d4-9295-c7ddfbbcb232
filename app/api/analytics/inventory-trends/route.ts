import { NextRequest, NextResponse } from 'next/server';
import { generateInventoryTrends } from '@/app/services/analytics';
import { successResponse, errorResponse } from '@/app/lib/api-response';
import { logApiRequest, logError } from '@/app/services/logging';
import { standardizeAnalyticsResponse } from '@/app/lib/analytics-helpers';
import { 
  isBaseError, 
  InvalidParameterError, 
  DatabaseQueryError, 
  DataProcessingError, 
  AnalyticsError 
} from '@/app/lib/errors';
import withDatabase from '@/app/middlewares/withDatabase';

/**
 * GET handler for generating inventory trends data
 * @param request - The incoming request with query parameters
 * @returns JSON response with inventory trends data
 */
async function handleGET(request: NextRequest): Promise<NextResponse> {
  const startTime = Date.now();
  try {
    const searchParams = request.nextUrl.searchParams;
    
    // Parse query parameters for logging
    const params = {
      timeRange: searchParams.get('timeRange'),
      category: searchParams.get('category')
    };
    
    // Log API request
    await logApiRequest('GET', '/api/analytics/inventory-trends', params, true);
    
    // Parse query parameters
    const timeRange = searchParams.get('timeRange') || 'month';
    const categoryFilter = searchParams.get('category') || null;
    
    // Generate the report with the specified options
    const reportData = await generateInventoryTrends({
      timeRange,
      categoryFilter
    });
    
    // Standardize the response format to ensure consistent structure
    const standardizedData = standardizeAnalyticsResponse(reportData);
    
    const duration = Date.now() - startTime;
    
    return successResponse(
      standardizedData,
      'Inventory trends data generated successfully',
      { duration }
    );
  } catch (error) {
    const duration = Date.now() - startTime;
    await logError('API', `Error in GET /api/analytics/inventory-trends (${duration}ms)`, error);
    
    // Handle different types of errors with appropriate responses
    if (isBaseError(error)) {
      // Handle our custom error types
      return errorResponse(
        error.code,
        error.message,
        error.details,
        error.status
      );
    } else if (error instanceof Error) {
      // Handle standard Error objects
      return errorResponse(
        'ANALYTICS_ERROR',
        error.message || 'An unexpected error occurred',
        [],
        500
      );
    } else {
      // Handle unknown error types
      return errorResponse(
        'UNKNOWN_ERROR',
        'An unknown error occurred',
        [],
        500
      );
    }
  }
}

// Wrap the handler with the withDatabase middleware to ensure connection is established
export const GET = withDatabase(handleGET) as (request: NextRequest) => Promise<NextResponse>;
