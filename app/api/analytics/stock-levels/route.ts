import { NextRequest, NextResponse } from 'next/server';
import { generateStockLevels } from '@/app/services/analytics';
import { successResponse, errorResponse } from '@/app/lib/api-response';
import { logApiRequest, logError } from '@/app/services/logging';
import { standardizeAnalyticsResponse } from '@/app/lib/analytics-helpers';
import { InvalidParameterError, DatabaseQueryError, DataProcessingError, AnalyticsError } from '@/app/lib/errors';

/**
 * GET handler for generating stock levels data
 * @param request - The incoming request with query parameters
 * @returns JSON response with stock levels data
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    const searchParams = request.nextUrl.searchParams;
    
    // Parse and validate query parameters
    const categoryFilter = searchParams.get('category');
    
    // Validate category parameter if present
    if (categoryFilter !== null && typeof categoryFilter !== 'string') {
      const errorMessage = 'Invalid category parameter: must be a string';
      await logError('API', errorMessage, { category: categoryFilter });
      return errorResponse(
        'INVALID_PARAMETER',
        errorMessage,
        [{ parameter: 'category', value: categoryFilter, expectedType: 'string' }],
        400
      );
    }
    
    // Log API request with parameters
    const params = { category: categoryFilter };
    await logApiRequest('GET', '/api/analytics/stock-levels', params, true);
    
    try {
      // Generate the report with the specified options
      const reportData = await generateStockLevels({ categoryFilter });
      
      // Standardize the response format to ensure consistent structure
      const standardizedData = standardizeAnalyticsResponse(reportData);
      
      const duration = Date.now() - startTime;
      
      return successResponse(
        standardizedData,
        'Stock levels data generated successfully',
        { duration }
      );
    } catch (serviceError) {
      const duration = Date.now() - startTime;
      let errorCode = 'ANALYTICS_ERROR';
      let statusCode = 500;
      let message = 'An error occurred generating stock levels data';
      let details: any[] = [];
      
      // Handle specific error types
      if (serviceError instanceof InvalidParameterError) {
        errorCode = 'INVALID_PARAMETER';
        statusCode = 400;
        message = serviceError.message;
        details = serviceError.details || [];
      } else if (serviceError instanceof DatabaseQueryError) {
        errorCode = 'DATABASE_ERROR';
        statusCode = 503;
        message = 'Database error while generating stock levels data';
        details = serviceError.details || [];
      } else if (serviceError instanceof DataProcessingError) {
        errorCode = 'DATA_PROCESSING_ERROR';
        statusCode = 500;
        message = 'Error processing data for stock levels';
        details = serviceError.details || [];
      } else if (serviceError instanceof AnalyticsError) {
        message = serviceError.message;
        details = serviceError.details || [];
      }
      
      await logError('API', `Error in GET /api/analytics/stock-levels (${duration}ms): ${message}`, serviceError);
      
      return errorResponse(
        errorCode,
        message,
        details,
        statusCode
      );
    }
  } catch (error) {
    const duration = Date.now() - startTime;
    await logError('API', `Unhandled error in GET /api/analytics/stock-levels (${duration}ms)`, error);
    
    return errorResponse(
      'INTERNAL_SERVER_ERROR',
      error instanceof Error ? error.message : 'An unknown error occurred',
      [],
      500
    );
  }
}
