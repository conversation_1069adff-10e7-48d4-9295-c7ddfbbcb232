import { NextRequest, NextResponse } from 'next/server';
// Import service functions and error handler from the new product service
import {
  getProductById,
  getProductByProductId,
  updateProductById,
  updateProductByProductId,
  deleteProductById,
  deleteProductByProductId
} from '@/app/services/product.service';
import withErrorHandling from '@/app/middlewares/withErrorHandling';
import { successResponse } from '@/app/lib/api-response';
import { NotFoundError, InvalidParameterError } from '@/app/lib/errors';
import mongoose from 'mongoose';

interface RouteParams {
  id: string; // This 'id' from the route corresponds to either _id or product_id
}

const ROUTE_PATH = '/api/products/[id]'; // Define route path for logging

/**
 * GET handler for fetching a specific product by its ID or product_id
 * @param request - The incoming request
 * @param params - Route parameters including the product ID
 * @returns JSON response with product data or error
 */
async function handleGET(
  request: NextRequest,
  context?: { params: RouteParams }
) {
  if (!context || !context.params || !context.params.id) {
    throw new InvalidParameterError('Product ID parameter is missing.');
  }
  const id = context.params.id;

  // Check for includeAssembly query parameter
  const { searchParams } = new URL(request.url);
  const includeAssembly = searchParams.get('includeAssembly') === 'true';

  console.log(`[API] GET /api/products/${id} - Fetching product (includeAssembly: ${includeAssembly})`);

  let product;
  if (mongoose.Types.ObjectId.isValid(id)) {
    product = await getProductById(id, includeAssembly);
  }
  if (!product) {
    product = await getProductByProductId(id, includeAssembly);
  }

  if (!product) {
    throw new NotFoundError(`Product with ID ${id} not found`);
  }

  console.log(`[API] Fetched product ${id} successfully`);
  return successResponse(product, 'Product retrieved successfully');
}

/**
 * PUT handler for updating a specific product by its ID or product_id
 * @param request - The incoming request with updated product data
 * @param params - Route parameters including the product ID
 * @returns JSON response with updated product data or error
 */
async function handlePUT(
  request: NextRequest,
  context?: { params: RouteParams }
) {
  if (!context || !context.params || !context.params.id) {
    throw new InvalidParameterError('Product ID parameter is missing.');
  }
  const id = context.params.id;
  console.log(`[API] PUT /api/products/${id} - Updating product`);
  const updateData = await request.json();

  if (!updateData || typeof updateData !== 'object' || Object.keys(updateData).length === 0) {
    throw new InvalidParameterError('Update data is required and cannot be empty');
  }

  // Validate hierarchical components if provided in update
  if (updateData.components !== undefined) {
    if (updateData.components !== null && !Array.isArray(updateData.components)) {
      throw new InvalidParameterError('Components must be an array or null');
    }

    // If components array is provided, validate structure
    if (Array.isArray(updateData.components)) {
      const componentErrors: string[] = [];
      updateData.components.forEach((component: any, index: number) => {
        if (!component.assemblyId) {
          componentErrors.push(`Component ${index + 1}: assemblyId is required`);
        }
        if (typeof component.quantityRequired !== 'number' || component.quantityRequired <= 0) {
          componentErrors.push(`Component ${index + 1}: quantityRequired must be a positive number`);
        }
        if (component.children && !Array.isArray(component.children)) {
          componentErrors.push(`Component ${index + 1}: children must be an array`);
        }
      });

      if (componentErrors.length > 0) {
        throw new InvalidParameterError(`Invalid component structure: ${componentErrors.join(', ')}`);
      }
    }
  }

  let updatedProduct;
  if (mongoose.Types.ObjectId.isValid(id)) {
    updatedProduct = await updateProductById(id, updateData);
  }
  if (!updatedProduct) {
    updatedProduct = await updateProductByProductId(id, updateData);
  }

  if (!updatedProduct) {
    throw new NotFoundError(`Product with ID ${id} not found for update`);
  }

  console.log(`[API] Updated product ${id} successfully`);
  return successResponse(updatedProduct, 'Product updated successfully');
}

/**
 * DELETE handler for removing a specific product by its ID or product_id
 * @param _request - The incoming request (unused)
 * @param params - Route parameters including the product ID
 * @returns JSON response indicating success or failure
 */
async function handleDELETE(
  _request: NextRequest,
  context?: { params: RouteParams }
) {
  if (!context || !context.params || !context.params.id) {
    throw new InvalidParameterError('Product ID parameter is missing.');
  }
  const id = context.params.id;
  console.log(`[API] DELETE /api/products/${id} - Deleting product`);

  let result;
  if (mongoose.Types.ObjectId.isValid(id)) {
    result = await deleteProductById(id);
  }
  if (!result) { // This check might be redundant if service throws NotFoundError
    result = await deleteProductByProductId(id);
  }

  // Assuming service throws NotFoundError if product doesn't exist to be deleted.
  // If result is null/false from service and no error thrown, it means not found.
  if (!result) { 
      throw new NotFoundError(`Product with ID ${id} not found for deletion`);
  }

  console.log(`[API] Deleted product ${id} successfully`);
  // For DELETE, typically a 204 No Content or a 200 with a success message is returned.
  // successResponse by default returns 200.
  return successResponse(null, 'Product deleted successfully'); 
}

// Apply the withErrorHandling middleware to our handlers
export const GET = withErrorHandling(handleGET, ROUTE_PATH);
export const PUT = withErrorHandling(handlePUT, ROUTE_PATH);
export const DELETE = withErrorHandling(handleDELETE, ROUTE_PATH);
