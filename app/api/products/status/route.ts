import { NextRequest, NextResponse } from 'next/server';
import { getProductsByStatus } from '@/app/services/product.service';
import withErrorHandling from '@/app/middlewares/withErrorHandling';
import { successResponse } from '@/app/lib/api-response';
import { InvalidParameterError } from '@/app/lib/errors';

// Maximum allowed limit per request to prevent overloading
const MAX_LIMIT = 500;
const ROUTE_PATH = '/api/products/status';

/**
 * GET handler for fetching products by status
 * @param request - The incoming request
 * @returns JSON response with products data
 */
async function handleGET(request: NextRequest) {
    console.log('[API] GET /api/products/status - Fetching products by status');
    const url = new URL(request.url);

    // Get status (required)
    const status = url.searchParams.get('status');
    if (!status) {
      throw new InvalidParameterError('Status parameter is required');
    }

    // Validate status value (must match Product schema enum)
    const validStatuses = ['active', 'discontinued', 'in_development'];
    if (!validStatuses.includes(status)) {
      throw new InvalidParameterError(`Invalid status value. Must be one of: ${validStatuses.join(', ')}`);
    }

    // Pagination parameters
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    let limit = parseInt(url.searchParams.get('limit') || '20', 10);
    limit = Math.min(limit, MAX_LIMIT); // Enforce max limit

    // Sorting parameters
    const sortField = url.searchParams.get('sortField') || 'name'; // Default sort by name
    const sortOrderParam = url.searchParams.get('sortOrder') || 'asc';
    const sortOrder = sortOrderParam === 'asc' ? 1 : -1;

    // Additional filters
    const filter: any = {};
    
    // Category filter
    const categoryId = url.searchParams.get('categoryId') || url.searchParams.get('category_id');
    if (categoryId) {
      filter.categoryId = categoryId;
    }
    
    // Price range filter (using correct field name from schema)
    const minPrice = url.searchParams.get('minPrice');
    const maxPrice = url.searchParams.get('maxPrice');
    if (minPrice || maxPrice) {
      filter.sellingPrice = {};
      if (minPrice) filter.sellingPrice.$gte = parseFloat(minPrice);
      if (maxPrice) filter.sellingPrice.$lte = parseFloat(maxPrice);
    }

    // Prepare options for service function
    const options = {
      page,
      limit,
      sort: { [sortField]: sortOrder },
      filter,
    };

    console.log(`[API] Calling getProductsByStatus service with status: ${status}, options: ${JSON.stringify(options)}`);

    // Call service function with the validated status
    const serviceStartTime = Date.now();
    const result = await getProductsByStatus(status as 'active' | 'discontinued' | 'in_development', options);
    const serviceDuration = Date.now() - serviceStartTime;

    console.log(`[API] Service getProductsByStatus completed in ${serviceDuration}ms with ${result.products.length} results`);

    // Return response
    return successResponse(
      result.products,
      'Products by status retrieved successfully',
      { pagination: result.pagination, status, duration: serviceDuration }
    );
}

export const GET = withErrorHandling(handleGET, ROUTE_PATH);