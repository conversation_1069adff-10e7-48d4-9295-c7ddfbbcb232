import { checkDatabaseHealth, getConnectionState } from '@/app/lib/mongodb';
import withDatabase from '@/app/middlewares/withDatabase';
import { NextRequest, NextResponse } from 'next/server';
import pkg from '../../../package.json';
import { env } from '../../utils/env';

/**
 * GET handler for retrieving application status information
 * @param request - The incoming request
 * @returns JSON response with status information
 */
async function handleGET(request: NextRequest) {
  try {
    // Check database health
    const dbHealth = await checkDatabaseHealth();
    
    // Get connection state
    const connectionState = getConnectionState();
    
    // Get application version from package.json
    const appVersion = pkg.version || 'unknown';
    
    // Prepare status response
    const status = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      application: {
        name: pkg.name || 'trend-ims',
        version: appVersion,
        environment: env.NODE_ENV || 'development',
        uptime: typeof process !== 'undefined' ? process.uptime() : 0
      },
      database: {
        status: dbHealth.status,
        message: dbHealth.message,
        connectionState: connectionState.state,
        readyState: connectionState.readyState
      }
    };
    
    return NextResponse.json(status);
  } catch (error) {
    console.error('[API] Error in GET /api/status:', 
      error instanceof Error ? error.message : String(error));
    
    return NextResponse.json(
      { 
        status: 'error', 
        message: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// Wrap the handler with the withDatabase middleware to ensure connection is established
export const GET = withDatabase(handleGET); 