import connectToDatabase, { handleMongoDBError } from '@/app/lib/mongodb';
import InventoryTransaction from '@/app/models/inventorytransaction.model';
import { IPart as IPartModel } from '@/app/models/part.model';
import mongoose from 'mongoose';
import { NextRequest, NextResponse } from 'next/server';
import { adjustStockLevelByDelta, getPartById } from '../../services/part.service'; // Added getPartById, changed updateStockLevel to adjustStockLevelByDelta
import { generateTransactionId } from '../../services/transactionIdGenerator';

// Maximum allowed limit per request to prevent overloading
const MAX_LIMIT = 500;

// Define interface for the transaction creation request
interface CreateTransactionRequest {
  // Updated to match database schema
  partId: string; // Changed from itemId to match database schema
  itemType?: 'Part' | 'Assembly' | 'Product'; // Made optional for backward compatibility
  warehouseId: string;
  transactionType?: 'stock_in_purchase' | 'stock_out_production' | 'adjustment_cycle_count' | 'stock_in_production' | 'transfer_out' | 'transfer_in' | 'sales_shipment';
  quantity?: number;
  transactionDate?: Date;
  referenceNumber?: string;
  referenceType?: 'PurchaseOrder' | 'WorkOrder' | 'SalesOrder' | 'StockAdjustment';
  userId: string;
  notes?: string;
}

/**
 * GET handler for inventory transactions
 * Fetches a list of inventory transactions with pagination and filtering options
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  try {
    console.log('[API] GET /api/inventory-transactions - Fetching transactions');
    await connectToDatabase();

    // Parse query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '10', 10), MAX_LIMIT);
    const skip = (page - 1) * limit;

    // Build filter object
    const filter: any = {};

    // Filter by part ID if provided
    const partId = url.searchParams.get('partId');
    if (partId) {
      try {
        filter.partId = new mongoose.Types.ObjectId(partId);
      } catch (err) {
        console.error('Invalid partId format', err);
        return NextResponse.json(
          { success: false, error: 'Invalid partId format' },
          { status: 400 }
        );
      }
    }

    const itemType = url.searchParams.get('itemType');
    if (itemType) {
      if (!['Part', 'Assembly', 'Product'].includes(itemType)) {
        return NextResponse.json(
          { success: false, error: 'Invalid itemType format' },
          { status: 400 }
        );
      }
      filter.itemType = itemType;
    }

    // Filter by warehouse ID if provided
    const warehouseId = url.searchParams.get('warehouseId');
    if (warehouseId) {
      try {
        filter.warehouseId = new mongoose.Types.ObjectId(warehouseId);
      } catch (err) {
        console.error('Invalid warehouseId format', err);
        return NextResponse.json(
          { success: false, error: 'Invalid warehouseId format' },
          { status: 400 }
        );
      }
    }

    // Filter by transaction type if provided
    const transactionType = url.searchParams.get('transactionType');
    if (transactionType) {
      filter.transactionType = transactionType;
    }

    // Handle search parameter for text search across transactions
    const searchTerm = url.searchParams.get('search');
    let useAggregation = false;

    if (searchTerm && searchTerm.trim()) {
      useAggregation = true; // We need aggregation to search in populated fields
    }

    // Filter by date range if provided
    const startDateParam = url.searchParams.get('startDate');
    const endDateParam = url.searchParams.get('endDate');

    if (startDateParam && endDateParam) {
      filter.transactionDate = {
        $gte: new Date(startDateParam),
        $lte: new Date(endDateParam)
      };
    } else if (startDateParam) {
      filter.transactionDate = { $gte: new Date(startDateParam) };
    } else if (endDateParam) {
      filter.transactionDate = { $lte: new Date(endDateParam) };
    } else {
      // If no specific date range is provided, check for period filter
      const period = url.searchParams.get('period');
      let startDate: Date | null = null;
      const now = new Date();

      switch (period) {
        case 'day':
          startDate = new Date(now.setHours(0, 0, 0, 0));
          break;
        case 'week':
          startDate = new Date(now.setDate(now.getDate() - 7));
          break;
        case 'month':
          startDate = new Date(now.setMonth(now.getMonth() - 1));
          break;
        default:
          startDate = null;
      }

      if (startDate) {
        filter.transactionDate = { $gte: startDate };
      }
    }

    let transactions, total;

    if (useAggregation) {
      // Use aggregation pipeline for search functionality
      const searchRegex = new RegExp(searchTerm!.trim(), 'i');

      const pipeline = [
        // First, do the lookups to get related data
        {
          $lookup: {
            from: 'parts',
            localField: 'partId',
            foreignField: '_id',
            as: 'partData'
          }
        },
        {
          $lookup: {
            from: 'warehouses',
            localField: 'warehouseId',
            foreignField: '_id',
            as: 'warehouseData'
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userData'
          }
        },
        {
          $addFields: {
            partData: { $arrayElemAt: ['$partData', 0] },
            warehouseData: { $arrayElemAt: ['$warehouseData', 0] },
            userData: { $arrayElemAt: ['$userData', 0] }
          }
        },
        // Apply base filters and search
        {
          $match: {
            ...filter,
            $or: [
              { notes: searchRegex },
              { referenceNumber: searchRegex },
              { transactionType: searchRegex },
              { 'partData.partNumber': searchRegex },
              { 'partData.name': searchRegex },
              { 'warehouseData.name': searchRegex },
              { 'warehouseData.location_id': searchRegex },
              { 'userData.username': searchRegex },
              { 'userData.first_name': searchRegex },
              { 'userData.last_name': searchRegex }
            ]
          }
        },
        // Transform the data to match the populate format
        {
          $addFields: {
            partId: {
              $cond: {
                if: { $ne: ['$partData', null] },
                then: {
                  _id: '$partData._id',
                  partNumber: '$partData.partNumber',
                  name: '$partData.name',
                  description: '$partData.description'
                },
                else: '$partId'
              }
            },
            warehouseId: {
              $cond: {
                if: { $ne: ['$warehouseData', null] },
                then: {
                  _id: '$warehouseData._id',
                  location_id: '$warehouseData.location_id',
                  name: '$warehouseData.name'
                },
                else: '$warehouseId'
              }
            },
            userId: {
              $cond: {
                if: { $ne: ['$userData', null] },
                then: {
                  _id: '$userData._id',
                  username: '$userData.username',
                  first_name: '$userData.first_name',
                  last_name: '$userData.last_name'
                },
                else: '$userId'
              }
            }
          }
        },
        // Remove the temporary fields
        {
          $unset: ['partData', 'warehouseData', 'userData']
        },
        { $sort: { transactionDate: -1 as -1 } },
        { $skip: skip },
        { $limit: limit }
      ];

      // Get transactions with search
      transactions = await InventoryTransaction.aggregate(pipeline);

      // Get total count for search results
      const countPipeline = [
        {
          $lookup: {
            from: 'parts',
            localField: 'partId',
            foreignField: '_id',
            as: 'partData'
          }
        },
        {
          $lookup: {
            from: 'warehouses',
            localField: 'warehouseId',
            foreignField: '_id',
            as: 'warehouseData'
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'userData'
          }
        },
        {
          $addFields: {
            partData: { $arrayElemAt: ['$partData', 0] },
            warehouseData: { $arrayElemAt: ['$warehouseData', 0] },
            userData: { $arrayElemAt: ['$userData', 0] }
          }
        },
        {
          $match: {
            ...filter,
            $or: [
              { notes: searchRegex },
              { referenceNumber: searchRegex },
              { transactionType: searchRegex },
              { 'partData.partNumber': searchRegex },
              { 'partData.name': searchRegex },
              { 'warehouseData.name': searchRegex },
              { 'warehouseData.location_id': searchRegex },
              { 'userData.username': searchRegex },
              { 'userData.first_name': searchRegex },
              { 'userData.last_name': searchRegex }
            ]
          }
        },
        { $count: 'total' }
      ];

      const countResult = await InventoryTransaction.aggregate(countPipeline);
      total = countResult.length > 0 ? countResult[0].total : 0;

    } else {
      // Use regular query with population for non-search requests
      try {
        // First, get transactions without populate to avoid ObjectId cast errors
        const rawTransactions = await (InventoryTransaction as any).find(filter)
          .sort({ transactionDate: -1 })
          .skip(skip)
          .limit(limit)
          .lean();

        // Get total count for pagination
        total = await InventoryTransaction.countDocuments(filter);

        // Now manually populate each transaction, handling errors gracefully
        transactions = await Promise.all(rawTransactions.map(async (transaction: any) => {
          const populatedTransaction = { ...transaction };

          // Populate partId if it's a valid ObjectId
          if (transaction.partId && mongoose.Types.ObjectId.isValid(transaction.partId)) {
            try {
              const Part = mongoose.model('Part');
              const part = await Part.findById(transaction.partId)
                .select('partNumber name description')
                .lean();
              if (part) {
                populatedTransaction.partId = part;
              }
            } catch (partError) {
              console.warn(`Failed to populate partId ${transaction.partId}:`, partError);
              // Keep original partId value
            }
          }

          // Populate warehouseId if it's a valid ObjectId
          if (transaction.warehouseId && mongoose.Types.ObjectId.isValid(transaction.warehouseId)) {
            try {
              const Warehouse = mongoose.model('Warehouse');
              const warehouse = await Warehouse.findById(transaction.warehouseId)
                .select('location_id name')
                .lean();
              if (warehouse) {
                populatedTransaction.warehouseId = warehouse;
              }
            } catch (warehouseError) {
              console.warn(`Failed to populate warehouseId ${transaction.warehouseId}:`, warehouseError);
              // Keep original warehouseId value
            }
          }

          // Populate userId if it's a valid ObjectId
          if (transaction.userId && mongoose.Types.ObjectId.isValid(transaction.userId)) {
            try {
              const User = mongoose.model('User');
              const user = await User.findById(transaction.userId)
                .select('username first_name last_name')
                .lean();
              if (user) {
                populatedTransaction.userId = user;
              }
            } catch (userError) {
              console.warn(`Failed to populate userId ${transaction.userId}:`, userError);
              // Keep original userId value
            }
          }

          // Also populate warehouse references in from/to objects
          if (transaction.from?.warehouseId && mongoose.Types.ObjectId.isValid(transaction.from.warehouseId)) {
            try {
              const Warehouse = mongoose.model('Warehouse');
              const warehouse = await Warehouse.findById(transaction.from.warehouseId)
                .select('location_id name')
                .lean();
              if (warehouse) {
                populatedTransaction.from = {
                  ...transaction.from,
                  warehouse: warehouse
                };
              }
            } catch (fromWarehouseError) {
              console.warn(`Failed to populate from.warehouseId ${transaction.from.warehouseId}:`, fromWarehouseError);
            }
          }

          if (transaction.to?.warehouseId && mongoose.Types.ObjectId.isValid(transaction.to.warehouseId)) {
            try {
              const Warehouse = mongoose.model('Warehouse');
              const warehouse = await Warehouse.findById(transaction.to.warehouseId)
                .select('location_id name')
                .lean();
              if (warehouse) {
                populatedTransaction.to = {
                  ...transaction.to,
                  warehouse: warehouse
                };
              }
            } catch (toWarehouseError) {
              console.warn(`Failed to populate to.warehouseId ${transaction.to.warehouseId}:`, toWarehouseError);
            }
          }

          return populatedTransaction;
        }));

      } catch (populateError: any) {
        console.error('Error during manual populate operation:', populateError);

        // Final fallback: return raw data without population
        transactions = await (InventoryTransaction as any).find(filter)
          .sort({ transactionDate: -1 })
          .skip(skip)
          .limit(limit)
          .lean();

        total = await InventoryTransaction.countDocuments(filter);
      }
    }

    const duration = Date.now() - startTime;
    console.log(`[API] Inventory transactions query completed in ${duration}ms`);

    return NextResponse.json({
      success: true,
      data: transactions,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      },
      meta: { duration }
    });
  } catch (error: any) {
    console.error('Error fetching inventory transactions:', error);
    const errorMessage = handleMongoDBError(error);
    return NextResponse.json(
      { success: false, error: errorMessage || 'Failed to fetch inventory transactions' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for inventory transactions
 * Creates a new inventory transaction and updates inventory levels
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  console.log('[API] POST /api/inventory-transactions - Received request');
  let session: mongoose.ClientSession | null = null;

  try {
    await connectToDatabase();
    session = await mongoose.startSession();
    session.startTransaction();

    const data = (await request.json()) as CreateTransactionRequest;
    console.log('[API] POST /api/inventory-transactions - Request body:', data);

    // Validate required fields
    if (!data.partId || !data.warehouseId || !data.userId || data.quantity === undefined || !data.transactionType) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: partId, warehouseId, userId, quantity, transactionType' },
        { status: 400 }
      );
    }
    // itemType is optional for backward compatibility, default to 'Part'
    if (data.itemType && !['Part', 'Assembly', 'Product'].includes(data.itemType)) {
      return NextResponse.json({ success: false, error: 'Invalid itemType specified' }, { status: 400 });
    }

    if (!mongoose.Types.ObjectId.isValid(data.partId)) {
      if (session) { await session.abortTransaction(); session.endSession(); }
      return NextResponse.json({ success: false, error: 'Invalid partId format' }, { status: 400 });
    }
    if (!mongoose.Types.ObjectId.isValid(data.warehouseId)) {
      if (session) { await session.abortTransaction(); session.endSession(); }
      return NextResponse.json({ success: false, error: 'Invalid warehouseId format' }, { status: 400 });
    }
    if (!mongoose.Types.ObjectId.isValid(data.userId)) {
      if (session) { await session.abortTransaction(); session.endSession(); }
      return NextResponse.json({ success: false, error: 'Invalid userId format' }, { status: 400 });
    }

    let part: IPartModel | null = null;
    let previousStock: number | undefined;

    // Default to 'Part' if itemType is not specified for backward compatibility
    const itemType = data.itemType || 'Part';

    if (itemType === 'Part') {
      part = await getPartById(data.partId);
      if (!part) {
        throw new Error(`Part not found with ID ${data.partId}`);
      }
      if (!part.inventory || (part.inventory && (part.inventory.currentStock === undefined || part.inventory?.warehouseId === undefined))) {
        throw new Error(`Inventory details missing for part ${data.partId}. Cannot process transaction.`);
      }
      if (part.inventory?.warehouseId.toString() !== data.warehouseId) {
        throw new Error(`Warehouse ID mismatch for part ${data.partId}. Expected ${part.inventory?.warehouseId}, got ${data.warehouseId}.`);
      }
      previousStock = part.inventory?.currentStock;
    } else {
      // For Assembly/Product, stock management might be different or not applicable here
      // For now, we'll assume previousStock needs to be fetched or defaults if not a 'Part'
      // This part needs to be expanded based on how Assembly/Product stock is handled
      console.warn(`Stock adjustment for itemType '${itemType}' is not fully implemented. Assuming previous stock is 0 or managed elsewhere.`);
      // As a placeholder, if we can't get previous stock, we can't reliably calculate newStock for the transaction log for non-Parts.
      // However, the transaction log requires previousStock and newStock.
      // This indicates a need for a more robust way to get current stock for Assemblies/Products if they are to be transacted here.
      // For now, let's throw an error if it's not a Part, as adjustStockLevelByDelta is part-specific.
      throw new Error(`Stock transactions for itemType '${itemType}' are not yet fully supported by this endpoint for stock level adjustments.`);
    }

    // previousStock is now set conditionally above if itemType is 'Part'
    const transactionQuantity = data.quantity as number;
    let quantityChangeForUpdateStockLevel: number = transactionQuantity;
    const type = data.transactionType as string;

    if (type === 'stock_out_production' || type === 'transfer_out' || type === 'sales_shipment') {
      quantityChangeForUpdateStockLevel = -Math.abs(transactionQuantity);
    } else if (type === 'stock_in_purchase' || type === 'stock_in_production' || type === 'transfer_in') {
      quantityChangeForUpdateStockLevel = Math.abs(transactionQuantity);
    } else if (type !== 'adjustment_cycle_count') {
      throw new Error(`Invalid transactionType: ${type}`);
    }
    // For 'adjustment_cycle_count', quantityChangeForUpdateStockLevel remains data.quantity (which is the delta)

    const newStock = (previousStock || 0) + quantityChangeForUpdateStockLevel;

    if (data.itemType === 'Part' && newStock < 0 && type !== 'adjustment_cycle_count') {
        throw new Error(`Insufficient stock for part ${part?.partNumber}. Current: ${previousStock}, Requested change: ${quantityChangeForUpdateStockLevel}, results in ${newStock}`);
    }

    // Generate human-readable transaction ID
    // Ensure transactionDate is a proper Date object
    let transactionDate: Date;
    if (data.transactionDate) {
      // If transactionDate is provided, convert it to Date object if it's a string
      transactionDate = typeof data.transactionDate === 'string' ? new Date(data.transactionDate) : data.transactionDate;
    } else {
      // Use current date if not provided
      transactionDate = new Date();
    }

    console.log(`[API] Transaction date processing: input=${data.transactionDate}, processed=${transactionDate.toISOString()}`);
    const humanReadableTransactionId = await generateTransactionId(type, transactionDate);

    const newTransactionDoc = new InventoryTransaction({
      transactionId: humanReadableTransactionId,
      partId: data.partId,
      itemType: itemType,
      warehouseId: data.warehouseId,
      transactionType: type,
      quantity: transactionQuantity, // Store the actual quantity moved in the transaction log
      previousStock,
      newStock,
      transactionDate,
      referenceNumber: data.referenceNumber || null,
      referenceType: data.referenceType || null,
      userId: data.userId,
      notes: data.notes || null,
    });
    const savedTransaction = await newTransactionDoc.save({ session });

    let updatedItem: IPartModel | null = null;
    if (itemType === 'Part') {
      updatedItem = await adjustStockLevelByDelta(data.partId, data.warehouseId, quantityChangeForUpdateStockLevel, { session });
      if (!updatedItem) {
        throw new Error('Failed to update part stock level after creating transaction.');
      }
    }
    // If not a 'Part', stock adjustment is skipped for now as Assembly/Product inventory
    // management may be handled differently.

    await session.commitTransaction();
    session.endSession();

    const duration = Date.now() - startTime;
    console.log(`[API] Inventory transaction processed successfully in ${duration}ms`);

    return NextResponse.json({
      success: true,
      data: {
        transaction: savedTransaction,
        updatedItem: updatedItem
      },
      meta: { duration }
    });

  } catch (error: any) {
    if (session) {
      await session.abortTransaction();
      session.endSession();
    }
    const duration = Date.now() - startTime;
    console.error(`[API] Error in POST /api/inventory-transactions (${duration}ms):`, error);
    if (error.message.includes('Insufficient stock') || error.message.includes('Part not found') || error.message.includes('Item not found') || error.message.includes('Warehouse ID mismatch') || error.message.includes('Invalid transactionType') || error.message.includes('not yet fully supported')) {
        return NextResponse.json({ success: false, error: error.message }, { status: 400 });
    }
    const errorMessageString = handleMongoDBError(error);
    return NextResponse.json(
      { success: false, error: errorMessageString || 'Failed to process inventory transaction' },
      { status: 500 } // Default to 500 as handleMongoDBError from lib/mongodb.ts returns string
    );
  }
}