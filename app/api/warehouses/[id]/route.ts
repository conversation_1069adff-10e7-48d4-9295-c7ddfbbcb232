import { NextRequest, NextResponse } from 'next/server';
// Import new service functions
import {
  getWarehouseByLocationId,
  updateWarehouseByLocationId as updateWarehouseNew,
  deleteWarehouseByLocationId as deleteWarehouseNew,
  UpdateWarehouseDto
} from '@/app/services/warehouse.service';
// Keep legacy import for backward compatibility during transition
import { handleMongoDBError } from '@/app/services/mongodb';

interface RouteParams {
  id: string; // This 'id' from the route corresponds to the location_id field
}

/**
 * GET handler for fetching a specific warehouse by its location_id
 * @param _request - The incoming request (unused)
 * @param params - Route parameters including the warehouse location ID
 * @returns JSON response with warehouse data or error
 */
export async function GET(
  _request: NextRequest,
  context: { params: Promise<RouteParams> }
) {
  const startTime = Date.now();
  const { id: locationId } = await context.params; // The unique string identifier (location_id)
  try {
    console.log(`[API] GET /api/warehouses/${locationId} - Fetching warehouse`);

    // Call the new service function to get the warehouse
    const warehouse = await getWarehouseByLocationId(locationId);

    const duration = Date.now() - startTime;

    if (!warehouse) {
      console.log(`[API] Warehouse ${locationId} not found (${duration}ms)`);
      return NextResponse.json(
        { data: null, error: `Warehouse with location ID ${locationId} not found`, meta: { duration } },
        { status: 404 }
      );
    }

    console.log(`[API] Fetched warehouse ${locationId} successfully (${duration}ms)`);
    return NextResponse.json({ data: warehouse, error: null, meta: { duration } });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error fetching warehouse ${locationId} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * PUT handler for updating a specific warehouse by its location_id
 * @param request - The incoming request with updated warehouse data
 * @param params - Route parameters including the warehouse location ID
 * @returns JSON response with updated warehouse data or error
 */
export async function PUT(
  request: NextRequest,
  context: { params: Promise<RouteParams> }
) {
  const startTime = Date.now();
  const { id: locationId } = await context.params;
  try {
    console.log(`[API] PUT /api/warehouses/${locationId} - Updating warehouse`);
    const updateData = await request.json();

    if (!updateData || typeof updateData !== 'object' || Object.keys(updateData).length === 0) {
      return NextResponse.json({ data: null, error: 'Update data is required and cannot be empty' }, { status: 400 });
    }

    // Transform the request data to match the new service DTO
    const updateWarehouseDto: UpdateWarehouseDto = {
      name: updateData.name,
      location: updateData.location,
      capacity: updateData.capacity,
      manager: updateData.manager,
      contact: updateData.contact,
      isBinTracked: updateData.isBinTracked
    };

    // Call the new service function to update the warehouse
    const updatedWarehouse = await updateWarehouseNew(locationId, updateWarehouseDto);

    const duration = Date.now() - startTime;
    console.log(`[API] Updated warehouse ${locationId} successfully (${duration}ms)`);
    return NextResponse.json({ data: updatedWarehouse, error: null, meta: { duration } });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error updating warehouse ${locationId} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}

/**
 * DELETE handler for removing a specific warehouse by its location_id
 * @param _request - The incoming request (unused)
 * @param params - Route parameters including the warehouse location ID
 * @returns JSON response indicating success or failure
 */
export async function DELETE(
  _request: NextRequest,
  context: { params: Promise<RouteParams> }
) {
  const startTime = Date.now();
  const { id: locationId } = await context.params;
  try {
    console.log(`[API] DELETE /api/warehouses/${locationId} - Deleting warehouse`);

    // Call the new service function to delete the warehouse
    const result = await deleteWarehouseNew(locationId);

    const duration = Date.now() - startTime;
    console.log(`[API] Deleted warehouse ${locationId} successfully (${duration}ms)`);
    return NextResponse.json({ 
      data: result, 
      error: null, 
      meta: { duration } 
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Error deleting warehouse ${locationId} (${duration}ms):`, error);
    const { message, status } = handleMongoDBError(error);
    return NextResponse.json(
      { data: null, error: message, meta: { duration } },
      { status }
    );
  }
}
