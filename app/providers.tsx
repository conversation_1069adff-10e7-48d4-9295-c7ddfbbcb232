"use client";

import React from 'react';
import { ThemeProvider } from "@/app/context/ThemeContext";
import { AuthProvider } from "@/app/context/MockAuthContext";
import { AppProvider } from "@/app/context/AppContext";
import { Toaster } from 'react-hot-toast';

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider>
      <AuthProvider>
        <AppProvider>
          {children}
          <Toaster position="top-right" /> 
        </AppProvider>
      </AuthProvider>
    </ThemeProvider>
  );
} 