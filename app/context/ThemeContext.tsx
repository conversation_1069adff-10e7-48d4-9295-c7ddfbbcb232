"use client";

/**
 * Enhanced Theme Context Module
 * Provides theme management functionality with support for multiple theme variants
 * Maintains backward compatibility with existing light/dark theme system
 */
import {
    DEFAULT_THEME_MODE,
    DEFAULT_THEME_VARIANT,
    getAvailableThemes
} from '@/app/config/themes.config';
import {
    CurrentTheme,
    ThemeContextValue,
    ThemeMode,
    ThemeVariant
} from '@/app/types/theme.types';
import {
    applyThemeToDocument,
    createCurrentTheme,
    initializeTheme,
    storeThemeMode,
    storeThemeVariant,
    toggleThemeMode
} from '@/app/utils/theme.utils';
import React, { createContext, useCallback, useContext, useEffect, useState } from 'react';

/** Legacy theme type for backward compatibility */
type LegacyTheme = 'light' | 'dark';

/**
 * Default theme context used before provider is mounted
 */
const defaultThemeContext: ThemeContextValue = {
  currentTheme: {
    mode: DEFAULT_THEME_MODE,
    variant: DEFAULT_THEME_VARIANT,
    resolvedMode: DEFAULT_THEME_MODE as 'light' | 'dark',
    isDark: (DEFAULT_THEME_MODE as string) === 'dark',
    isLight: DEFAULT_THEME_MODE === 'light',
    isSystem: false,
    config: {
      id: 'default-light',
      name: 'Default Light',
      variant: DEFAULT_THEME_VARIANT,
      mode: DEFAULT_THEME_MODE,
      description: 'Default theme',
      preview: { primary: '#3b82f6', secondary: '#f1f5f9', accent: '#f1f5f9', background: '#ffffff' },
      className: 'theme-default-light'
    }
  },
  availableThemes: [],
  setMode: () => console.warn('ThemeProvider not mounted'),
  setVariant: () => console.warn('ThemeProvider not mounted'),
  setTheme: () => console.warn('ThemeProvider not mounted'),
  toggleTheme: () => console.warn('ThemeProvider not mounted'),
  theme: {
    mode: DEFAULT_THEME_MODE,
    variant: DEFAULT_THEME_VARIANT,
    resolvedMode: DEFAULT_THEME_MODE as 'light' | 'dark',
    isDark: (DEFAULT_THEME_MODE as string) === 'dark',
    isLight: DEFAULT_THEME_MODE === 'light',
    isSystem: false,
    config: {
      id: 'default-light',
      name: 'Default Light',
      variant: DEFAULT_THEME_VARIANT,
      mode: DEFAULT_THEME_MODE,
      description: 'Default theme',
      preview: { primary: '#3b82f6', secondary: '#f1f5f9', accent: '#f1f5f9', background: '#ffffff' },
      className: 'theme-default-light'
    }
  }, // Now returns CurrentTheme object for component compatibility
  mode: DEFAULT_THEME_MODE, // Legacy theme mode string
};

/** Theme context for providing theme information throughout the app */
const ThemeContext = createContext<ThemeContextValue>(defaultThemeContext);

/**
 * Enhanced Theme Provider component that manages theme variants and modes
 * Persists theme choice in localStorage and syncs with system preferences
 * Maintains backward compatibility with existing theme system
 * @param children - Child components that will have access to the theme context
 */
export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Initialize with default theme for SSR
  const [currentTheme, setCurrentTheme] = useState<CurrentTheme>(() => ({
    mode: DEFAULT_THEME_MODE,
    variant: DEFAULT_THEME_VARIANT,
    resolvedMode: DEFAULT_THEME_MODE as 'light' | 'dark',
    isDark: (DEFAULT_THEME_MODE as string) === 'dark',
    isLight: DEFAULT_THEME_MODE === 'light',
    isSystem: false,
    config: defaultThemeContext.currentTheme.config!
  }));
  const [mounted, setMounted] = useState(false);
  const [availableThemes] = useState(() => getAvailableThemes());

  /**
   * Initialize theme from localStorage or system preference after mounting
   */
  useEffect(() => {
    if (mounted) return; // Only initialize once

    const initialTheme = initializeTheme();
    setCurrentTheme(initialTheme);
    applyThemeToDocument(initialTheme);

    // Ensure theme is stored in localStorage immediately for cross-page tests
    storeThemeMode(initialTheme.mode);
    storeThemeVariant(initialTheme.variant);

    setMounted(true);

    // Force a small delay to ensure localStorage is written
    setTimeout(() => {
      // Verify storage was successful
      const storedMode = localStorage.getItem('theme-mode');
      const storedVariant = localStorage.getItem('theme-variant');
      if (!storedMode || !storedVariant) {
        console.warn('Theme persistence verification failed, retrying...');
        storeThemeMode(initialTheme.mode);
        storeThemeVariant(initialTheme.variant);
      }
    }, 100);
  }, [mounted]);

  /**
   * Apply theme to document when theme changes
   */
  useEffect(() => {
    if (!mounted) return;
    applyThemeToDocument(currentTheme);
  }, [currentTheme, mounted]);

  /**
   * Handle system theme changes and transitions
   */
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    // Add smooth transitions when theme changes
    const enableTransitions = () => {
      const style = document.createElement('style');
      style.textContent = `
        * {
          transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
        }
      `;
      document.head.appendChild(style);
      
      // Remove the style after the transition completes
      setTimeout(() => {
        document.head.removeChild(style);
      }, 200);
    };

    // Handle system preference changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleSystemChange = () => {
      if (currentTheme.mode === 'system') {
        enableTransitions();
        const newMode = mediaQuery.matches ? 'dark' : 'light';
        const newTheme = createCurrentTheme(currentTheme.variant, 'system');
        
        if (newTheme) {
          setCurrentTheme({
            ...newTheme,
            resolvedMode: newMode,
            isDark: newMode === 'dark',
            isLight: newMode === 'light',
          });
          
          // Apply with a small delay to allow state to update
          setTimeout(() => {
            applyThemeToDocument({
              ...newTheme,
              resolvedMode: newMode,
              isDark: newMode === 'dark',
              isLight: newMode === 'light',
            });
          }, 10);
        }
      }
    };

    // Initial setup
    setMounted(true);
    mediaQuery.addEventListener('change', handleSystemChange);
    
    // Cleanup
    return () => {
      mediaQuery.removeEventListener('change', handleSystemChange);
    };
  }, [currentTheme.mode, currentTheme.variant]);

  /**
   * Set theme mode (light/dark) while preserving variant
   */
  const setMode = useCallback((mode: ThemeMode) => {
    if (!mounted) return;

    const newTheme = createCurrentTheme(currentTheme.variant, mode);
    if (newTheme) {
      setCurrentTheme(newTheme);
      storeThemeMode(mode);
    }
  }, [currentTheme.variant, mounted]);

  /**
   * Set theme variant while preserving mode
   */
  const setVariant = useCallback((variant: ThemeVariant) => {
    if (!mounted) return;

    const newTheme = createCurrentTheme(variant, currentTheme.mode);
    if (newTheme) {
      setCurrentTheme(newTheme);
      storeThemeVariant(variant);
    }
  }, [currentTheme.mode, mounted]);

  /**
   * Set complete theme configuration (variant + mode) with smooth transitions
   */
  const setTheme = useCallback((variant: ThemeVariant, mode: ThemeMode) => {
    // Enable smooth transitions
    if (typeof document !== 'undefined') {
      document.documentElement.style.transition = 'background-color 0.2s ease, color 0.2s ease';
      
      // Remove transition after it completes
      setTimeout(() => {
        if (document.documentElement) {
          document.documentElement.style.transition = '';
        }
      }, 200);
    }

    const newTheme = createCurrentTheme(variant, mode);
    if (newTheme) {
      setCurrentTheme(newTheme);
      
      // Small delay to ensure state updates before applying to document
      requestAnimationFrame(() => {
        applyThemeToDocument(newTheme);
      });
      
      storeThemeVariant(variant);
      storeThemeMode(mode);
    }
  }, []);

  /**
   * Toggle between light and dark mode (backward compatibility)
   */
  const toggleTheme = useCallback(() => {
    if (!mounted) return;

    const newTheme = toggleThemeMode(currentTheme);
    if (newTheme) {
      setCurrentTheme(newTheme);
      storeThemeMode(newTheme.mode);
    }
  }, [currentTheme, mounted]);

  /**
   * Legacy setTheme function for backward compatibility
   */
  const legacySetTheme = useCallback((legacyTheme: LegacyTheme) => {
    setMode(legacyTheme);
  }, [setMode]);

  // Create context value with both new and legacy properties
  const contextValue: ThemeContextValue = {
    currentTheme,
    availableThemes, // Always provide available themes
    setMode: mounted ? setMode : () => console.warn('ThemeProvider not mounted'),
    setVariant: mounted ? setVariant : () => console.warn('ThemeProvider not mounted'),
    setTheme: mounted ? setTheme : () => console.warn('ThemeProvider not mounted'),
    toggleTheme: mounted ? toggleTheme : () => console.warn('ThemeProvider not mounted'),
    // Legacy properties for backward compatibility
    theme: currentTheme, // Now returns CurrentTheme object for component compatibility
    mode: currentTheme.mode, // Legacy theme mode string
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * Custom hook to use the enhanced theme context
 * @returns The theme context with current theme and theme management functions
 */
export const useTheme = (): ThemeContextValue => {
  const context = useContext(ThemeContext);
  return context;
};

