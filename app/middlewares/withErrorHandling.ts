import { NextRequest, NextResponse } from 'next/server';
import withDatabase from './withDatabase'; // Note: This is a .js file
import { handleApiError } from '@/app/lib/api-error-handler';

// Type definitions for our two handler signatures
type StaticHandler = (request: NextRequest) => Promise<NextResponse>;

// A generic dynamic handler where `P` is the shape of the params object.
// This allows TypeScript to infer the exact type of `params` from the route handler.
type DynamicHandler<P> = (
  request: NextRequest,
  context: { params: P }
) => Promise<NextResponse>;

// --- Function Overloads ---
// These overloads provide strong typing for the middleware's return value.

function withErrorHandling(handler: StaticHandler, routePath: string): StaticHandler;
function withErrorHandling<P>(handler: DynamicHandler<P>, routePath: string): DynamicHandler<P>;

// --- Implementation ---
// The implementation uses the generic `P` to maintain type safety.

function withErrorHandling<P>(
  handler: Static<PERSON>andler | DynamicHandler<P>,
  routePath: string
): StaticHandler | DynamicHandler<P> {
  // withDatabase is a JS middleware, so we cast the handler to 'any'.
  // The logic inside withDatabase correctly passes through the arguments.
  const handlerWithDb = withDatabase(handler as any);

  // We check the original handler's argument count to distinguish static vs. dynamic.
  if (handler.length <= 1) {
    // This is a static handler
    const enhancedStaticHandler: StaticHandler = async (request) => {
      const startTime = Date.now();
      try {
        const response = await handlerWithDb(request);
        return response;
      } catch (error) {
        return handleApiError(error, routePath, startTime);
      }
    };
    return enhancedStaticHandler;

  } else {
    // This is a dynamic handler
    const enhancedDynamicHandler: DynamicHandler<P> = async (request, context) => {
      const startTime = Date.now();
      try {
        const response = await handlerWithDb(request, context);
        return response;
      } catch (error) {
        return handleApiError(error, routePath, startTime);
      }
    };
    return enhancedDynamicHandler;
  }
}

export default withErrorHandling; 