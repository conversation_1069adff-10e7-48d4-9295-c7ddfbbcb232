import { NextRequest, NextResponse } from 'next/server';
import withDatabase from './withDatabase';
import withPerformanceMonitoring from './withPerformanceMonitoring';
import { handleApiError } from '@/app/lib/api-error-handler';

/**
 * Combines database connection, error handling, and performance monitoring
 * for API routes into a single middleware
 * 
 * @param handler - The API route handler
 * @param routePath - The API route path
 * @returns A wrapped handler with combined middleware functionality
 */
export default function withApiMiddleware(
  handler: (request: NextRequest) => Promise<NextResponse>,
  routePath: string
) {
  // First add database connection
  const handlerWithDb = withDatabase(handler);
  
  // Then add performance monitoring
  const handlerWithPerformance = withPerformanceMonitoring(handlerWithDb, routePath);
  
  // Finally add error handling
  return async function enhancedHandler(request: NextRequest) {
    const startTime = Date.now();
    try {
      // Call the handler chain (database + performance monitoring)
      return await handlerWithPerformance(request);
    } catch (error) {
      // Handle any errors
      return await handleApiError(error, routePath, startTime);
    }
  };
} 