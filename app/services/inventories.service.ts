import mongoose, { ClientSession, Types } from 'mongoose';
import { captureException, setTag } from '../lib/logging-utils';
import connectToDatabase from '../lib/mongodb';
import Inventories, { IInventories } from '../models/inventories.model';
import Warehouse from '../models/warehouse.model';

/**
 * NEW INVENTORIES SERVICE
 * 
 * This service handles all operations on the new dedicated inventories collection.
 * It provides atomic operations for stock management and supports the event-sourced
 * inventory architecture.
 * 
 * Key Features:
 * - Atomic stock increment/decrement operations
 * - Multi-warehouse and multi-state inventory management
 * - Transaction-safe operations with MongoDB sessions
 * - Comprehensive error handling and logging
 * - Performance-optimized queries with proper indexing
 */

// Logger function for tracking database operations
const logOperation = (operation: string, entity: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[InventoriesService][${timestamp}] ${operation} on ${entity}${details ? ': ' + JSON.stringify(details) : ''}`);
};

/**
 * Standardized error handling for MongoDB operations
 */
export const handleMongoDBError = (error: any) => {
  console.error('[InventoriesService Error]', error);

  setTag('error.type', 'database');
  setTag('error.database', 'mongodb');
  setTag('error.service', 'inventories');

  let errorType = 'unknown';
  let errorStatus = 500;
  let errorMessage = '';

  if (error.name === 'ValidationError') {
    errorType = 'validation';
    errorStatus = 400;
    const validationErrors = Object.values(error.errors)
      .map((err: any) => err.message)
      .join(', ');
    errorMessage = `Validation failed: ${validationErrors}`;
    setTag('error.subtype', 'validation');
  } else if (error.code === 11000) {
    errorType = 'duplicate';
    errorStatus = 409;
    errorMessage = `Duplicate inventory record: This part/warehouse/stockType combination already exists`;
    setTag('error.subtype', 'duplicate_key');
  } else if (error.name === 'CastError') {
    errorType = 'cast';
    errorStatus = 400;
    errorMessage = `Invalid data format: ${error.message}`;
    setTag('error.subtype', 'cast_error');
  } else {
    errorType = 'database';
    errorStatus = 500;
    errorMessage = `Database error: ${error.message}`;
    setTag('error.subtype', 'general');
  }

  captureException(error, {
    errorType,
    errorStatus,
    errorMessage
  });

  return { message: errorMessage, status: errorStatus };
};

/**
 * Interface for stock operation parameters
 */
export interface StockOperationParams {
  partId: string | Types.ObjectId;
  warehouseId: string | Types.ObjectId;
  stockType: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected';
  quantity: number;
  session?: ClientSession;
}

/**
 * Interface for inventory query parameters
 */
export interface InventoryQueryParams {
  partId?: string | Types.ObjectId;
  warehouseId?: string | Types.ObjectId;
  stockType?: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected';
  minQuantity?: number;
  maxQuantity?: number;
}

/**
 * InventoriesService class - handles all inventory operations
 */
export class InventoriesService {
  
  /**
   * Increment stock for a specific part/warehouse/stockType combination
   * Uses atomic $inc operation with upsert for safety
   */
  static async incrementStock(params: StockOperationParams): Promise<IInventories> {
    try {
      await connectToDatabase();
      
      const { partId, warehouseId, stockType, quantity, session } = params;
      
      logOperation('incrementStock', 'service', { 
        partId: partId.toString(), 
        warehouseId: warehouseId.toString(), 
        stockType, 
        quantity 
      });

      if (quantity <= 0) {
        throw new Error('Quantity must be positive for increment operation');
      }

      const filter = {
        partId: new Types.ObjectId(partId),
        warehouseId: new Types.ObjectId(warehouseId),
        stockType
      };

      const update = {
        $inc: { quantity: quantity },
        $set: { lastUpdated: new Date() },
        $setOnInsert: {
          partId: new Types.ObjectId(partId),
          warehouseId: new Types.ObjectId(warehouseId),
          stockType,
          createdAt: new Date()
        }
      };

      const options = {
        upsert: true,
        new: true,
        ...(session && { session })
      };

      const result = await Inventories.findOneAndUpdate(filter, update, options);
      
      if (!result) {
        throw new Error('Failed to increment stock - no result returned');
      }

      logOperation('incrementStock completed', 'service', { 
        inventoryId: result._id,
        newQuantity: result.quantity 
      });

      return result;
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to increment stock: ${errorDetails.message}`);
    }
  }

  /**
   * Decrement stock for a specific part/warehouse/stockType combination
   * Ensures stock doesn't go below zero
   */
  static async decrementStock(params: StockOperationParams): Promise<IInventories> {
    try {
      await connectToDatabase();
      
      const { partId, warehouseId, stockType, quantity, session } = params;
      
      logOperation('decrementStock', 'service', { 
        partId: partId.toString(), 
        warehouseId: warehouseId.toString(), 
        stockType, 
        quantity 
      });

      if (quantity <= 0) {
        throw new Error('Quantity must be positive for decrement operation');
      }

      const filter = {
        partId: new Types.ObjectId(partId),
        warehouseId: new Types.ObjectId(warehouseId),
        stockType,
        quantity: { $gte: quantity } // Ensure sufficient stock
      };

      const update = {
        $inc: { quantity: -quantity },
        $set: { lastUpdated: new Date() }
      };

      const options = {
        new: true,
        ...(session && { session })
      };

      const result = await Inventories.findOneAndUpdate(filter, update, options);
      
      if (!result) {
        // Check if the inventory record exists but has insufficient stock
        const existingRecord = await Inventories.findOne({
          partId: new Types.ObjectId(partId),
          warehouseId: new Types.ObjectId(warehouseId),
          stockType
        }, null, { ...(session && { session }) });

        if (existingRecord) {
          throw new Error(`Insufficient stock: Available ${existingRecord.quantity}, requested ${quantity}`);
        } else {
          throw new Error('Inventory record not found for decrement operation');
        }
      }

      logOperation('decrementStock completed', 'service', { 
        inventoryId: result._id,
        newQuantity: result.quantity 
      });

      return result;
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to decrement stock: ${errorDetails.message}`);
    }
  }

  /**
   * Get current stock for a specific part/warehouse/stockType combination
   */
  static async getStock(params: Omit<StockOperationParams, 'quantity'>): Promise<number> {
    try {
      await connectToDatabase();
      
      const { partId, warehouseId, stockType } = params;
      
      const inventory = await Inventories.findOne({
        partId: new Types.ObjectId(partId),
        warehouseId: new Types.ObjectId(warehouseId),
        stockType
      });

      return inventory?.quantity || 0;
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to get stock: ${errorDetails.message}`);
    }
  }

  /**
   * Get all inventory records for a specific part
   */
  static async getPartInventory(partId: string | Types.ObjectId): Promise<IInventories[]> {
    try {
      await connectToDatabase();

      logOperation('getPartInventory', 'service', { partId: partId.toString() });

      const inventories = await Inventories.find({
        partId: new Types.ObjectId(partId)
      })
      .populate('warehouseId', 'name location_id location')
      .sort({ stockType: 1, warehouseId: 1 });

      return inventories;
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to get part inventory: ${errorDetails.message}`);
    }
  }

  /**
   * Get detailed inventory breakdown for a part with warehouse grouping
   * This provides the multi-warehouse view needed for the part details modal
   */
  static async getPartInventoryWithWarehouseBreakdown(partId: string | Types.ObjectId) {
    try {
      await connectToDatabase();

      logOperation('getPartInventoryWithWarehouseBreakdown', 'service', { partId: partId.toString() });

      const breakdown = await Inventories.aggregate([
        { $match: { partId: new Types.ObjectId(partId) } },

        // Lookup warehouse details
        {
          $lookup: {
            from: 'warehouses',
            localField: 'warehouseId',
            foreignField: '_id',
            as: 'warehouse',
            pipeline: [
              { $project: { name: 1, location_id: 1, location: 1 } }
            ]
          }
        },

        // Unwind warehouse (should always be one)
        { $unwind: '$warehouse' },

        // Group by warehouse to get stock levels per warehouse
        {
          $group: {
            _id: '$warehouseId',
            warehouseName: { $first: '$warehouse.name' },
            warehouseLocationId: { $first: '$warehouse.location_id' },
            warehouseLocation: { $first: '$warehouse.location' },
            stockLevels: {
              $push: {
                stockType: '$stockType',
                quantity: '$quantity',
                lastUpdated: '$lastUpdated'
              }
            },
            totalQuantity: { $sum: '$quantity' },
            lastUpdated: { $max: '$lastUpdated' }
          }
        },

        // Transform stock levels into a more usable format
        {
          $addFields: {
            stockLevelsByType: {
              $arrayToObject: {
                $map: {
                  input: '$stockLevels',
                  as: 'stock',
                  in: {
                    k: '$$stock.stockType',
                    v: '$$stock.quantity'
                  }
                }
              }
            }
          }
        },

        // Add default values for missing stock types
        {
          $addFields: {
            stockLevelsByType: {
              raw: { $ifNull: ['$stockLevelsByType.raw', 0] },
              hardening: { $ifNull: ['$stockLevelsByType.hardening', 0] },
              grinding: { $ifNull: ['$stockLevelsByType.grinding', 0] },
              finished: { $ifNull: ['$stockLevelsByType.finished', 0] },
              rejected: { $ifNull: ['$stockLevelsByType.rejected', 0] }
            }
          }
        },

        // Sort by warehouse name for consistent display
        { $sort: { warehouseName: 1 } },

        // Final projection
        {
          $project: {
            warehouseId: '$_id',
            warehouseName: 1,
            warehouseLocationId: 1,
            warehouseLocation: 1,
            stockLevels: '$stockLevelsByType',
            totalQuantity: 1,
            lastUpdated: 1,
            _id: 0
          }
        }
      ]);

      // Also get overall totals
      const totals = await Inventories.aggregate([
        { $match: { partId: new Types.ObjectId(partId) } },
        {
          $group: {
            _id: '$stockType',
            totalQuantity: { $sum: '$quantity' }
          }
        },
        {
          $group: {
            _id: null,
            stockTotals: {
              $push: {
                stockType: '$_id',
                quantity: '$totalQuantity'
              }
            },
            grandTotal: { $sum: '$totalQuantity' }
          }
        },
        {
          $addFields: {
            stockTotalsByType: {
              $arrayToObject: {
                $map: {
                  input: '$stockTotals',
                  as: 'stock',
                  in: {
                    k: '$$stock.stockType',
                    v: '$$stock.quantity'
                  }
                }
              }
            }
          }
        },
        {
          $project: {
            totals: {
              raw: { $ifNull: ['$stockTotalsByType.raw', 0] },
              hardening: { $ifNull: ['$stockTotalsByType.hardening', 0] },
              grinding: { $ifNull: ['$stockTotalsByType.grinding', 0] },
              finished: { $ifNull: ['$stockTotalsByType.finished', 0] },
              rejected: { $ifNull: ['$stockTotalsByType.rejected', 0] }
            },
            grandTotal: 1,
            _id: 0
          }
        }
      ]);

      const result = {
        warehouseBreakdown: breakdown,
        totals: totals.length > 0 ? totals[0].totals : {
          raw: 0,
          hardening: 0,
          grinding: 0,
          finished: 0,
          rejected: 0
        },
        grandTotal: totals.length > 0 ? totals[0].grandTotal : 0,
        warehouseCount: breakdown.length
      };

      logOperation('getPartInventoryWithWarehouseBreakdown completed', 'service', {
        partId: partId.toString(),
        warehouseCount: result.warehouseCount,
        grandTotal: result.grandTotal
      });

      return result;
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to get part inventory breakdown: ${errorDetails.message}`);
    }
  }

  /**
   * Get aggregated stock totals for a part across all warehouses and stock types
   */
  static async getPartStockSummary(partId: string | Types.ObjectId) {
    try {
      await connectToDatabase();
      
      logOperation('getPartStockSummary', 'service', { partId: partId.toString() });

      const summary = await Inventories.aggregate([
        { $match: { partId: new Types.ObjectId(partId) } },
        {
          $group: {
            _id: null,
            totalStock: { $sum: '$quantity' },
            stockByType: {
              $push: {
                stockType: '$stockType',
                quantity: '$quantity',
                warehouseId: '$warehouseId'
              }
            },
            warehouseCount: { $addToSet: '$warehouseId' },
            lastUpdated: { $max: '$lastUpdated' }
          }
        },
        {
          $project: {
            _id: 0,
            totalStock: 1,
            stockByType: 1,
            warehouseCount: { $size: '$warehouseCount' },
            lastUpdated: 1
          }
        }
      ]);

      return summary.length > 0 ? summary[0] : {
        totalStock: 0,
        stockByType: [],
        warehouseCount: 0,
        lastUpdated: null
      };
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to get part stock summary: ${errorDetails.message}`);
    }
  }

  /**
   * Get inventory records with optional filtering
   */
  static async queryInventory(params: InventoryQueryParams = {}): Promise<IInventories[]> {
    try {
      await connectToDatabase();
      
      logOperation('queryInventory', 'inventories', params);

      const filter: any = {};
      
      if (params.partId) {
        filter.partId = new Types.ObjectId(params.partId);
      }
      
      if (params.warehouseId) {
        filter.warehouseId = new Types.ObjectId(params.warehouseId);
      }
      
      if (params.stockType) {
        filter.stockType = params.stockType;
      }
      
      if (params.minQuantity !== undefined || params.maxQuantity !== undefined) {
        filter.quantity = {};
        if (params.minQuantity !== undefined) {
          filter.quantity.$gte = params.minQuantity;
        }
        if (params.maxQuantity !== undefined) {
          filter.quantity.$lte = params.maxQuantity;
        }
      }

      const inventories = await Inventories.find(filter)
        .populate('partId', 'partNumber name')
        .populate('warehouseId', 'name location_id location')
        .sort({ partId: 1, stockType: 1, warehouseId: 1 });

      return inventories;
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to query inventory: ${errorDetails.message}`);
    }
  }

  /**
   * Create or update an inventory record
   */
  static async upsertInventory(inventoryData: Partial<IInventories>): Promise<IInventories> {
    try {
      await connectToDatabase();
      
      logOperation('upsertInventory', 'inventories', inventoryData);

      const { partId, warehouseId, stockType, ...updateData } = inventoryData;
      
      if (!partId || !warehouseId || !stockType) {
        throw new Error('partId, warehouseId, and stockType are required');
      }

      const filter = {
        partId: new Types.ObjectId(partId),
        warehouseId: new Types.ObjectId(warehouseId),
        stockType
      };

      const update = {
        $set: {
          ...updateData,
          lastUpdated: new Date()
        },
        $setOnInsert: {
          partId: new Types.ObjectId(partId),
          warehouseId: new Types.ObjectId(warehouseId),
          stockType,
          createdAt: new Date()
        }
      };

      const options = {
        upsert: true,
        new: true
      };

      const result = await Inventories.findOneAndUpdate(filter, update, options);
      
      if (!result) {
        throw new Error('Failed to upsert inventory record');
      }

      return result;
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to upsert inventory: ${errorDetails.message}`);
    }
  }

  /**
   * Delete an inventory record
   */
  static async deleteInventory(partId: string | Types.ObjectId, warehouseId: string | Types.ObjectId, stockType: string): Promise<boolean> {
    try {
      await connectToDatabase();
      
      logOperation('deleteInventory', 'service', { partId: partId.toString(), warehouseId: warehouseId.toString(), stockType });

      const result = await Inventories.deleteOne({
        partId: new Types.ObjectId(partId),
        warehouseId: new Types.ObjectId(warehouseId),
        stockType
      });

      return result.deletedCount > 0;
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to delete inventory: ${errorDetails.message}`);
    }
  }

  /**
   * Get warehouse inventory summary
   */
  static async getWarehouseInventorySummary(warehouseId: string | Types.ObjectId) {
    try {
      await connectToDatabase();
      
      logOperation('getWarehouseInventorySummary', 'service', { warehouseId: warehouseId.toString() });

      const summary = await Inventories.aggregate([
        { $match: { warehouseId: new Types.ObjectId(warehouseId) } },
        {
          $group: {
            _id: '$stockType',
            totalQuantity: { $sum: '$quantity' },
            partCount: { $addToSet: '$partId' },
            lastUpdated: { $max: '$lastUpdated' }
          }
        },
        {
          $project: {
            stockType: '$_id',
            totalQuantity: 1,
            partCount: { $size: '$partCount' },
            lastUpdated: 1,
            _id: 0
          }
        },
        { $sort: { stockType: 1 } }
      ]);

      return summary;
    } catch (error) {
      const errorDetails = handleMongoDBError(error);
      throw new Error(`Failed to get warehouse inventory summary: ${errorDetails.message}`);
    }
  }
}

/**
 * Execute atomic stock transfer between two inventory locations
 * This is the core function for internal transfers and process moves
 */
export async function executeAtomicStockTransfer(
  fromParams: StockOperationParams,
  toParams: StockOperationParams
): Promise<{ from: IInventories; to: IInventories }> {
  const session = await mongoose.startSession();

  try {
    logOperation('executeAtomicStockTransfer', 'service', {
      from: {
        partId: fromParams.partId.toString(),
        warehouseId: fromParams.warehouseId.toString(),
        stockType: fromParams.stockType,
        quantity: fromParams.quantity
      },
      to: {
        partId: toParams.partId.toString(),
        warehouseId: toParams.warehouseId.toString(),
        stockType: toParams.stockType,
        quantity: toParams.quantity
      }
    });

    const result = await session.withTransaction(async () => {
      // Decrement from source
      const fromResult = await InventoriesService.decrementStock({
        ...fromParams,
        session
      });

      // Increment to destination
      const toResult = await InventoriesService.incrementStock({
        ...toParams,
        session
      });

      return { from: fromResult, to: toResult };
    });

    logOperation('executeAtomicStockTransfer completed', 'service', {
      fromQuantity: result.from.quantity,
      toQuantity: result.to.quantity
    });

    return result;
  } catch (error: any) {
    logOperation('executeAtomicStockTransfer failed', 'service', { error: error.message });
    throw error;
  } finally {
    await session.endSession();
  }
}

export default InventoriesService;
