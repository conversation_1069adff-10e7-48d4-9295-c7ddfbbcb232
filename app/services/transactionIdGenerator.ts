import connectToDatabase from '../lib/mongodb';
import InventoryTransaction from '@/app/models/inventorytransaction.model';

/**
 * Transaction type abbreviations for human-readable IDs
 */
const TRANSACTION_TYPE_ABBREVIATIONS: Record<string, string> = {
  'stock_in_purchase': 'SIP',
  'stock_out_production': 'SOP',
  'adjustment_cycle_count': 'ACC',
  'stock_in_production': 'SIN',
  'transfer_out': 'TRO',
  'transfer_in': 'TRI',
  'sales_shipment': 'SLS'
};

/**
 * Logger function for tracking transaction ID generation operations
 */
const logOperation = (operation: string, entity: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[TransactionIdGenerator][${timestamp}] ${operation} on ${entity}${details ? ': ' + JSON.stringify(details) : ''}`);
};

/**
 * Generate a human-readable transaction ID based on the local date.
 * Format: [TransactionTypeAbbr]-[YYYYMMDD]-[SequentialNumber]
 * Example: SIP-20250712-001, SOP-20250712-002
 * @param transactionType - The type of transaction
 * @param transactionDate - The date of the transaction (defaults to current date)
 * @returns Promise<string> - The generated transaction ID
 */
export async function generateTransactionId(
  transactionType: string,
  transactionDate: Date = new Date()
): Promise<string> {
  const startTime = Date.now();
  logOperation('GENERATE_START', 'service', { transactionType, transactionDate: transactionDate.toISOString() });

  try {
    // Step 1: Establish database connection
    logOperation('CONNECTING_TO_DATABASE', 'database');
    const dbConnection = await connectToDatabase();
    logOperation('DATABASE_CONNECTION_SUCCESS', 'service', {
      hasMongoose: !!dbConnection.mongoose,
      hasDb: !!dbConnection.db,
      connectionState: dbConnection.mongoose?.connection?.readyState
    });

    // Step 2: Validate transaction type and get abbreviation
    const typeAbbr = TRANSACTION_TYPE_ABBREVIATIONS[transactionType] || 'TXN';
    if (!TRANSACTION_TYPE_ABBREVIATIONS[transactionType]) {
      logOperation('UNKNOWN_TRANSACTION_TYPE', 'service', { transactionType, fallbackAbbr: typeAbbr });
    }

    // Step 3: Format the date using local timezone components to avoid UTC conversion issues
    const year = transactionDate.getFullYear();
    const month = (transactionDate.getMonth() + 1).toString().padStart(2, '0');
    const day = transactionDate.getDate().toString().padStart(2, '0');
    const dateStr = `${year}${month}${day}`;

    // Create the base pattern for today's transactions
    const basePattern = `${typeAbbr}-${dateStr}`;
    logOperation('PATTERN_GENERATED', 'service', { basePattern, dateStr });

    // Step 4: Validate model availability
    if (!InventoryTransaction) {
      throw new Error('InventoryTransaction model is not available');
    }
    logOperation('MODEL_VALIDATION_SUCCESS', 'service', { modelName: 'InventoryTransaction' });

    // Step 5: Build and execute database query
    const regexPattern = `^${basePattern}-\\d{3}$`;
    logOperation('QUERY_START', 'service', { regexPattern, basePattern });

    let lastTransaction;
    try {
      lastTransaction = await (InventoryTransaction as any).findOne({
        transactionId: { $regex: regexPattern }
      })
      .sort({ transactionId: -1 })
      .lean()
      .exec();

      logOperation('QUERY_SUCCESS', 'service', {
        foundTransaction: !!lastTransaction,
        lastTransactionId: lastTransaction?.transactionId
      });
    } catch (queryError: any) {
      logOperation('QUERY_ERROR', 'service', {
        error: queryError.message,
        errorType: queryError.constructor.name,
        regexPattern
      });
      throw new Error(`Database query failed: ${queryError.message}`);
    }

    // Step 6: Calculate sequential number
    let sequentialNumber = 1;

    if (lastTransaction) {
      const lastTransactionId = lastTransaction.transactionId;
      const lastSequentialStr = lastTransactionId.split('-').pop();
      if (lastSequentialStr) {
        const lastSequential = parseInt(lastSequentialStr, 10);
        if (!isNaN(lastSequential)) {
          sequentialNumber = lastSequential + 1;
        }
      }
      logOperation('SEQUENTIAL_CALCULATION', 'service', {
        lastTransactionId,
        lastSequentialStr,
        calculatedSequential: sequentialNumber
      });
    } else {
      logOperation('NO_PREVIOUS_TRANSACTIONS', 'service', { basePattern });
    }

    // Step 7: Format the sequential number with leading zeros (3 digits)
    const sequentialStr = sequentialNumber.toString().padStart(3, '0');

    // Step 8: Generate the final transaction ID
    const transactionId = `${basePattern}-${sequentialStr}`;

    const duration = Date.now() - startTime;
    logOperation('GENERATE_SUCCESS', 'service', {
      transactionId,
      duration: `${duration}ms`,
      sequentialNumber,
      basePattern
    });

    return transactionId;
  } catch (error: any) {
    const duration = Date.now() - startTime;

    // Enhanced error logging with full details
    logOperation('GENERATE_ERROR', 'service', {
      error: error.message,
      errorType: error.constructor.name,
      stack: error.stack,
      duration: `${duration}ms`,
      transactionType,
      transactionDate: transactionDate.toISOString()
    });

    console.error('[TransactionIdGenerator] DETAILED ERROR INFORMATION:');
    console.error('Error Type:', error.constructor.name);
    console.error('Error Message:', error.message);
    console.error('Error Stack:', error.stack);
    console.error('Transaction Type:', transactionType);
    console.error('Transaction Date:', transactionDate.toISOString());
    console.error('Duration before error:', `${duration}ms`);

    // Fallback to a timestamp-based ID if generation fails
    const timestamp = Date.now().toString();
    const typeAbbr = TRANSACTION_TYPE_ABBREVIATIONS[transactionType] || 'TXN';
    const fallbackId = `${typeAbbr}-${timestamp}`;

    logOperation('FALLBACK_ID_GENERATED', 'service', {
      fallbackId,
      originalError: error.message
    });

    return fallbackId;
  }
}

/**
 * Validate if a transaction ID follows the expected format
 * @param transactionId - The transaction ID to validate
 * @returns boolean - True if valid format
 */
export function isValidTransactionId(transactionId: string): boolean {
  // Pattern: [3-letter-abbr]-[8-digit-date]-[3-digit-sequential]
  const pattern = /^[A-Z]{3}-\d{8}-\d{3}$/;
  return pattern.test(transactionId);
}

/**
 * Parse a transaction ID to extract its components
 * @param transactionId - The transaction ID to parse
 * @returns Object with parsed components or null if invalid
 */
export function parseTransactionId(transactionId: string): {
  typeAbbr: string;
  date: string;
  sequential: number;
} | null {
  if (!isValidTransactionId(transactionId)) {
    return null;
  }

  const parts = transactionId.split('-');
  if (parts.length !== 3) {
    return null;
  }

  return {
    typeAbbr: parts[0]!,
    date: parts[1]!,
    sequential: parseInt(parts[2]!, 10)
  };
}

/**
 * Get the full transaction type name from abbreviation
 * @param abbreviation - The transaction type abbreviation
 * @returns The full transaction type name or the abbreviation if not found
 */
export function getTransactionTypeFromAbbreviation(abbreviation: string): string {
  // Create a reverse map for efficient lookup
  const reverseMap = Object.entries(TRANSACTION_TYPE_ABBREVIATIONS)
    .reduce((acc, [key, value]) => {
      acc[value] = key;
      return acc;
    }, {} as Record<string, string>);
  
  return reverseMap[abbreviation] || abbreviation;
}
