import { format } from 'date-fns';
import mongoose, { Query, Types } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { captureException, setTag } from '../lib/logging-utils';
import connectToDatabase from '../lib/mongodb';
import Assembly, { IAssembly, IAssemblyPartRequired } from '../models/assembly.model';
import Batch from '../models/batch.model';
import BatchLog from '../models/batchLog.model';
import Category from '../models/category.model';
import Delivery from '../models/delivery.model';
import { IInventoryTransaction, InventoryTransaction } from '../models/inventorytransaction.model';
import Part, { IPartDocument } from '../models/part.model';
import Product, { IProduct } from '../models/product.model';
import PurchaseOrder from '../models/purchaseOrder.model';
import Setting from '../models/settings.model';
import Supplier, { ISupplier } from '../models/supplier.model';
import User from '../models/user.model';
import Warehouse from '../models/warehouse.model';
import WorkOrder from '../models/workOrder.model';

// Type definitions for common interfaces
interface PaginationOptions {
  page?: number;
  limit?: number;
  sort?: Record<string, 1 | -1>;
  filter?: Record<string, any>;
}

interface PaginationResult {
  totalCount: number;
  totalPages: number;
  currentPage: number;
  limit: number;
}

interface FetchPartsOptions extends PaginationOptions {
  includeSubParts?: boolean;
  includeInventory?: boolean;
}

interface FetchSuppliersOptions extends PaginationOptions {
  includeParts?: boolean;
  partsLimit?: number;
}

interface FetchAssembliesOptions extends PaginationOptions {
  includeParts?: boolean;
}

interface FetchProductsOptions extends PaginationOptions {
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: Record<string, any>;
}

// Logger function for tracking database operations
const logOperation = (operation: string, collection: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[MongoDB][${timestamp}] ${operation} on ${collection}${details ? ': ' + JSON.stringify(details) : ''}`);
};

/**
 * Standardized error handling for MongoDB operations
 * @param error The error object from MongoDB/Mongoose
 * @throws Error with a standardized message
 */
export const handleMongoDBError = (error: any) => { // Added export keyword
  console.error('[MongoDB Error]', error);

  // Set Sentry tags for better filtering
  setTag('error.type', 'database');
  setTag('error.database', 'mongodb');

  // Determine error type and set appropriate tags
  let errorType = 'unknown';
  let errorStatus = 500;
  let errorMessage = '';

  // Check for specific MongoDB error types
  if (error.name === 'ValidationError') {
    // Handle validation errors
    errorType = 'validation';
    errorStatus = 400;
    const validationErrors = Object.values(error.errors)
      .map((err: any) => err.message)
      .join(', ');
    errorMessage = `Validation failed: ${validationErrors}`;

    // Set Sentry tags for validation errors
    setTag('error.subtype', 'validation');
  } else if (error.code === 11000) {
    // Handle duplicate key errors
    errorType = 'duplicate';
    errorStatus = 409;
    errorMessage = `Duplicate entry: A record with this ID already exists`;

    // Set Sentry tags for duplicate key errors
    setTag('error.subtype', 'duplicate_key');
  } else {
    // Handle other errors
    errorType = 'database';
    errorStatus = 500;
    errorMessage = `Database error: ${error.message}`;

    // Set Sentry tags for general database errors
    setTag('error.subtype', 'general');
  }

  // Capture the exception in Sentry
  captureException(error, {
    errorType,
    errorStatus,
    errorMessage
  });

  // Return standardized error response
  return { message: errorMessage, status: errorStatus };
};

// Batch Log Management Functions
export async function addBatchLog(batchLogData: { batchId: string; event: string; userId: string; details?: any; timestamp?: Date }) {
  logOperation('ADD', 'batchLog', { batchId: batchLogData.batchId, event: batchLogData.event, userId: batchLogData.userId });
  try {
    await connectToDatabase();

    const newBatchLog = new BatchLog({
      ...batchLogData,
      timestamp: batchLogData.timestamp || new Date(), // Default to now if not provided
    });

    const savedBatchLog = await newBatchLog.save();
    logOperation('SUCCESS', 'batchLog_creation', { _id: savedBatchLog._id });
    return savedBatchLog.toObject(); // Return plain object for consistency
  } catch (error: any) {
    logOperation('ERROR', 'batchLog_creation', { error: error.message, data: batchLogData });
    // Capture with Sentry via handleMongoDBError and rethrow a simple message for the API layer
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to add batch log.');
  }
}

interface GetBatchLogsOptions {
  page?: number;
  limit?: number;
  sort?: Record<string, 1 | -1>;
}

export async function getBatchLogs(batchId: string, options: GetBatchLogsOptions = {}): Promise<{
  batchLogs: any[];
  pagination: PaginationResult;
  error?: string;
}> {
  const { page = 1, limit = 20, sort = { timestamp: -1 } } = options;
  logOperation('FETCH_ALL', 'batchLogs', { batchId, page, limit, sort: JSON.stringify(sort) });

  try {
    await connectToDatabase();
    const skip = (page - 1) * limit;

    const logs = await (BatchLog as any).find({ batchId })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    const totalCount = await BatchLog.countDocuments({ batchId });

    logOperation('SUCCESS', 'batchLogs_fetch', {
      batchId,
      count: logs.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit),
    });

    return {
      batchLogs: logs,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit,
      },
    };
  } catch (error: any) {
    logOperation('ERROR', 'batchLogs_fetch', { batchId, error: error.message });
    const errDetails = handleMongoDBError(error);
    // Return a structure consistent with successful calls but with empty data
    return {
      batchLogs: [],
      pagination: {
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        limit,
      },
      error: errDetails.message || 'Failed to fetch batch logs.',
    };
  }
}

// Helper interface for populated references with name
interface PopulatedNameRef {
  _id: Types.ObjectId;
  name: string;
}

// Helper interface for populated Warehouse references
interface PopulatedWarehouseRef {
  _id: Types.ObjectId;
  name: string;
  location?: {
    city?: string;
    // other fields if needed by frontend
  };
}

// Helper interface for populated Supplier references
interface PopulatedSupplierRef {
  _id: Types.ObjectId;
  name: string;
  contactInfo?: {
    contactPerson?: string;
    // other fields if needed by frontend
  };
}

// Interface describing the shape of a Part after .lean() and specific .populate() calls in getPart
export interface LeanPopulatedPart {
  _id: Types.ObjectId;
  partNumber: string;
  name: string;
  businessName?: string | null; // NEW FIELD: Human-readable business name for the part
  description?: string | null;
  technicalSpecs?: string | null;
  isManufactured: boolean;
  reorderLevel?: number | null;
  status: 'active' | 'inactive' | 'obsolete';
  inventory: {
    currentStock: number;
    warehouseId: PopulatedWarehouseRef | null; // Populated from Types.ObjectId, becomes null if ref not found
    safetyStockLevel: number;
    maximumStockLevel: number;
    averageDailyUsage: number;
    abcClassification: string;
    lastStockUpdate?: Date | null;
  };
  supplierId?: PopulatedSupplierRef | null; // Optional field, populated if present
  unitOfMeasure: string;
  costPrice: number;
  categoryId?: PopulatedNameRef | null; // Optional field, populated if present
  createdAt?: Date;
  updatedAt?: Date;
}

// Parts Management Functions
export async function fetchParts(options: FetchPartsOptions = {}): Promise<{
  parts: any[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
  error?: string;
}> {
  const {
    page = 1,
    limit = 20,
    sort = { updatedAt: -1 },
    filter = {},
    includeSubParts = false,
    includeInventory = false
  } = options;

  try {
    // Assuming Part model is imported at the top of the file like other models
    // const { Part } = await import('@/app/models'); // Removed dynamic import

    const skip = (page - 1) * limit;

    let selection = '_id partNumber name description technicalSpecs isManufactured reorderLevel status supplierId unitOfMeasure costPrice categoryId createdAt updatedAt inventory';

    const query = Part.find(filter)
      .select(selection)
      .populate('supplierId', 'name contactPerson email phone address')
      .populate('categoryId', 'name description')
      .sort(sort)
      .skip(skip)
      .limit(limit);

    if (includeInventory) {
      query.populate({
        path: 'inventory.warehouseId',
        select: 'name warehouseCode location',
        model: 'Warehouse' // Assumes Warehouse model is imported and available
      });
    }

    // if (includeSubParts) {
    //     // NOTE: Part.model.ts does not currently define a 'subParts' field.
    //     // If this functionality is needed, the schema must be updated.
    //     // Temporarily commenting out to align with current schema and investigate N+1.
    //     // query.populate('subParts'); 
    // }

    const partsFromDb = await query.lean();

    const processedParts = partsFromDb.map((part: any) => {
      const newPart = { ...part };

      if (newPart.supplierId && typeof newPart.supplierId === 'object') {
        newPart.supplier = newPart.supplierId;
        newPart.supplierName = (newPart.supplierId as { name?: string }).name || null;
      } else {
        newPart.supplier = null;
        newPart.supplierName = null;
      }

      if (newPart.categoryId && typeof newPart.categoryId === 'object') {
        newPart.category = newPart.categoryId;
        newPart.categoryName = (newPart.categoryId as { name?: string }).name || null;
      } else {
        newPart.category = null;
        newPart.categoryName = null;
      }

      if (includeInventory && newPart.inventory && newPart.inventory?.warehouseId && typeof newPart.inventory.warehouseId === 'object') {
        newPart.inventory.warehouse = newPart.inventory.warehouseId;
      } else if (newPart.inventory) {
        newPart.inventory.warehouse = null;
      }

      return newPart;
    });

    const total = await Part.countDocuments(filter);
    const pages = Math.ceil(total / limit);

    return {
      parts: processedParts,
      pagination: {
        total,
        page,
        limit,
        pages
      }
    };
  } catch (error: unknown) { // Typed error as unknown for better type safety
    console.error('[Service] Error in fetchParts:', error); // Simplified log message
    let errorMessage = 'Failed to fetch parts';
    if (error instanceof Error) {
        errorMessage = error.message;
    }
    // Use page and limit from the destructured options at the function start
    return {
        parts: [],
        pagination: { total: 0, page, limit, pages: 0 },
        error: errorMessage
    };
  }
}

interface GetPartOptions {
  includeSubParts?: boolean;
  session?: any;
}

export async function getPart(identifier: string, options: GetPartOptions = {}): Promise<LeanPopulatedPart | null> {
  const { includeSubParts = false, session = null } = options;

  logOperation('GET', 'part', { identifier: identifier, includeSubParts });
  try {
    await connectToDatabase();

    let partDocument: LeanPopulatedPart | null = null;
    let queryCriteria: object = {}; // Use 'object' for more general criteria type, initialize

    const baseSelection = '_id partNumber name description technicalSpecs isManufactured reorderLevel status inventory supplierId unitOfMeasure costPrice categoryId createdAt updatedAt';
    const populateOptions = {
      path: 'subParts.partId',
      model: 'Part',
      select: baseSelection // Populate sub-parts with the same base fields
    };

    // Determine if the identifier is likely an ObjectId
    if (mongoose.Types.ObjectId.isValid(identifier)) {
      logOperation('ATTEMPT_FIND_BY_ID', 'part', { _id: identifier, includeSubParts });
      let queryById: Query<IPartDocument | null, IPartDocument, {}, IPartDocument> = Part.findById(identifier);
      if (session) {
        queryById = queryById.session(session);
      }
      
      let currentSelection = baseSelection;
      // if (includeSubParts) {
      //   // NOTE: Part.model.ts does not currently define a 'subParts' field.
      //   // Temporarily commenting out.
      //   // currentSelection += ' subParts isAssembly';
      //   // queryById = queryById.populate(populateOptions);
      // }
      queryById = queryById
        .populate({ path: 'inventory.warehouseId', select: 'name location.city' })
        .populate({ path: 'supplierId', select: 'name contactInfo.contactPerson' })
        .populate({ path: 'categoryId', select: 'name' })
        .select(currentSelection);
      partDocument = await queryById.lean<LeanPopulatedPart>() as LeanPopulatedPart | null;

      if (partDocument) {
        logOperation('SUCCESS_FIND_BY_ID', 'part', { _id: identifier });
      } else {
        // If not found by ID, it might be a partNumber that happens to be a valid ObjectId format
        logOperation('NOT_FOUND_BY_ID_TRY_PARTNUMBER', 'part', { _id_attempt: identifier, partNumber_attempt: identifier });
        queryCriteria = { partNumber: identifier }; // Set criteria for fallback search
      }
    } else {
      // If not a valid ObjectId format, assume it's a partNumber
      logOperation('ASSUME_PARTNUMBER', 'part', { partNumber: identifier });
      queryCriteria = { partNumber: identifier };
    }

    // If not found by ID (or if it wasn't an ObjectId to begin with, or if it was an ObjectId-like partNumber), try by partNumber
    if (!partDocument && Object.keys(queryCriteria).length > 0) { // queryCriteria would be set if a partNumber search is intended
      logOperation('ATTEMPT_FIND_BY_PARTNUMBER', 'part', { criteria: queryCriteria, includeSubParts });
      let queryByPartNumber: Query<IPartDocument | null, IPartDocument, {}, IPartDocument> = Part.findOne(queryCriteria);
      if (session) {
        queryByPartNumber = queryByPartNumber.session(session);
      }
      
      let currentSelectionForPartNumber = baseSelection;
      // if (includeSubParts) {
      //   // NOTE: Part.model.ts does not currently define a 'subParts' field.
      //   // Temporarily commenting out.
      //   // currentSelectionForPartNumber += ' subParts isAssembly';
      //   // queryByPartNumber = queryByPartNumber.populate(populateOptions);
      // }
      queryByPartNumber = queryByPartNumber
        .populate({ path: 'inventory.warehouseId', select: 'name location.city' })
        .populate({ path: 'supplierId', select: 'name contactInfo.contactPerson' })
        .populate({ path: 'categoryId', select: 'name' })
        .select(currentSelectionForPartNumber);
      partDocument = await queryByPartNumber.lean<LeanPopulatedPart>() as LeanPopulatedPart | null;
      
      if (partDocument) {
        logOperation('SUCCESS_FIND_BY_PARTNUMBER', 'part', { criteria: queryCriteria });
      }
    }

    if (!partDocument) {
      logOperation('NOT_FOUND', 'part', { identifier: identifier });
      return null;
    }

    return partDocument;
  } catch (error: any) {
    logOperation('ERROR', 'get_part', { identifier: identifier, error: error.message });
    handleMongoDBError(error); // This function should ideally throw or return a structured error
    return null; // Return null on error, API layer handles 404 or 500 based on this
  }
}

interface CreatePartData {
  _id?: string;
  partNumber: string;
  name: string;
  description?: string;
  technicalSpecs?: string;
  isManufactured?: boolean;
  reorderLevel?: number;
  status?: 'active' | 'inactive' | 'obsolete';
  inventory: {
    currentStock: number;
    warehouseId: string;
    safetyStockLevel: number;
    maximumStockLevel: number;
    averageDailyUsage: number;
    abcClassification: string;
    lastStockUpdate?: Date;
  };
  supplierId?: string;
  unitOfMeasure: string;
  costPrice: number;
  categoryId?: string;
}

export async function addPart(partData: CreatePartData): Promise<IPartDocument | null> {
  logOperation('ADD', 'part', { name: partData.name });
  try {
    logOperation('CONNECT', 'mongoose');
    await connectToDatabase();

    const finalId = partData._id || uuidv4();
    logOperation('PREPARE_CREATE', 'part', { _id: finalId });

    // partData is expected to come from the API layer already structured according to IPart and IInventory (using camelCase)
    // The API layer should ensure `partData.inventory` contains all required fields for IInventory.

    logOperation('CREATE', 'part', { _id: finalId, name: partData.name });
    const newPart = new Part({
      _id: finalId, // Explicitly set the String _id
      partNumber: partData.partNumber, // Added partNumber
      name: partData.name,
      description: partData.description,
      technicalSpecs: partData.technicalSpecs, // Changed from technical_specs
      isManufactured: partData.isManufactured ?? false, // Changed from is_manufactured
      reorderLevel: partData.reorderLevel, // Changed from reorder_level
      status: partData.status || 'active',
      inventory: partData.inventory, // Directly use the validated inventory object from partData
      supplierId: partData.supplierId, // Changed from supplier_id
      unitOfMeasure: partData.unitOfMeasure,
      costPrice: partData.costPrice,
      categoryId: partData.categoryId
      // createdAt and updatedAt are handled by timestamps: true
    });

    const savedPart = await newPart.save();
    logOperation('SUCCESS', 'part_creation', { _id: savedPart._id });

    return savedPart;
  } catch (error: any) {
    logOperation('ERROR', 'part_creation', { error: error.message, stack: error.stack });
    handleMongoDBError(error);
    return null;
  }
}

interface UpdatePartData extends Partial<CreatePartData> {
  technical_specs?: string;
  is_manufactured?: boolean;
  reorder_level?: number;
  supplier_id?: string;
  category?: string;
  currentStock?: number;
  location?: string;
  lastCountDate?: Date;
  current_stock?: number;
}

export async function updatePart(partId: string, partData: UpdatePartData): Promise<IPartDocument | null> {
  logOperation('UPDATE', 'part', { _id: partId });
  try {
    await connectToDatabase();

    const updatePayload: Record<string, any> = { ...partData };
    delete updatePayload._id;

    // Ensure field names in updatePayload match the Mongoose schema (camelCase)
    if (updatePayload.technical_specs !== undefined) {
      updatePayload.technicalSpecs = updatePayload.technical_specs;
      delete updatePayload.technical_specs;
    }
    if (updatePayload.is_manufactured !== undefined) {
      updatePayload.isManufactured = updatePayload.is_manufactured;
      delete updatePayload.is_manufactured;
    }
    if (updatePayload.reorder_level !== undefined) {
      updatePayload.reorderLevel = updatePayload.reorder_level;
      delete updatePayload.reorder_level;
    }
    if (updatePayload.supplier_id !== undefined) {
      updatePayload.supplierId = updatePayload.supplier_id;
      delete updatePayload.supplier_id;
    }
    if (updatePayload.category !== undefined) { // Assuming 'category' might be old field for categoryId
        updatePayload.categoryId = updatePayload.category;
        delete updatePayload.category;
    }

    // Inventory is expected to be passed as a complete object if it's being updated.
    // The Part model (IPart) defines inventory as IInventory.
    // The API layer should send partData.inventory structured correctly.
    if (partData.inventory) {
        updatePayload.inventory = partData.inventory; // Directly assign if provided
    }

    // Remove any legacy flat inventory fields if they were somehow passed
    delete updatePayload.currentStock;
    delete updatePayload.location;
    delete updatePayload.lastCountDate;
    delete updatePayload.current_stock; // also remove snake_case version

    // updatedAt is handled by timestamps: true

    // Update part using its _id
    const part = await Part.findByIdAndUpdate(
      partId,
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run schema validators
    ).lean();

    if (!part) {
      logOperation('UPDATE_NOT_FOUND', 'part', { _id: partId });
      throw new Error(`Part with ID ${partId} not found`);
    }

    logOperation('UPDATE_SUCCESS', 'part', { _id: partId });
    return part;
  } catch (error: any) {
    logOperation('ERROR', 'part', { _id: partId, error: error.message });
    handleMongoDBError(error);
    // This function's success response is a single part object, not paginated.
    // So, the error should not include pagination. Throwing or returning simple error object.
    throw new Error(handleMongoDBError(error).message || `Failed to update part ${partId}`);
    // Or: return { success: false, message: handleMongoDBError(error).message || `Failed to update part ${partId}`, error: handleMongoDBError(error) };
  }
}

export async function deletePart(partId: string) { // partId is the _id (string)
  logOperation('DELETE', 'part', { _id: partId });
  try {
    await connectToDatabase();

    // Check for dependencies in Assemblies
    const assembliesUsingPart = await (Assembly as any).find({ "partsRequired.partId": partId }).limit(1).lean();
    if (assembliesUsingPart.length > 0) {
      const message = `Part with ID ${partId} is used in one or more assemblies (e.g., Assembly code: ${assembliesUsingPart[0].assemblyCode}) and cannot be deleted.`;
      logOperation('CONFLICT', 'delete_part_dependency', { _id: partId, assembly: assembliesUsingPart[0]._id });
      return { success: false, message, statusCode: 409 };
    }

    // Check for dependencies in Purchase Orders
    // Assuming PurchaseOrder model is imported and 'items.partId' is the correct path
    const purchaseOrdersUsingPart = await (PurchaseOrder as any).find({ "items.partId": partId }).limit(1).lean();
    if (purchaseOrdersUsingPart.length > 0) {
      const poIdentifier = purchaseOrdersUsingPart[0].po_number || purchaseOrdersUsingPart[0]._id.toString();
      const message = `Part with ID ${partId} is referenced in one or more purchase orders (e.g., PO: ${poIdentifier}) and cannot be deleted.`;
      logOperation('CONFLICT', 'delete_part_dependency_po', { _id: partId, purchaseOrder: purchaseOrdersUsingPart[0]._id });
      return { success: false, message, statusCode: 409 };
    }

    // Check for dependencies in Products (Bill of Materials)
    const productsUsingPart = await (Product as any).find({
      "billOfMaterials.componentId": partId,
      "billOfMaterials.componentType": "Part"
    }).limit(1).lean();
    if (productsUsingPart.length > 0) {
      const productIdentifier = productsUsingPart[0].productCode || productsUsingPart[0]._id.toString();
      const message = `Part with ID ${partId} is used in one or more products (e.g., Product code: ${productIdentifier}) and cannot be deleted.`;
      logOperation('CONFLICT', 'delete_part_dependency_product', { _id: partId, product: productsUsingPart[0]._id });
      return { success: false, message, statusCode: 409 };
    }

    // Check for dependencies in Work Orders
    // Assuming WorkOrder model is imported and 'items.partId' or similar is the correct path
    const workOrdersUsingPart = await (WorkOrder as any).find({
      $or: [
        { partIdToManufacture: partId },
        { "billOfMaterialsSnapshot.partId": partId }
      ]
    }).limit(1).lean();
    if (workOrdersUsingPart.length > 0) {
      const woIdentifier = workOrdersUsingPart[0].woNumber || workOrdersUsingPart[0]._id.toString();
      const message = `Part with ID ${partId} is referenced in one or more work orders (e.g., WO: ${woIdentifier}) and cannot be deleted.`;
      logOperation('CONFLICT', 'delete_part_dependency_wo', { _id: partId, workOrder: workOrdersUsingPart[0]._id });
      return { success: false, message, statusCode: 409 };
    }

    // Check for dependencies in Inventory Transactions
    const transactionsForPart = await (InventoryTransaction as any).find({
      partId: partId,
      itemType: 'Part'
    }).limit(1).lean();
    if (transactionsForPart.length > 0) {
      const transactionIdentifier = transactionsForPart[0]._id.toString(); // Or another meaningful field if available
      const message = `Part with ID ${partId} has existing inventory transactions (e.g., Transaction ID: ${transactionIdentifier}) and cannot be deleted. Consider marking as inactive instead.`;
      logOperation('CONFLICT', 'delete_part_dependency_transaction', { _id: partId, transaction: transactionsForPart[0]._id, itemType: 'Part' });
      return { success: false, message, statusCode: 409 };
    }

    const result = await Part.findByIdAndDelete(partId);
    if (!result) {
      logOperation('NOT_FOUND', 'part', { _id: partId });
      return { success: false, message: `Part with ID ${partId} not found`, statusCode: 404 };
    }

    logOperation('SUCCESS', 'delete_part', { _id: partId });
    return { success: true, message: `Part with ID ${partId} deleted successfully` };
  } catch (error: any) {
    logOperation('ERROR', 'part', { _id: partId, error: error.message });
    handleMongoDBError(error);
    return { success: false, message: `Error deleting part: ${error.message}`, statusCode: 500 }; // Return error message with statusCode
  }
}

// Define an interface for the structure returned by searchParts
interface ISearchPartsResult {
  parts: {
    _id: string;
    partNumber?: string;
    name: string;
    businessName?: string | null; // NEW FIELD: Human-readable business name for the part
    description?: string | null;
    technicalSpecs?: string | null; // Using camelCase
    status?: string;
    reorderLevel?: number | null; // Using camelCase
    isManufactured?: boolean;
    isAssembly?: boolean; // Added field to identify if part is an assembly
    supplierId?: string;
    unitOfMeasure?: string;
    costPrice?: number;
    categoryId?: string;
    inventory?: {
      currentStock?: number;
      warehouseId?: string;
      safetyStockLevel?: number;
      maximumStockLevel?: number;
      averageDailyUsage?: number;
      abcClassification?: string;
      lastStockUpdate?: Date;
      // Not including location as it's not in the schema
    };
    createdAt?: Date;
    updatedAt?: Date;
  }[];
  pagination: {
    totalCount: number;
    totalPages: number;
    currentPage: number;
    limit: number;
  };
}

// Helper function to escape special characters for RegExp
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& means the whole matched string
}

// Add explicit return type annotation
export async function searchParts(options: any = {}): Promise<ISearchPartsResult> {
  const {
    query = '', // General search query string
    page = 1,
    limit = 20,
    filter = {}, // Specific field filters
    sort = { updatedAt: -1 } // Default sort
    // Removed 'fields' option as search logic is now more specific
  } = options;

  logOperation('SEARCH', 'parts', { query, page, limit, filter: JSON.stringify(filter), sort: JSON.stringify(sort) });

  try {
    // Connect using Mongoose for model operations
    await connectToDatabase();

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // --- Build Combined Query ---
    let finalQuery: any = {};
    const conditions = [];

    // Add specific field filters if any
    if (filter && Object.keys(filter).length > 0) {
      conditions.push(filter);
    }

    // Trim the query and check if it's non-empty
    const trimmedQuery = query ? query.trim() : '';

    // Add regex-based search if trimmedQuery is provided
    if (trimmedQuery) {
      const escapedQuery = escapeRegExp(trimmedQuery);
      const searchRegex = new RegExp(escapedQuery, 'i'); // Case-insensitive regex with escaped query
      const searchableFields = ['partNumber', 'name', 'description', 'technicalSpecs'];
      const regexConditions = searchableFields.map((field: string) => ({ [field]: searchRegex }));
      conditions.push({ $or: regexConditions }); // Search for query in any of the specified fields
    }

    // Construct the final query based on the conditions
    if (conditions.length > 1) {
      finalQuery = { $and: conditions }; // Combine all conditions if multiple exist
    } else if (conditions.length === 1) {
      finalQuery = conditions[0]; // Use the single condition
    } else {
      // No query and no filter, or empty query and no filter, fetch all documents (respecting pagination)
      finalQuery = {};
    }

    logOperation('DEBUG', 'search_parts_query', { finalQuery: JSON.stringify(finalQuery) });

    // Use a more optimized query approach
    // Execute search with pagination, combined query, and sorting
    const parts = await (Part as any).find(finalQuery) // Use finalQuery
      .sort(sort)
      .skip(skip)
      .limit(limit)
      // Select fields based on the updated Part schema with camelCase field names
      .select('_id partNumber name description technicalSpecs status inventory reorderLevel createdAt updatedAt isManufactured supplierId unitOfMeasure costPrice categoryId isAssembly')
      // Ensure lean() is used to improve performance
      .lean();

    // Get total count for pagination info using the combined query - more efficiently
    const totalCount = await Part.countDocuments(finalQuery).exec(); // Use finalQuery

    // Enhanced logging for debugging search results
    console.log(`[MongoDBService-searchParts] Executed query: ${JSON.stringify(finalQuery)}`);
    console.log(`[MongoDBService-searchParts] Found ${parts.length} parts with this query.`);
    console.log(`[MongoDBService-searchParts] Total matching documents in collection (ignoring pagination): ${totalCount}`);
    if (parts.length > 0 && parts.length <= 5) { // Log first few parts if found, up to 5
        console.log(`[MongoDBService-searchParts] First few parts returned (up to 5): ${JSON.stringify(parts.slice(0,5).map((p: any) => ({ _id: p._id, name: p.name, partNumber: p.partNumber }))) }`);
    } else if (parts.length > 5) {
        console.log(`[MongoDBService-searchParts] More than 5 parts found, logging only the count.`);
    }

    logOperation('SEARCH_SUCCESS', 'parts', {
      query,
      filter: JSON.stringify(filter),
      count: parts.length,
      totalCount
    });

    return {
      parts,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'search_parts', { query, filter: JSON.stringify(filter), error: error.message });
    handleMongoDBError(error);
    // Ensure function returns in case of error with the correct structure
    return { parts: [], pagination: { totalCount: 0, totalPages: 0, currentPage: page, limit } };
  }
}

// Product Management Functions
export async function getProduct(productCode: string): Promise<any | null> {
  logOperation('GET', 'product', { productCode });
  try {
    await connectToDatabase();

    // Try to find by _id first if it looks like a MongoDB ObjectId
    if (mongoose.Types.ObjectId.isValid(productCode)) {
      const product = await (Product as any).findById(productCode)
        .populate('main_assembly_id')
        .lean();

      if (product) {
        logOperation('SUCCESS', 'get_product_by_id', { _id: productCode });
        return product;
      }
    }

    // Otherwise search by product_id field
    const product = await (Product as any).findOne({ product_id: productCode })
      .populate('main_assembly_id')
      .lean();

    if (!product) {
      logOperation('NOT_FOUND', 'product', { productCode });
      return null;
    }

    logOperation('SUCCESS', 'get_product', { productCode });
    return product;
  } catch (error: any) {
    logOperation('ERROR', 'get_product', { productCode, error: error.message });
    handleMongoDBError(error);
    return null; // Return null on error
  }
}

export async function updateProduct(productCode: string, updateData: Partial<IProduct>): Promise<any | null> {
  logOperation('UPDATE', 'product', { productCode });
  try {
    await connectToDatabase();

    // Try to find by _id first if it looks like a MongoDB ObjectId
    let product;
    if (mongoose.Types.ObjectId.isValid(productCode)) {
      product = await (Product as any).findById(productCode);
      if (!product) {
        product = await (Product as any).findOne({ product_id: productCode });
      }
    } else {
      product = await (Product as any).findOne({ product_id: productCode });
    }

    if (!product) {
      logOperation('UPDATE_NOT_FOUND', 'product', { productCode });
      throw new Error(`Product with code ${productCode} not found`);
    }

    // Update the product fields
    Object.keys(updateData).forEach(key => {
      (product as any)[key] = (updateData as any)[key];
    });

    // Save and return the updated product
    const updatedProduct = await product.save();
    logOperation('UPDATE_SUCCESS', 'product', { productCode });
    return updatedProduct;
  } catch (error: any) {
    logOperation('ERROR', 'update_product', { productCode, error: error.message });
    handleMongoDBError(error);
    return null; // Return null on error
  }
}

export async function deleteProduct(productCode: string): Promise<{ success: boolean; message: string }> {
  logOperation('DELETE', 'product', { productCode });
  try {
    await connectToDatabase();

    // Try to delete by _id first if it looks like a MongoDB ObjectId
    let result;
    if (mongoose.Types.ObjectId.isValid(productCode)) {
      result = await (Product as any).findByIdAndDelete(productCode);
      if (result) {
        logOperation('DELETE_SUCCESS', 'product', { _id: productCode });
        return { success: true, message: `Product ${productCode} deleted successfully` };
      }
    }

    // Otherwise delete by product_id field
    result = await (Product as any).findOneAndDelete({ product_id: productCode });

    if (!result) {
      logOperation('DELETE_NOT_FOUND', 'product', { productCode });
      throw new Error(`Product with code ${productCode} not found`);
    }

    logOperation('DELETE_SUCCESS', 'product', { productCode });
    return { success: true, message: `Product ${productCode} deleted successfully` };
  } catch (error: any) {
    logOperation('ERROR', 'delete_product', { productCode, error: error.message });
    handleMongoDBError(error);
    return { success: false, message: `Error deleting product: ${error.message}` }; // Return error message
  }
}

export async function fetchProducts(options: FetchProductsOptions = {}): Promise<{
  products: any[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
} | null> {
  const {
    page = 1,
    limit = 10,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    search = '',
    filters = {}
  } = options;

  logOperation('FETCH_ALL', 'products', { page, limit, sortBy, sortOrder, search });

  try {
    await connectToDatabase();
    const skip = (page - 1) * limit;

    // Build the query
    let query: any = {};

    // Add search functionality
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { product_id: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Add any additional filters
    Object.keys(filters).forEach(key => {
      if (filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
        query[key] = filters[key];
      }
    });

    // Execute the query
    const sortDirection = sortOrder === 'asc' ? 1 : -1;
    const products = await (Product as any).find(query)
      .sort({ [sortBy]: sortDirection })
      .skip(skip)
      .limit(limit)
      .populate('main_assembly_id')
      .lean();

    // Get total count for pagination
    const total = await Product.countDocuments(query);

    logOperation('SUCCESS', 'fetch_products', { count: products.length, total });

    return {
      products,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  } catch (error: any) {
    logOperation('ERROR', 'fetch_products', { error: error.message });
    handleMongoDBError(error);
    return null;
  }
}

export async function addProduct(productData: Partial<IProduct>): Promise<any | null> {
  logOperation('ADD', 'product', { name: productData.name });
  try {
    await connectToDatabase();

    const newProduct = new Product(productData);
    const savedProduct = await newProduct.save();

    logOperation('SUCCESS', 'add_product', { _id: savedProduct._id });
    return savedProduct;
  } catch (error: any) {
    logOperation('ERROR', 'add_product', { error: error.message });
    handleMongoDBError(error);
    return null;
  }
}

// Supplier Management Functions
export async function fetchSuppliers(options: FetchSuppliersOptions = {}): Promise<{
  suppliers: any[];
  pagination: PaginationResult;
} | null> {
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 },
    filter = {},
    includeParts = false,
    partsLimit = 10
  } = options;
  logOperation('FETCH_ALL', 'suppliers', { page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter) });
  try {
    // Connect using Mongoose for model operations
    await connectToDatabase();
    const skip = (page - 1) * limit;

    // Fetch suppliers with pagination and select fields based on updated schema
    const suppliers = await (Supplier as any).find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('supplier_id name contactPerson email phone address specialty rating createdAt') // Select updated fields
      .lean();

    const totalCount = await Supplier.countDocuments(filter);

    logOperation('SUCCESS', 'suppliers', { count: suppliers.length, totalCount, page, totalPages: Math.ceil(totalCount / limit) });

    return {
      suppliers,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'suppliers', { error: error.message });
    handleMongoDBError(error);
    return null;
  }
}

interface CreateSupplierData {
  supplier_id?: string;
  name: string;
  contactPerson?: string;
  email?: string;
  phone?: string;
  address?: string;
  specialty?: string[];
  rating?: number;
}

export async function addSupplier(supplierData: CreateSupplierData): Promise<any | null> {
  logOperation('ADD', 'supplier', { name: supplierData.name });
  try {
    await connectToDatabase();

    // Use provided supplier_id or generate one
    const supplier_id = supplierData.supplier_id || uuidv4();
    logOperation('PREPARE_CREATE', 'supplier', { supplier_id });

    // Create supplier based on the new schema
    const newSupplier = new Supplier({
      supplier_id: supplier_id, // Ensure unique identifier is set
      name: supplierData.name,
      contactPerson: supplierData.contactPerson,
      email: supplierData.email,
      phone: supplierData.phone,
      address: supplierData.address,
      specialty: supplierData.specialty || [], // Default to empty array
      rating: supplierData.rating,
      // createdAt is handled by timestamps: true
      // Removed is_active field
    });

    const savedSupplier = await newSupplier.save();
    logOperation('SUCCESS', 'supplier_creation', { _id: savedSupplier._id, supplier_id: savedSupplier.supplier_id });
    return savedSupplier;
  } catch (error) {
    handleMongoDBError(error);
  } // Corrected: Removed extra closing brace if any, ensured try has a catch
}

export async function updateSupplier(supplierId: string, supplierData: any) { // supplierId is the unique string identifier
  logOperation('UPDATE', 'supplier', { supplier_id: supplierId });
  try {
    await connectToDatabase();

    // Find the supplier by ID (could be ObjectId or supplier_id field)
    let supplier;
    if (mongoose.Types.ObjectId.isValid(supplierId)) {
      supplier = await (Supplier as any).findById(supplierId);
      if (!supplier) {
        supplier = await (Supplier as any).findOne({ supplier_id: supplierId });
      }
    } else {
      supplier = await (Supplier as any).findOne({ supplier_id: supplierId });
    }

    if (!supplier) {
      logOperation('NOT_FOUND', 'supplier', { supplier_id: supplierId });
      throw new Error(`Supplier with ID ${supplierId} not found`);
    }

    // Update only the fields that are provided
    Object.keys(supplierData).forEach(key => {
      if (supplierData[key] !== undefined) {
        supplier[key] = supplierData[key];
      }
    });

    // Save the updated supplier
    const updatedSupplier = await supplier.save();
    logOperation('SUCCESS', 'update_supplier', { supplier_id: supplierId });
    return updatedSupplier;
  } catch (error: any) {
    logOperation('ERROR', 'supplier', { supplier_id: supplierId, error: error.message });
    handleMongoDBError(error);
    return null; // Return null on error
  }
}

export async function deleteSupplier(supplierIdOrBusinessKey: string) {
  logOperation('DELETE', 'supplier', { identifier: supplierIdOrBusinessKey });
  try {
    await connectToDatabase();

    // Step 1: Find the supplier to get its ObjectId, regardless of input type
    let supplierToDelete: ISupplier | null = null;
    let supplierObjectId: mongoose.Types.ObjectId | null = null;

    if (mongoose.Types.ObjectId.isValid(supplierIdOrBusinessKey)) {
      supplierToDelete = await (Supplier as any).findById(supplierIdOrBusinessKey).lean();
      if (supplierToDelete) {
        supplierObjectId = supplierToDelete._id;
      }
    }

    if (!supplierToDelete) { // If not found by ObjectId or input was not an ObjectId
      supplierToDelete = await (Supplier as any).findOne({ supplier_id: supplierIdOrBusinessKey }).lean();
      if (supplierToDelete) {
        supplierObjectId = supplierToDelete._id;
      }
    }

    if (!supplierToDelete || !supplierObjectId) {
      logOperation('NOT_FOUND', 'supplier', { identifier: supplierIdOrBusinessKey });
      return { success: false, message: `Supplier with identifier ${supplierIdOrBusinessKey} not found`, statusCode: 404 };
    }

    // Step 2: Check for dependencies in Parts
    const partsUsingSupplier = await (Part as any).find({ supplierId: supplierObjectId }).limit(1).lean();
    if (partsUsingSupplier.length > 0) {
      const message = `Supplier ${supplierToDelete.name} (ID: ${supplierObjectId}) is linked to one or more parts (e.g., Part Number: ${partsUsingSupplier[0].partNumber}) and cannot be deleted.`;
      logOperation('CONFLICT', 'delete_supplier_dependency', { _id: supplierObjectId, part: partsUsingSupplier[0]._id });
      return { success: false, message, statusCode: 409 };
    }

    // Step 3: Perform the deletion using ObjectId for consistency
    const result = await (Supplier as any).findByIdAndDelete(supplierObjectId);

    if (!result) {
      // This case should ideally not be reached if supplierToDelete was found, but as a safeguard:
      logOperation('NOT_FOUND', 'supplier_on_delete_attempt', { _id: supplierObjectId });
      return { success: false, message: `Supplier with ID ${supplierObjectId} not found during delete attempt`, statusCode: 404 };
    }

    logOperation('SUCCESS', 'delete_supplier', { _id: supplierObjectId });
    return { success: true, message: `Supplier ${supplierToDelete.name} (ID: ${supplierObjectId}) deleted successfully` };
  } catch (error: any) {
    logOperation('ERROR', 'delete_supplier', { identifier: supplierIdOrBusinessKey, error: error.message });
    handleMongoDBError(error);
    return { success: false, message: `Error deleting supplier: ${error.message}`, statusCode: 500 };
  }
}

export async function getSupplier(supplierId: string) { // supplierId is the unique string identifier
  logOperation('GET', 'supplier', { supplier_id: supplierId });
  try {
    await connectToDatabase();
    const supplier = await (Supplier as any).findOne({ supplier_id: supplierId })
      // Select fields based on updated Supplier schema
      .select('supplier_id name contactPerson email phone address specialty rating createdAt updatedAt')
      .lean();

    if (!supplier) {
      logOperation('NOT_FOUND', 'supplier', { supplier_id: supplierId });
      // Return null if not found, let the API layer handle the 404
      return null;
    }
    logOperation('SUCCESS', 'get_supplier', { supplier_id: supplierId });
    return supplier;
  } catch (error: any) {
    logOperation('ERROR', 'get_supplier', { supplier_id: supplierId, error: error.message });
    // Re-throw error after logging and standardizing (handleMongoDBError throws)
    handleMongoDBError(error);
    // This line might not be reached if handleMongoDBError always throws, but added for type safety
    throw error;
  }
}

// Purchase Order Functions
export async function fetchPurchaseOrders(options: any = {}) {
  const { // Destructure options with defaults
    page = 1,
    limit = 20,
    sort = { order_date: -1 },
    filter = {}
  } = options;
  logOperation('FETCH_ALL', 'purchaseOrders', options);
  try {
    await connectToDatabase();
    const skip = (page - 1) * limit;
    const purchaseOrders = await (PurchaseOrder as any).find(filter)
      .populate('supplier_id', 'name contactPerson email')
      .populate('created_by', 'username name email')
      .populate({ path: 'items.item_id', model: 'Part', select: 'partNumber name description unitOfMeasure' })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
    const totalCount = await PurchaseOrder.countDocuments(filter);
    logOperation('SUCCESS', 'purchaseorders', { count: purchaseOrders.length, totalCount, page, totalPages: Math.ceil(totalCount / limit) });
    return {
      purchaseOrders,
      pagination: { totalCount, totalPages: Math.ceil(totalCount / limit), currentPage: page, limit }
    };
  } catch (error: any) {
    logOperation('ERROR', 'purchaseorders', { error: error.message });
    const errDetails = handleMongoDBError(error);
    // Use destructured page and limit for consistent error return
    return { purchaseOrders: [], pagination: { totalCount: 0, totalPages: 0, currentPage: page, limit }, error: errDetails.message || 'Failed to fetch purchase orders' };
  }
}

export async function getPurchaseOrder(poNumber: string) { // Find by poNumber (string)
  logOperation('GET', 'purchase_order', { poNumber });
  try {
    await connectToDatabase();

    // Try by _id first if it looks like a MongoDB ObjectId
    if (mongoose.Types.ObjectId.isValid(poNumber)) {
      const po = await (PurchaseOrder as any).findById(poNumber)
        .populate('supplier_id')
        .populate({ path: 'items.item_id', model: 'Part', select: 'partNumber name description unitOfMeasure' })
        .lean();

      if (po) {
        logOperation('SUCCESS', 'get_purchaseorder_by_id', { _id: poNumber });
        return po;
      }
    }

    // Otherwise try by poNumber field
    const po = await (PurchaseOrder as any).findOne({ poNumber })
      .populate('supplierId')
      .populate({ path: 'items.item_id', model: 'Part', select: 'partNumber name description unitOfMeasure' })
      .lean();

    if (!po) {
      logOperation('NOT_FOUND', 'purchase_order', { poNumber });
      return null;
    }

    logOperation('SUCCESS', 'get_purchaseorder', { poNumber });
    return po;
  } catch (error: any) {
    logOperation('ERROR', 'get_purchaseorder', { poNumber: poNumber, error: error.message });
    handleMongoDBError(error);
    return null; // Return null on error
  }
}

export async function createPurchaseOrder(poData: any) {
  logOperation('CREATE', 'purchase_order', { poNumber: poData.poNumber });
  try {
    await connectToDatabase();

    // Create the purchase order with the provided data
    const newPO = new PurchaseOrder(poData);
    const savedPO = await newPO.save();

    logOperation('SUCCESS', 'purchase_order_creation', { poNumber: savedPO.poNumber });
    return savedPO;
  } catch (error: any) {
    logOperation('ERROR', 'purchaseorder_creation', { poNumber: poData.poNumber, error: error.message });
    handleMongoDBError(error);
    return null; // Return null on error
  }
}

// Alias for createPurchaseOrder to maintain backward compatibility
export const addPurchaseOrder = createPurchaseOrder;

export async function updatePurchaseOrder(poNumber: string, poData: any) { // Find by poNumber
  logOperation('UPDATE', 'purchase_order', { poNumber });
  try {
    await connectToDatabase();

    // Find the purchase order
    let po;
    if (mongoose.Types.ObjectId.isValid(poNumber)) {
      po = await (PurchaseOrder as any).findById(poNumber);
      if (!po) {
        po = await (PurchaseOrder as any).findOne({ poNumber });
      }
    } else {
      po = await (PurchaseOrder as any).findOne({ poNumber });
    }

    if (!po) {
      logOperation('NOT_FOUND', 'purchase_order', { poNumber });
      throw new Error(`Purchase order ${poNumber} not found`);
    }

    // Update fields
    Object.keys(poData).forEach(key => {
      po[key] = poData[key];
    });

    // Save updated PO
    const updatedPO = await po.save();
    logOperation('SUCCESS', 'update_purchaseorder', { poNumber });
    return updatedPO;
  } catch (error: any) {
    logOperation('ERROR', 'update_purchaseorder', { poNumber: poNumber, error: error.message });
    handleMongoDBError(error);
    return null; // Return null on error
  }
}

export async function deletePurchaseOrder(poNumber: string) { // Find by poNumber
  logOperation('DELETE', 'purchase_order', { poNumber });
  try {
    await connectToDatabase();

    // Try delete by _id first
    let result;
    if (mongoose.Types.ObjectId.isValid(poNumber)) {
      result = await (PurchaseOrder as any).findByIdAndDelete(poNumber);
      if (result) {
        logOperation('SUCCESS', 'delete_purchaseorder', { _id: poNumber });
        return { success: true, message: `Purchase order ${poNumber} deleted successfully` };
      }
    }

    // Try by poNumber field
    result = await (PurchaseOrder as any).findOneAndDelete({ poNumber });

    if (!result) {
      logOperation('NOT_FOUND', 'purchase_order', { poNumber });
      throw new Error(`Purchase order ${poNumber} not found`);
    }

    logOperation('SUCCESS', 'delete_purchaseorder', { poNumber });
    return { success: true, message: `Purchase order ${poNumber} deleted successfully` };
  } catch (error: any) {
    logOperation('ERROR', 'delete_purchaseorder', { poNumber: poNumber, error: error.message });
    handleMongoDBError(error);
    return { success: false, message: `Error deleting purchase order: ${error.message}` }; // Return error message
  }
}

// Inventory Transactions
export async function recordTransaction(transactionData: {
  partId: string;
  warehouseId: string; // Added warehouseId
  userId: string; // Added userId
  transactionType: 'stock_in' | 'stock_out' | 'adjustment' | 'initial'; // Legacy types for internal mapping
  quantity: number; // For stock_in/out: actual change. For adjustment/initial: IS the new stock level.
  transactionDate?: Date;
  referenceNumber: string;
  notes?: string;
  originalSqlId?: any; // Will be ignored for the new model
  session?: mongoose.ClientSession; // Optional session for atomicity
}): Promise<IInventoryTransaction | undefined> {
  const { partId, warehouseId, userId, transactionType, quantity, referenceNumber } = transactionData; // Added warehouseId, userId
  logOperation('RECORD_TRANSACTION', 'transaction', { partId, warehouseId, userId, transactionType, quantity, referenceNumber }); // Added warehouseId, userId to log

  const session = transactionData.session || await mongoose.startSession();
  const ownsSession = !transactionData.session; // Track if we created the session

  try {
    await connectToDatabase(); // Ensure connection

    // Start transaction if we own the session
    if (ownsSession) {
      session.startTransaction();
      logOperation('START_TRANSACTION', 'transaction', { partId });
    }

    // 1. Find the part
    const part = await Part.findById(partId).session(session);
    if (!part) {
      throw new Error(`Part with ID ${partId} not found`);
    }

    // 2. Determine previous and new stock levels
    const previousStock = part.inventory?.currentStock ?? 0; // Assuming 'currentStock' field in inventory object
    let newStock: number;
    let changeQuantity: number; // The quantity value to store in the transaction record

    switch (transactionType) {
      case 'stock_in':
        newStock = previousStock + quantity;
        changeQuantity = quantity; // Positive quantity for stock in
        break;
      case 'stock_out':
        newStock = previousStock - quantity; // Subtract positive quantity for stock out
        changeQuantity = -quantity; // Store negative quantity for stock out
        if (newStock < 0) {
            // Optional: Prevent stock going negative, or allow based on business rules
            // throw new Error(`Insufficient stock for part ${partId}. Required: ${quantity}, Available: ${previousStock}`);
            logOperation('WARN', 'transaction', { partId, message: 'Stock level went negative' });
        }
        break;
      case 'adjustment':
      case 'initial':
        // For adjustment/initial, the provided quantity IS the new stock level
        newStock = quantity;
        changeQuantity = newStock - previousStock; // Calculate the effective change
        break;
      default:
        throw new Error(`Invalid transaction type: ${transactionType}`);
    }

    // 3. Update the Part's inventory
    // Assuming inventory field exists and has a currentStock subfield
    const updatedPart = await Part.findByIdAndUpdate(
      partId,
      { $set: { 'inventory.currentStock': newStock } }, // Update the nested field
      { new: true, session: session }
    );

    if (!updatedPart) {
        throw new Error(`Failed to update inventory for part ${partId}`);
    }
    logOperation('UPDATE_INVENTORY', 'part', { partId, previousStock, newStock });


    // 4. Create and save the transaction record
    // Map legacy transaction type to canonical InventoryTransactionType
    let canonicalTransactionType: IInventoryTransaction['transactionType'];
    let inventoryTxQuantity: number; // Quantity for the InventoryTransaction record (positive magnitude)

    switch (transactionType) {
      case 'stock_in':
        canonicalTransactionType = 'purchase_receipt'; // Map to current transaction type
        inventoryTxQuantity = quantity; // Input quantity is positive
        break;
      case 'stock_out':
        canonicalTransactionType = 'sales_shipment';
        inventoryTxQuantity = quantity; // Input quantity is positive, representing amount removed
        break;
      case 'adjustment':
      case 'initial':
        canonicalTransactionType = 'adjustment';
        inventoryTxQuantity = Math.abs(newStock - previousStock);
        break;
      default:
        // Should be caught by earlier switch, but as a safeguard:
        throw new Error(`Invalid transaction type for mapping: ${transactionType}`);
    }

    // 4. Create and save the transaction record
    const newInventoryTransaction = new InventoryTransaction({
      partId: partId,
      transactionType: canonicalTransactionType,
      quantity: inventoryTxQuantity, // Store the positive magnitude of items transacted
      previousStock: previousStock,
      newStock: newStock,
      transactionDate: transactionData.transactionDate || new Date(),
      referenceNumber: transactionData.referenceNumber,
      notes: transactionData.notes,
      warehouseId: warehouseId, // Added warehouseId
      userId: userId, // Added userId
      // originalSqlId is not part of InventoryTransaction schema and will be omitted
      // schemaVersion is handled by model default
      // createdAt/updatedAt handled by timestamps
    });

    const savedTransaction = await newInventoryTransaction.save({ session: session });
    logOperation('SAVE_TRANSACTION', 'inventory_transaction', { _id: savedTransaction._id, partId });

    // Commit transaction if we own the session
    if (ownsSession) {
      await session.commitTransaction();
      logOperation('COMMIT_TRANSACTION', 'transaction', { partId });
    }

    return savedTransaction.toObject() as IInventoryTransaction;

  } catch (error: any) {
    // Abort transaction on error if we own the session
    if (ownsSession) {
      await session.abortTransaction();
      logOperation('ABORT_TRANSACTION', 'transaction', { partId, error: error.message });
    }
    logOperation('ERROR', 'record_transaction', { partId, error: error.message });
    handleMongoDBError(error); // Moved inside the catch block
    return undefined; // Return undefined on error
  } finally {
    // End session if we own it
    if (ownsSession) {
      session.endSession();
      logOperation('END_SESSION', 'transaction', { partId });
    }
  }
}

export async function getTransactionHistory(partId: string, options: any = {}): Promise<{ transactions: IInventoryTransaction[], pagination: any }> { // partId is the string _id of the Part
  const {
    page = 1,
    limit = 50, // Default to more transactions per page
    sort = { transactionDate: -1 } // Default sort by date descending
  } = options;
  logOperation('GET_TRANSACTION_HISTORY', 'transaction', { partId, page, limit });
  try {
    await connectToDatabase();
    const skip = (page - 1) * limit;

    // Get transactions for this part using the string partId directly
    const transactions = await (InventoryTransaction as any).find({ partId: partId })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      // Select fields relevant to InventoryTransaction schema
      .select('partId transactionType quantity previousStock newStock transactionDate referenceNumber notes createdAt userId locationId') // Added userId, locationId if available
      // Removed populate for user_id as it's not in the schema, but userId is a direct field now
      .lean();

     const totalCount = await InventoryTransaction.countDocuments({ partId: partId });

     logOperation('SUCCESS', 'get_transaction_history', { partId, count: transactions.length, totalCount });

    return {
        transactions,
        pagination: {
            totalCount,
            totalPages: Math.ceil(totalCount / limit),
            currentPage: page,
            limit
        }
    };
  } catch (error: any) {
    logOperation('ERROR', 'get_transaction_history', { partId, error: error.message });
    handleMongoDBError(error);
    // Return empty result on error
    return { transactions: [] as IInventoryTransaction[], pagination: { totalCount: 0, totalPages: 0, currentPage: page, limit } };
  }
}

// --- Assembly Management Functions ---

// Interface for fetchAssemblies options
interface FetchAssembliesOptions {
  page?: number;
  limit?: number;
  sort?: Record<string, 1 | -1>;
  filter?: Record<string, any>;
  includeParts?: boolean; // Option to populate parts details
}

// Interface for the structure returned by fetchAssemblies
interface FetchAssembliesResult {
  assemblies: IAssembly[];
  pagination: {
    totalCount: number;
    totalPages: number;
    currentPage: number;
    limit: number;
  };
}

/**
 * Fetches a list of assemblies with pagination, sorting, filtering, and optional part population.
 * @param options - Options for fetching assemblies.
 * @returns An object containing the list of assemblies and pagination info.
 */
export async function fetchAssemblies(options: FetchAssembliesOptions = {}): Promise<FetchAssembliesResult> {
  const {
    page = 1,
    limit = 10, // Default limit
    sort = { updatedAt: -1 }, // Default sort
    filter = {},
    includeParts = false // Default to not populating parts
  } = options;

  logOperation('FETCH_ALL', 'assemblies', { page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter), includeParts: includeParts });

  try {
    await connectToDatabase();
    const skip = (page - 1) * limit;

    let query = (Assembly as any).find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      // Select fields based on the Assembly schema - ensure we get _id and assemblyCode
      .select('_id assemblyCode name description status partsRequired isTopLevel productId parentId notes createdAt updatedAt'); // Removed createdBy

    // Conditionally populate partsRequired with complete part information if requested
    if (includeParts) {
      query = query
        .populate({
          path: 'partsRequired.partId',
          model: 'Part',
          // Include all essential part fields for visual analysis
          select: '_id partNumber name description technicalSpecs isManufactured reorderLevel status inventory unitOfMeasure costPrice categoryId createdAt updatedAt'
        });
    }

    const assemblies = await query.lean(); // Execute the query

    const totalCount = await (Assembly as any).countDocuments(filter);

    logOperation('SUCCESS', 'assemblies', { count: assemblies.length, totalCount, page, totalPages: Math.ceil(totalCount / limit) });

    return {
      assemblies,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'fetch_assemblies', { error: error.message });
    handleMongoDBError(error);
    // Return empty result on error
    return { assemblies: [] as IAssembly[], pagination: { totalCount: 0, totalPages: 0, currentPage: page, limit } };
  }
}


/**
 * Adds a new assembly to the database.
 * @param assemblyData - The data for the new assembly.
 * @returns The newly created assembly document.
 */
export async function addAssembly(assemblyData: Partial<IAssembly>) { // Changed input type
  logOperation('ADD', 'assembly', { name: assemblyData.name });
  try {
    await connectToDatabase();

    const assemblyCode = assemblyData.assemblyCode;
    if (!assemblyCode) {
        throw new Error("Assembly code is missing in the provided data for addAssembly service.");
    }
    logOperation('PREPARE_CREATE', 'assembly', { assemblyCode });

    // Ensure partsRequired is a flat list and partIds are valid ObjectIds
    let processedPartsRequired: IAssemblyPartRequired[] = [];
    if (assemblyData.partsRequired && Array.isArray(assemblyData.partsRequired)) {
      processedPartsRequired = assemblyData.partsRequired.map((part: any) => {
        let partId = part.partId;
        if (typeof partId === 'object' && partId !== null && partId._id) {
          partId = partId._id.toString();
        }
        if (!mongoose.Types.ObjectId.isValid(partId)) {
          logOperation('ERROR', 'assembly_invalid_partId_in_add', { partId });
          throw new Error(`Invalid partId format: ${partId}`);
        }
        return {
          partId: new mongoose.Types.ObjectId(partId),
          quantityRequired: part.quantity || 1, // Use canonical 'quantityRequired'
          unitOfMeasure: part.unitOfMeasure === undefined ? null : part.unitOfMeasure // Handle optional unitOfMeasure, model defaults to null
        };
      }).filter(Boolean) as IAssemblyPartRequired[]; // Filter out any nulls from potential errors if any part was invalid (though we throw now)
    } else {
      assemblyData.partsRequired = []; // Ensure it's an empty array if not provided or invalid
    }

    // Map legacy status values to canonical status values if needed
    let statusValue = assemblyData.status || 'pending_review'; // Default to pending_review
    if ((statusValue as any) === 'design_complete' || (statusValue as any) === 'design_phase') {
      statusValue = 'pending_review';
    }

    const newAssemblyData: Partial<IAssembly> = {
      ...assemblyData, // Spread incoming data first
      assemblyCode: assemblyCode,
      status: statusValue as IAssembly['status'], // Cast to ensure type compatibility
      partsRequired: processedPartsRequired,
      // isTopLevel is now a virtual and should not be set directly.
      // It's derived from parentId.
      // Ensure other fields like name, description, notes, parentId, manufacturingLeadTime, costData are passed through if present
    };
    // Remove fields not in schema or handled by Mongoose/DB
    delete (newAssemblyData as any).createdBy;
    delete (newAssemblyData as any).updatedBy;
    // assembly_id is not a property of IAssembly, so no need to delete it.

    const newAssembly = new Assembly(newAssemblyData);

    logOperation('DEBUG', 'assembly_pre_save', { assemblyObject: newAssembly.toObject() });

    const savedAssembly = await newAssembly.save();
    logOperation('SUCCESS', 'assembly_creation', { _id: savedAssembly._id, assemblyCode: savedAssembly.assemblyCode });
    return savedAssembly.toObject(); // Return plain object
  } catch (error: any) {
    logOperation('ERROR', 'assembly_creation_save', { name: assemblyData.name, error: error.message, stack: error.stack });
    const errorResponse = handleMongoDBError(error);
    const specificError = new Error(errorResponse.message);
    (specificError as any).status = errorResponse.status; 
    throw specificError; 
  }
}

/**
 * Fetches a single assembly by its _id or assemblyCode.
 * @param id - The unique identifier (can be either assemblyCode or _id) of the assembly.
 * @param includeParts - Whether to populate part details.
 * @returns The assembly document or null if not found.
 */
export async function getAssembly(identifier: string, includeParts: boolean = false): Promise<IAssembly | null> {
  logOperation('GET', 'assembly', { identifier, includeParts });
  try {
    await connectToDatabase();

    let queryCriteria = {};
    if (mongoose.Types.ObjectId.isValid(identifier)) {
      queryCriteria = { _id: identifier };
      logOperation('GET_ATTEMPT', 'assembly_by_id', { _id: identifier });
    } else {
      queryCriteria = { assemblyCode: identifier };
      logOperation('GET_ATTEMPT', 'assembly_by_code', { assemblyCode: identifier });
    }

    let queryBuilder = (Assembly as any).findOne(queryCriteria)
      .select('_id assemblyCode name description status partsRequired isTopLevel productId parentId notes createdAt updatedAt'); // Removed createdBy

    if (includeParts) {
      queryBuilder = queryBuilder.populate({
        path: 'partsRequired.partId',
        model: 'Part',
        select: '_id partNumber name description technicalSpecs isManufactured reorderLevel status inventory unitOfMeasure costPrice categoryId createdAt updatedAt'
      });
    }

    const assembly = await queryBuilder.lean();

    if (!assembly) {
      logOperation('NOT_FOUND', 'assembly', { identifier });
      return null;
    }

    logOperation('SUCCESS', 'get_assembly', { identifier });
    return assembly;
  } catch (error: any) {
    logOperation('ERROR', 'get_assembly', { identifier, error: error.message });
    handleMongoDBError(error);
    throw error; 
  }
}

/**
 * Updates an existing assembly by its assemblyCode or _id.
 * @param id - The unique identifier (can be either assemblyCode or _id) of the assembly to update.
 * @param updateData - An object containing the fields to update.
 * @returns The updated assembly document.
 */
export async function updateAssembly(identifier: string, updateData: Partial<IAssembly>): Promise<IAssembly | null> {
  logOperation('UPDATE', 'assembly', { identifier });
  try {
    await connectToDatabase();

    const updatePayload: any = { ...updateData };
    delete updatePayload._id;
    delete updatePayload.assemblyCode; // assemblyCode should not be changed via this generic update
    delete updatePayload.createdAt;
    delete updatePayload.createdBy; // Not in schema
    delete updatePayload.updatedBy; // Not in schema
    delete updatePayload.assembly_id; // Legacy field handled by pre-save hook

    // Ensure partsRequired is a flat list and partIds are valid ObjectIds
    if (updatePayload.partsRequired && Array.isArray(updatePayload.partsRequired)) {
      updatePayload.partsRequired = updatePayload.partsRequired.map((part: any) => {
        let partId = part.partId;
        if (typeof partId === 'object' && partId !== null && partId._id) {
          partId = partId._id.toString();
        }
        if (!mongoose.Types.ObjectId.isValid(partId)) {
          logOperation('ERROR', 'assembly_invalid_partId_in_update', { partId });
          throw new Error(`Invalid partId format: ${partId}`);
        }
        
        return {
          partId: new mongoose.Types.ObjectId(partId),
          quantity: part.quantity || 1, // Use canonical 'quantity' field
          unitOfMeasure: part.unitOfMeasure === undefined ? null : part.unitOfMeasure // Handle optional unitOfMeasure
        };
      }).filter(Boolean);
    }
    
    // Map legacy status values if present in updateData
    if (updatePayload.status && (updatePayload.status === 'design_complete' || updatePayload.status === 'design_phase')) {
      updatePayload.status = 'pending_review';
    }

    let queryCriteria = {};
    if (mongoose.Types.ObjectId.isValid(identifier)) {
      queryCriteria = { _id: identifier };
      logOperation('UPDATE_ATTEMPT', 'assembly_by_id', { _id: identifier });
    } else {
      queryCriteria = { assemblyCode: identifier };
      logOperation('UPDATE_ATTEMPT', 'assembly_by_code', { assemblyCode: identifier });
    }

    const updatedAssembly = await (Assembly as any).findOneAndUpdate(
      queryCriteria,
      { $set: updatePayload },
      { new: true, runValidators: true }
    ).lean();

    if (!updatedAssembly) {
      logOperation('UPDATE_NOT_FOUND', 'assembly', { identifier });
      // Consider returning null or a specific error object if preferred by API design
      throw new Error(`Assembly with identifier ${identifier} not found`); 
    }

    logOperation('UPDATE_SUCCESS', 'assembly', { identifier });
    return updatedAssembly;
  } catch (error: any) {
    logOperation('ERROR', 'update_assembly', { identifier, error: error.message });
    handleMongoDBError(error);
    throw error; 
  }
}

/**
 * Deletes an assembly by its _id or assemblyCode.
 * @param id - The unique identifier (can be either assemblyCode or _id) of the assembly to delete.
 * @returns An object indicating success or failure.
 */
export async function deleteAssembly(id: string): Promise<{ success: boolean; message: string }> {
  logOperation('DELETE', 'assembly', { id });
  try {
    await connectToDatabase();

    // Determine the query based on id format
    let query = {};
    try {
      // Check if the id is a valid ObjectId
      if (mongoose.Types.ObjectId.isValid(id)) {
        query = { _id: id };
        logOperation('DELETE_ATTEMPT', 'assembly', { _id: id });
      } else {
        // If not a valid ObjectId, use as assemblyCode
        query = { assemblyCode: id };
        logOperation('DELETE_ATTEMPT', 'assembly', { assemblyCode: id });
      }
    } catch (error) {
      // If any error in parsing, default to using as assemblyCode
      query = { assemblyCode: id };
      logOperation('DELETE_ATTEMPT', 'assembly', { assemblyCode: id });
    }

    // First, verify the assembly exists - Remove .lean() for better type inference
    const existingAssembly = await (Assembly as any).findOne(query).select('_id assemblyCode'); // Removed .lean()

    if (!existingAssembly) {
      logOperation('DELETE_NOT_FOUND', 'assembly', { id });
      throw new Error(`Assembly with identifier ${id} not found`);
    }

    logOperation('DELETE_FOUND', 'assembly', {
      id,
      _id: existingAssembly?._id, // Use optional chaining
      assemblyCode: existingAssembly?.assemblyCode // Use optional chaining
    });

    // Find by query (either _id or assemblyCode) and delete
    const assemblyIdForChecks = existingAssembly._id;

    // Check for dependencies in Products
    const productDependency = await (Product as any).findOne({ assemblyId: assemblyIdForChecks }).select('_id productCode').lean();
    if (productDependency) {
      const errorMessage = `Assembly ${existingAssembly.assemblyCode || id} cannot be deleted. It is referenced by Product ${productDependency.productCode || productDependency._id}.`;
      logOperation('DELETE_CONFLICT', 'assembly_dependency_product', { assemblyId: assemblyIdForChecks, productId: productDependency._id, productCode: productDependency.productCode });
      const err = new Error(errorMessage);
      (err as any).status = 409;
      (err as any).code = 'DEPENDENCY_CONFLICT_PRODUCT';
      throw err;
    }

    // Check for dependencies as parentId in other Assemblies
    const subAssemblyDependency = await (Assembly as any).findOne({ parentId: assemblyIdForChecks }).select('_id assemblyCode').lean();
    if (subAssemblyDependency) {
      const errorMessage = `Assembly ${existingAssembly.assemblyCode || id} cannot be deleted. It is set as the parent for Assembly ${subAssemblyDependency.assemblyCode || subAssemblyDependency._id}.`;
      logOperation('DELETE_CONFLICT', 'assembly_dependency_parent', { assemblyId: assemblyIdForChecks, childAssemblyId: subAssemblyDependency._id, childAssemblyCode: subAssemblyDependency.assemblyCode });
      const err = new Error(errorMessage);
      (err as any).status = 409;
      (err as any).code = 'DEPENDENCY_CONFLICT_SUBASSEMBLY';
      throw err;
    }

    // Check for dependencies in WorkOrders
    const workOrderDependency = await (WorkOrder as any).findOne({ assemblyId: assemblyIdForChecks }).select('_id woNumber').lean();
    if (workOrderDependency) {
      const errorMessage = `Assembly ${existingAssembly.assemblyCode || id} cannot be deleted. It is referenced by Work Order ${workOrderDependency.woNumber || workOrderDependency._id}.`;
      logOperation('DELETE_CONFLICT', 'assembly_dependency_workorder', { assemblyId: assemblyIdForChecks, workOrderId: workOrderDependency._id, woNumber: workOrderDependency.woNumber });
      const err = new Error(errorMessage);
      (err as any).status = 409;
      (err as any).code = 'DEPENDENCY_CONFLICT_WORKORDER';
      throw err;
    }

    const result = await (Assembly as any).deleteOne(query);

    if (!result) {
      logOperation('DELETE_FAILED', 'assembly', { id });
      throw new Error(`Assembly with identifier ${id} could not be deleted`);
    }

    // Verify deletion was successful - Use optional chaining for existingAssembly._id - Remove .lean()
    const verifyDeletion = await (Assembly as any).findOne({ _id: existingAssembly?._id }); // Removed .lean()
    if (verifyDeletion) {
      logOperation('DELETE_VERIFICATION_FAILED', 'assembly', {
        id,
        _id: existingAssembly?._id // Use optional chaining
      });
      throw new Error(`Assembly deletion verification failed - assembly still exists in database`);
    }

    logOperation('DELETE_SUCCESS', 'assembly', {
      id,
      _id: existingAssembly._id,
      assemblyCode: existingAssembly.assemblyCode
    });

    return {
      success: true,
      message: `Assembly ${id} deleted successfully`
    };
  } catch (error: any) {
    logOperation('ERROR', 'delete_assembly', { id, errorMessage: error.message, errorCode: (error as any).code, errorStatus: (error as any).status, errorStack: error.stack });
    handleMongoDBError(error); // Throws standardized error
    throw error; // Re-throw for API layer
  }
}

// ... (rest of the code remains the same)
// --- Work Order Management Functions ---

/**
 * Fetches a list of work orders with pagination, sorting, and filtering.
 * @param options - Options for fetching work orders.
 * @returns An object containing the list of work orders and pagination info.
 */
interface FetchWorkOrdersOptions extends PaginationOptions {}

export async function fetchWorkOrders(options: FetchWorkOrdersOptions = {}): Promise<{
  workOrders: any[];
  pagination: PaginationResult;
} | null> {
  const {
    page = 1,
    limit = 20,
    sort = { createdAt: -1 },
    filter = {}
  } = options;

  logOperation('FETCH_ALL', 'workorders', { page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter) });

  try {
    await connectToDatabase();
    const skip = (page - 1) * limit;

    // Fetch work orders with pagination, sorting, and populate related data
    const workOrders = await (WorkOrder as any).find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate({
        path: 'assignedTo',
        model: 'User',
        select: 'username first_name last_name email'
      })
      .populate({
        path: 'assemblyId',
        model: 'Assembly',
        select: 'assemblyCode name'
      })
      .populate({
        path: 'productId',
        model: 'Product',
        select: 'productCode name'
      })
      // Select fields based on WorkOrder schema
      .select('woNumber assemblyId partIdToManufacture productId quantity status priority assignedTo completedAt createdAt updatedAt')
      .lean();

    // Get total count for pagination
    const totalCount = await WorkOrder.countDocuments(filter);

    logOperation('SUCCESS', 'workorders', { count: workOrders.length, totalCount, page, totalPages: Math.ceil(totalCount / limit) });

    return {
      workOrders,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'workorders', { error: error.message });
    handleMongoDBError(error);
    // Return empty result on error
    return { workOrders: [], pagination: { totalCount: 0, totalPages: 0, currentPage: page, limit } };
  }
}

/**
 * Gets a specific work order by its woNumber.
 * @param woNumber - The unique work order number.
 * @returns The work order document or null if not found.
 */
export async function getWorkOrder(woNumber: string): Promise<any | null> {
  logOperation('GET', 'workorder', { woNumber });
  try {
    await connectToDatabase();

    // Find work order by woNumber and populate related data
    const workOrder = await (WorkOrder as any).findOne({ woNumber })
      .populate({
        path: 'assignedTo',
        model: 'User',
        select: 'username fullName email'
      })
      .populate({
        path: 'assemblyId',
        model: 'Assembly',
        select: 'assemblyCode name'
      })
      .populate({
        path: 'productId',
        model: 'Product',
        select: 'productCode name'
      })
      // Select fields based on WorkOrder schema
      .select('woNumber assemblyId partIdToManufacture productId quantity status priority assignedTo completedAt createdAt updatedAt')
      .lean();

    if (!workOrder) {
      logOperation('NOT_FOUND', 'workorder', { woNumber });
      return null;
    }

    logOperation('SUCCESS', 'get_workorder', { woNumber });
    return workOrder;
  } catch (error: any) {
    logOperation('ERROR', 'get_workorder', { woNumber, error: error.message });
    handleMongoDBError(error);
    // This function's success response is a single work order or null. No pagination.
    throw new Error(handleMongoDBError(error).message || `Failed to get work order ${woNumber}`);
    // Or: return { success: false, message: handleMongoDBError(error).message, data: null };
  }
}

/**
 * Creates a new work order.
 * @param workOrderData - The data for the new work order.
 * @returns The newly created work order document.
 */
interface CreateWorkOrderData {
  woNumber?: string;
  assemblyId?: string;
  partIdToManufacture?: string;
  productId?: string;
  quantity: number;
  status?: string;
  priority?: string;
  assignedTo?: string;
  dueDate?: Date;
  notes?: string;
}

export async function createWorkOrder(workOrderData: CreateWorkOrderData): Promise<any | null> {
  logOperation('CREATE', 'workorder', { woNumber: workOrderData.woNumber });
  try {
    await connectToDatabase();

    // Generate a work order number if not provided
    const woNumber = workOrderData.woNumber || `WO-${format(new Date(), 'yyyyMMdd')}-${uuidv4().substring(0, 4).toUpperCase()}`;
    logOperation('PREPARE_CREATE', 'workorder', { woNumber });

    // Create work order based on the schema
    const newWorkOrder = new WorkOrder({
      woNumber,
      assemblyId: workOrderData.assemblyId || null,
      partIdToManufacture: workOrderData.partIdToManufacture || null,
      productId: workOrderData.productId || null,
      quantity: workOrderData.quantity,
      status: workOrderData.status || 'pending',
      priority: workOrderData.priority || 'medium',
      assignedTo: workOrderData.assignedTo,
      completedAt: (workOrderData as any).completedAt || null,
      // createdAt/updatedAt handled by timestamps
    });

    // Validate that at least one of assemblyId, partIdToManufacture, or productId is provided
    if (!newWorkOrder.assemblyId && !newWorkOrder.partIdToManufacture && !newWorkOrder.productId) {
      throw new Error('At least one of assemblyId, partIdToManufacture, or productId must be provided');
    }

    const savedWorkOrder = await newWorkOrder.save();
    logOperation('SUCCESS', 'workorder_creation', { _id: savedWorkOrder._id, woNumber: savedWorkOrder.woNumber });
    return savedWorkOrder;
  } catch (error: any) {
    logOperation('ERROR', 'workorder_creation', { woNumber: workOrderData.woNumber, error: error.message });
    handleMongoDBError(error);
    // No pagination.
    throw new Error(handleMongoDBError(error).message || 'Failed to create work order.');
    // Or: return { success: false, message: handleMongoDBError(error).message, data: null };
  }
}

/**
 * Updates an existing work order by its woNumber.
 * @param woNumber - The unique work order number.
 * @param workOrderData - The data to update.
 * @returns The updated work order document.
 */
export async function updateWorkOrder(woNumber: string, workOrderData: Partial<CreateWorkOrderData>): Promise<any | null> {
  logOperation('UPDATE', 'workorder', { woNumber });
  try {
    await connectToDatabase();

    // Prepare update payload, removing fields that shouldn't be updated directly
    const updatePayload = { ...workOrderData } as any;
    delete updatePayload._id;
    delete updatePayload.woNumber; // Don't update the work order number
    delete updatePayload.createdAt;
    // updatedAt is handled by timestamps

    // If status is being updated to 'completed', set completedAt to current date if not provided
    if (updatePayload.status === 'completed' && !updatePayload.completedAt) {
      updatePayload.completedAt = new Date();
    }

    // If status is being updated to something other than 'completed', clear completedAt
    if (updatePayload.status && updatePayload.status !== 'completed') {
      updatePayload.completedAt = null;
    }

    const workOrder = await (WorkOrder as any).findOneAndUpdate(
      { woNumber },
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run validators
    ).lean();

    if (!workOrder) {
      logOperation('UPDATE_NOT_FOUND', 'workorder', { woNumber });
      throw new Error(`Work order with number ${woNumber} not found`);
    }

    logOperation('UPDATE_SUCCESS', 'workorder', { woNumber });
    return workOrder;
  } catch (error: any) {
    logOperation('ERROR', 'update_workorder', { woNumber, error: error.message });
    handleMongoDBError(error);
    // No pagination.
    throw new Error(handleMongoDBError(error).message || `Failed to update work order ${woNumber}`);
    // Or: return { success: false, message: handleMongoDBError(error).message, data: null };
  }
}

/**
 * Deletes a work order by its woNumber.
 * @param woNumber - The unique work order number.
 * @returns An object indicating success or failure.
 */
export async function deleteWorkOrder(woNumber: string): Promise<{ success: boolean; message: string }> {
  logOperation('DELETE', 'workorder', { woNumber });
  try {
    await connectToDatabase();

    // Delete the work order
    const result = await (WorkOrder as any).findOneAndDelete({ woNumber });

    if (!result) {
      logOperation('DELETE_NOT_FOUND', 'workorder', { woNumber });
      throw new Error(`Work order with number ${woNumber} not found`);
    }

    logOperation('DELETE_SUCCESS', 'workorder', { woNumber });
    return { success: true, message: `Work order ${woNumber} deleted` };
  } catch (error: any) {
    logOperation('ERROR', 'delete_workorder', { woNumber, error: error.message });
    handleMongoDBError(error);
    // No pagination.
    // throw new Error(handleMongoDBError(error).message || `Failed to delete work order ${woNumber}`);
    return { success: false, message: handleMongoDBError(error).message || `Failed to delete work order ${woNumber}` };
  }
}

// Warehouse Management Functions

/**
 * Fetches warehouses with pagination, sorting, and filtering.
 * @param options - Options for pagination, sorting, and filtering.
 * @returns Warehouses array and pagination info.
 */
interface FetchWarehousesOptions extends PaginationOptions {
  includeParts?: boolean;
  partsLimit?: number;
}

export async function fetchWarehouses(options: FetchWarehousesOptions = {}): Promise<{
  warehouses: any[];
  pagination: PaginationResult;
} | null> {
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 },
    filter = {},
    includeParts = false,
    partsLimit = 10
  } = options;

  logOperation('FETCH_ALL', 'warehouses', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter),
    includeParts,
    partsLimit
  });

  try {
    // Connect using Mongoose for model operations
    await connectToDatabase();
    const skip = (page - 1) * limit;

    // Fetch warehouses with pagination and select fields based on schema
    const warehouses = await (Warehouse as any).find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('location_id name location capacity manager contact isActive isBinTracked createdAt updatedAt')
      .lean();

    const totalCount = await Warehouse.countDocuments(filter);

    logOperation('SUCCESS', 'warehouses', {
      count: warehouses.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      warehouses,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'warehouses', { error: error.message });
    handleMongoDBError(error);
    // Return empty result on error
    return {
      warehouses: [],
      pagination: {
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        limit
      }
    };
  }
}

/**
 * Gets a single warehouse by its location_id.
 * @param locationId - The unique location_id of the warehouse.
 * @returns The warehouse or null if not found.
 */
export async function getWarehouse(locationId: string): Promise<any | null> {
  logOperation('GET', 'warehouse', { location_id: locationId });
  try {
    await connectToDatabase();

    // Find warehouse by its location_id
    const warehouse = await (Warehouse as any).findOne({ location_id: locationId })
      .select('location_id name location capacity manager contact createdAt updatedAt')
      .lean();

    if (!warehouse) {
      logOperation('NOT_FOUND', 'warehouse', { location_id: locationId });
      return null;
    }

    logOperation('SUCCESS', 'get_warehouse', { location_id: locationId });
    return warehouse;
  } catch (error: any) {
    logOperation('ERROR', 'get_warehouse', { location_id: locationId, error: error.message });
    handleMongoDBError(error);
    return null;
  }
}

/**
 * Adds a new warehouse.
 * @param warehouseData - The data for the new warehouse.
 * @returns The newly created warehouse.
 */
interface CreateWarehouseData {
  location_id?: string;
  name: string;
  location?: string;
  capacity?: number;
  manager?: string;
  contact?: string;
}

export async function addWarehouse(warehouseData: CreateWarehouseData): Promise<any | null> {
  logOperation('ADD', 'warehouse', { name: warehouseData.name });
  try {
    await connectToDatabase();

    // Ensure location_id is provided or generate one
    const locationId = warehouseData.location_id || `WH-${uuidv4().substring(0, 8).toUpperCase()}`;
    logOperation('PREPARE_CREATE', 'warehouse', { location_id: locationId });

    // Create warehouse based on the schema
    const newWarehouse = new Warehouse({
      location_id: locationId,
      name: warehouseData.name,
      location: warehouseData.location,
      capacity: warehouseData.capacity,
      manager: warehouseData.manager,
      contact: warehouseData.contact,
      schemaVersion: (warehouseData as any).schemaVersion || 1
      // createdAt and updatedAt are handled by timestamps: true
    });

    const savedWarehouse = await newWarehouse.save();
    logOperation('SUCCESS', 'warehouse_creation', {
      _id: savedWarehouse._id,
      location_id: savedWarehouse.location_id
    });

    return savedWarehouse;
  } catch (error: any) {
    logOperation('ERROR', 'warehouse_creation', { error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Updates an existing warehouse by its location_id.
 * @param locationId - The unique location_id of the warehouse to update.
 * @param warehouseData - The data to update.
 * @returns The updated warehouse.
 */
export async function updateWarehouse(locationId: string, warehouseData: Partial<CreateWarehouseData>): Promise<any | null> {
  logOperation('UPDATE', 'warehouse', { location_id: locationId });
  try {
    await connectToDatabase();

    // Prepare update payload, removing fields that shouldn't be updated directly
    const updatePayload = { ...warehouseData } as any;
    delete updatePayload._id; // Don't update MongoDB ObjectId
    delete updatePayload.location_id; // Don't update the unique identifier
    delete updatePayload.createdAt; // Don't update creation timestamp
    // updatedAt is handled by timestamps: true

    const warehouse = await (Warehouse as any).findOneAndUpdate(
      { location_id: locationId }, // Find by the unique location_id
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run validators
    ).lean();

    if (!warehouse) {
      logOperation('UPDATE_NOT_FOUND', 'warehouse', { location_id: locationId });
      throw new Error(`Warehouse with location ID ${locationId} not found`);
    }

    logOperation('UPDATE_SUCCESS', 'warehouse', { location_id: locationId });
    return warehouse;
  } catch (error: any) {
    logOperation('ERROR', 'warehouse', { location_id: locationId, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Deletes a warehouse by its location_id.
 * @param locationId - The unique location_id of the warehouse to delete.
 * @returns Object indicating success or failure.
 */
export async function deleteWarehouse(locationId: string): Promise<{ success: boolean; message: string }> {
  logOperation('DELETE', 'warehouse', { location_id: locationId });
  try {
    await connectToDatabase();

    // Delete the warehouse by its location_id
    const result = await (Warehouse as any).findOneAndDelete({ location_id: locationId });

    if (!result) {
      logOperation('DELETE_NOT_FOUND', 'warehouse', { location_id: locationId });
      throw new Error(`Warehouse with location ID ${locationId} not found or already deleted`);
    }

    logOperation('DELETE_SUCCESS', 'warehouse', { location_id: locationId });
    return { success: true, message: `Warehouse ${locationId} deleted` };
  } catch (error: any) {
    logOperation('ERROR', 'warehouse', { location_id: locationId, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

// Category Management Functions

/**
 * Fetches categories with pagination, sorting, and filtering.
 * @param options - Options for pagination, sorting, and filtering.
 * @returns Categories array and pagination info.
 */
export async function fetchCategories(options: any = {}) { // Added export
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 }, // Default sort by name
    filter = {},
    includeParts = false, // New option to include parts
    partsLimit = 10 // Limit for parts per category, adjust as needed
  } = options;

  logOperation('FETCH_ALL', 'categories', { // Added includeParts, partsLimit
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter),
    includeParts: includeParts,
    partsLimit: partsLimit
  });

  try {
    // Connect using Mongoose for model operations
    await connectToDatabase();
    const skip = (page - 1) * limit;

    // Fetch categories with pagination
    let categories;

    if (includeParts) {
      // Use aggregation with $lookup to avoid N+1 queries
      const sortStage: any = {}; // Typed sortStage as any
      Object.entries(sort).forEach(([key, value]) => {
        (sortStage as any)[key] = value; // Use type assertion for indexing
      });

      // Create the aggregation pipeline
      const pipeline = [
        { $match: filter },
        { $sort: sortStage },
        { $skip: skip },
        { $limit: limit },
        // Lookup to get parent category information
        {
          $lookup: {
            from: 'categories', // The collection to join with
            localField: 'parentCategory', // Field from the categories collection
            foreignField: '_id', // Field from the parentCategory collection
            as: 'parentCategoryData' // Output array field
          }
        },
        // Lookup to get parts for each category
        {
          $lookup: {
            from: 'parts', // The collection to join with
            let: { categoryId: '$_id' }, // Variable to use in the pipeline
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ['$categoryId', '$$categoryId'] }
                }
              },
              { $limit: partsLimit },
              {
                $project: {
                  _id: 1,
                  name: 1,
                  description: 1
                }
              }
            ],
            as: 'parts' // Output array field
          }
        },
        // Unwind and regroup parentCategory to get single object instead of array
        {
          $addFields: {
            parentCategory: {
              $cond: {
                if: { $gt: [{ $size: '$parentCategoryData' }, 0] },
                then: {
                  _id: { $arrayElemAt: ['$parentCategoryData._id', 0] },
                  name: { $arrayElemAt: ['$parentCategoryData.name', 0] }
                },
                else: null
              }
            }
          }
        },
        // Remove the parentCategoryData array
        { $project: { parentCategoryData: 0 } }
      ];

      logOperation('AGGREGATE', 'categories', {
        pipeline: JSON.stringify(pipeline.map(stage => Object.keys(stage)[0]))
      });

      categories = await Category.aggregate(pipeline);
    } else {
      // If parts are not needed, use regular find query with populate
      categories = await (Category as any).find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .select('_id name description parentCategory createdAt updatedAt')
        .populate({
          path: 'parentCategory',
          model: 'Category',
          select: '_id name' // Only select essential fields from parent
        })
        .lean();
    }

    const totalCount = await Category.countDocuments(filter);

    logOperation('SUCCESS', 'categories', {
      count: categories.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      categories,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'categories', { error: error.message });
    handleMongoDBError(error);
    // Return empty result on error
    return {
      categories: [],
      pagination: {
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        limit
      }
    };
  }
}

/**
 * Gets a single category by its ID.
 * @param categoryId - The unique ID of the category.
 * @returns The category or null if not found.
 */
export async function getCategory(categoryId: string) {
  logOperation('GET', 'category', { _id: categoryId });
  try {
    await connectToDatabase();

    // Find category by its ID
    const category = await (Category as any).findById(categoryId)
      .select('_id name description parentCategory createdAt updatedAt')
      .populate({
        path: 'parentCategory',
        model: 'Category',
        select: '_id name' // Only select essential fields from parent
      })
      .lean();

    if (!category) {
      logOperation('NOT_FOUND', 'category', { _id: categoryId });
      return null;
    }

    logOperation('SUCCESS', 'get_category', { _id: categoryId });
    return category;
  } catch (error: any) {
    logOperation('ERROR', 'get_category', { _id: categoryId, error: error.message });
    handleMongoDBError(error);
    return null;
  }
}

/**
 * Adds a new category.
 * @param categoryData - The data for the new category.
 * @returns The newly created category.
 */
export async function addCategory(categoryData: any) {
  logOperation('ADD', 'category', { name: categoryData.name });
  try {
    await connectToDatabase();

    // Create category based on the schema
    const newCategory = new Category({
      name: categoryData.name,
      description: categoryData.description,
      parentCategory: categoryData.parentCategory
      // createdAt and updatedAt are handled by timestamps: true
    });

    const savedCategory = await newCategory.save();
    logOperation('SUCCESS', 'category_creation', {
      _id: savedCategory._id,
      name: savedCategory.name
    });

    return savedCategory;
  } catch (error: any) {
    logOperation('ERROR', 'category_creation', { error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Updates an existing category by its ID.
 * @param categoryId - The unique ID of the category to update.
 * @param categoryData - The data to update.
 * @returns The updated category.
 */
export async function updateCategory(categoryId: string, categoryData: any) {
  logOperation('UPDATE', 'category', { _id: categoryId });
  try {
    await connectToDatabase();

    // Prepare update payload, removing fields that shouldn't be updated directly
    const updatePayload = { ...categoryData };
    delete updatePayload._id; // Don't update MongoDB ObjectId
    delete updatePayload.createdAt; // Don't update creation timestamp
    // updatedAt is handled by timestamps: true

    const category = await (Category as any).findByIdAndUpdate(
      categoryId,
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run validators
    ).lean();

    if (!category) {
      logOperation('UPDATE_NOT_FOUND', 'category', { _id: categoryId });
      throw new Error(`Category with ID ${categoryId} not found`);
    }

    logOperation('UPDATE_SUCCESS', 'category', { _id: categoryId });
    return category;
  } catch (error: any) {
    logOperation('ERROR', 'category', { _id: categoryId, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Deletes a category by its ID.
 * @param categoryId - The unique ID of the category to delete.
 * @returns Object indicating success or failure.
 */
export async function deleteCategory(categoryId: string) {
  logOperation('DELETE', 'category', { _id: categoryId });
  try {
    await connectToDatabase();

    // Check if this category is a parent for any other categories
    const childCategories = await (Category as any).findOne({ parentCategory: categoryId });
    if (childCategories) {
      logOperation('DELETE_FAILED', 'category', { _id: categoryId, reason: 'has_children' });
      throw new Error(`Cannot delete category with ID ${categoryId} because it has child categories`);
    }

    // Check if this category is used by any products
    const products = await (Product as any).findOne({ categoryId: categoryId });
    if (products) {
      logOperation('DELETE_FAILED', 'category', { _id: categoryId, reason: 'used_by_products' });
      throw new Error(`Cannot delete category with ID ${categoryId} because it is used by products`);
    }

    // Delete the category
    const result = await (Category as any).findByIdAndDelete(categoryId);

    if (!result) {
      logOperation('DELETE_NOT_FOUND', 'category', { _id: categoryId });
      throw new Error(`Category with ID ${categoryId} not found or already deleted`);
    }

    logOperation('DELETE_SUCCESS', 'category', { _id: categoryId });
    return { success: true, message: `Category ${categoryId} deleted` };
  } catch (error: any) {
    logOperation('ERROR', 'category', { _id: categoryId, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

// Delivery Management Functions

/**
 * Fetches deliveries with pagination, sorting, and filtering.
 * @param options - Options for pagination, sorting, and filtering.
 * @returns Deliveries array and pagination info.
 */
export async function fetchDeliveries(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { scheduledDate: -1 }, // Default sort by scheduled date (newest first)
    filter = {}
  } = options;

  logOperation('FETCH_ALL', 'deliveries', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter)
  });

  try {
    // Connect using Mongoose for model operations
    await connectToDatabase();
    const skip = (page - 1) * limit;

    // Fetch deliveries with pagination
    const deliveries = await (Delivery as any).find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('_id deliveryId referenceType referenceId supplierId status scheduledDate actualDate trackingNumber notes receivedBy createdAt updatedAt')
      .populate([
        {
          path: 'supplierId',
          model: 'Supplier',
          select: 'supplier_id name contactPerson' // Only select essential fields
        },
        {
          path: 'receivedBy',
          model: 'User',
          select: 'username first_name last_name' // Only select essential fields
        }
      ])
      .lean();

    const totalCount = await Delivery.countDocuments(filter);

    logOperation('SUCCESS', 'deliveries', {
      count: deliveries.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      deliveries,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'deliveries', { error: error.message });
    handleMongoDBError(error);
    // Return empty result on error
    return {
      deliveries: [],
      pagination: {
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        limit
      }
    };
  }
}

/**
 * Gets a single delivery by its deliveryId.
 * @param deliveryId - The unique deliveryId of the delivery.
 * @returns The delivery or null if not found.
 */
export async function getDelivery(deliveryId: string) {
  logOperation('GET', 'delivery', { deliveryId });
  try {
    await connectToDatabase();

    // Find delivery by its deliveryId
    const delivery = await (Delivery as any).findOne({ deliveryId })
      .select('_id deliveryId referenceType referenceId supplierId status scheduledDate actualDate trackingNumber notes receivedBy createdAt updatedAt')
      .populate([
        {
          path: 'supplierId',
          model: 'Supplier',
          select: 'supplier_id name contactPerson' // Only select essential fields
        },
        {
          path: 'receivedBy',
          model: 'User',
          select: 'username first_name last_name' // Only select essential fields
        }
      ])
      .lean();

    if (!delivery) {
      logOperation('NOT_FOUND', 'delivery', { deliveryId });
      return null;
    }

    logOperation('SUCCESS', 'get_delivery', { deliveryId });
    return delivery;
  } catch (error: any) {
    logOperation('ERROR', 'get_delivery', { deliveryId, error: error.message });
    handleMongoDBError(error);
    return null;
  }
}

/**
 * Adds a new delivery.
 * @param deliveryData - The data for the new delivery.
 * @returns The newly created delivery.
 */
export async function addDelivery(deliveryData: any) {
  logOperation('ADD', 'delivery', { deliveryId: deliveryData.deliveryId });
  try {
    await connectToDatabase();

    // Ensure deliveryId is provided or generate one
    const deliveryId = deliveryData.deliveryId || `DEL-${uuidv4().substring(0, 8).toUpperCase()}`;
    logOperation('PREPARE_CREATE', 'delivery', { deliveryId });

    // Create delivery based on the schema
    const newDelivery = new Delivery({
      deliveryId,
      referenceType: deliveryData.referenceType,
      referenceId: deliveryData.referenceId,
      supplierId: deliveryData.supplierId,
      status: deliveryData.status,
      scheduledDate: deliveryData.scheduledDate,
      actualDate: deliveryData.actualDate,
      trackingNumber: deliveryData.trackingNumber,
      notes: deliveryData.notes,
      receivedBy: deliveryData.receivedBy
      // createdAt and updatedAt are handled by timestamps: true
    });

    const savedDelivery = await newDelivery.save();
    logOperation('SUCCESS', 'delivery_creation', {
      _id: savedDelivery._id,
      deliveryId: savedDelivery.deliveryId
    });

    return savedDelivery;
  } catch (error: any) {
    logOperation('ERROR', 'delivery_creation', { error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Updates an existing delivery by its deliveryId.
 * @param deliveryId - The unique deliveryId of the delivery to update.
 * @param deliveryData - The data to update.
 * @returns The updated delivery.
 */
export async function updateDelivery(deliveryId: string, deliveryData: any) {
  logOperation('UPDATE', 'delivery', { deliveryId });
  try {
    await connectToDatabase();

    // Prepare update payload, removing fields that shouldn't be updated directly
    const updatePayload = { ...deliveryData };
    delete updatePayload._id; // Don't update MongoDB ObjectId
    delete updatePayload.deliveryId; // Don't update the unique identifier
    delete updatePayload.createdAt; // Don't update creation timestamp
    // updatedAt is handled by timestamps: true

    const delivery = await (Delivery as any).findOneAndUpdate(
      { deliveryId }, // Find by the unique deliveryId
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run validators
    ).lean();

    if (!delivery) {
      logOperation('UPDATE_NOT_FOUND', 'delivery', { deliveryId });
      throw new Error(`Delivery with ID ${deliveryId} not found`);
    }

    logOperation('UPDATE_SUCCESS', 'delivery', { deliveryId });
    return delivery;
  } catch (error: any) {
    logOperation('ERROR', 'delivery', { deliveryId, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Deletes a delivery by its deliveryId.
 * @param deliveryId - The unique deliveryId of the delivery to delete.
 * @returns Object indicating success or failure.
 */
export async function deleteDelivery(deliveryId: string) {
  logOperation('DELETE', 'delivery', { deliveryId });
  try {
    await connectToDatabase();

    // Delete the delivery by its deliveryId
    const result = await (Delivery as any).findOneAndDelete({ deliveryId });

    if (!result) {
      logOperation('DELETE_NOT_FOUND', 'delivery', { deliveryId });
      throw new Error(`Delivery with ID ${deliveryId} not found or already deleted`);
    }

    logOperation('DELETE_SUCCESS', 'delivery', { deliveryId });
    return { success: true, message: `Delivery ${deliveryId} deleted` };
  } catch (error: any) {
    logOperation('ERROR', 'delivery', { deliveryId, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

// User Management Functions

/**
 * Fetches users with pagination, sorting, and filtering.
 * @param options - Options for pagination, sorting, and filtering.
 * @returns Users array and pagination info.
 */
export async function fetchUsers(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { username: 1 }, // Default sort by username
    filter = {}
  } = options;

  logOperation('FETCH_ALL', 'users', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter)
  });

  try {
    // Connect using Mongoose for model operations
    await connectToDatabase();
    const skip = (page - 1) * limit;

    // Fetch users with pagination
    const users = await (User as any).find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('_id username email first_name last_name roles is_active createdAt updatedAt')
      .lean();

    const totalCount = await User.countDocuments(filter);

    logOperation('SUCCESS', 'users', {
      count: users.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      users,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'users', { error: error.message });
    handleMongoDBError(error);
    // Return empty result on error
    return {
      users: [],
      pagination: {
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        limit
      }
    };
  }
}

/**
 * Gets a single user by its username.
 * @param username - The unique username of the user.
 * @returns The user or null if not found.
 */
export async function getUser(username: string) {
  logOperation('GET', 'user', { username });
  try {
    await connectToDatabase();

    // Find user by its username
    const user = await (User as any).findOne({ username })
      .select('_id username email first_name last_name roles is_active createdAt updatedAt')
      .lean();

    if (!user) {
      logOperation('NOT_FOUND', 'user', { username });
      return null;
    }

    logOperation('SUCCESS', 'get_user', { username });
    return user;
  } catch (error: any) {
    logOperation('ERROR', 'get_user', { username, error: error.message });
    handleMongoDBError(error);
    return null;
  }
}

/**
 * Adds a new user.
 * @param userData - The data for the new user.
 * @returns The newly created user.
 */
export async function addUser(userData: any) {
  logOperation('ADD', 'user', { username: userData.username });
  try {
    await connectToDatabase();

    // Create user based on the schema
    const newUser = new User({
      user_id: userData.user_id || `USR-${Date.now()}`, // Generate if not provided
      username: userData.username,
      email: userData.email,
      first_name: userData.first_name,
      last_name: userData.last_name,
      roles: userData.roles || ['viewer'], // Default role
      password_hash: userData.password_hash, // Should be pre-hashed
      is_active: userData.is_active !== undefined ? userData.is_active : true,
      department: userData.department || null,
      job_title: userData.job_title || null,
      phone_number: userData.phone_number || null
      // createdAt and updatedAt are handled by timestamps: true
    });

    const savedUser = await newUser.save();
    logOperation('SUCCESS', 'user_creation', {
      _id: savedUser._id,
      username: savedUser.username
    });

    return savedUser;
  } catch (error: any) {
    logOperation('ERROR', 'user_creation', { error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Updates an existing user by its username.
 * @param username - The unique username of the user to update.
 * @param userData - The data to update.
 * @returns The updated user.
 */
export async function updateUser(username: string, userData: any) {
  logOperation('UPDATE', 'user', { username });
  try {
    await connectToDatabase();

    // Prepare update payload, removing fields that shouldn't be updated directly
    const updatePayload = { ...userData };
    delete updatePayload._id; // Don't update MongoDB ObjectId
    delete updatePayload.username; // Don't update the unique identifier
    delete updatePayload.createdAt; // Don't update creation timestamp
    // updatedAt is handled by timestamps: true

    const user = await (User as any).findOneAndUpdate(
      { username }, // Find by the unique username
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run validators
    ).lean();

    if (!user) {
      logOperation('UPDATE_NOT_FOUND', 'user', { username });
      throw new Error(`User with username ${username} not found`);
    }

    logOperation('UPDATE_SUCCESS', 'user', { username });
    return user;
  } catch (error: any) {
    logOperation('ERROR', 'user', { username, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Deletes a user by its username.
 * @param username - The unique username of the user to delete.
 * @returns Object indicating success or failure.
 */
export async function deleteUser(username: string) {
  logOperation('DELETE', 'user', { username });
  try {
    await connectToDatabase();

    // Check if this user is referenced by other collections
    // For example, check if the user is assigned to any work orders
    const workOrders = await (WorkOrder as any).findOne({ assignedTo: { $exists: true } });
    if (workOrders) {
      logOperation('DELETE_FAILED', 'user', { username, reason: 'referenced_by_work_orders' });
      throw new Error(`Cannot delete user with username ${username} because it is referenced by work orders`);
    }

    // Delete the user by its username
    const result = await (User as any).findOneAndDelete({ username });

    if (!result) {
      logOperation('DELETE_NOT_FOUND', 'user', { username });
      throw new Error(`User with username ${username} not found or already deleted`);
    }

    logOperation('DELETE_SUCCESS', 'user', { username });
    return { success: true, message: `User ${username} deleted` };
  } catch (error: any) {
    logOperation('ERROR', 'user', { username, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

// Batch Management Functions

/**
 * Fetches batches with pagination, sorting, and filtering.
 * @param options - Options for pagination, sorting, and filtering.
 * @returns Batches array and pagination info.
 */
interface FetchBatchesOptions extends PaginationOptions {
  includeParts?: boolean;
}

export async function fetchBatches(options: FetchBatchesOptions = {}): Promise<{
  batches: any[];
  pagination: PaginationResult;
} | null> {
  const {
    page = 1,
    limit = 20,
    sort = { createdAt: -1 },
    filter = {},
    includeParts = false
  } = options;

  logOperation('FETCH_ALL', 'batches', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter),
    includeParts: includeParts
  });

  try {
    // Connect using Mongoose for model operations
    await connectToDatabase();
    const skip = (page - 1) * limit;

    // Prepare population options
    const populateOptions = [
      {
        path: 'partId',
        model: 'Part',
        select: '_id name description' // Only select essential fields
      },
      {
        path: 'assemblyId',
        model: 'Assembly',
        select: '_id assemblyCode name' // Only select essential fields
      },
      {
        path: 'workOrderId',
        model: 'WorkOrder',
        select: '_id woNumber status' // Only select essential fields
      }
    ];

    // Fetch batches with pagination
    const batches = await (Batch as any).find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('_id batchCode partId assemblyId quantityPlanned quantityProduced startDate endDate status notes workOrderId createdAt updatedAt')
      .populate(populateOptions)
      .lean();

    const totalCount = await Batch.countDocuments(filter);

    logOperation('SUCCESS', 'batches', {
      count: batches.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      batches,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'batches', { error: error.message });
    handleMongoDBError(error);
    // Return empty result on error
    return {
      batches: [],
      pagination: {
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        limit
      }
    };
  }
}

/**
 * Gets a single batch by its ID.
 * @param batchId - The unique ID of the batch.
 * @returns The batch or null if not found.
 */
export async function getBatch(batchId: string): Promise<any | null> {
  logOperation('GET', 'batch', { _id: batchId });
  try {
    await connectToDatabase();

    // Find batch by its ID
    const batch = await (Batch as any).findById(batchId)
      .select('_id batchCode partId assemblyId quantityPlanned quantityProduced startDate endDate status notes workOrderId createdAt updatedAt')
      .populate([
        {
          path: 'partId',
          model: 'Part',
          select: '_id name description' // Only select essential fields
        },
        {
          path: 'assemblyId',
          model: 'Assembly',
          select: '_id assemblyCode name' // Only select essential fields
        },
        {
          path: 'workOrderId',
          model: 'WorkOrder',
          select: '_id woNumber status' // Only select essential fields
        }
      ])
      .lean();

    if (!batch) {
      logOperation('NOT_FOUND', 'batch', { _id: batchId });
      return null;
    }

    logOperation('SUCCESS', 'get_batch', { _id: batchId });
    return batch;
  } catch (error: any) {
    logOperation('ERROR', 'get_batch', { _id: batchId, error: error.message });
    handleMongoDBError(error);
    return null;
  }
}

/**
 * Adds a new batch.
 * @param batchData - The data for the new batch.
 * @returns The newly created batch.
 */
interface CreateBatchData {
  batchCode?: string;
  assemblyId?: string;
  workOrderId?: string;
  quantity?: number;
  status?: string;
  startDate?: Date;
  endDate?: Date;
  notes?: string;
}

export async function addBatch(batchData: CreateBatchData, _userId?: string): Promise<any | null> {
  logOperation('ADD', 'batch', { batchCode: batchData.batchCode });
  try {
    await connectToDatabase();

    // Generate a batch code if not provided
    const batchCode = batchData.batchCode || `BATCH-${uuidv4().substring(0, 8).toUpperCase()}`;
    logOperation('PREPARE_CREATE', 'batch', { batchCode });

    // Create batch based on the schema
    const newBatch = new Batch({
      batchCode,
      partId: (batchData as any).partId,
      assemblyId: (batchData as any).assemblyId,
      quantityPlanned: (batchData as any).quantityPlanned,
      quantityProduced: (batchData as any).quantityProduced,
      startDate: batchData.startDate || new Date(),
      endDate: batchData.endDate,
      status: batchData.status || 'pending',
      notes: batchData.notes,
      workOrderId: batchData.workOrderId
      // createdAt and updatedAt are handled by timestamps: true
    });

    const savedBatch = await newBatch.save();
    logOperation('SUCCESS', 'batch_creation', {
      _id: savedBatch._id,
      batchCode: savedBatch.batchCode
    });

    return savedBatch;
  } catch (error: any) {
    logOperation('ERROR', 'batch_creation', { error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Updates an existing batch by its ID.
 * @param batchId - The unique ID of the batch to update.
 * @param batchData - The data to update.
 * @returns The updated batch.
 */
export async function updateBatch(batchId: string, batchData: Partial<CreateBatchData>, _userId?: string): Promise<any | null> {
  logOperation('UPDATE', 'batch', { _id: batchId });
  try {
    await connectToDatabase();

    // Prepare update payload, removing fields that shouldn't be updated directly
    const updatePayload = { ...batchData } as any;
    delete updatePayload._id; // Don't update MongoDB ObjectId
    delete updatePayload.batchCode; // Don't update the unique identifier
    delete updatePayload.createdAt; // Don't update creation timestamp
    // updatedAt is handled by timestamps: true

    const batch = await (Batch as any).findByIdAndUpdate(
      batchId,
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run validators
    ).lean();

    if (!batch) {
      logOperation('UPDATE_NOT_FOUND', 'batch', { _id: batchId });
      throw new Error(`Batch with ID ${batchId} not found`);
    }

    logOperation('UPDATE_SUCCESS', 'batch', { _id: batchId });
    return batch;
  } catch (error: any) {
    logOperation('ERROR', 'batch', { _id: batchId, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Deletes a batch by its ID.
 * @param batchId - The unique ID of the batch to delete.
 * @returns Object indicating success or failure.
 */
export async function deleteBatch(batchId: string): Promise<{ success: boolean; message: string }> {
  logOperation('DELETE', 'batch', { _id: batchId });
  try {
    await connectToDatabase();

    // Delete the batch by its ID
    const result = await (Batch as any).findByIdAndDelete(batchId);

    if (!result) {
      logOperation('DELETE_NOT_FOUND', 'batch', { _id: batchId });
      throw new Error(`Batch with ID ${batchId} not found or already deleted`);
    }

    logOperation('DELETE_SUCCESS', 'batch', { _id: batchId });
    return { success: true, message: `Batch ${batchId} deleted` };
  } catch (error: any) {
    logOperation('ERROR', 'batch', { _id: batchId, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Fetches batches associated with a specific work order.
 * @param woNumber - The unique work order number.
 * @param options - Options for pagination, sorting, and filtering.
 * @returns Batches array and pagination info.
 */
export async function getBatchesByWorkOrderId(woNumber: string, options: PaginationOptions & { additionalFilter?: Record<string, any> } = {}): Promise<{
  batches: any[];
  pagination: PaginationResult;
} | null> {
  const {
    page = 1,
    limit = 10,
    sort = { createdAt: -1 },
    additionalFilter = {}
  } = options;

  logOperation('GET_BATCHES_BY_WORKORDER', 'batches', { woNumber, page, limit, sort: JSON.stringify(sort) });

  try {
    await connectToDatabase();

    // First, find the work order by woNumber to get its _id
    const workOrder = await (WorkOrder as any).findOne({ woNumber }).select('_id').lean();

    if (!workOrder) {
      logOperation('NOT_FOUND', 'workorder', { woNumber });
      return {
        batches: [],
        pagination: {
          totalCount: 0,
          totalPages: 0,
          currentPage: page,
          limit
        }
      };
    }

    const workOrderId = (workOrder as { _id: import('mongoose').Types.ObjectId })._id;
    const skip = (page - 1) * limit;

    // Combine the workOrderId filter with any additional filters
    const filter = {
      workOrderId,
      ...additionalFilter
    };

    // Fetch batches with pagination
    const batches = await (Batch as any).find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('_id batchCode partId assemblyId quantityPlanned quantityProduced startDate endDate status notes workOrderId createdAt updatedAt')
      .populate([
        {
          path: 'partId',
          model: 'Part',
          select: '_id name description' // Only select essential fields
        },
        {
          path: 'assemblyId',
          model: 'Assembly',
          select: '_id assemblyCode name' // Only select essential fields
        },
        {
          path: 'workOrderId',
          model: 'WorkOrder',
          select: '_id woNumber status' // Only select essential fields
        }
      ])
      .lean();

    // Get total count for pagination
    const totalCount = await Batch.countDocuments(filter);

    logOperation('SUCCESS', 'get_batches_by_workorder', {
      woNumber,
      workOrderId,
      count: batches.length,
      totalCount
    });

    return {
      batches,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'get_batches_by_workorder', { woNumber, error: error.message });
    handleMongoDBError(error);
    // Return empty result on error
    return {
      batches: [],
      pagination: {
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        limit
      }
    };
  }
}

// Settings Management Functions

/**
 * Fetches settings with pagination, sorting, and filtering.
 * @param options - Options for pagination, sorting, and filtering.
 * @returns Settings array and pagination info.
 */
export async function fetchSettings(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { key: 1 }, // Default sort by key
    filter = {}
  } = options;

  logOperation('FETCH_ALL', 'settings', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter)
  });

  try {
    // Connect using Mongoose for model operations
    await connectToDatabase();
    const skip = (page - 1) * limit;

    // Fetch settings with pagination
    const settings = await (Setting as any).find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('_id key value description dataType group lastModifiedBy lastModifiedAt createdAt updatedAt')
      .populate({
        path: 'lastModifiedBy',
        model: 'User',
        select: 'username first_name last_name' // Only select essential fields
      })
      .lean();

    const totalCount = await Setting.countDocuments(filter);

    logOperation('SUCCESS', 'settings', {
      count: settings.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit)
    });

    return {
      settings,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit
      }
    };
  } catch (error: any) {
    logOperation('ERROR', 'settings', { error: error.message });
    handleMongoDBError(error);
    // Return empty result on error
    return {
      settings: [],
      pagination: {
        totalCount: 0,
        totalPages: 0,
        currentPage: page,
        limit
      }
    };
  }
}

/**
 * Gets a single setting by its key.
 * @param key - The unique key of the setting.
 * @returns The setting or null if not found.
 */
export async function getSetting(key: string) {
  logOperation('GET', 'setting', { key });
  try {
    await connectToDatabase();

    // Find setting by its key
    const setting = await (Setting as any).findOne({ key })
      .select('_id key value description dataType group lastModifiedBy lastModifiedAt createdAt updatedAt')
      .populate({
        path: 'lastModifiedBy',
        model: 'User',
        select: 'username first_name last_name' // Only select essential fields
      })
      .lean();

    if (!setting) {
      logOperation('NOT_FOUND', 'setting', { key });
      return null;
    }

    logOperation('SUCCESS', 'get_setting', { key });
    return setting;
  } catch (error: any) {
    logOperation('ERROR', 'get_setting', { key, error: error.message });
    handleMongoDBError(error);
    return null;
  }
}

/**
 * Adds a new setting.
 * @param settingData - The data for the new setting.
 * @returns The newly created setting.
 */
export async function addSetting(settingData: any) {
  logOperation('ADD', 'setting', { key: settingData.key });
  try {
    await connectToDatabase();

    // Create setting based on the schema
    const newSetting = new Setting({
      key: settingData.key,
      value: settingData.value,
      description: settingData.description,
      dataType: settingData.dataType,
      group: settingData.group,
      lastModifiedBy: settingData.lastModifiedBy,
      lastModifiedAt: new Date()
      // createdAt and updatedAt are handled by timestamps: true
    });

    const savedSetting = await newSetting.save();
    logOperation('SUCCESS', 'setting_creation', {
      _id: savedSetting._id,
      key: savedSetting.key
    });

    return savedSetting;
  } catch (error: any) {
    logOperation('ERROR', 'setting_creation', { error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Updates an existing setting by its key.
 * @param key - The unique key of the setting to update.
 * @param settingData - The data to update.
 * @returns The updated setting.
 */
export async function updateSetting(key: string, settingData: any) {
  logOperation('UPDATE', 'setting', { key });
  try {
    await connectToDatabase();

    // Prepare update payload, removing fields that shouldn't be updated directly
    const updatePayload = { ...settingData };
    delete updatePayload._id; // Don't update MongoDB ObjectId
    delete updatePayload.key; // Don't update the unique identifier
    delete updatePayload.createdAt; // Don't update creation timestamp
    // updatedAt is handled by timestamps: true

    // Always update lastModifiedAt
    updatePayload.lastModifiedAt = new Date();

    const setting = await (Setting as any).findOneAndUpdate(
      { key }, // Find by the unique key
      { $set: updatePayload },
      { new: true, runValidators: true } // Return updated doc, run validators
    ).lean();

    if (!setting) {
      logOperation('UPDATE_NOT_FOUND', 'setting', { key });
      throw new Error(`Setting with key ${key} not found`);
    }

    logOperation('UPDATE_SUCCESS', 'setting', { key });
    return setting;
  } catch (error: any) {
    logOperation('ERROR', 'setting', { key, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}

/**
 * Deletes a setting by its key.
 * @param key - The unique key of the setting to delete.
 * @returns Object indicating success or failure.
 */
export async function deleteSetting(key: string) {
  logOperation('DELETE', 'setting', { key });
  try {
    await connectToDatabase();

    // Delete the setting by its key
    const result = await (Setting as any).findOneAndDelete({ key });

    if (!result) {
      logOperation('DELETE_NOT_FOUND', 'setting', { key });
      throw new Error(`Setting with key ${key} not found or already deleted`);
    }

    logOperation('DELETE_SUCCESS', 'setting', { key });
    return { success: true, message: `Setting ${key} deleted` };
  } catch (error: any) {
    logOperation('ERROR', 'setting', { key, error: error.message });
    handleMongoDBError(error);
    throw error; // Re-throw for API layer
  }
}
