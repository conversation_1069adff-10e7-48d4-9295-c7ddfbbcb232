import { Types } from 'mongoose';
import { captureException, setTag } from '../lib/logging-utils';
import connectToDatabase from '../lib/mongodb';
import { Category } from '../models/category.model';
import { IProduct, Product, IHierarchicalComponent } from '../models/product.model';
import Assembly from '../models/assembly.model';
import Part from '../models/part.model';

// Ensure models are registered with Mongoose
const ensureModelsRegistered = () => {
  // Reference the models to ensure they are registered
  Assembly.modelName;
  Part.modelName;
};

// Logger function for tracking database operations
const logOperation = (operation: string, entity: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[ProductService][${timestamp}] ${operation} on ${entity}${details ? ': ' + JSON.stringify(details) : ''}`);
};

/**
 * Standardized error handling for MongoDB operations
 * @param error The error object from MongoDB/Mongoose
 * @returns Error details with message and status code
 */
export const handleMongoDBError = (error: any) => {
  console.error('[ProductService Error]', error);

  setTag('error.type', 'database');
  setTag('error.database', 'mongodb');
  setTag('error.service', 'product');

  let errorType = 'unknown';
  let errorStatus = 500;
  let errorMessage = '';

  if (error.name === 'ValidationError') {
    errorType = 'validation';
    errorStatus = 400;
    const validationErrors = Object.values(error.errors)
      .map((err: any) => err.message)
      .join(', ');
    errorMessage = `Validation failed: ${validationErrors}`;
    setTag('error.subtype', 'validation');
  } else if (error.code === 11000) {
    errorType = 'duplicate';
    errorStatus = 409;
    // Check which field caused the duplicate key error
    const keyPattern = error.keyPattern || {};
    const keyValue = error.keyValue || {};
    
    if (keyPattern.product_id) {
      errorMessage = `Duplicate entry: A product with ID ${keyValue.product_id} already exists`;
    } else if (keyPattern.sku) {
      errorMessage = `Duplicate entry: A product with SKU ${keyValue.sku} already exists`;
    } else if (keyPattern.barcode) {
      errorMessage = `Duplicate entry: A product with barcode ${keyValue.barcode} already exists`;
    } else {
      errorMessage = `Duplicate entry: This product already exists`;
    }
    setTag('error.subtype', 'duplicate_key');
  } else {
    errorType = 'database';
    errorStatus = 500;
    errorMessage = `Database error: ${error.message}`;
    setTag('error.subtype', 'general');
  }

  captureException(error, {
    errorType,
    errorStatus,
    errorMessage
  });

  return { message: errorMessage, status: errorStatus };
};

// Interface for the service
export interface IProductService extends IProduct {}

// Define DTOs for product operations
export interface IImage {
  url: string;
  alt_text?: string | null;
}

export interface IDimension {
  length: number;
  width: number;
  height: number;
  unit: string;
}

export interface IWeight {
  value: number;
  unit: string;
}

export interface IAttribute {
  name: string;
  value: string;
}

export interface IBomComponent {
  item_id: Types.ObjectId;
  item_type: 'Part' | 'Assembly';
  quantity: number;
  unit_of_measure: string;
}

/**
 * Interface for hierarchical component in DTOs
 */
export interface HierarchicalComponentDto {
  assemblyId: string;
  quantityRequired: number;
  children?: HierarchicalComponentDto[];
}

export interface CreateProductDto {
  productCode: string;
  name: string;
  description: string;
  categoryId: string;
  status: 'active' | 'discontinued' | 'in_development';
  sellingPrice: number;
  components?: HierarchicalComponentDto[];
  // Legacy fields for backward compatibility
  assemblyId?: string | null;
  partId?: string | null;
}

export interface UpdateProductDto {
  name?: string;
  description?: string;
  categoryId?: string;
  status?: 'active' | 'discontinued' | 'in_development';
  sellingPrice?: number;
  components?: HierarchicalComponentDto[];
  // Legacy fields for backward compatibility
  assemblyId?: string | null;
  partId?: string | null;
}

/**
 * Helper function to convert DTO components to model components
 */
function convertDtoComponentsToModel(dtoComponents: HierarchicalComponentDto[]): IHierarchicalComponent[] {
  return dtoComponents.map(component => ({
    assemblyId: new Types.ObjectId(component.assemblyId),
    quantityRequired: component.quantityRequired,
    children: component.children ? convertDtoComponentsToModel(component.children) : []
  }));
}

/**
 * Helper function to convert model components to DTO components
 */
function convertModelComponentsToDto(modelComponents: IHierarchicalComponent[]): HierarchicalComponentDto[] {
  return modelComponents.map(component => ({
    assemblyId: component.assemblyId.toString(),
    quantityRequired: component.quantityRequired,
    children: component.children ? convertModelComponentsToDto(component.children) : []
  }));
}

/**
 * Helper function to validate that all referenced assemblies exist
 */
async function validateComponentAssemblies(components: HierarchicalComponentDto[]): Promise<void> {
  for (const component of components) {
    if (!Types.ObjectId.isValid(component.assemblyId)) {
      throw new Error(`Invalid assembly ID format: ${component.assemblyId}`);
    }

    const assembly = await (Assembly.findById as any)(component.assemblyId);
    if (!assembly) {
      throw new Error(`Assembly with ID ${component.assemblyId} does not exist`);
    }

    // Recursively validate children
    if (component.children && component.children.length > 0) {
      await validateComponentAssemblies(component.children);
    }
  }
}

/**
 * Common aggregation pipeline to join parts with inventories and reconstruct inventory object
 * This pipeline maintains backward compatibility by reconstructing the embedded inventory structure
 * from the new dedicated inventories collection.
 *
 * SHARED WITH ASSEMBLY SERVICE: This ensures consistent inventory data across all features
 */
const getPartInventoryAggregationPipeline = () => [
  // Stage 1: Lookup inventories for each part
  {
    $lookup: {
      from: 'inventories',
      localField: '_id',
      foreignField: 'partId',
      as: 'inventoryRecords'
    }
  },

  // Stage 2: Lookup warehouse information for each inventory record
  {
    $lookup: {
      from: 'warehouses',
      localField: 'inventoryRecords.warehouseId',
      foreignField: '_id',
      as: 'warehouseInfo'
    }
  },

  // Stage 3: Reconstruct the inventory object to maintain backward compatibility
  {
    $addFields: {
      inventory: {
        $let: {
          vars: {
            // Group inventory records by warehouse (taking the first warehouse for now)
            warehouseInventory: { $arrayElemAt: ['$inventoryRecords', 0] },
            warehouseData: { $arrayElemAt: ['$warehouseInfo', 0] }
          },
          in: {
            stockLevels: {
              $reduce: {
                input: '$inventoryRecords',
                initialValue: { raw: 0, hardening: 0, grinding: 0, finished: 0, rejected: 0 },
                in: {
                  raw: { $add: ['$$value.raw', { $cond: [{ $eq: ['$$this.stockType', 'raw'] }, '$$this.quantity', 0] }] },
                  hardening: { $add: ['$$value.hardening', { $cond: [{ $eq: ['$$this.stockType', 'hardening'] }, '$$this.quantity', 0] }] },
                  grinding: { $add: ['$$value.grinding', { $cond: [{ $eq: ['$$this.stockType', 'grinding'] }, '$$this.quantity', 0] }] },
                  finished: { $add: ['$$value.finished', { $cond: [{ $eq: ['$$this.stockType', 'finished'] }, '$$this.quantity', 0] }] },
                  rejected: { $add: ['$$value.rejected', { $cond: [{ $eq: ['$$this.stockType', 'rejected'] }, '$$this.quantity', 0] }] }
                }
              }
            },
            warehouseId: '$$warehouseInventory.warehouseId',
            safetyStockLevel: { $ifNull: ['$$warehouseInventory.safetyStockLevel', 0] },
            maximumStockLevel: { $ifNull: ['$$warehouseInventory.maximumStockLevel', 0] },
            averageDailyUsage: { $ifNull: ['$$warehouseInventory.averageDailyUsage', 0] },
            abcClassification: { $ifNull: ['$$warehouseInventory.abcClassification', 'C'] },
            lastStockUpdate: '$$warehouseInventory.lastUpdated',
            // Backward compatibility: currentStock as finished quantity
            currentStock: {
              $reduce: {
                input: '$inventoryRecords',
                initialValue: 0,
                in: { $add: ['$$value', { $cond: [{ $eq: ['$$this.stockType', 'finished'] }, '$$this.quantity', 0] }] }
              }
            }
          }
        }
      }
    }
  },

  // Stage 4: Clean up temporary fields
  {
    $unset: ['inventoryRecords', 'warehouseInfo']
  }
];

/**
 * Aggregation pipeline to populate assemblies with parts that have inventory data for products
 * This replaces the old .populate() approach with a more efficient aggregation pipeline
 */
const getProductAssemblyAggregationPipeline = () => [
  // Stage 1: Lookup legacy assemblyId for backward compatibility
  {
    $lookup: {
      from: 'assemblies',
      let: { legacyAssemblyId: '$assemblyId' },
      pipeline: [
        { $match: { $expr: { $eq: ['$_id', '$$legacyAssemblyId'] } } },
        // Unwind partsRequired to process each part individually
        { $unwind: { path: '$partsRequired', preserveNullAndEmptyArrays: true } },
        // Lookup part details with inventory data
        {
          $lookup: {
            from: 'parts',
            let: { partId: '$partsRequired.partId' },
            pipeline: [
              { $match: { $expr: { $eq: ['$_id', '$$partId'] } } },
              ...getPartInventoryAggregationPipeline(),
              {
                $project: {
                  _id: 1,
                  partNumber: 1,
                  name: 1,
                  businessName: 1,
                  description: 1,
                  technicalSpecs: 1,
                  isManufactured: 1,
                  status: 1,
                  inventory: 1,
                  unitOfMeasure: 1,
                  costPrice: 1,
                  categoryId: 1,
                  isAssembly: 1
                }
              }
            ],
            as: 'partsRequired.partDetails'
          }
        },
        // Flatten the part details
        {
          $addFields: {
            'partsRequired.partId': { $arrayElemAt: ['$partsRequired.partDetails', 0] }
          }
        },
        // Clean up temporary fields
        { $unset: ['partsRequired.partDetails'] },
        // Group back to reconstruct the partsRequired array
        {
          $group: {
            _id: '$_id',
            name: { $first: '$name' },
            assemblyCode: { $first: '$assemblyCode' },
            productId: { $first: '$productId' },
            parentId: { $first: '$parentId' },
            isTopLevel: { $first: '$isTopLevel' },
            status: { $first: '$status' },
            version: { $first: '$version' },
            manufacturingInstructions: { $first: '$manufacturingInstructions' },
            estimatedBuildTime: { $first: '$estimatedBuildTime' },
            createdAt: { $first: '$createdAt' },
            updatedAt: { $first: '$updatedAt' },
            partsRequired: {
              $push: {
                $cond: [
                  { $ne: ['$partsRequired', null] },
                  '$partsRequired',
                  '$$REMOVE'
                ]
              }
            }
          }
        }
      ],
      as: 'legacyAssemblyData'
    }
  },

  // Stage 2: Flatten legacy assembly data
  {
    $addFields: {
      assemblyId: { $arrayElemAt: ['$legacyAssemblyData', 0] }
    }
  },

  // Stage 3: ENHANCED - Lookup assembly data for hierarchical components
  {
    $lookup: {
      from: 'assemblies',
      let: {
        componentAssemblyIds: {
          $cond: {
            if: { $isArray: '$components' },
            then: {
              $reduce: {
                input: '$components',
                initialValue: [],
                in: {
                  $concatArrays: [
                    '$$value',
                    {
                      $cond: {
                        if: { $ne: ['$$this.assemblyId', null] },
                        then: ['$$this.assemblyId'],
                        else: []
                      }
                    }
                  ]
                }
              }
            },
            else: []
          }
        }
      },
      pipeline: [
        { $match: { $expr: { $in: ['$_id', '$$componentAssemblyIds'] } } },
        {
          $project: {
            _id: 1,
            name: 1,
            assemblyCode: 1,
            description: 1,
            status: 1,
            partsRequired: 1
          }
        }
      ],
      as: 'hierarchicalAssemblyData'
    }
  },

  // Stage 4: ENHANCED - Populate hierarchical components with assembly data
  {
    $addFields: {
      components: {
        $cond: {
          if: { $isArray: '$components' },
          then: {
            $map: {
              input: '$components',
              as: 'component',
              in: {
                $mergeObjects: [
                  '$$component',
                  {
                    assemblyId: {
                      $let: {
                        vars: {
                          assemblyLookup: {
                            $arrayElemAt: [
                              {
                                $filter: {
                                  input: '$hierarchicalAssemblyData',
                                  cond: { $eq: ['$$this._id', '$$component.assemblyId'] }
                                }
                              },
                              0
                            ]
                          }
                        },
                        in: {
                          $cond: {
                            if: { $ne: ['$$assemblyLookup', null] },
                            then: '$$assemblyLookup',
                            else: '$$component.assemblyId'
                          }
                        }
                      }
                    }
                  }
                ]
              }
            }
          },
          else: '$components'
        }
      }
    }
  },

  // Stage 5: Clean up temporary fields
  {
    $unset: ['legacyAssemblyData', 'hierarchicalAssemblyData']
  }
];

/**
 * Fetches products with pagination, sorting, and filtering.
 */
export async function getAllProducts(options: any = {}) {
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 }, // Default sort by name
    filter = {},
    includeAssembly = false,
  } = options;

  logOperation('FETCH_ALL', 'service', {
    page,
    limit,
    sort: JSON.stringify(sort),
    filter: JSON.stringify(filter),
    includeAssembly
  });

  try {
    await connectToDatabase();
    const skip = (page - 1) * limit;

    // REFACTORED: Use aggregation pipeline with inventory integration instead of populate
    if (includeAssembly) {
      console.log('[ProductService] Using aggregation pipeline with inventory integration...');

      // Enhanced pipeline with assembly population and inventory data
      const enhancedPipeline = [
        { $match: filter },
        { $sort: sort },
        { $skip: skip },
        { $limit: limit },
        ...getProductAssemblyAggregationPipeline(),
        // Lookup category information
        {
          $lookup: {
            from: 'categories',
            localField: 'categoryId',
            foreignField: '_id',
            as: 'categoryData'
          }
        },
        // Flatten category data
        {
          $addFields: {
            categoryId: { $arrayElemAt: ['$categoryData', 0] }
          }
        },
        // Clean up temporary fields
        {
          $unset: ['categoryData']
        }
      ];

      const products = await Product.aggregate(enhancedPipeline);
      const totalCount = await Product.countDocuments(filter);

      console.log('[ProductService] Assembly data populated successfully via aggregation pipeline with inventory integration');

      // Transform hierarchical components for each product
      const transformedProducts = products.map((product: any) => {
        // Transform hierarchical components and legacy assembly data for card display
        if (product.components && product.components.length > 0) {
          // Components are already populated, transform for card display
          product.components = transformHierarchicalComponentsForCard(product.components);
        }
        // Fallback to legacy assemblyId transformation for backward compatibility
        else if (product.assemblyId && product.assemblyId.partsRequired) {
          // Create components array with assembly structure for BOM integration
          // The BOM integration helper expects components with assemblyId objects
          product.components = [{
            assemblyId: {
              _id: product.assemblyId._id,
              name: product.assemblyId.name,
              assemblyCode: product.assemblyId.assemblyCode,
              partsRequired: product.assemblyId.partsRequired
            },
            quantityRequired: 1, // Product contains 1 assembly
            children: [], // No nested assemblies in this structure
            // FIXED: Add assemblyDetails for frontend compatibility
            assemblyDetails: {
              _id: product.assemblyId._id,
              name: product.assemblyId.name,
              assemblyCode: product.assemblyId.assemblyCode,
              partsRequired: product.assemblyId.partsRequired
            }
          }];

          // Add legacy assembly info for backward compatibility
          product.assemblyDetails = {
            name: product.assemblyId.name,
            assemblyCode: product.assemblyId.assemblyCode,
            part_id: product.assemblyId.assemblyCode
          };
        }

        // Transform category data to match expected format
        if (product.categoryId) {
          product.category = {
            _id: product.categoryId._id,
            name: product.categoryId.name
          };
        }

        return product;
      });

      logOperation('FETCH_ALL_SUCCESS', 'service', {
        count: transformedProducts.length,
        totalCount,
        includeAssembly
      });

      return {
        products: transformedProducts,
        pagination: {
          page,
          limit,
          total: totalCount,
          pages: Math.ceil(totalCount / limit)
        }
      };
    }

    // Use aggregation pipeline for non-assembly requests (better performance)
    const pipeline = [
      { $match: filter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit },
      // Lookup to get category information if needed
      {
        $lookup: {
          from: 'categories',
          localField: 'categoryId',
          foreignField: '_id',
          as: 'categoryData'
        }
      },
      // Unwind and regroup category to get single object instead of array
      {
        $addFields: {
          category: {
            $cond: {
              if: { $gt: [{ $size: '$categoryData' }, 0] },
              then: {
                _id: { $arrayElemAt: ['$categoryData._id', 0] },
                name: { $arrayElemAt: ['$categoryData.name', 0] }
              },
              else: null
            }
          }
        }
      },
      // Remove the categoryData array
      { $project: { categoryData: 0 } }
    ];

    const products = await Product.aggregate(pipeline);
    const totalCount = await Product.countDocuments(filter);

    logOperation('FETCH_ALL_SUCCESS', 'service', {
      count: products.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit),
      includeAssembly: false
    });

    return {
      products,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    };
  } catch (error: any) {
    logOperation('FETCH_ALL_ERROR', 'service', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to fetch products');
  }
}

/**
 * Helper function to transform hierarchical components for UnifiedItemCard compatibility
 * Recursively processes nested components and converts them to the format expected by UnifiedItemCard
 * Also maintains compatibility with BOM integration helpers
 */
function transformHierarchicalComponentsForCard(components: any[]): any[] {
  return components.map((component: any) => {
    // Check if we have assemblyDetails (populated assembly data)
    const assemblyDetails = component.assemblyDetails;
    const assemblyId = component.assemblyId;

    // FIXED: Handle hierarchical components with proper assembly name resolution
    // If we have assemblyDetails, use it to populate assemblyId for BOM integration
    if (assemblyDetails) {
      // Create the component structure expected by UnifiedItemCard AND BOM integration
      const cardComponent: any = {
        // Put the populated assembly object in assemblyId for BOM integration
        // Include all necessary fields that BOM integration helper expects
        assemblyId: {
          _id: typeof assemblyId === 'string' ? assemblyId : assemblyId?._id,
          name: assemblyDetails.name,
          assemblyCode: assemblyDetails.assemblyCode,
          partsRequired: assemblyDetails.partsRequired || []
        },
        // Keep assemblyDetails for UnifiedItemCard display
        assemblyDetails: assemblyDetails,
        quantityRequired: component.quantityRequired,
        children: component.children ? transformHierarchicalComponentsForCard(component.children) : []
      };

      return cardComponent;
    }

    // FIXED: Handle case where assemblyId is populated (from aggregation pipeline)
    if (typeof assemblyId === 'object' && assemblyId && assemblyId.name) {
      return {
        assemblyId: assemblyId,
        // FIXED: Add assemblyDetails for frontend compatibility
        assemblyDetails: {
          _id: assemblyId._id,
          name: assemblyId.name,
          assemblyCode: assemblyId.assemblyCode,
          partsRequired: assemblyId.partsRequired || []
        },
        quantityRequired: component.quantityRequired,
        children: component.children ? transformHierarchicalComponentsForCard(component.children) : []
      };
    }

    // FIXED: Handle case where assemblyId is a string (ObjectId) - need to create placeholder
    if (typeof assemblyId === 'string' || (assemblyId && !assemblyId.name)) {
      return {
        assemblyId: assemblyId,
        // FIXED: Add placeholder assemblyDetails to prevent "Unknown Assembly"
        assemblyDetails: {
          _id: typeof assemblyId === 'string' ? assemblyId : assemblyId._id,
          name: 'Assembly (Loading...)', // Better than "Unknown Assembly"
          assemblyCode: 'LOADING',
          partsRequired: []
        },
        quantityRequired: component.quantityRequired,
        children: component.children ? transformHierarchicalComponentsForCard(component.children) : []
      };
    }

    // Final fallback: no assembly data available
    return {
      assemblyId: component.assemblyId,
      quantityRequired: component.quantityRequired,
      children: component.children ? transformHierarchicalComponentsForCard(component.children) : []
    };
  });
}

/**
 * Helper function to transform hierarchical components for BOM viewer compatibility
 * Recursively processes nested components and converts them to the format expected by EnhancedBomViewer
 */
function transformHierarchicalComponentsForBomViewer(components: any[], level: number = 0, parentId: string | null = null): any[] {
  const transformedComponents: any[] = [];

  components.forEach((component: any, index: number) => {
    const assembly = component.assemblyId;
    const componentId = `${parentId || 'root'}-${index}`;

    if (!assembly) {
      // Handle case where assembly data is not populated
      transformedComponents.push({
        assemblyId: component.assemblyId,
        quantityRequired: component.quantityRequired,
        level,
        parentId,
        children: component.children ? transformHierarchicalComponentsForBomViewer(component.children, level + 1, componentId) : []
      });
      return;
    }

    // Create the main assembly component for BOM viewer
    const bomComponent: any = {
      assembly: {
        _id: assembly._id,
        name: assembly.name,
        assemblyCode: assembly.assemblyCode,
        partsRequired: []
      },
      quantity: component.quantityRequired,
      assemblyId: assembly._id,
      level,
      parentId,
      children: []
    };

    // Process parts within this assembly
    if (assembly.partsRequired && assembly.partsRequired.length > 0) {
      assembly.partsRequired.forEach((partRequired: any, partIndex: number) => {
        const partName = typeof partRequired.partId?.name === 'object'
          ? (partRequired.partId.name.name || partRequired.partId.name._id || 'Unknown Part')
          : (partRequired.partId?.name || 'Unknown Part');

        const partComponent = {
          part: {
            _id: partRequired.partId?._id || `part-${componentId}-${partIndex}`,
            name: partName,
            partNumber: partRequired.partId?.partNumber,
            description: partRequired.partId?.description,
            category: partRequired.partId?.category,
            type: partRequired.partId?.type,
            current_stock: partRequired.partId?.inventory?.currentStock || 0,
            is_assembly: partRequired.partId?.isAssembly || false
          },
          quantity: partRequired.quantityRequired,
          unitOfMeasure: partRequired.unitOfMeasure,
          partId: partRequired.partId?._id,
          level: level + 1,
          parentId: componentId
        };

        bomComponent.assembly.partsRequired.push(partComponent);
      });
    }

    // Recursively process child components
    if (component.children && component.children.length > 0) {
      bomComponent.children = transformHierarchicalComponentsForBomViewer(component.children, level + 1, componentId);
    }

    transformedComponents.push(bomComponent);
  });

  return transformedComponents;
}

/**
 * Helper function to flatten hierarchical components into a flat array for BOM viewer
 * This is used when the BOM viewer expects a flat structure with level indicators
 */
function flattenHierarchicalComponents(components: any[], level: number = 0, parentId: string | null = null): any[] {
  const flattened: any[] = [];

  components.forEach((component: any, index: number) => {
    const componentId = `${parentId || 'root'}-${index}`;

    // Add the current component
    flattened.push({
      ...component,
      level,
      parentId,
      componentId
    });

    // Recursively add children
    if (component.children && component.children.length > 0) {
      const childComponents = flattenHierarchicalComponents(component.children, level + 1, componentId);
      flattened.push(...childComponents);
    }

    // If this is an assembly with parts, add them as well
    if (component.assembly && component.assembly.partsRequired) {
      component.assembly.partsRequired.forEach((part: any, partIndex: number) => {
        flattened.push({
          ...part,
          level: level + 1,
          parentId: componentId,
          componentId: `${componentId}-part-${partIndex}`
        });
      });
    }
  });

  return flattened;
}

/**
 * Helper function to count hierarchical parts recursively
 * This processes nested children arrays to display total counts including all nested levels
 */
function countHierarchicalParts(components: IHierarchicalComponent[]): {
  totalComponents: number;
  totalParts: number;
  maxDepth: number;
} {
  let totalComponents = 0;
  let totalParts = 0;
  let maxDepth = 0;

  function countRecursively(items: IHierarchicalComponent[], currentDepth: number = 0): void {
    maxDepth = Math.max(maxDepth, currentDepth);

    items.forEach((component) => {
      totalComponents++;

      // Count parts in this component's assembly (if populated)
      if ((component as any).assemblyId?.partsRequired) {
        totalParts += (component as any).assemblyId.partsRequired.length;
      }

      // Recursively count children
      if (component.children && component.children.length > 0) {
        countRecursively(component.children, currentDepth + 1);
      }
    });
  }

  countRecursively(components);

  return {
    totalComponents,
    totalParts,
    maxDepth
  };
}

/**
 * Helper function to validate hierarchical component structure
 * Ensures no circular references and validates assembly references
 */
async function validateHierarchicalComponentStructure(
  components: IHierarchicalComponent[],
  visited: Set<string> = new Set(),
  depth: number = 0
): Promise<{
  isValid: boolean;
  errors: string[];
  maxDepth: number;
}> {
  const errors: string[] = [];
  let maxDepth = depth;

  // Prevent infinite recursion
  if (depth > 10) {
    errors.push('Maximum nesting depth exceeded (10 levels)');
    return { isValid: false, errors, maxDepth };
  }

  for (const component of components) {
    const assemblyIdStr = component.assemblyId.toString();

    // Check for circular references
    if (visited.has(assemblyIdStr)) {
      errors.push(`Circular reference detected: Assembly ${assemblyIdStr} appears multiple times in the hierarchy`);
      continue;
    }

    // Add to visited set for this path
    const newVisited = new Set(visited);
    newVisited.add(assemblyIdStr);

    // Validate assembly exists
    try {
      const assembly = await (Assembly.findById as any)(component.assemblyId);
      if (!assembly) {
        errors.push(`Assembly ${assemblyIdStr} not found`);
      }
    } catch (error) {
      errors.push(`Error validating assembly ${assemblyIdStr}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Validate quantity
    if (component.quantityRequired <= 0) {
      errors.push(`Invalid quantity ${component.quantityRequired} for assembly ${assemblyIdStr}`);
    }

    // Recursively validate children
    if (component.children && component.children.length > 0) {
      const childValidation = await validateHierarchicalComponentStructure(
        component.children,
        newVisited,
        depth + 1
      );

      errors.push(...childValidation.errors);
      maxDepth = Math.max(maxDepth, childValidation.maxDepth);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    maxDepth
  };
}

/**
 * Gets a single product by its MongoDB ObjectId.
 */
export async function getProductById(id: string, includeAssembly: boolean = false): Promise<IProductService | null> {
  logOperation('GET_BY_ID', 'service', { id, includeAssembly });

  if (!Types.ObjectId.isValid(id)) {
    logOperation('GET_BY_ID_INVALID_FORMAT', 'service', { id });
    throw new Error('Invalid ID format');
  }

  try {
    await connectToDatabase();

    // Ensure models are registered if we need assembly data
    let product: any = null;

    if (includeAssembly) {
      // REFACTORED: Use aggregation pipeline with inventory integration
      console.log('[ProductService] Using aggregation pipeline with inventory integration for getProductById...');

      const pipeline = [
        { $match: { _id: new Types.ObjectId(id) } },
        ...getProductAssemblyAggregationPipeline(),
        // Lookup category information
        {
          $lookup: {
            from: 'categories',
            localField: 'categoryId',
            foreignField: '_id',
            as: 'categoryData'
          }
        },
        // Flatten category data
        {
          $addFields: {
            categoryId: { $arrayElemAt: ['$categoryData', 0] }
          }
        },
        // Clean up temporary fields
        {
          $unset: ['categoryData']
        }
      ];

      const results = await Product.aggregate(pipeline);
      product = results.length > 0 ? results[0] : null;
    } else {
      // Simple find without assembly population
      product = await Product.findById(id)
        .populate({
          path: 'categoryId',
          model: 'Category',
          select: '_id name'
        })
        .lean();
    }

    if (!product) {
      logOperation('GET_BY_ID_NOT_FOUND', 'service', { id });
      return null;
    }

    // Transform hierarchical components and legacy assembly data for BOM viewer compatibility
    if (includeAssembly) {
      // If product has hierarchical components, use them
      if (product.components && product.components.length > 0) {
        // Components are already populated, just ensure proper format for BOM viewer
        (product as any).components = transformHierarchicalComponentsForBomViewer(product.components);
      }
      // Fallback to legacy assemblyId transformation for backward compatibility
      else if (product.assemblyId && (product.assemblyId as any).partsRequired) {
        // Transform partsRequired to components format expected by BomViewer
        (product as any).components = (product.assemblyId as any).partsRequired.map((partRequired: any) => {
          // Ensure name is always a string, not an object
          const partName = typeof partRequired.partId.name === 'object'
            ? (partRequired.partId.name.name || partRequired.partId.name._id || 'Unknown Part')
            : (partRequired.partId.name || 'Unknown Part');

          return {
            part: {
              _id: partRequired.partId._id,
              name: partName,
              partNumber: partRequired.partId.partNumber,
              description: partRequired.partId.description,
              category: partRequired.partId.category,
              type: partRequired.partId.type,
              current_stock: partRequired.partId.inventory?.currentStock || 0,
              is_assembly: partRequired.partId.isAssembly || false
            },
            quantity: partRequired.quantityRequired,
            unitOfMeasure: partRequired.unitOfMeasure,
            partId: partRequired.partId._id
          };
        });
        (product as any).main_assembly_id = {
          name: (product.assemblyId as any).name,
          part_id: (product.assemblyId as any).assemblyCode
        };
      }
    }

    logOperation('GET_BY_ID_SUCCESS', 'service', { id, includeAssembly });
    return product;
  } catch (error: any) {
    logOperation('GET_BY_ID_ERROR', 'service', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get product by ID ${id}`);
  }
}

/**
 * Gets a single product by its product_id.
 */
export async function getProductByProductId(productId: string, includeAssembly: boolean = false): Promise<IProductService | null> {
  logOperation('GET_BY_PRODUCT_ID', 'service', { productId, includeAssembly });
  try {
    await connectToDatabase();

    let product: any = null;

    if (includeAssembly) {
      // REFACTORED: Use aggregation pipeline with inventory integration
      console.log('[ProductService] Using aggregation pipeline with inventory integration for getProductByProductId...');

      const pipeline = [
        { $match: { product_id: productId } },
        ...getProductAssemblyAggregationPipeline(),
        // Lookup category information
        {
          $lookup: {
            from: 'categories',
            localField: 'categoryId',
            foreignField: '_id',
            as: 'categoryData'
          }
        },
        // Flatten category data
        {
          $addFields: {
            categoryId: { $arrayElemAt: ['$categoryData', 0] }
          }
        },
        // Clean up temporary fields
        {
          $unset: ['categoryData']
        }
      ];

      const results = await Product.aggregate(pipeline);
      product = results.length > 0 ? results[0] : null;
    } else {
      // Simple find without assembly population
      product = await Product.findOne({ product_id: productId })
        .populate({
          path: 'categoryId',
          model: 'Category',
          select: '_id name'
        })
        .lean();
    }

    if (!product) {
      logOperation('GET_BY_PRODUCT_ID_NOT_FOUND', 'service', { productId });
      return null;
    }

    // Transform assembly data to components format for BOM viewer compatibility
    if (includeAssembly && product.assemblyId && (product.assemblyId as any).partsRequired) {
      // Transform partsRequired to components format expected by BomViewer
      (product as any).components = (product.assemblyId as any).partsRequired.map((partRequired: any) => {
        // Ensure name is always a string, not an object
        const partName = typeof partRequired.partId.name === 'object'
          ? (partRequired.partId.name.name || partRequired.partId.name._id || 'Unknown Part')
          : (partRequired.partId.name || 'Unknown Part');

        return {
          part: {
            _id: partRequired.partId._id,
            name: partName,
            partNumber: partRequired.partId.partNumber,
            description: partRequired.partId.description,
            category: partRequired.partId.category,
            type: partRequired.partId.type,
            current_stock: partRequired.partId.inventory?.currentStock || 0,
            is_assembly: partRequired.partId.isAssembly || false
          },
          quantity: partRequired.quantityRequired,
          unitOfMeasure: partRequired.unitOfMeasure,
          partId: partRequired.partId._id
        };
      });
      (product as any).main_assembly_id = {
        name: (product.assemblyId as any).name,
        part_id: (product.assemblyId as any).assemblyCode
      };
    }

    logOperation('GET_BY_PRODUCT_ID_SUCCESS', 'service', { productId });
    return product;
  } catch (error: any) {
    logOperation('GET_BY_PRODUCT_ID_ERROR', 'service', { productId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get product by product_id ${productId}`);
  }
}

/**
 * Gets a single product by its SKU.
 */
export async function getProductBySku(sku: string): Promise<IProductService | null> {
  logOperation('GET_BY_SKU', 'service', { sku });
  try {
    await connectToDatabase();

    const product = await (Product.findOne as any)({ sku })
      .populate({
        path: 'categoryId',
        model: 'Category',
        select: '_id name'
      })
      .lean() as IProductService | null;
    
    if (!product) {
      logOperation('GET_BY_SKU_NOT_FOUND', 'service', { sku });
      return null;
    }
    
    logOperation('GET_BY_SKU_SUCCESS', 'service', { sku });
    return product;
  } catch (error: any) {
    logOperation('GET_BY_SKU_ERROR', 'service', { sku, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get product by SKU ${sku}`);
  }
}

/**
 * Creates a new product.
 */
export async function createProduct(productData: CreateProductDto): Promise<IProductService> {
  logOperation('CREATE', 'service', { 
    productCode: productData.productCode,
    name: productData.name 
  });
  
  try {
    await connectToDatabase();

    // Check if category exists if provided
    if (productData.categoryId) {
      if (!Types.ObjectId.isValid(productData.categoryId)) {
        throw new Error('Invalid category ID format');
      }

      const categoryExists = await (Category.findById as any)(productData.categoryId);
      if (!categoryExists) {
        throw new Error(`Category with ID ${productData.categoryId} does not exist`);
      }
    }

    // Validate hierarchical components if provided
    if (productData.components && productData.components.length > 0) {
      await validateComponentAssemblies(productData.components);
    }

    // Prepare product data
    const productPayload: any = {
      productCode: productData.productCode,
      name: productData.name,
      description: productData.description,
      categoryId: productData.categoryId ? new Types.ObjectId(productData.categoryId) : null,
      status: productData.status,
      sellingPrice: productData.sellingPrice
    };

    // Add hierarchical components if provided
    if (productData.components && productData.components.length > 0) {
      productPayload.components = convertDtoComponentsToModel(productData.components);
    }

    // Add legacy fields for backward compatibility
    if (productData.assemblyId) {
      productPayload.assemblyId = new Types.ObjectId(productData.assemblyId);
    }
    if (productData.partId) {
      productPayload.partId = new Types.ObjectId(productData.partId);
    }

    // Create product based on the schema
    const newProduct = new Product(productPayload);

    const savedProduct = await newProduct.save();
    
    logOperation('CREATE_SUCCESS', 'service', {
      _id: savedProduct._id,
      productCode: savedProduct.productCode
    });

    return savedProduct.toObject() as IProductService;
  } catch (error: any) {
    logOperation('CREATE_ERROR', 'service', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to create product');
  }
}

/**
 * Updates an existing product by its MongoDB ObjectId.
 */
export async function updateProductById(id: string, updateData: UpdateProductDto): Promise<IProductService | null> {
  logOperation('UPDATE_BY_ID', 'service', { id, data: updateData });
  
  if (!Types.ObjectId.isValid(id)) {
    logOperation('UPDATE_BY_ID_INVALID_FORMAT', 'service', { id });
    throw new Error('Invalid ID format');
  }
  
  try {
    await connectToDatabase();
    
    // Check if product exists
    const existingProduct = await (Product.findById as any)(id);
    if (!existingProduct) {
      logOperation('UPDATE_BY_ID_NOT_FOUND', 'service', { id });
      return null;
    }
    
    // Check if category exists if provided
    if (updateData.categoryId) {
      if (!Types.ObjectId.isValid(updateData.categoryId)) {
        throw new Error('Invalid category ID format');
      }

      const categoryExists = await (Category.findById as any)(updateData.categoryId);
      if (!categoryExists) {
        throw new Error(`Category with ID ${updateData.categoryId} does not exist`);
      }
    }

    // Validate hierarchical components if provided
    if (updateData.components && updateData.components.length > 0) {
      await validateComponentAssemblies(updateData.components);
    }

    // Prepare update payload, converting IDs to ObjectIds as needed
    const updatePayload: any = {};

    // Copy basic fields
    if (updateData.name !== undefined) updatePayload.name = updateData.name;
    if (updateData.description !== undefined) updatePayload.description = updateData.description;
    if (updateData.status !== undefined) updatePayload.status = updateData.status;
    if (updateData.sellingPrice !== undefined) updatePayload.sellingPrice = updateData.sellingPrice;

    // Handle category ID
    if (updateData.categoryId) {
      updatePayload.categoryId = new Types.ObjectId(updateData.categoryId);
    }

    // Handle hierarchical components
    if (updateData.components !== undefined) {
      if (updateData.components.length > 0) {
        updatePayload.components = convertDtoComponentsToModel(updateData.components);
      } else {
        updatePayload.components = [];
      }
    }

    // Handle legacy fields for backward compatibility
    if (updateData.assemblyId !== undefined) {
      updatePayload.assemblyId = updateData.assemblyId ? new Types.ObjectId(updateData.assemblyId) : null;
    }
    if (updateData.partId !== undefined) {
      updatePayload.partId = updateData.partId ? new Types.ObjectId(updateData.partId) : null;
    }
    
    const product = await (Product.findByIdAndUpdate as any)(
      id,
      { $set: updatePayload },
      { new: true, runValidators: true }
    ).lean() as IProductService | null;

    if (!product) {
      logOperation('UPDATE_BY_ID_NOT_FOUND', 'service', { id });
      return null;
    }
    
    logOperation('UPDATE_BY_ID_SUCCESS', 'service', { id });
    return product;
  } catch (error: any) {
    logOperation('UPDATE_BY_ID_ERROR', 'service', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to update product with ID ${id}`);
  }
}

/**
 * Updates an existing product by its product_id field.
 */
export async function updateProductByProductId(productId: string, updateData: UpdateProductDto): Promise<IProductService | null> {
  logOperation('UPDATE_BY_PRODUCT_ID', 'service', { productId, data: updateData });
  
  try {
    await connectToDatabase();
    
    // Check if product exists
    const existingProduct = await (Product.findOne as any)({ productCode: productId });
    if (!existingProduct) {
      logOperation('UPDATE_BY_PRODUCT_ID_NOT_FOUND', 'service', { productId });
      return null;
    }
    
    // Check if category exists if provided
    if (updateData.categoryId) {
      if (!Types.ObjectId.isValid(updateData.categoryId)) {
        throw new Error('Invalid category ID format');
      }
      
      const categoryExists = await (Category.findById as any)(updateData.categoryId);
      if (!categoryExists) {
        throw new Error(`Category with ID ${updateData.categoryId} does not exist`);
      }
    }
    
    // Prepare update payload, converting IDs to ObjectIds as needed
    const updatePayload: any = { ...updateData };
    if (updateData.categoryId) {
      updatePayload.categoryId = new Types.ObjectId(updateData.categoryId);
    }
    if (updateData.assemblyId) {
      updatePayload.assemblyId = new Types.ObjectId(updateData.assemblyId);
    }
    if (updateData.partId) {
      updatePayload.partId = new Types.ObjectId(updateData.partId);
    }
    
    const productByProductId = await (Product.findOneAndUpdate as any)(
      { productCode: productId },
      { $set: updatePayload },
      { new: true, runValidators: true }
    ).lean() as IProductService | null;
    if (!productByProductId) {
      logOperation('UPDATE_BY_PRODUCT_ID_NOT_FOUND', 'service', { productId });
      return null;
    }
    logOperation('UPDATE_BY_PRODUCT_ID_SUCCESS', 'service', { productId });
    return productByProductId;
  } catch (error: any) {
    logOperation('UPDATE_BY_PRODUCT_ID_ERROR', 'service', { productId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to update product with product_id ${productId}`);
  }
}

/**
 * Deletes a product by its MongoDB ObjectId.
 */
export async function deleteProductById(id: string): Promise<{ success: boolean; message: string }> {
  logOperation('DELETE_BY_ID', 'service', { id });
  
  if (!Types.ObjectId.isValid(id)) {
    logOperation('DELETE_BY_ID_INVALID_FORMAT', 'service', { id });
    throw new Error('Invalid ID format');
  }
  
  try {
    await connectToDatabase();
    
    // Could add additional checks here, like:
    // - Check if product is referenced in sales orders
    // - Check if product is part of an active work order
    // - etc.
    
    const result = await (Product.findByIdAndDelete as any)(id);
    
    if (!result) {
      logOperation('DELETE_BY_ID_NOT_FOUND', 'service', { id });
      throw new Error(`Product with ID ${id} not found or already deleted`);
    }
    
    logOperation('DELETE_BY_ID_SUCCESS', 'service', { id });
    return { success: true, message: `Product ${id} deleted` };
  } catch (error: any) {
    logOperation('DELETE_BY_ID_ERROR', 'service', { id, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to delete product with ID ${id}`);
  }
}

/**
 * Deletes a product by its product_id field.
 */
export async function deleteProductByProductId(productId: string): Promise<{ success: boolean; message: string }> {
  logOperation('DELETE_BY_PRODUCT_ID', 'service', { productId });
  
  try {
    await connectToDatabase();
    
    // Could add additional checks here
    
    const result = await (Product.findOneAndDelete as any)({ product_id: productId });
    
    if (!result) {
      logOperation('DELETE_BY_PRODUCT_ID_NOT_FOUND', 'service', { productId });
      throw new Error(`Product with product_id ${productId} not found or already deleted`);
    }
    
    logOperation('DELETE_BY_PRODUCT_ID_SUCCESS', 'service', { productId });
    return { success: true, message: `Product ${productId} deleted` };
  } catch (error: any) {
    logOperation('DELETE_BY_PRODUCT_ID_ERROR', 'service', { productId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to delete product with product_id ${productId}`);
  }
}

/**
 * Searches products based on a text query string.
 */
export async function searchProducts(options: any = {}) {
  const {
    query = '',
    page = 1,
    limit = 20,
    sort = { name: 1 },
    filter = {},
    includeAssembly = false
  } = options;

  logOperation('SEARCH', 'service', { query, page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter), includeAssembly });

  try {
    await connectToDatabase();
    const skip = (page - 1) * limit;

    // Build search filter combining text search and other filters
    let searchFilter: any = { ...filter };

    if (query) {
      // Create regex search across multiple fields
      searchFilter.$or = [
        { name: new RegExp(query, 'i') },
        { productCode: new RegExp(query, 'i') }, // Corrected from product_id
        { description: new RegExp(query, 'i') }
        // TODO: Clarify if sku, barcode, and tags are required product fields.
        // If so, add them to the Product model and uncomment the lines below.
        // { sku: new RegExp(query, 'i') },
        // { barcode: new RegExp(query, 'i') },
        // { tags: new RegExp(query, 'i') } // Ensure 'tags' field exists and is indexed if used
      ];
    }

    // REFACTORED: Use aggregation pipeline with inventory integration instead of populate
    if (includeAssembly) {
      console.log('[ProductService] Using aggregation pipeline with inventory integration for searchProducts...');

      // Enhanced pipeline with assembly population and inventory data
      const enhancedPipeline = [
        { $match: searchFilter },
        { $sort: sort },
        { $skip: skip },
        { $limit: limit },
        ...getProductAssemblyAggregationPipeline(),
        // Lookup category information
        {
          $lookup: {
            from: 'categories',
            localField: 'categoryId',
            foreignField: '_id',
            as: 'categoryData'
          }
        },
        // Flatten category data
        {
          $addFields: {
            categoryId: { $arrayElemAt: ['$categoryData', 0] }
          }
        },
        // Clean up temporary fields
        {
          $unset: ['categoryData']
        }
      ];

      const products = await Product.aggregate(enhancedPipeline);
      const totalCount = await Product.countDocuments(searchFilter);

      console.log('[ProductService] Search with assembly data completed successfully via aggregation pipeline with inventory integration');

      // Transform hierarchical components for each product
      const transformedProducts = products.map((product: any) => {
        // Transform hierarchical components and legacy assembly data for card display
        if (product.components && product.components.length > 0) {
          // Components are already populated, transform for card display
          product.components = transformHierarchicalComponentsForCard(product.components);
        }
        // Fallback to legacy assemblyId transformation for backward compatibility
        else if (product.assemblyId && product.assemblyId.partsRequired) {
          // Create components array with assembly structure for BOM integration
          // The BOM integration helper expects components with assemblyId objects
          product.components = [{
            assemblyId: {
              _id: product.assemblyId._id,
              name: product.assemblyId.name,
              assemblyCode: product.assemblyId.assemblyCode,
              partsRequired: product.assemblyId.partsRequired
            },
            quantityRequired: 1, // Product contains 1 assembly
            children: [], // No nested assemblies in this structure
            // FIXED: Add assemblyDetails for frontend compatibility
            assemblyDetails: {
              _id: product.assemblyId._id,
              name: product.assemblyId.name,
              assemblyCode: product.assemblyId.assemblyCode,
              partsRequired: product.assemblyId.partsRequired
            }
          }];

          // Add legacy assembly info for backward compatibility
          product.assemblyDetails = {
            name: product.assemblyId.name,
            assemblyCode: product.assemblyId.assemblyCode,
            part_id: product.assemblyId.assemblyCode
          };
        }

        // Transform category data to match expected format
        if (product.categoryId) {
          product.category = {
            _id: product.categoryId._id,
            name: product.categoryId.name
          };
        }

        return product;
      });

      const pagination = {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      };

      logOperation('SEARCH_SUCCESS', 'service', {
        query,
        count: transformedProducts.length,
        totalCount,
        includeAssembly
      });

      return {
        products: transformedProducts,
        pagination
      };
    }

    const pipeline = [
      { $match: searchFilter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit },
      // Lookup to get category information
      {
        $lookup: {
          from: 'categories',
          localField: 'categoryId',
          foreignField: '_id',
          as: 'categoryData'
        }
      },
      // Unwind and regroup category to get single object instead of array
      { 
        $addFields: { 
          category: { 
            $cond: { 
              if: { $gt: [{ $size: '$categoryData' }, 0] }, 
              then: { 
                _id: { $arrayElemAt: ['$categoryData._id', 0] },
                name: { $arrayElemAt: ['$categoryData.name', 0] }
              }, 
              else: null 
            } 
          } 
        } 
      },
      // Remove the categoryData array
      { $project: { categoryData: 0 } }
    ];

    const [products, totalCount] = await Promise.all([
      Product.aggregate(pipeline),
      Product.countDocuments(searchFilter)
    ]);

    const pagination = {
      page,
      limit,
      total: totalCount,
      pages: Math.ceil(totalCount / limit)
    };

    logOperation('SEARCH_SUCCESS', 'service', {
      query,
      count: products.length,
      totalCount,
      includeAssembly: false
    });
    return { products, pagination };
  } catch (error: any) {
    logOperation('SEARCH_ERROR', 'service', { query, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to search products');
  }
}

/**
 * Gets products by categoryId with pagination.
 */
export async function getProductsByCategory(categoryId: string, options: any = {}) {
  logOperation('GET_BY_CATEGORY', 'service', { categoryId });
  
  if (!Types.ObjectId.isValid(categoryId)) {
    logOperation('GET_BY_CATEGORY_INVALID_FORMAT', 'service', { categoryId });
    throw new Error('Invalid category ID format');
  }
  
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 },
    filter = {}
  } = options;
  
  try {
    await connectToDatabase();
    const skip = (page - 1) * limit;
    
    // Combine category filter with additional filters
    const searchFilter = {
      ...filter,
      categoryId: new Types.ObjectId(categoryId)
    };
    
    const pipeline = [
      { $match: searchFilter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit },
      // Lookup to get category information
      {
        $lookup: {
          from: 'categories',
          localField: 'categoryId',
          foreignField: '_id',
          as: 'categoryData'
        }
      },
      // Unwind and regroup category to get single object instead of array
      { 
        $addFields: { 
          category: { 
            $cond: { 
              if: { $gt: [{ $size: '$categoryData' }, 0] }, 
              then: { 
                _id: { $arrayElemAt: ['$categoryData._id', 0] },
                name: { $arrayElemAt: ['$categoryData.name', 0] }
              }, 
              else: null 
            } 
          } 
        } 
      },
      // Remove the categoryData array
      { $project: { categoryData: 0 } }
    ];
    
    const [products, totalCount] = await Promise.all([
      Product.aggregate(pipeline),
      Product.countDocuments(searchFilter)
    ]);
    
    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };
    
    logOperation('GET_BY_CATEGORY_SUCCESS', 'service', { categoryId, count: products.length, pagination });
    return { products, pagination };
  } catch (error: any) {
    logOperation('GET_BY_CATEGORY_ERROR', 'service', { categoryId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get products for category ${categoryId}`);
  }
}

/**
 * Gets products by status with pagination.
 */
export async function getProductsByStatus(status: 'active' | 'discontinued' | 'in_development', options: any = {}) {
  logOperation('GET_BY_STATUS', 'service', { status });
  
  const {
    page = 1,
    limit = 20,
    sort = { name: 1 },
    filter = {}
  } = options;
  
  try {
    await connectToDatabase();
    const skip = (page - 1) * limit;
    
    // Combine status filter with additional filters
    const searchFilter = { 
      ...filter,
      status
    };
    
    const pipeline = [
      { $match: searchFilter },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit },
      // Lookup to get category information
      {
        $lookup: {
          from: 'categories',
          localField: 'categoryId',
          foreignField: '_id',
          as: 'categoryData'
        }
      },
      // Unwind and regroup category to get single object instead of array
      { 
        $addFields: { 
          category: { 
            $cond: { 
              if: { $gt: [{ $size: '$categoryData' }, 0] }, 
              then: { 
                _id: { $arrayElemAt: ['$categoryData._id', 0] },
                name: { $arrayElemAt: ['$categoryData.name', 0] }
              }, 
              else: null 
            } 
          } 
        } 
      },
      // Remove the categoryData array
      { $project: { categoryData: 0 } }
    ];
    
    const [products, totalCount] = await Promise.all([
      Product.aggregate(pipeline),
      Product.countDocuments(searchFilter)
    ]);
    
    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };
    
    logOperation('GET_BY_STATUS_SUCCESS', 'service', { status, count: products.length, pagination });
    return { products, pagination };
  } catch (error: any) {
    logOperation('GET_BY_STATUS_ERROR', 'service', { status, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to get products with status ${status}`);
  }
} 