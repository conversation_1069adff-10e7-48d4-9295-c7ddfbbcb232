import mongoose, { Types } from 'mongoose';
import { connectToDatabase } from '@/app/lib/mongodb';
import { Inventories, IInventories } from '@/app/models/inventories.model';
import { Location } from '@/app/models/location.model';
import { Part } from '@/app/models/part.model';
import { logOperation } from '@/app/services/logging';

// DTOs for inventory operations with location support
export interface CreateInventoryDto {
  partId: string;
  locationId: string; // Updated to use locationId instead of warehouseId
  stockType: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected';
  quantity: number;
  safetyStockLevel?: number;
  maximumStockLevel?: number;
  averageDailyUsage?: number;
  abcClassification?: string;
  notes?: string;
}

export interface UpdateInventoryDto extends Partial<CreateInventoryDto> {}

export interface InventoryQueryOptions {
  page?: number;
  limit?: number;
  sort?: Record<string, 1 | -1>;
  filter?: Record<string, any>;
  partId?: string;
  locationId?: string;
  warehouseId?: string; // For filtering by warehouse (will lookup locations)
  stockType?: string;
}

// Interface for aggregated inventory data
export interface AggregatedInventoryData {
  partId: string;
  warehouses: {
    warehouseId: string;
    warehouseName: string;
    totalStock: number;
    locations: {
      locationId: string;
      locationName: string;
      stockTypes: {
        stockType: string;
        quantity: number;
        safetyStockLevel?: number;
        maximumStockLevel?: number;
      }[];
    }[];
    stockTypes?: {
      stockType: string;
      quantity: number;
      safetyStockLevel?: number;
      maximumStockLevel?: number;
    }[]; // Add stockTypes at warehouse level for backward compatibility
  }[];
  totalStockAllWarehouses: number;
  grandTotal?: number; // Add for frontend compatibility
  totals?: {
    raw: number;
    hardening: number;
    grinding: number;
    finished: number;
    rejected: number;
  }; // Add for frontend compatibility
  warehouseCount?: number; // Add for frontend compatibility
  stockLevelsSummary: {
    raw: number;
    hardening: number;
    grinding: number;
    finished: number;
    rejected: number;
  };
}

/**
 * Creates or updates inventory record for a specific part at a specific location
 */
export async function upsertInventory(inventoryData: CreateInventoryDto): Promise<IInventories> {
  logOperation('UPSERT_INVENTORY_V2', 'service', { 
    partId: inventoryData.partId, 
    locationId: inventoryData.locationId,
    stockType: inventoryData.stockType 
  });

  try {
    await connectToDatabase();

    // Validate ObjectIds
    if (!Types.ObjectId.isValid(inventoryData.partId) || 
        !Types.ObjectId.isValid(inventoryData.locationId)) {
      throw new Error('Invalid part ID or location ID format');
    }

    // Verify part and location exist
    const [part, location] = await Promise.all([
      Part.findById(inventoryData.partId),
      Location.findById(inventoryData.locationId)
    ]);

    if (!part) {
      throw new Error(`Part with ID ${inventoryData.partId} not found`);
    }

    if (!location) {
      throw new Error(`Location with ID ${inventoryData.locationId} not found`);
    }

    // Upsert inventory record
    const filter = {
      partId: new Types.ObjectId(inventoryData.partId),
      locationId: new Types.ObjectId(inventoryData.locationId),
      stockType: inventoryData.stockType
    };

    const updateData = {
      ...inventoryData,
      partId: new Types.ObjectId(inventoryData.partId),
      locationId: new Types.ObjectId(inventoryData.locationId),
      lastUpdated: new Date(),
      // BACKWARD COMPATIBILITY: Also set warehouseId for migration period
      warehouseId: location.warehouseId
    };

    const inventory = await Inventories.findOneAndUpdate(
      filter,
      updateData,
      { 
        new: true, 
        upsert: true, 
        runValidators: true,
        setDefaultsOnInsert: true
      }
    );

    logOperation('UPSERT_INVENTORY_V2_SUCCESS', 'service', {
      inventoryId: inventory._id,
      partId: inventoryData.partId,
      locationId: inventoryData.locationId,
      stockType: inventoryData.stockType,
      quantity: inventoryData.quantity
    });

    return inventory;

  } catch (error: any) {
    logOperation('UPSERT_INVENTORY_V2_ERROR', 'service', {
      partId: inventoryData.partId,
      locationId: inventoryData.locationId,
      error: error.message
    });
    throw error;
  }
}

/**
 * Gets detailed inventory breakdown for a specific part
 */
export async function getPartInventoryBreakdown(partId: string): Promise<AggregatedInventoryData> {
  logOperation('GET_PART_INVENTORY_BREAKDOWN', 'service', { partId });

  try {
    await connectToDatabase();

    if (!Types.ObjectId.isValid(partId)) {
      throw new Error('Invalid part ID format');
    }

    // Complex aggregation to get hierarchical inventory data
    // NOTE: Fallback to warehouse-based aggregation since locations collection doesn't exist yet
    const pipeline = [
      // Match inventories for this part
      { $match: { partId: new Types.ObjectId(partId) } },

      // Lookup warehouse details directly (since locationId doesn't exist yet)
      {
        $lookup: {
          from: 'warehouses',
          localField: 'warehouseId',
          foreignField: '_id',
          as: 'warehouse'
        }
      },
      { $unwind: '$warehouse' },
      
      // Group by warehouse (no locations yet)
      {
        $group: {
          _id: {
            warehouseId: '$warehouse._id',
            warehouseName: '$warehouse.name'
          },
          stockTypes: {
            $push: {
              stockType: '$stockType',
              quantity: '$quantity',
              safetyStockLevel: '$safetyStockLevel',
              maximumStockLevel: '$maximumStockLevel'
            }
          },
          warehouseTotalStock: { $sum: '$quantity' }
        }
      },
      
      // Since we don't have locations yet, we can skip the second grouping
      // and go directly to the final structure
      
      // Final grouping to get overall structure
      {
        $group: {
          _id: null,
          warehouses: {
            $push: {
              warehouseId: '$_id.warehouseId',
              warehouseName: '$_id.warehouseName',
              locations: [], // Empty locations array for now
              stockTypes: '$stockTypes',
              totalStock: '$warehouseTotalStock'
            }
          },
          totalStockAllWarehouses: { $sum: '$warehouseTotalStock' }
        }
      }
    ];

    const result = await Inventories.aggregate(pipeline);
    
    if (!result || result.length === 0) {
      // Return empty structure if no inventory found
      return {
        partId,
        warehouses: [],
        totalStockAllWarehouses: 0,
        stockLevelsSummary: {
          raw: 0,
          hardening: 0,
          grinding: 0,
          finished: 0,
          rejected: 0
        }
      };
    }

    const inventoryData = result[0];

    // Calculate stock levels summary
    const stockLevelsSummary = await getStockLevelsSummary(partId);

    const aggregatedData: AggregatedInventoryData = {
      partId,
      warehouses: inventoryData.warehouses || [],
      totalStockAllWarehouses: inventoryData.totalStockAllWarehouses || 0,
      grandTotal: inventoryData.totalStockAllWarehouses || 0, // Add for frontend compatibility
      totals: stockLevelsSummary, // Add for frontend compatibility
      warehouseCount: (inventoryData.warehouses || []).length, // Add for frontend compatibility
      stockLevelsSummary
    };

    logOperation('GET_PART_INVENTORY_BREAKDOWN_SUCCESS', 'service', {
      partId,
      warehouseCount: aggregatedData.warehouses.length,
      totalStock: aggregatedData.totalStockAllWarehouses
    });

    return aggregatedData;

  } catch (error: any) {
    logOperation('GET_PART_INVENTORY_BREAKDOWN_ERROR', 'service', {
      partId,
      error: error.message
    });
    throw error;
  }
}

/**
 * Gets stock levels summary for a part across all locations
 */
async function getStockLevelsSummary(partId: string) {
  const pipeline = [
    { $match: { partId: new Types.ObjectId(partId) } },
    {
      $group: {
        _id: '$stockType',
        totalQuantity: { $sum: '$quantity' }
      }
    }
  ];

  const results = await Inventories.aggregate(pipeline);
  
  const summary = {
    raw: 0,
    hardening: 0,
    grinding: 0,
    finished: 0,
    rejected: 0
  };

  results.forEach(result => {
    if (result._id in summary) {
      summary[result._id as keyof typeof summary] = result.totalQuantity;
    }
  });

  return summary;
}

/**
 * Updates inventory quantity for a specific part/location/stockType combination
 */
export async function updateInventoryQuantity(
  partId: string,
  locationId: string,
  stockType: string,
  newQuantity: number
): Promise<IInventories | null> {
  logOperation('UPDATE_INVENTORY_QUANTITY_V2', 'service', {
    partId,
    locationId,
    stockType,
    newQuantity
  });

  try {
    await connectToDatabase();

    if (!Types.ObjectId.isValid(partId) || !Types.ObjectId.isValid(locationId)) {
      throw new Error('Invalid part ID or location ID format');
    }

    if (newQuantity < 0) {
      throw new Error('Quantity cannot be negative');
    }

    const updatedInventory = await Inventories.findOneAndUpdate(
      {
        partId: new Types.ObjectId(partId),
        locationId: new Types.ObjectId(locationId),
        stockType
      },
      {
        quantity: newQuantity,
        lastUpdated: new Date()
      },
      { new: true, runValidators: true }
    );

    logOperation('UPDATE_INVENTORY_QUANTITY_V2_SUCCESS', 'service', {
      partId,
      locationId,
      stockType,
      newQuantity,
      updated: !!updatedInventory
    });

    return updatedInventory;

  } catch (error: any) {
    logOperation('UPDATE_INVENTORY_QUANTITY_V2_ERROR', 'service', {
      partId,
      locationId,
      stockType,
      error: error.message
    });
    throw error;
  }
}
