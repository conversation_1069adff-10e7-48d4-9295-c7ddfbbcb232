import mongoose, { ClientSession, Types } from 'mongoose';
// import { InjectModel } from '@nestjs/mongoose'; // Example if using NestJS
import { captureException, setTag } from '../lib/logging-utils';
import connectToDatabase from '../lib/mongodb';
import Part, { IInventory, IPart, IPartDocument } from '../models/part.model';
import Inventories from '../models/inventories.model';
import Warehouse from '../models/warehouse.model';
import { deletePart } from './mongodb'; // Import deletePart from mongodb.ts
// PERFORMANCE OPTIMIZATION: Import caching utilities
import { generatePartsKey, generateSearchKey, invalidatePattern, withCache } from '../lib/cache';
// PERFORMANCE MONITORING: Import N+1 detection utilities
import { monitorDatabaseOperation } from '../lib/n1-detection';

// Logger function for tracking database operations
const logOperation = (operation: string, entity: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[PartService][${timestamp}] ${operation} on ${entity}${details ? ': ' + JSON.stringify(details) : ''}`);
};

/**
 * Standardized error handling for MongoDB operations
 * @param error The error object from MongoDB/Mongoose
 * @returns Error details with message and status code
 */
export const handleMongoDBError = (error: any) => {
  console.error('[PartService Error]', error);

  setTag('error.type', 'database');
  setTag('error.database', 'mongodb');
  setTag('error.service', 'part');

  let errorType = 'unknown';
  let errorStatus = 500;
  let errorMessage = '';

  if (error.name === 'ValidationError') {
    errorType = 'validation';
    errorStatus = 400;
    const validationErrors = Object.values(error.errors)
      .map((err: any) => err.message)
      .join(', ');
    errorMessage = `Validation failed: ${validationErrors}`;
    setTag('error.subtype', 'validation');
  } else if (error.code === 11000) {
    errorType = 'duplicate';
    errorStatus = 409;
    errorMessage = `Duplicate entry: A record with this ID already exists`;
    setTag('error.subtype', 'duplicate_key');
  } else {
    errorType = 'database';
    errorStatus = 500;
    errorMessage = `Database error: ${error.message}`;
    setTag('error.subtype', 'general');
  }

  captureException(error, {
    errorType,
    errorStatus,
    errorMessage
  });

  return { message: errorMessage, status: errorStatus };
};

// import { PartNotFoundException, DuplicatePartNumberException, InvalidStockOperationException } from '../common/exceptions/part.exceptions.ts'; // Custom exceptions to be defined

// Local IPart interface removed, using IPart and IPartDocument from part.model.ts directly.

/**
 * Common aggregation pipeline to join parts with inventories and reconstruct inventory object
 * This pipeline maintains backward compatibility by reconstructing the embedded inventory structure
 * from the new dedicated inventories collection.
 */
const getPartInventoryAggregationPipeline = () => [
  // Stage 1: Lookup inventories for each part
  {
    $lookup: {
      from: 'inventories',
      localField: '_id',
      foreignField: 'partId',
      as: 'inventoryRecords'
    }
  },

  // Stage 2: Lookup warehouse information for the primary warehouse
  {
    $lookup: {
      from: 'warehouses',
      let: { partId: '$_id' },
      pipeline: [
        {
          $lookup: {
            from: 'inventories',
            let: { warehouseId: '$_id' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ['$partId', '$$partId'] },
                      { $eq: ['$warehouseId', '$$warehouseId'] }
                    ]
                  }
                }
              }
            ],
            as: 'partInventories'
          }
        },
        {
          $match: {
            'partInventories.0': { $exists: true }
          }
        },
        {
          $limit: 1
        }
      ],
      as: 'primaryWarehouse'
    }
  },

  // Stage 3: Reconstruct the inventory object for backward compatibility
  {
    $addFields: {
      inventory: {
        $cond: {
          if: { $gt: [{ $size: '$inventoryRecords' }, 0] },
          then: {
            // Reconstruct stockLevels from inventory records
            stockLevels: {
              raw: {
                $sum: {
                  $map: {
                    input: {
                      $filter: {
                        input: '$inventoryRecords',
                        cond: { $eq: ['$$this.stockType', 'raw'] }
                      }
                    },
                    as: 'record',
                    in: '$$record.quantity'
                  }
                }
              },
              hardening: {
                $sum: {
                  $map: {
                    input: {
                      $filter: {
                        input: '$inventoryRecords',
                        cond: { $eq: ['$$this.stockType', 'hardening'] }
                      }
                    },
                    as: 'record',
                    in: '$$record.quantity'
                  }
                }
              },
              grinding: {
                $sum: {
                  $map: {
                    input: {
                      $filter: {
                        input: '$inventoryRecords',
                        cond: { $eq: ['$$this.stockType', 'grinding'] }
                      }
                    },
                    as: 'record',
                    in: '$$record.quantity'
                  }
                }
              },
              finished: {
                $sum: {
                  $map: {
                    input: {
                      $filter: {
                        input: '$inventoryRecords',
                        cond: { $eq: ['$$this.stockType', 'finished'] }
                      }
                    },
                    as: 'record',
                    in: '$$record.quantity'
                  }
                }
              },
              rejected: {
                $sum: {
                  $map: {
                    input: {
                      $filter: {
                        input: '$inventoryRecords',
                        cond: { $eq: ['$$this.stockType', 'rejected'] }
                      }
                    },
                    as: 'record',
                    in: '$$record.quantity'
                  }
                }
              }
            },
            // Use primary warehouse ID or first available warehouse
            warehouseId: {
              $cond: {
                if: { $gt: [{ $size: '$primaryWarehouse' }, 0] },
                then: { $arrayElemAt: ['$primaryWarehouse._id', 0] },
                else: { $arrayElemAt: ['$inventoryRecords.warehouseId', 0] }
              }
            },
            // Default values for other inventory fields (can be enhanced later)
            safetyStockLevel: 0,
            maximumStockLevel: 1000,
            averageDailyUsage: 0,
            abcClassification: 'C',
            lastStockUpdate: {
              $max: '$inventoryRecords.lastUpdated'
            },
            // Virtual currentStock field (sum of finished stock for backward compatibility)
            currentStock: {
              $sum: {
                $map: {
                  input: {
                    $filter: {
                      input: '$inventoryRecords',
                      cond: { $eq: ['$$this.stockType', 'finished'] }
                    }
                  },
                  as: 'record',
                  in: '$$record.quantity'
                }
              }
            }
          },
          else: {
            // Default inventory object for parts with no inventory records
            stockLevels: {
              raw: 0,
              hardening: 0,
              grinding: 0,
              finished: 0,
              rejected: 0
            },
            warehouseId: null,
            safetyStockLevel: 0,
            maximumStockLevel: 1000,
            averageDailyUsage: 0,
            abcClassification: 'C',
            lastStockUpdate: null,
            currentStock: 0
          }
        }
      }
    }
  },

  // Stage 4: Remove temporary fields
  {
    $project: {
      inventoryRecords: 0,
      primaryWarehouse: 0
    }
  }
];

// SCHEMA ALIGNMENT: Updated all DTOs and logic to use canonical field names from database_schema_updated.md. Legacy/incorrect field names removed.
// Updated DTO to match the new schema
export interface CreatePartDto {
  partNumber: string;
  name: string;
  businessName?: string | null; // NEW FIELD: Human-readable business name for the part
  description?: string | null;
  technicalSpecs?: string | null;
  isManufactured: boolean;
  reorderLevel?: number | null;
  status: 'active' | 'inactive' | 'obsolete'; // Aligned with part.model.ts
  inventory: {
    stockLevels: {
      raw: number;
      hardening: number;
      grinding: number;
      finished: number;
      rejected: number;
    };
    warehouseId: string; // Will be converted to ObjectId
    safetyStockLevel: number;
    maximumStockLevel: number;
    averageDailyUsage: number;
    abcClassification: string;
    lastStockUpdate?: Date | null; // Optional, will be set if not provided during creation
    // BACKWARD COMPATIBILITY: Accept legacy currentStock and convert to stockLevels.finished
    currentStock?: number; // Will be mapped to stockLevels.finished if provided
  };
  supplierId?: string | null; // Will be converted to ObjectId
  unitOfMeasure: string;
  costPrice: number;
  categoryId?: string | null; // Will be converted to ObjectId
}

// Define which inventory fields are updatable via a general PUT
export interface UpdatePartInventoryDto {
  stockLevels?: {
    raw?: number;
    hardening?: number;
    grinding?: number;
    finished?: number;
    rejected?: number;
  };
  safetyStockLevel?: number;
  maximumStockLevel?: number;
  averageDailyUsage?: number;
  abcClassification?: string;
  warehouseId?: string; // Allow warehouse updates - will be converted to ObjectId
  lastStockUpdate?: Date | null; // Allow manual stock update timestamp
  // BACKWARD COMPATIBILITY: Accept legacy currentStock and convert to stockLevels.finished
  currentStock?: number; // Will be mapped to stockLevels.finished if provided
}

// UpdatePartDto allows partial updates to allowed fields.
// It omits fields that should be immutable or system-managed (e.g., partNumber, createdAt, updatedAt)
// or fields within inventory that are managed differently (e.g., currentStock).
export interface UpdatePartDto {
  name?: string;
  businessName?: string | null; // NEW FIELD: Human-readable business name for the part
  description?: string | null;
  technicalSpecs?: string | null;
  isManufactured?: boolean;
  reorderLevel?: number | null;
  status?: 'active' | 'inactive' | 'obsolete';
  inventory?: UpdatePartInventoryDto;
  supplierId?: string | null;
  unitOfMeasure?: string;
  costPrice?: number;
  categoryId?: string | null;
}

// This structure uses exported functions rather than a class, 
// aligning with the existing code snippet provided for this file.
// If a class-based service is preferred, that can be adjusted.

/**
 * Creates a new part.
 */
export async function createPart(partData: CreatePartDto): Promise<IPartDocument> {
  logOperation('CREATE', 'service', { partNumber: partData.partNumber });
  await connectToDatabase();
  try {
    const existingPart = await Part.findOne({ partNumber: partData.partNumber }).exec();
    if (existingPart) {
      throw new Error(`Part with number ${partData.partNumber} already exists.`); // Consider custom error
    }

    // Handle both new stockLevels structure and legacy currentStock for backward compatibility
    let stockLevels;
    if (partData.inventory?.stockLevels) {
      // New structure provided
      stockLevels = partData.inventory?.stockLevels;
    } else if (partData.inventory?.currentStock !== undefined) {
      // Legacy structure - convert currentStock to stockLevels.finished
      stockLevels = {
        raw: 0,
        hardening: 0,
        grinding: 0,
        finished: partData.inventory?.currentStock,
        rejected: 0
      };
    } else {
      // Default to zero stock levels
      stockLevels = {
        raw: 0,
        hardening: 0,
        grinding: 0,
        finished: 0,
        rejected: 0
      };
    }

    // Ensure all inventory fields are present as per IInventory, providing defaults if necessary
    const inventoryData: IInventory = {
      stockLevels,
      warehouseId: new Types.ObjectId(partData.inventory?.warehouseId),
      safetyStockLevel: partData.inventory?.safetyStockLevel,
      maximumStockLevel: partData.inventory?.maximumStockLevel,
      averageDailyUsage: partData.inventory?.averageDailyUsage,
      abcClassification: partData.inventory?.abcClassification,
      lastStockUpdate: partData.inventory?.lastStockUpdate || new Date(), // Set lastStockUpdate if not provided
    };

    const partToSave = new Part({
      ...partData, // Spreads all top-level fields from DTO
      inventory: inventoryData, // Assign the fully constructed inventory object
      supplierId: partData.supplierId ? new Types.ObjectId(partData.supplierId) : null,
      categoryId: partData.categoryId ? new Types.ObjectId(partData.categoryId) : null,
      // Ensure status from DTO is used, it's already part of ...partData
    });

    const savedPart = await partToSave.save();
    logOperation('CREATE_SUCCESS', 'service', { partId: savedPart._id, partNumber: savedPart.partNumber });

    // PERFORMANCE OPTIMIZATION: Invalidate cache when new part is created
    invalidatePattern('parts:');
    invalidatePattern('search:');

    return savedPart as IPartDocument;
  } catch (error: any) {
    logOperation('CREATE_ERROR', 'service', { partNumber: partData.partNumber, error: error.message });
    if (error.message.includes('already exists')) {
        const errDetails = handleMongoDBError({ code: 11000 }); // Simulate duplicate error for consistent handling
        throw new Error(errDetails.message || `Part with number ${partData.partNumber} already exists.`);
    }
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to create part');
  }
}

/**
 * Retrieves a part by its MongoDB ObjectId.
 * OPTIMIZED: Uses aggregation pipeline instead of populate() to eliminate N+1 queries
 */
export async function getPartById(id: string): Promise<IPartDocument | null> {
  logOperation('GET_BY_ID', 'service', { id });
  await connectToDatabase();
  if (!Types.ObjectId.isValid(id)) {
    logOperation('GET_BY_ID_INVALID_FORMAT', 'service', { id });
    throw new Error('Invalid ID format'); // Consider custom error
  }
  try {
    // UNIFIED AGGREGATION PIPELINE: Uses inventories collection integration
    const pipeline = [
      // Match specific part by ID
      { $match: { _id: new Types.ObjectId(id) } },

      // Inventory aggregation: Reconstruct inventory from inventories collection
      ...getPartInventoryAggregationPipeline(),

      // Optimized lookups with minimal projections
      {
        $lookup: {
          from: 'suppliers',
          localField: 'supplierId',
          foreignField: '_id',
          as: 'supplier',
          pipeline: [{ $project: { name: 1, contactPerson: 1 } }]
        }
      },

      {
        $lookup: {
          from: 'categories',
          localField: 'categoryId',
          foreignField: '_id',
          as: 'category',
          pipeline: [{ $project: { name: 1 } }]
        }
      },

      // Simplified transformation
      {
        $addFields: {
          supplier: { $arrayElemAt: ['$supplier', 0] },
          category: { $arrayElemAt: ['$category', 0] },
          businessName: { $ifNull: ['$businessName', null] }
        }
      },

      // Explicit projection to ensure businessName is included
      {
        $project: {
          _id: 1,
          partNumber: 1,
          name: 1,
          businessName: { $ifNull: ['$businessName', null] }, // Explicitly include businessName
          description: 1,
          technicalSpecs: 1,
          isManufactured: 1,
          reorderLevel: 1,
          status: 1,
          inventory: 1,
          supplierId: 1,
          unitOfMeasure: 1,
          costPrice: 1,
          categoryId: 1,
          createdAt: 1,
          updatedAt: 1,
          supplier: 1,
          category: 1
        }
      }
    ];

    const parts = await Part.aggregate(pipeline).exec() as IPart[];
    const part = parts.length > 0 ? parts[0] : null;

    if (!part) {
      logOperation('GET_BY_ID_NOT_FOUND', 'service', { id });
      return null;
    }
    logOperation('GET_BY_ID_SUCCESS', 'service', { id });
    return part as IPartDocument;
  } catch (error: any) {
    logOperation('GET_BY_ID_ERROR', 'service', { id, error: error.message });
    handleMongoDBError(error);
    return null; // Or rethrow, depending on desired error handling strategy
  }
}

/**
 * Retrieves a part by its unique partNumber.
 * OPTIMIZED: Uses aggregation pipeline instead of populate() to eliminate N+1 queries
 */
export async function getPartByPartNumberService(partNumber: string): Promise<IPartDocument | null> {
  logOperation('GET_BY_PARTNUMBER', 'service', { partNumber });
  await connectToDatabase();
  try {
    // PERFORMANCE OPTIMIZATION: Replace populate() with aggregation pipeline
    const pipeline = [
      // Match specific part by partNumber
      { $match: { partNumber } },

      // Optimized lookups with minimal projections
      {
        $lookup: {
          from: 'warehouses',
          localField: 'inventory.warehouseId',
          foreignField: '_id',
          as: 'warehouse',
          pipeline: [{ $project: { name: 1, location: 1 } }]
        }
      },

      {
        $lookup: {
          from: 'suppliers',
          localField: 'supplierId',
          foreignField: '_id',
          as: 'supplier',
          pipeline: [{ $project: { name: 1, contactPerson: 1 } }]
        }
      },

      {
        $lookup: {
          from: 'categories',
          localField: 'categoryId',
          foreignField: '_id',
          as: 'category',
          pipeline: [{ $project: { name: 1 } }]
        }
      },

      // Simplified transformation
      {
        $addFields: {
          warehouse: { $arrayElemAt: ['$warehouse', 0] },
          supplier: { $arrayElemAt: ['$supplier', 0] },
          category: { $arrayElemAt: ['$category', 0] }
        }
      },

      // Explicit projection to ensure businessName is included
      {
        $project: {
          _id: 1,
          partNumber: 1,
          name: 1,
          businessName: { $ifNull: ['$businessName', null] }, // Explicitly include businessName
          description: 1,
          technicalSpecs: 1,
          isManufactured: 1,
          reorderLevel: 1,
          status: 1,
          inventory: 1,
          supplierId: 1,
          unitOfMeasure: 1,
          costPrice: 1,
          categoryId: 1,
          createdAt: 1,
          updatedAt: 1,
          warehouse: 1,
          supplier: 1,
          category: 1
        }
      }
    ];

    const parts = await Part.aggregate(pipeline).exec() as IPart[];
    const part = parts.length > 0 ? parts[0] : null;

    if (!part) {
      logOperation('GET_BY_PARTNUMBER_NOT_FOUND', 'service', { partNumber });
      return null;
    }
    logOperation('GET_BY_PARTNUMBER_SUCCESS', 'service', { partNumber });
    return part as IPartDocument;
  } catch (error: any) {
    logOperation('GET_BY_PARTNUMBER_ERROR', 'service', { partNumber, error: error.message });
    handleMongoDBError(error);
    return null;
  }
}

/**
 * Retrieves all parts, possibly with filters and pagination.
 * UNIFIED: Uses aggregation pipeline with inventories collection integration
 * PERFORMANCE: Includes caching for frequently accessed data
 */
export async function getAllParts(options: any = {}): Promise<{ parts: IPart[], pagination: any }> {
  const {
    page = 1,
    limit = 50, // Balanced default for good UX and performance
    sort = { updatedAt: -1 },
    filter = {},
    status,
    search
  } = options;
  logOperation('GET_ALL', 'service', { page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter), status, search });

  // PERFORMANCE OPTIMIZATION: Generate cache key for this request
  const cacheKey = generatePartsKey(page, limit, filter, sort);

  // Try to get from cache first (cache for 30 seconds for frequently accessed data)
  return withCache(cacheKey, async () => {
    await connectToDatabase();
  try {
    const skip = (page - 1) * limit;

    // Build match stage with enhanced filtering
    const matchStage: any = {};
    if (filter.status || status) matchStage.status = filter.status || status;
    if (filter.isManufactured !== undefined) matchStage.isManufactured = filter.isManufactured;
    if (filter.categoryId) matchStage.categoryId = new Types.ObjectId(filter.categoryId);
    if (filter.supplierId) matchStage.supplierId = new Types.ObjectId(filter.supplierId);

    // Add search functionality
    if (search) {
      matchStage.$or = [
        { partNumber: { $regex: search, $options: 'i' } },
        { name: { $regex: search, $options: 'i' } },
        { businessName: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // UNIFIED AGGREGATION PIPELINE: Uses inventories collection integration
    const pipeline = [
      // 1. EARLY FILTERING: Apply filters first to reduce dataset size
      ...(Object.keys(matchStage).length > 0 ? [{ $match: matchStage }] : []),

      // 2. INVENTORY AGGREGATION: Reconstruct inventory from inventories collection
      ...getPartInventoryAggregationPipeline(),

      // 3. OPTIMIZED LOOKUPS: Use pipeline projections to minimize data transfer
      {
        $lookup: {
          from: 'suppliers',
          localField: 'supplierId',
          foreignField: '_id',
          as: 'supplier',
          pipeline: [
            { $project: { name: 1, contactPerson: 1 } }
          ]
        }
      },

      {
        $lookup: {
          from: 'categories',
          localField: 'categoryId',
          foreignField: '_id',
          as: 'category',
          pipeline: [
            { $project: { name: 1 } }
          ]
        }
      },

      // 4. SORTING: Apply sorting before pagination for consistent results
      { $sort: sort },

      // 5. PAGINATION: Apply after all transformations
      { $skip: skip },
      { $limit: limit },

      // 6. SIMPLIFIED TRANSFORMATION: Use simpler field mapping
      {
        $addFields: {
          supplier: { $arrayElemAt: ['$supplier', 0] },
          category: { $arrayElemAt: ['$category', 0] },
          // Ensure businessName field is always present, even if it doesn't exist in the document
          businessName: { $ifNull: ['$businessName', null] }
        }
      },

      // 7. EXPLICIT PROJECTION: Ensure all required fields are included, especially businessName
      {
        $project: {
          _id: 1,
          partNumber: 1,
          name: 1,
          businessName: { $ifNull: ['$businessName', null] }, // Explicitly include businessName, default to null if missing
          description: 1,
          technicalSpecs: 1,
          isManufactured: 1,
          reorderLevel: 1,
          status: 1,
          inventory: 1,
          supplierId: 1,
          unitOfMeasure: 1,
          costPrice: 1,
          categoryId: 1,
          createdAt: 1,
          updatedAt: 1,
          supplier: 1,
          category: 1
        }
      }
    ];

    // PERFORMANCE MONITORING: Monitor aggregation pipeline execution
    const [parts, totalCount] = await Promise.all([
      monitorDatabaseOperation('aggregate', 'parts', pipeline, () =>
        Part.aggregate(pipeline).exec() as Promise<IPart[]>
      ),
      monitorDatabaseOperation('countDocuments', 'parts', matchStage, () =>
        Object.keys(matchStage).length > 0 ? Part.countDocuments(matchStage) : Part.countDocuments({})
      )
    ]);

    const pagination = {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: page,
      limit,
    };
    logOperation('GET_ALL_SUCCESS', 'service', { count: parts.length, pagination });
    return { parts, pagination };
  } catch (error: any) {
    logOperation('GET_ALL_ERROR', 'service', { error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to fetch all parts');
  }
  }, 30000); // Cache for 30 seconds
}

/**
 * Updates an existing part.
 */
export async function updatePartService(id: string, updateData: UpdatePartDto): Promise<IPartDocument | null> {
  logOperation('UPDATE', 'service', { partId: id, updateData });
  await connectToDatabase();

  if (!mongoose.Types.ObjectId.isValid(id)) {
    logOperation('UPDATE_INVALID_ID', 'service', { partId: id });
    throw new Error('Invalid Part ID format'); // Consider custom error
  }

  try {
    // Prepare a mutable copy of updateData for modifications
    const updatePayload: any = { ...updateData };

    // Convert string IDs to ObjectIds for other references if they exist in updateData
    if (updatePayload.supplierId && typeof updatePayload.supplierId === 'string') {
      updatePayload.supplierId = new Types.ObjectId(updatePayload.supplierId);
    }
    if (updatePayload.categoryId && typeof updatePayload.categoryId === 'string') {
      updatePayload.categoryId = new Types.ObjectId(updatePayload.categoryId);
    }
    
    // Handle inventory updates using dot notation to avoid validation issues
    if (updatePayload.inventory) {
      const inventoryUpdates = updatePayload.inventory;
      delete updatePayload.inventory; // Remove from main payload

      // Convert inventory updates to dot notation
      for (const [key, value] of Object.entries(inventoryUpdates)) {
        if (key === 'warehouseId' && typeof value === 'string') {
          // Convert warehouseId string to ObjectId
          updatePayload[`inventory.${key}`] = new Types.ObjectId(value);
        } else if (key === 'stockLevels' && typeof value === 'object' && value !== null) {
          // Handle stockLevels object updates
          for (const [stockKey, stockValue] of Object.entries(value)) {
            updatePayload[`inventory.stockLevels.${stockKey}`] = stockValue;
          }
        } else if (key === 'currentStock' && typeof value === 'number') {
          // BACKWARD COMPATIBILITY: Convert currentStock to stockLevels.finished
          updatePayload['inventory.stockLevels.finished'] = value;
        } else {
          updatePayload[`inventory.${key}`] = value;
        }
      }

      // If any inventory field is being updated, set lastStockUpdate
      if (Object.keys(inventoryUpdates).length > 0) {
        updatePayload['inventory.lastStockUpdate'] = new Date();
      }
    }

    const updatedPart = await Part.findByIdAndUpdate(id, updatePayload, { new: true, runValidators: true }).exec();
    if (!updatedPart) {
      logOperation('UPDATE_NOT_FOUND', 'service', { partId: id });
      throw new Error(`Part not found with id ${id}, unable to update.`);
    }
    logOperation('UPDATE_SUCCESS', 'service', { partId: id, updatedFields: Object.keys(updateData) });

    // PERFORMANCE OPTIMIZATION: Invalidate cache when part is updated
    invalidatePattern('parts:');
    invalidatePattern('search:');

    return updatedPart as IPartDocument | null;
  } catch (error: any) {
    logOperation('UPDATE_ERROR', 'service', { partId: id, error: error.message });
    // Example of specific error handling, adapt as needed
    if (error.code === 11000 || (error.message && error.message.includes('duplicate key'))) { 
        const errDetails = handleMongoDBError({ code: 11000 }); // Simulate specific error for handler
        throw new Error(errDetails.message || 'A data conflict occurred: a unique identifier may already exist.');
    }
    const errDetails = handleMongoDBError(error); // General handler
    throw new Error(errDetails.message || `Failed to update part ${id}`);
  }
}

/**
 * Deletes a part by its ObjectId, utilizing the comprehensive deletePart logic from mongodb.ts.
 */
export async function deletePartService(id: string): Promise<void> {
  logOperation('SERVICE_DELETE_PART_START', 'service', { partId: id });
  await connectToDatabase(); // Ensure connection

  if (!Types.ObjectId.isValid(id)) {
    logOperation('SERVICE_DELETE_PART_INVALID_ID', 'service', { partId: id });
    const err = new Error('Invalid Part ID format.');
    (err as any).statusCode = 400;
    throw err;
  }

  try {
    // Call the centralized deletePart function from mongodb.ts
    const result = await deletePart(id);

    if (!result.success) {
      logOperation('SERVICE_DELETE_PART_FAILED_FROM_DB', 'service', { partId: id, message: result.message, statusCode: result.statusCode });
      const error = new Error(result.message);
      (error as any).statusCode = result.statusCode || 500;
      throw error;
    }

    logOperation('SERVICE_DELETE_PART_SUCCESS', 'service', { partId: id });

    // PERFORMANCE OPTIMIZATION: Invalidate cache when part is deleted
    invalidatePattern('parts:');
    invalidatePattern('search:');

    // On successful deletion, no specific document is returned by deletePart from mongodb.ts
  } catch (error: any) {
    if (error.statusCode) {
        logOperation('SERVICE_DELETE_PART_ERROR_WITH_STATUS', 'service', { partId: id, message: error.message, statusCode: error.statusCode });
        throw error; // Re-throw the error with its existing statusCode
    }
    
    logOperation('SERVICE_DELETE_PART_ERROR_UNHANDLED', 'service', { partId: id, originalError: error.message });
    // Using the local handleMongoDBError for now
    const errDetails = handleMongoDBError(error);
    const newError = new Error(errDetails.message || `Failed to delete part ${id}`);
    (newError as any).statusCode = errDetails.status || 500;
    throw newError;
  }
}

/**
 * Adjusts the stock level of a specific part's embedded inventory.
 * Validates that the transaction warehouseId matches the part's embedded inventory warehouseId.
 */
export async function adjustStockLevelByDelta(
  itemId: string,
  warehouseId: string,
  quantityChange: number,
  options: { session?: ClientSession } = {}
): Promise<IPart | null> {
  const { session } = options;
  logOperation('ADJUST_STOCK_DELTA', 'service', { itemId, quantityChange, warehouseId });
  await connectToDatabase();

  if (!mongoose.Types.ObjectId.isValid(itemId) || !mongoose.Types.ObjectId.isValid(warehouseId)) {
    logOperation('ADJUST_STOCK_DELTA_INVALID_ID', 'service', { itemId, warehouseId });
    throw new Error('Invalid Item ID or Warehouse ID format for stock adjustment');
  }

  try {
    const part = await Part.findById(itemId).session(session || null).exec();
    if (!part) {
      logOperation('ADJUST_STOCK_DELTA_PART_NOT_FOUND', 'service', { itemId });
      throw new Error(`Part not found with id ${itemId} for stock adjustment`);
    }

    // Ensure the part has an inventory sub-document
    if (!part.inventory || !part.inventory?.warehouseId) {
      logOperation('ADJUST_STOCK_DELTA_NO_INVENTORY', 'service', { itemId });
      throw new Error(`Part ${itemId} does not have embedded inventory information or a warehouse ID.`);
    }

    if (part.inventory?.warehouseId.toString() !== warehouseId) {
      logOperation('ADJUST_STOCK_DELTA_WAREHOUSE_MISMATCH', 'service', { 
        itemId, 
        partWarehouse: part.inventory?.warehouseId.toString(), 
        txWarehouse: warehouseId 
      });
      throw new Error(
        `Warehouse ID mismatch: Part ${itemId} is stocked in warehouse ${part.inventory?.warehouseId}, but transaction is for warehouse ${warehouseId}.`
      );
    }

    if (part.inventory) {
      part.inventory.currentStock = (part.inventory.currentStock || 0) + quantityChange;
    }
    part.inventory && (part.inventory.lastStockUpdate = new Date());
    
    // Optional: Validate stock not negative if business rule requires
    // if (part.inventory?.currentStock < 0) {
    //   // Abort transaction if session is provided
    //   if (session && session.inTransaction()) {
    //     await session.abortTransaction();
    //   }
    //   logOperation('ADJUST_STOCK_DELTA_NEGATIVE_STOCK', 'service', { itemId, currentStock: part.inventory?.currentStock });
    //   throw new Error('Stock level cannot become negative.'); 
    // }

    const saveOptions: any = {};
    if (session !== undefined) {
      saveOptions.session = session;
    }
    const updatedPart = await part.save(saveOptions);
    logOperation('ADJUST_STOCK_DELTA_SUCCESS', 'service', { itemId, newStock: updatedPart.inventory?.currentStock });
    return updatedPart.toObject() as IPart;
  } catch (error: any) {
    logOperation('ADJUST_STOCK_DELTA_ERROR', 'service', { itemId, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || `Failed to adjust stock for part ${itemId}`);
  }
}

/**
 * Checks if a part is below its reorder level.
 * Updated to use the new schema structure with inventory sub-document
 */
export async function checkReorderPoint(partId: string): Promise<boolean> {
  logOperation('CHECK_REORDER_POINT', 'service', { partId });
  const part = await getPartById(partId); // Uses the service function already defined
  if (!part || part.reorderLevel === null || part.reorderLevel === undefined || typeof part.inventory?.currentStock !== 'number') {
    logOperation('CHECK_REORDER_POINT_NOT_APPLICABLE', 'service', { partId, reorderLevel: part?.reorderLevel, currentStock: part?.inventory?.currentStock });
    return false;
  }
  const needsReorder = part.inventory?.currentStock < part.reorderLevel;
  logOperation('CHECK_REORDER_POINT_RESULT', 'service', { partId, needsReorder });
  return needsReorder;
}

/**
 * Search for parts with text search and filtering options
 * OPTIMIZED: Uses aggregation pipeline instead of populate() to eliminate N+1 queries
 * PERFORMANCE: Includes caching for search results
 */
export async function searchParts(options: any = {}) {
  const {
    query = '',
    page = 1,
    limit = 50, // Consistent with getAllParts default
    sort = { updatedAt: -1 },
    filter = {}
  } = options;

  logOperation('SEARCH', 'service', { query, page, limit, sort: JSON.stringify(sort), filter: JSON.stringify(filter) });

  // PERFORMANCE OPTIMIZATION: Generate cache key for search results
  const cacheKey = generateSearchKey(query, page, limit, filter);

  // Cache search results for 15 seconds (shorter than regular queries due to dynamic nature)
  return withCache(cacheKey, async () => {

  try {
    await connectToDatabase();
    const skip = (page - 1) * limit;
    // Build search filter with enhanced search capabilities
    let searchFilter: any = { ...filter };

    if (query) {
      searchFilter.$or = [
        { partNumber: { $regex: query, $options: 'i' } },
        { name: { $regex: query, $options: 'i' } },
        { businessName: { $regex: query, $options: 'i' } },
        { description: { $regex: query, $options: 'i' } }
      ];
    }

    // UNIFIED SEARCH AGGREGATION PIPELINE: Uses inventories collection integration
    const pipeline = [
      // 1. EARLY FILTERING: Apply search filters first to reduce dataset size
      { $match: searchFilter },

      // 2. INVENTORY AGGREGATION: Reconstruct inventory from inventories collection
      ...getPartInventoryAggregationPipeline(),

      // 3. OPTIMIZED LOOKUPS: Use pipeline projections to minimize data transfer
      {
        $lookup: {
          from: 'suppliers',
          localField: 'supplierId',
          foreignField: '_id',
          as: 'supplier',
          pipeline: [
            { $project: { name: 1, contactPerson: 1 } }
          ]
        }
      },

      {
        $lookup: {
          from: 'categories',
          localField: 'categoryId',
          foreignField: '_id',
          as: 'category',
          pipeline: [
            { $project: { name: 1 } }
          ]
        }
      },

      // 4. SORTING: Apply sorting before pagination for consistent results
      { $sort: sort },

      // 5. PAGINATION: Apply after all transformations
      { $skip: skip },
      { $limit: limit },

      // 6. SIMPLIFIED TRANSFORMATION: Use simpler field mapping
      {
        $addFields: {
          supplier: { $arrayElemAt: ['$supplier', 0] },
          category: { $arrayElemAt: ['$category', 0] },
          businessName: { $ifNull: ['$businessName', null] }
        }
      },

      // 7. EXPLICIT PROJECTION: Ensure all required fields are included, especially businessName
      {
        $project: {
          _id: 1,
          partNumber: 1,
          name: 1,
          businessName: { $ifNull: ['$businessName', null] }, // Explicitly include businessName, default to null if missing
          description: 1,
          technicalSpecs: 1,
          isManufactured: 1,
          reorderLevel: 1,
          status: 1,
          inventory: 1,
          supplierId: 1,
          unitOfMeasure: 1,
          costPrice: 1,
          categoryId: 1,
          createdAt: 1,
          updatedAt: 1,
          supplier: 1,
          category: 1
        }
      }
    ];

    // PERFORMANCE MONITORING: Monitor search aggregation pipeline execution
    const [parts, totalCount] = await Promise.all([
      monitorDatabaseOperation('aggregate', 'parts', pipeline, () =>
        Part.aggregate(pipeline).exec() as Promise<IPart[]>
      ),
      monitorDatabaseOperation('countDocuments', 'parts', searchFilter, () =>
        Part.countDocuments(searchFilter)
      )
    ]);

    const pagination = {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
        limit,
    };

    logOperation('SEARCH_SUCCESS', 'service', { query, count: parts.length, pagination });
    return { parts, pagination };
  } catch (error: any) {
    logOperation('SEARCH_ERROR', 'service', { query, error: error.message });
    const errDetails = handleMongoDBError(error);
    throw new Error(errDetails.message || 'Failed to search parts');
  }
  }, 15000); // Cache for 15 seconds
}

// Unit Test Considerations:
// - Mock connectToDatabase, Part model methods (findOne, findById, save, etc.)
// - Test successful CRUD operations.
// - Test error handling: duplicate partNumber, validation errors, part not found, invalid IDs.
// - Test business logic: stock updates, reorder point checks.
// - Test pagination and filtering in getAllParts and searchParts.

// Export an instance of the service if not using DI framework like NestJS, or export the class itself.
// export const partService = new PartService(); 