#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to fix errorResponse calls
function fixErrorResponseCalls(content) {
  // Pattern: errorResponse(message, statusCode, { duration })
  // Should be: errorResponse(code, message, [{ duration }], statusCode)
  
  // Fix simple errorResponse calls with message and status code
  content = content.replace(
    /errorResponse\(\s*([^,]+),\s*(\d+),\s*\{\s*duration\s*\}\s*\)/g,
    'errorResponse("API_ERROR", $1, [{ duration }], $2)'
  );
  
  // Fix errorResponse calls with message, status, and duration object
  content = content.replace(
    /errorResponse\(\s*([^,]+),\s*(\d+),\s*\{\s*duration:\s*([^}]+)\s*\}\s*\)/g,
    'errorResponse("API_ERROR", $1, [{ duration: $3 }], $2)'
  );
  
  return content;
}

// Function to fix logOperation calls
function fixLogOperationCalls(content) {
  // Pattern: logOperation('OPERATION', { details })
  // Should be: logOperation('OPERATION', 'entity', { details })
  
  content = content.replace(
    /logOperation\(\s*'([^']+)',\s*\{/g,
    "logOperation('$1', 'service', {"
  );
  
  return content;
}

// Function to fix LocationQueryOptions type issues
function fixLocationQueryOptions(content) {
  // Fix the warehouseId undefined issue
  content = content.replace(
    /warehouseId: string \| undefined;/g,
    'warehouseId?: string;'
  );
  
  return content;
}

// Function to fix inventory field access
function fixInventoryAccess(content) {
  // Fix part.inventory access that might be undefined
  content = content.replace(
    /part\.inventory\./g,
    'part.inventory?.'
  );
  
  return content;
}

// Function to fix IMovementLocation issues
function fixMovementLocation(content) {
  // Add locationId to movement location objects
  content = content.replace(
    /\{\s*warehouseId:\s*([^,}]+),\s*stockType:\s*([^}]+)\s*\}/g,
    '{ locationId: $1, warehouseId: $1, stockType: $2 }'
  );
  
  return content;
}

// Files to process
const filesToFix = [
  'app/api/locations/route.ts',
  'app/api/locations/[id]/route.ts',
  'app/api/warehouses/[id]/locations/route.ts',
  'app/api/inventories/part/[partId]/route.ts',
  'app/services/location.service.ts',
  'app/services/inventories-v2.service.ts',
  'app/services/stockmovement.service.ts',
  'app/services/part.service.ts'
];

console.log('Fixing TypeScript errors...');

filesToFix.forEach(filePath => {
  const fullPath = path.join(__dirname, filePath);
  
  if (fs.existsSync(fullPath)) {
    console.log(`Processing ${filePath}...`);
    
    let content = fs.readFileSync(fullPath, 'utf8');
    
    // Apply fixes
    content = fixErrorResponseCalls(content);
    content = fixLogOperationCalls(content);
    content = fixLocationQueryOptions(content);
    content = fixInventoryAccess(content);
    content = fixMovementLocation(content);
    
    fs.writeFileSync(fullPath, content);
    console.log(`Fixed ${filePath}`);
  } else {
    console.log(`File not found: ${filePath}`);
  }
});

console.log('TypeScript error fixes completed!');
