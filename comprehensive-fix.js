#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Fix logOperation calls throughout the codebase
function fixLogOperationCalls(content) {
  // Pattern: logOperation('OPERATION', 'entity', { details })
  // The issue is that many calls have 3 parameters but the function expects 2-4
  // Let's fix the most common pattern where details is the third parameter
  
  // Fix calls with 3 parameters where the third is an object
  content = content.replace(
    /logOperation\(\s*'([^']+)',\s*'([^']+)',\s*\{([^}]*)\}\s*\)/g,
    "logOperation('$1', '$2', {$3})"
  );
  
  return content;
}

// Fix errorResponse calls
function fixErrorResponseCalls(content) {
  // The errorResponse function expects (code, message, details?, statusCode?)
  // But many calls are using (message, statusCode, details)
  
  // Fix pattern: errorResponse(message, statusCode, { duration })
  content = content.replace(
    /errorResponse\(\s*([^,]+),\s*(\d+),\s*\[?\s*\{\s*duration[^}]*\}\s*\]?\s*\)/g,
    'errorResponse("API_ERROR", $1, undefined, $2)'
  );
  
  return content;
}

// Fix inventory access issues
function fixInventoryAccess(content) {
  // Fix part.inventory.field to part.inventory?.field
  content = content.replace(
    /(\w+)\.inventory\.(\w+)/g,
    '$1.inventory?.$2'
  );
  
  return content;
}

// Fix assignment to optional property access
function fixOptionalPropertyAssignment(content) {
  // Fix updatedPart.inventory.field = value to updatedPart.inventory && (updatedPart.inventory.field = value)
  content = content.replace(
    /(\w+)\.inventory\?\.(\w+)\s*=\s*([^;]+);/g,
    '$1.inventory && ($1.inventory.$2 = $3);'
  );
  
  return content;
}

console.log('Starting comprehensive TypeScript fixes...');

// Get all TypeScript files in services and api directories
const serviceFiles = glob.sync('app/services/**/*.ts');
const apiFiles = glob.sync('app/api/**/*.ts');
const allFiles = [...serviceFiles, ...apiFiles];

console.log(`Found ${allFiles.length} TypeScript files to process`);

allFiles.forEach(filePath => {
  try {
    console.log(`Processing ${filePath}...`);
    
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Apply fixes
    content = fixLogOperationCalls(content);
    content = fixErrorResponseCalls(content);
    content = fixInventoryAccess(content);
    content = fixOptionalPropertyAssignment(content);
    
    fs.writeFileSync(filePath, content);
    console.log(`✓ Fixed ${filePath}`);
  } catch (error) {
    console.error(`✗ Error processing ${filePath}:`, error.message);
  }
});

console.log('Comprehensive TypeScript fixes completed!');

// Now let's create a temporary logOperation wrapper to handle the signature mismatch
const logOperationFix = `
// Temporary fix for logOperation signature mismatch
const originalLogOperation = logOperation;
export const logOperation = (operation: string, entityOrDetails: string | any, details?: any, saveToDb?: boolean) => {
  if (typeof entityOrDetails === 'string') {
    // New signature: logOperation(operation, entity, details?, saveToDb?)
    return originalLogOperation(operation, entityOrDetails, details, saveToDb);
  } else {
    // Legacy signature: logOperation(operation, details)
    return originalLogOperation(operation, 'service', entityOrDetails, false);
  }
};
`;

// Add this fix to the logging service
const loggingServicePath = 'app/services/logging.ts';
if (fs.existsSync(loggingServicePath)) {
  let loggingContent = fs.readFileSync(loggingServicePath, 'utf8');
  if (!loggingContent.includes('Temporary fix for logOperation')) {
    loggingContent += '\n' + logOperationFix;
    fs.writeFileSync(loggingServicePath, loggingContent);
    console.log('✓ Added logOperation compatibility fix');
  }
}

console.log('All fixes applied!');
