/**
 * Test script to verify multi-warehouse inventory functionality
 * 
 * This script tests the new multi-warehouse inventory display feature
 * by creating test data and verifying the API endpoints work correctly.
 */

const { MongoClient, ObjectId } = require('mongodb');

// Configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/inventory_management';
const TEST_DATABASE = 'inventory_management';

async function testMultiWarehouseInventory() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('✅ Connected to MongoDB');
    
    const db = client.db(TEST_DATABASE);
    
    // Clean up any existing test data
    await cleanupTestData(db);
    
    // Create test warehouses
    const warehouses = await createTestWarehouses(db);
    console.log('✅ Created test warehouses:', warehouses.map(w => w.name));
    
    // Create test part
    const part = await createTestPart(db);
    console.log('✅ Created test part:', part.partNumber);
    
    // Create inventory records across multiple warehouses
    const inventoryRecords = await createTestInventoryRecords(db, part._id, warehouses);
    console.log('✅ Created inventory records across', warehouses.length, 'warehouses');
    
    // Test the aggregation pipeline
    await testAggregationPipeline(db, part._id);
    
    // Test the new API endpoint (simulate)
    await testInventoryBreakdownLogic(db, part._id);
    
    console.log('\n🎉 All tests passed! Multi-warehouse inventory display should work correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await client.close();
  }
}

async function cleanupTestData(db) {
  await db.collection('parts').deleteMany({ partNumber: { $regex: /^TEST-/ } });
  await db.collection('warehouses').deleteMany({ location_id: { $regex: /^TEST-/ } });
  await db.collection('inventories').deleteMany({ 
    $or: [
      { 'partId': { $in: await db.collection('parts').find({ partNumber: { $regex: /^TEST-/ } }).map(p => p._id).toArray() } },
      { 'warehouseId': { $in: await db.collection('warehouses').find({ location_id: { $regex: /^TEST-/ } }).map(w => w._id).toArray() } }
    ]
  });
  console.log('🧹 Cleaned up existing test data');
}

async function createTestWarehouses(db) {
  const warehouses = [
    {
      _id: new ObjectId(),
      location_id: 'TEST-WH-01',
      name: 'Test Production Floor',
      location: 'Main Building, Ground Floor',
      capacity: 1000,
      manager: 'Test Manager 1',
      contact: '<EMAIL>',
      isBinTracked: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: new ObjectId(),
      location_id: 'TEST-WH-02',
      name: 'Test Machine Shop',
      location: 'Building B, Second Floor',
      capacity: 500,
      manager: 'Test Manager 2',
      contact: '<EMAIL>',
      isBinTracked: false,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];
  
  await db.collection('warehouses').insertMany(warehouses);
  return warehouses;
}

async function createTestPart(db) {
  const part = {
    _id: new ObjectId(),
    partNumber: 'TEST-PART-001',
    name: 'Test Multi-Warehouse Part',
    businessName: 'Test Part',
    description: 'A test part for multi-warehouse inventory testing',
    technicalSpecs: 'Test specifications',
    isManufactured: true,
    status: 'active',
    supplierId: null,
    unitOfMeasure: 'pcs',
    costPrice: 10.50,
    categoryId: null,
    createdAt: new Date(),
    updatedAt: new Date()
  };
  
  await db.collection('parts').insertOne(part);
  return part;
}

async function createTestInventoryRecords(db, partId, warehouses) {
  const inventoryRecords = [
    // Warehouse 1 inventory
    {
      partId: partId,
      warehouseId: warehouses[0]._id,
      stockType: 'raw',
      quantity: 50,
      lastUpdated: new Date()
    },
    {
      partId: partId,
      warehouseId: warehouses[0]._id,
      stockType: 'finished',
      quantity: 25,
      lastUpdated: new Date()
    },
    // Warehouse 2 inventory
    {
      partId: partId,
      warehouseId: warehouses[1]._id,
      stockType: 'raw',
      quantity: 30,
      lastUpdated: new Date()
    },
    {
      partId: partId,
      warehouseId: warehouses[1]._id,
      stockType: 'hardening',
      quantity: 15,
      lastUpdated: new Date()
    },
    {
      partId: partId,
      warehouseId: warehouses[1]._id,
      stockType: 'finished',
      quantity: 40,
      lastUpdated: new Date()
    }
  ];
  
  await db.collection('inventories').insertMany(inventoryRecords);
  return inventoryRecords;
}

async function testAggregationPipeline(db, partId) {
  console.log('\n🔍 Testing aggregation pipeline...');
  
  // Test the part service aggregation pipeline
  const pipeline = [
    { $match: { _id: partId } },
    {
      $lookup: {
        from: 'inventories',
        localField: '_id',
        foreignField: 'partId',
        as: 'inventoryRecords'
      }
    },
    {
      $addFields: {
        inventory: {
          stockLevels: {
            raw: {
              $sum: {
                $map: {
                  input: { $filter: { input: '$inventoryRecords', cond: { $eq: ['$$this.stockType', 'raw'] } } },
                  as: 'record',
                  in: '$$record.quantity'
                }
              }
            },
            finished: {
              $sum: {
                $map: {
                  input: { $filter: { input: '$inventoryRecords', cond: { $eq: ['$$this.stockType', 'finished'] } } },
                  as: 'record',
                  in: '$$record.quantity'
                }
              }
            }
          }
        }
      }
    }
  ];
  
  const result = await db.collection('parts').aggregate(pipeline).toArray();
  console.log('📊 Aggregation result:', JSON.stringify(result[0]?.inventory?.stockLevels, null, 2));
  
  // Verify totals
  const expectedRaw = 50 + 30; // 80
  const expectedFinished = 25 + 40; // 65
  
  if (result[0]?.inventory?.stockLevels?.raw === expectedRaw && 
      result[0]?.inventory?.stockLevels?.finished === expectedFinished) {
    console.log('✅ Aggregation pipeline working correctly');
  } else {
    throw new Error('❌ Aggregation pipeline not working as expected');
  }
}

async function testInventoryBreakdownLogic(db, partId) {
  console.log('\n🔍 Testing inventory breakdown logic...');
  
  // Simulate the new service method logic
  const breakdown = await db.collection('inventories').aggregate([
    { $match: { partId: partId } },
    {
      $lookup: {
        from: 'warehouses',
        localField: 'warehouseId',
        foreignField: '_id',
        as: 'warehouse',
        pipeline: [{ $project: { name: 1, location_id: 1, location: 1 } }]
      }
    },
    { $unwind: '$warehouse' },
    {
      $group: {
        _id: '$warehouseId',
        warehouseName: { $first: '$warehouse.name' },
        stockLevels: {
          $push: {
            stockType: '$stockType',
            quantity: '$quantity'
          }
        },
        totalQuantity: { $sum: '$quantity' }
      }
    }
  ]).toArray();
  
  console.log('📊 Warehouse breakdown:', JSON.stringify(breakdown, null, 2));
  
  if (breakdown.length === 2) {
    console.log('✅ Warehouse breakdown logic working correctly');
  } else {
    throw new Error('❌ Warehouse breakdown logic not working as expected');
  }
}

// Run the test
if (require.main === module) {
  testMultiWarehouseInventory().catch(console.error);
}

module.exports = { testMultiWarehouseInventory };
