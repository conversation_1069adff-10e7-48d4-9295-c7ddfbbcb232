/**
 * Check legacy data structure before migration
 */

import mongoose from 'mongoose';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

async function checkLegacyData() {
  console.log('🔍 Checking legacy data structure...');
  
  const uri = process.env.MONGODB_URI!;
  const dbName = process.env.MONGODB_DB_NAME || 'Trend_IMS';
  
  try {
    await mongoose.connect(uri, {
      dbName: dbName,
      serverSelectionTimeoutMS: 10000,
      connectTimeoutMS: 10000,
    });
    
    console.log('✅ Connected to MongoDB');
    
    // Check parts structure
    console.log('\n📦 PARTS COLLECTION ANALYSIS:');
    const partsCollection = mongoose.connection.db.collection('parts');
    const samplePart = await partsCollection.findOne({});
    
    if (samplePart) {
      console.log('Sample part structure:');
      console.log('- _id:', samplePart._id);
      console.log('- partNumber:', samplePart.partNumber);
      console.log('- name:', samplePart.name);
      console.log('- inventory structure:', JSON.stringify(samplePart.inventory, null, 2));
      
      // Check if already migrated
      if (samplePart.inventory?.stockLevels) {
        console.log('✅ Parts already have stockLevels structure');
      } else {
        console.log('⚠️ Parts need migration to stockLevels structure');
      }
    }
    
    // Check legacy transactions
    console.log('\n📋 LEGACY TRANSACTIONS ANALYSIS:');
    const legacyTransactions = mongoose.connection.db.collection('inventoryTransactions');
    const legacyCount = await legacyTransactions.countDocuments();
    console.log(`Legacy transactions count: ${legacyCount}`);
    
    if (legacyCount > 0) {
      const sampleTransaction = await legacyTransactions.findOne({});
      console.log('Sample legacy transaction structure:');
      console.log('- _id:', sampleTransaction._id);
      console.log('- transactionType:', sampleTransaction.transactionType);
      console.log('- partId:', sampleTransaction.partId);
      console.log('- quantity:', sampleTransaction.quantity);
      console.log('- warehouseId:', sampleTransaction.warehouseId);
      console.log('- previousStock:', sampleTransaction.previousStock);
      console.log('- newStock:', sampleTransaction.newStock);
      console.log('- transactionDate:', sampleTransaction.transactionDate);
    }
    
    // Check new transactions
    console.log('\n📋 NEW TRANSACTIONS ANALYSIS:');
    const newTransactions = mongoose.connection.db.collection('transactions');
    const newCount = await newTransactions.countDocuments();
    console.log(`New transactions count: ${newCount}`);
    
    if (newCount > 0) {
      const sampleNewTransaction = await newTransactions.findOne({});
      console.log('Sample new transaction structure:');
      console.log('- _id:', sampleNewTransaction._id);
      console.log('- transactionType:', sampleNewTransaction.transactionType);
      console.log('- from:', sampleNewTransaction.from);
      console.log('- to:', sampleNewTransaction.to);
    }
    
    // Check warehouses
    console.log('\n🏭 WAREHOUSES ANALYSIS:');
    const warehouses = mongoose.connection.db.collection('warehouses');
    const warehouseCount = await warehouses.countDocuments();
    console.log(`Warehouses count: ${warehouseCount}`);
    
    if (warehouseCount > 0) {
      const sampleWarehouse = await warehouses.findOne({});
      console.log('Sample warehouse:');
      console.log('- _id:', sampleWarehouse._id);
      console.log('- name:', sampleWarehouse.name);
      console.log('- location_id:', sampleWarehouse.location_id);
    }
    
    await mongoose.disconnect();
    console.log('\n✅ Legacy data analysis completed');
    
  } catch (error: any) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

checkLegacyData().catch(console.error);
