const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config({ path: '.env.local' });

const MONGODB_URI = process.env.MONGODB_URI;
const DB_NAME = process.env.MONGODB_DB_NAME || 'Trend_IMS';

console.log('🔗 Using MongoDB URI:', MONGODB_URI ? 'Found' : 'Not found');
console.log('🗄️  Using Database:', DB_NAME);

async function fixTransactionPartIds() {
  console.log('🔧 Starting InventoryTransaction partId fix...');
  
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('✅ Connected to MongoDB');
    
    const db = client.db(DB_NAME);
    const transactionsCollection = db.collection('inventorytransactions');
    const partsCollection = db.collection('parts');
    
    // First, let's see what we're dealing with
    console.log('\n📊 Analyzing InventoryTransaction collection...');
    
    const totalTransactions = await transactionsCollection.countDocuments();
    console.log(`Total transactions: ${totalTransactions}`);
    
    // Find transactions with string partIds (not ObjectIds)
    const invalidTransactions = await transactionsCollection.find({
      partId: { $type: "string" }
    }).toArray();
    
    console.log(`Transactions with string partIds: ${invalidTransactions.length}`);
    
    if (invalidTransactions.length === 0) {
      console.log('✅ No invalid partIds found. All transactions have proper ObjectId partIds.');
      return;
    }
    
    // Show some examples
    console.log('\n🔍 Examples of invalid partIds:');
    invalidTransactions.slice(0, 5).forEach((tx, i) => {
      console.log(`  ${i + 1}. Transaction ${tx._id}: partId = "${tx.partId}" (${typeof tx.partId})`);
    });
    
    // Get all parts to create a mapping from partNumber to ObjectId
    console.log('\n📋 Creating part number to ObjectId mapping...');
    const allParts = await partsCollection.find({}, { 
      projection: { _id: 1, partNumber: 1 } 
    }).toArray();
    
    const partNumberToId = {};
    allParts.forEach(part => {
      partNumberToId[part.partNumber] = part._id;
    });
    
    console.log(`Created mapping for ${allParts.length} parts`);
    
    // Fix the transactions
    console.log('\n🔧 Fixing invalid partIds...');
    let fixedCount = 0;
    let notFoundCount = 0;
    
    for (const transaction of invalidTransactions) {
      const partNumber = transaction.partId;
      const correctPartId = partNumberToId[partNumber];
      
      if (correctPartId) {
        // Update the transaction with the correct ObjectId
        await transactionsCollection.updateOne(
          { _id: transaction._id },
          { $set: { partId: correctPartId } }
        );
        fixedCount++;
        console.log(`  ✅ Fixed transaction ${transaction._id}: "${partNumber}" → ${correctPartId}`);
      } else {
        notFoundCount++;
        console.log(`  ❌ Part not found for transaction ${transaction._id}: "${partNumber}"`);
      }
    }
    
    console.log(`\n📈 Summary:`);
    console.log(`  ✅ Fixed: ${fixedCount} transactions`);
    console.log(`  ❌ Not found: ${notFoundCount} transactions`);
    
    // Verify the fix
    console.log('\n🔍 Verifying fix...');
    const remainingInvalid = await transactionsCollection.countDocuments({
      partId: { $type: "string" }
    });
    
    if (remainingInvalid === 0) {
      console.log('✅ All partIds are now valid ObjectIds!');
    } else {
      console.log(`⚠️  Still ${remainingInvalid} transactions with string partIds`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the fix
fixTransactionPartIds().catch(console.error);
