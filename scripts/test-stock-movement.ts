/**
 * Test script for the new stock movement system
 * 
 * This script demonstrates the new event-sourced inventory system by:
 * 1. Creating a test part with stockLevels
 * 2. Executing various stock movements
 * 3. Verifying the results
 * 
 * Run with: npx ts-node scripts/test-stock-movement.ts
 */

import mongoose from 'mongoose';
import connectToDatabase from '../app/lib/mongodb';
import Part from '../app/models/part.model';
import InventoryTransaction from '../app/models/inventorytransaction.model';
import { StockMovementService, StockMovementRequest } from '../app/services/stockmovement.service';

async function createTestPart() {
  console.log('🔧 Creating test part...');
  
  // Create a test warehouse ID (you may need to adjust this based on your actual warehouse data)
  const testWarehouseId = new mongoose.Types.ObjectId();
  
  const testPart = new Part({
    partNumber: 'TEST-001',
    name: 'Test Part for Stock Movement',
    businessName: 'Test Business Part',
    description: 'A test part for demonstrating the new stock movement system',
    isManufactured: true,
    status: 'active',
    inventory: {
      stockLevels: {
        raw: 100,
        hardening: 20,
        grinding: 15,
        finished: 50,
        rejected: 5
      },
      warehouseId: testWarehouseId,
      safetyStockLevel: 10,
      maximumStockLevel: 200,
      averageDailyUsage: 5.0,
      abcClassification: 'A',
      lastStockUpdate: new Date()
    },
    unitOfMeasure: 'pcs',
    costPrice: 25.50,
    supplierId: null,
    categoryId: null
  });
  
  const savedPart = await testPart.save();
  console.log(`✅ Created test part: ${savedPart.partNumber} (ID: ${savedPart._id})`);
  console.log('Initial stock levels:', savedPart.inventory.stockLevels);
  
  return { part: savedPart, warehouseId: testWarehouseId };
}

async function testPurchaseReceipt(partId: string, warehouseId: mongoose.Types.ObjectId) {
  console.log('\n📦 Testing purchase receipt (external → raw stock)...');
  
  const request: StockMovementRequest = {
    partId: partId,
    movementType: 'purchase_receipt',
    quantity: 50,
    to: {
      warehouseId: warehouseId.toString(),
      stockType: 'raw'
    },
    userId: new mongoose.Types.ObjectId().toString(),
    referenceNumber: 'PO-001',
    referenceType: 'PurchaseOrder',
    notes: 'Test purchase receipt'
  };
  
  const result = await StockMovementService.executeMovement(request);
  console.log('✅ Purchase receipt completed:', result.message);
  console.log('Updated stock levels:', result.updatedPart.inventory.stockLevels);
  
  return result;
}

async function testInternalTransfer(partId: string, warehouseId: mongoose.Types.ObjectId) {
  console.log('\n🔄 Testing internal transfer (raw → hardening)...');
  
  const request: StockMovementRequest = {
    partId: partId,
    movementType: 'process_move',
    quantity: 30,
    from: {
      warehouseId: warehouseId.toString(),
      stockType: 'raw'
    },
    to: {
      warehouseId: warehouseId.toString(),
      stockType: 'hardening'
    },
    userId: new mongoose.Types.ObjectId().toString(),
    referenceNumber: 'WO-001',
    referenceType: 'WorkOrder',
    notes: 'Move raw materials to hardening process'
  };
  
  const result = await StockMovementService.executeMovement(request);
  console.log('✅ Internal transfer completed:', result.message);
  console.log('Updated stock levels:', result.updatedPart.inventory.stockLevels);
  
  return result;
}

async function testSalesShipment(partId: string, warehouseId: mongoose.Types.ObjectId) {
  console.log('\n📤 Testing sales shipment (finished → external)...');
  
  const request: StockMovementRequest = {
    partId: partId,
    movementType: 'sales_shipment',
    quantity: 25,
    from: {
      warehouseId: warehouseId.toString(),
      stockType: 'finished'
    },
    userId: new mongoose.Types.ObjectId().toString(),
    referenceNumber: 'SO-001',
    referenceType: 'SalesOrder',
    notes: 'Ship finished goods to customer'
  };
  
  const result = await StockMovementService.executeMovement(request);
  console.log('✅ Sales shipment completed:', result.message);
  console.log('Updated stock levels:', result.updatedPart.inventory.stockLevels);
  
  return result;
}

async function testScrapDisposal(partId: string, warehouseId: mongoose.Types.ObjectId) {
  console.log('\n🗑️ Testing scrap disposal (rejected → scrap)...');
  
  const request: StockMovementRequest = {
    partId: partId,
    movementType: 'scrap_disposal',
    quantity: 3,
    from: {
      warehouseId: warehouseId.toString(),
      stockType: 'rejected'
    },
    to: {
      warehouseId: warehouseId.toString(),
      stockType: 'scrap'
    },
    userId: new mongoose.Types.ObjectId().toString(),
    referenceNumber: 'SCRAP-001',
    referenceType: 'StockAdjustment',
    notes: 'Dispose rejected parts as scrap'
  };
  
  const result = await StockMovementService.executeMovement(request);
  console.log('✅ Scrap disposal completed:', result.message);
  console.log('Updated stock levels:', result.updatedPart.inventory.stockLevels);
  
  return result;
}

async function verifyTransactions(partId: string) {
  console.log('\n📋 Verifying transaction records...');
  
  const transactions = await InventoryTransaction.find({ partId })
    .sort({ transactionDate: 1 })
    .populate('from.warehouseId to.warehouseId', 'name location_id');
  
  console.log(`Found ${transactions.length} transactions:`);
  
  transactions.forEach((tx, index) => {
    const fromDisplay = tx.from 
      ? `${tx.from.warehouseId || 'Unknown'} (${tx.from.stockType})`
      : 'External';
    const toDisplay = tx.to 
      ? `${tx.to.warehouseId || 'Unknown'} (${tx.to.stockType})`
      : 'External';
    
    console.log(`${index + 1}. ${tx.transactionType}: ${fromDisplay} → ${toDisplay} (${tx.quantity} units)`);
    console.log(`   Reference: ${tx.referenceType}: ${tx.referenceNumber}`);
    console.log(`   Notes: ${tx.notes}`);
  });
}

async function cleanupTestData(partId: string) {
  console.log('\n🧹 Cleaning up test data...');
  
  // Delete test transactions
  const deletedTransactions = await InventoryTransaction.deleteMany({ partId });
  console.log(`Deleted ${deletedTransactions.deletedCount} test transactions`);
  
  // Delete test part
  const deletedPart = await Part.findByIdAndDelete(partId);
  if (deletedPart) {
    console.log(`Deleted test part: ${deletedPart.partNumber}`);
  }
}

async function runTest() {
  console.log('🚀 Starting stock movement system test...');
  
  try {
    await connectToDatabase();
    console.log('✅ Connected to database');
    
    // Create test part
    const { part, warehouseId } = await createTestPart();
    const partId = part._id.toString();
    
    try {
      // Test various stock movements
      await testPurchaseReceipt(partId, warehouseId);
      await testInternalTransfer(partId, warehouseId);
      await testSalesShipment(partId, warehouseId);
      await testScrapDisposal(partId, warehouseId);
      
      // Verify transaction records
      await verifyTransactions(partId);
      
      // Get final part state
      const finalPart = await Part.findById(partId);
      console.log('\n📊 Final stock levels:', finalPart?.inventory.stockLevels);
      
      console.log('\n✅ All tests completed successfully!');
      
    } finally {
      // Clean up test data
      await cleanupTestData(partId);
    }
    
  } catch (error: any) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from database');
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  runTest().catch(console.error);
}

export { runTest };
