#!/usr/bin/env node

/**
 * TEST MIGRATION SCRIPT V3
 * 
 * This script tests the inventory migration process and validates the new
 * inventories collection integration without affecting production data.
 * 
 * Test Process:
 * 1. Run migration in dry-run mode
 * 2. Test new services with sample data
 * 3. Validate aggregation pipelines
 * 4. Compare results with current system
 * 5. Performance benchmarking
 */

const mongoose = require('mongoose');
const { InventoryMigration, Logger, MigrationStats } = require('./migrate-inventory-v3');

// Configuration
const CONFIG = {
  MONGODB_URI: process.env.MONGODB_URI || 'mongodb://localhost:27017/IMS',
  TEST_LOG_FILE: `test-migration-v3-${new Date().toISOString().split('T')[0]}.log`
};

// Test utilities
class MigrationTester {
  constructor(logger) {
    this.logger = logger;
    this.testResults = {
      passed: 0,
      failed: 0,
      errors: []
    };
  }

  async runTest(testName, testFunction) {
    try {
      this.logger.info(`Running test: ${testName}`);
      await testFunction();
      this.testResults.passed++;
      this.logger.success(`✓ ${testName} PASSED`);
    } catch (error) {
      this.testResults.failed++;
      this.testResults.errors.push({ testName, error: error.message });
      this.logger.error(`✗ ${testName} FAILED`, { error: error.message });
    }
  }

  getResults() {
    return {
      ...this.testResults,
      total: this.testResults.passed + this.testResults.failed,
      successRate: this.testResults.passed / (this.testResults.passed + this.testResults.failed) * 100
    };
  }
}

// Define test schemas (same as migration script)
const StockLevelsSchema = new mongoose.Schema({
  raw: { type: Number, default: 0 },
  hardening: { type: Number, default: 0 },
  grinding: { type: Number, default: 0 },
  finished: { type: Number, default: 0 },
  rejected: { type: Number, default: 0 }
}, { _id: false });

const InventorySchema = new mongoose.Schema({
  stockLevels: StockLevelsSchema,
  warehouseId: mongoose.Schema.Types.ObjectId,
  safetyStockLevel: Number,
  maximumStockLevel: Number,
  averageDailyUsage: Number,
  abcClassification: String,
  lastStockUpdate: Date
}, { _id: false });

const PartSchema = new mongoose.Schema({
  partNumber: String,
  name: String,
  inventory: InventorySchema
}, { timestamps: true });

const InventoriesSchema = new mongoose.Schema({
  partId: { type: mongoose.Schema.Types.ObjectId, ref: 'Part', required: true },
  warehouseId: { type: mongoose.Schema.Types.ObjectId, ref: 'Warehouse', required: true },
  stockType: { 
    type: String, 
    enum: ['raw', 'hardening', 'grinding', 'finished', 'rejected'],
    required: true 
  },
  quantity: { type: Number, required: true, min: 0, default: 0 },
  lastUpdated: { type: Date, default: Date.now }
}, { timestamps: true });

InventoriesSchema.index({ partId: 1, warehouseId: 1, stockType: 1 }, { unique: true });

// Test functions
async function testDryRunMigration(logger, tester) {
  await tester.runTest('Dry Run Migration', async () => {
    const stats = new MigrationStats();
    const migration = new InventoryMigration(logger, stats);
    
    // Set dry run mode
    process.env.DRY_RUN = 'true';
    
    await migration.initialize();
    await migration.validatePreMigration();
    await migration.executeMigration();
    
    const report = stats.getReport();
    
    if (report.errors > 0) {
      throw new Error(`Dry run migration had ${report.errors} errors`);
    }
    
    if (report.partsProcessed === 0) {
      throw new Error('No parts were processed in dry run');
    }
    
    logger.info('Dry run migration completed successfully', report);
    await migration.cleanup();
  });
}

async function testAggregationPipeline(logger, tester) {
  await tester.runTest('Aggregation Pipeline', async () => {
    const Part = mongoose.model('Part', PartSchema);
    
    // Test the aggregation pipeline that reconstructs inventory data
    const pipeline = [
      { $limit: 5 }, // Test with small sample
      {
        $lookup: {
          from: 'inventories',
          localField: '_id',
          foreignField: 'partId',
          as: 'inventoryRecords'
        }
      },
      {
        $addFields: {
          reconstructedInventory: {
            stockLevels: {
              raw: {
                $sum: {
                  $map: {
                    input: {
                      $filter: {
                        input: '$inventoryRecords',
                        cond: { $eq: ['$$this.stockType', 'raw'] }
                      }
                    },
                    as: 'record',
                    in: '$$record.quantity'
                  }
                }
              },
              finished: {
                $sum: {
                  $map: {
                    input: {
                      $filter: {
                        input: '$inventoryRecords',
                        cond: { $eq: ['$$this.stockType', 'finished'] }
                      }
                    },
                    as: 'record',
                    in: '$$record.quantity'
                  }
                }
              }
            }
          }
        }
      }
    ];
    
    const results = await Part.aggregate(pipeline);
    
    if (results.length === 0) {
      throw new Error('Aggregation pipeline returned no results');
    }
    
    // Validate that the pipeline structure is correct
    for (const result of results) {
      if (!result.reconstructedInventory || !result.reconstructedInventory.stockLevels) {
        throw new Error('Aggregation pipeline did not reconstruct inventory correctly');
      }
    }
    
    logger.info('Aggregation pipeline test completed', { resultsCount: results.length });
  });
}

async function testDataIntegrity(logger, tester) {
  await tester.runTest('Data Integrity', async () => {
    const Part = mongoose.model('Part', PartSchema);
    
    // Get sample parts with embedded inventory
    const sampleParts = await Part.find({ 'inventory.stockLevels': { $exists: true } }).limit(10);
    
    if (sampleParts.length === 0) {
      throw new Error('No parts with inventory data found for testing');
    }
    
    // Validate that each part has proper inventory structure
    for (const part of sampleParts) {
      if (!part.inventory || !part.inventory.stockLevels) {
        throw new Error(`Part ${part.partNumber} missing inventory data`);
      }
      
      const stockLevels = part.inventory.stockLevels;
      const requiredFields = ['raw', 'hardening', 'grinding', 'finished', 'rejected'];
      
      for (const field of requiredFields) {
        if (typeof stockLevels[field] !== 'number') {
          throw new Error(`Part ${part.partNumber} has invalid ${field} stock level`);
        }
      }
      
      if (!part.inventory.warehouseId) {
        throw new Error(`Part ${part.partNumber} missing warehouse ID`);
      }
    }
    
    logger.info('Data integrity test completed', { partsValidated: sampleParts.length });
  });
}

async function testPerformance(logger, tester) {
  await tester.runTest('Performance Benchmark', async () => {
    const Part = mongoose.model('Part', PartSchema);
    
    // Test current embedded model performance
    const startTime = Date.now();
    const currentResults = await Part.find({ 'inventory.stockLevels': { $exists: true } }).limit(50);
    const currentTime = Date.now() - startTime;
    
    // Test aggregation pipeline performance
    const aggStartTime = Date.now();
    const aggResults = await Part.aggregate([
      { $match: { 'inventory.stockLevels': { $exists: true } } },
      { $limit: 50 },
      {
        $lookup: {
          from: 'inventories',
          localField: '_id',
          foreignField: 'partId',
          as: 'inventoryRecords'
        }
      }
    ]);
    const aggTime = Date.now() - aggStartTime;
    
    logger.info('Performance benchmark completed', {
      currentModelTime: `${currentTime}ms`,
      aggregationTime: `${aggTime}ms`,
      currentResults: currentResults.length,
      aggregationResults: aggResults.length
    });
    
    // Performance should be reasonable (under 1 second for 50 records)
    if (currentTime > 1000 || aggTime > 1000) {
      throw new Error('Performance test failed - queries took too long');
    }
  });
}

async function testIndexes(logger, tester) {
  await tester.runTest('Index Validation', async () => {
    const db = mongoose.connection.db;
    
    // Check if parts collection has proper indexes
    const partsIndexes = await db.collection('parts').indexes();
    const hasPartNumberIndex = partsIndexes.some(index => 
      index.key && index.key.partNumber === 1
    );
    
    if (!hasPartNumberIndex) {
      throw new Error('Parts collection missing partNumber index');
    }
    
    // Check if inventories collection would have proper indexes (if it exists)
    try {
      const inventoriesIndexes = await db.collection('inventories').indexes();
      logger.info('Inventories collection indexes found', { 
        count: inventoriesIndexes.length 
      });
    } catch (error) {
      // Collection doesn't exist yet, which is expected
      logger.info('Inventories collection does not exist yet (expected)');
    }
    
    logger.info('Index validation completed');
  });
}

async function testTransactionModel(logger, tester) {
  await tester.runTest('Transaction Model Validation', async () => {
    const db = mongoose.connection.db;
    
    // Check transactions collection structure
    const sampleTransactions = await db.collection('transactions').find({}).limit(5).toArray();
    
    if (sampleTransactions.length === 0) {
      logger.warn('No transactions found for testing');
      return;
    }
    
    // Validate transaction structure
    for (const transaction of sampleTransactions) {
      if (!transaction.transactionId) {
        throw new Error('Transaction missing transactionId');
      }
      
      if (!transaction.partId) {
        throw new Error('Transaction missing partId');
      }
      
      // Check if transaction has new event-sourced structure
      const hasEventSourcedStructure = transaction.from || transaction.to;
      if (!hasEventSourcedStructure) {
        logger.warn('Transaction uses legacy structure', { 
          transactionId: transaction.transactionId 
        });
      }
    }
    
    logger.info('Transaction model validation completed', { 
      transactionsChecked: sampleTransactions.length 
    });
  });
}

// Main test execution
async function main() {
  const logger = new Logger(CONFIG.TEST_LOG_FILE);
  const tester = new MigrationTester(logger);

  try {
    logger.info('=== INVENTORY MIGRATION V3 TESTING STARTED ===', CONFIG);

    // Connect to MongoDB
    await mongoose.connect(CONFIG.MONGODB_URI);
    logger.info('Connected to MongoDB for testing');

    // Initialize models
    mongoose.model('Part', PartSchema);
    mongoose.model('Inventories', InventoriesSchema, 'inventories');

    // Run all tests
    await testDataIntegrity(logger, tester);
    await testIndexes(logger, tester);
    await testTransactionModel(logger, tester);
    await testAggregationPipeline(logger, tester);
    await testPerformance(logger, tester);
    await testDryRunMigration(logger, tester);

    // Generate test report
    const results = tester.getResults();
    logger.success('=== TESTING COMPLETED ===', results);

    console.log('\n=== TEST RESULTS ===');
    console.log(`Total Tests: ${results.total}`);
    console.log(`Passed: ${results.passed}`);
    console.log(`Failed: ${results.failed}`);
    console.log(`Success Rate: ${results.successRate.toFixed(1)}%`);
    console.log(`Log File: ${CONFIG.TEST_LOG_FILE}`);

    if (results.failed > 0) {
      console.log('\nFAILED TESTS:');
      results.errors.forEach(error => {
        console.log(`- ${error.testName}: ${error.error}`);
      });
      process.exit(1);
    } else {
      console.log('\n✓ ALL TESTS PASSED - Migration is ready for execution');
    }

  } catch (error) {
    logger.error('Testing failed with critical error', { 
      error: error.message, 
      stack: error.stack 
    });
    console.error('CRITICAL ERROR:', error.message);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    logger.close();
  }
}

// Handle process signals
process.on('SIGINT', () => {
  console.log('\nTesting interrupted by user');
  process.exit(1);
});

// Run tests if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { MigrationTester };
