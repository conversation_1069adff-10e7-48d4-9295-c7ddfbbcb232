#!/usr/bin/env node

/**
 * INVENTORY MIGRATION EXECUTION SCRIPT V3
 * 
 * This script executes the production-ready inventory schema migration
 * with comprehensive validation, monitoring, and safety features.
 */

const mongoose = require('mongoose');
const { ProductionLogger, MigrationStats, BackupManager, DataValidator, CONFIG } = require('./production-migration-v3');

// Define schemas for migration
const StockLevelsSchema = new mongoose.Schema({
  raw: { type: Number, default: 0 },
  hardening: { type: Number, default: 0 },
  grinding: { type: Number, default: 0 },
  finished: { type: Number, default: 0 },
  rejected: { type: Number, default: 0 }
}, { _id: false });

const InventorySchema = new mongoose.Schema({
  stockLevels: StockLevelsSchema,
  warehouseId: mongoose.Schema.Types.ObjectId,
  safetyStockLevel: Number,
  maximumStockLevel: Number,
  averageDailyUsage: Number,
  abcClassification: String,
  lastStockUpdate: Date
}, { _id: false });

const PartSchema = new mongoose.Schema({
  partNumber: String,
  name: String,
  inventory: InventorySchema
}, { timestamps: true });

const InventoriesSchema = new mongoose.Schema({
  partId: { type: mongoose.Schema.Types.ObjectId, ref: 'Part', required: true },
  warehouseId: { type: mongoose.Schema.Types.ObjectId, ref: 'Warehouse', required: true },
  stockType: { 
    type: String, 
    enum: ['raw', 'hardening', 'grinding', 'finished', 'rejected'],
    required: true 
  },
  quantity: { type: Number, required: true, min: 0, default: 0 },
  lastUpdated: { type: Date, default: Date.now },
  safetyStockLevel: Number,
  maximumStockLevel: Number,
  averageDailyUsage: Number,
  abcClassification: String,
  locationInWarehouse: String,
  notes: String
}, { timestamps: true });

// Create compound unique index
InventoriesSchema.index({ partId: 1, warehouseId: 1, stockType: 1 }, { unique: true });

// Main migration class
class InventoryMigrationExecutor {
  constructor(logger, stats, backupManager, validator) {
    this.logger = logger;
    this.stats = stats;
    this.backupManager = backupManager;
    this.validator = validator;
    this.Part = null;
    this.Inventories = null;
  }

  async initialize() {
    try {
      this.logger.info('Initializing migration executor...', { 
        dryRun: CONFIG.DRY_RUN,
        batchSize: CONFIG.BATCH_SIZE,
        mongoUri: CONFIG.MONGODB_URI.replace(/\/\/.*@/, '//***:***@') // Hide credentials
      });

      // Connect to MongoDB
      await mongoose.connect(CONFIG.MONGODB_URI, {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 30000,
        socketTimeoutMS: 45000,
      });

      this.logger.info('Connected to MongoDB successfully');

      // Initialize models
      this.Part = mongoose.model('Part', PartSchema);
      this.Inventories = mongoose.model('Inventories', InventoriesSchema, 'inventories');

      this.logger.success('Migration executor initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize migration executor', { error: error.message });
      throw error;
    }
  }

  async migratePart(part) {
    const inventoryRecords = [];

    try {
      if (!part.inventory || !part.inventory.stockLevels) {
        this.stats.addWarning('Part has no inventory data', { 
          partId: part._id, 
          partNumber: part.partNumber 
        });
        return inventoryRecords;
      }

      const { stockLevels, warehouseId, ...inventoryMetadata } = part.inventory;
      
      // Validate warehouse ID
      if (!warehouseId) {
        this.stats.addWarning('Part has no warehouse ID', { 
          partId: part._id, 
          partNumber: part.partNumber 
        });
        return inventoryRecords;
      }

      // Create inventory records for each stock type
      const stockTypes = ['raw', 'hardening', 'grinding', 'finished', 'rejected'];
      
      for (const stockType of stockTypes) {
        const quantity = stockLevels[stockType] || 0;
        
        // Create records for all stock types (including zero quantities for completeness)
        const inventoryRecord = {
          partId: part._id,
          warehouseId: warehouseId,
          stockType: stockType,
          quantity: quantity,
          lastUpdated: inventoryMetadata.lastStockUpdate || new Date(),
          safetyStockLevel: inventoryMetadata.safetyStockLevel || null,
          maximumStockLevel: inventoryMetadata.maximumStockLevel || null,
          averageDailyUsage: inventoryMetadata.averageDailyUsage || null,
          abcClassification: inventoryMetadata.abcClassification || null
        };

        inventoryRecords.push(inventoryRecord);
      }

      this.logger.debug(`Migrated part ${part.partNumber}`, {
        partId: part._id,
        recordsCreated: inventoryRecords.length,
        totalStock: Object.values(stockLevels).reduce((sum, qty) => sum + (qty || 0), 0)
      });

    } catch (error) {
      this.stats.addError(error, { 
        partId: part._id, 
        partNumber: part.partNumber 
      });
      this.logger.error(`Failed to migrate part ${part.partNumber}`, { 
        error: error.message,
        partId: part._id
      });
    }

    return inventoryRecords;
  }

  async executeMigration() {
    try {
      this.logger.info('Starting inventory migration execution...', { 
        dryRun: CONFIG.DRY_RUN,
        batchSize: CONFIG.BATCH_SIZE
      });

      // Get total count for progress tracking
      const totalParts = await this.Part.countDocuments({ 
        'inventory.stockLevels': { $exists: true } 
      });
      
      this.logger.info(`Found ${totalParts} parts to migrate`);

      let skip = 0;
      let allInventoryRecords = [];
      let processedCount = 0;

      // Process parts in batches
      while (skip < totalParts) {
        const batchStartTime = Date.now();
        
        const parts = await this.Part.find({ 
          'inventory.stockLevels': { $exists: true } 
        })
        .skip(skip)
        .limit(CONFIG.BATCH_SIZE)
        .lean();

        this.logger.info(`Processing batch: ${skip + 1}-${skip + parts.length} of ${totalParts}`);

        // Migrate each part in the batch
        for (const part of parts) {
          const inventoryRecords = await this.migratePart(part);
          allInventoryRecords.push(...inventoryRecords);
          processedCount++;
          this.stats.partsProcessed++;

          // Update performance metrics
          this.stats.updatePerformanceMetrics();

          // Create checkpoint periodically
          if (processedCount % CONFIG.CHECKPOINT_INTERVAL === 0) {
            this.stats.addCheckpoint(processedCount, allInventoryRecords.length);
            this.logger.info(`Checkpoint: Processed ${processedCount}/${totalParts} parts`, {
              inventoryRecords: allInventoryRecords.length,
              memoryUsage: process.memoryUsage()
            });
          }
        }

        const batchTime = Date.now() - batchStartTime;
        this.logger.info(`Batch completed in ${batchTime}ms`, {
          batchSize: parts.length,
          avgTimePerPart: Math.round(batchTime / parts.length)
        });

        skip += CONFIG.BATCH_SIZE;

        // Force garbage collection if available
        if (global.gc) {
          global.gc();
        }
      }

      // Insert inventory records in batches
      if (allInventoryRecords.length > 0 && !CONFIG.DRY_RUN) {
        this.logger.info(`Inserting ${allInventoryRecords.length} inventory records...`);
        
        const insertBatchSize = 1000;
        let insertedCount = 0;
        
        for (let i = 0; i < allInventoryRecords.length; i += insertBatchSize) {
          const batch = allInventoryRecords.slice(i, i + insertBatchSize);
          
          try {
            await this.Inventories.insertMany(batch, { ordered: false });
            insertedCount += batch.length;
            
            this.logger.info(`Inserted batch: ${insertedCount}/${allInventoryRecords.length} records`);
          } catch (error) {
            // Handle duplicate key errors gracefully
            if (error.code === 11000) {
              const duplicates = error.writeErrors?.length || 0;
              insertedCount += batch.length - duplicates;
              this.logger.warn(`Batch had ${duplicates} duplicate records`, { 
                batchStart: i,
                batchSize: batch.length
              });
            } else {
              throw error;
            }
          }
        }
        
        this.stats.inventoryRecordsCreated = insertedCount;
        this.logger.success(`Inventory records inserted successfully: ${insertedCount}/${allInventoryRecords.length}`);
      } else if (CONFIG.DRY_RUN) {
        this.logger.info('DRY RUN: Would insert inventory records', { 
          count: allInventoryRecords.length 
        });
        this.stats.inventoryRecordsCreated = allInventoryRecords.length;
      }

      this.logger.success('Migration execution completed successfully', {
        partsProcessed: this.stats.partsProcessed,
        inventoryRecordsCreated: this.stats.inventoryRecordsCreated,
        errors: this.stats.errors.length,
        warnings: this.stats.warnings.length
      });

    } catch (error) {
      this.logger.error('Migration execution failed', { error: error.message });
      throw error;
    }
  }

  async removeEmbeddedInventory() {
    if (CONFIG.DRY_RUN) {
      this.logger.info('DRY RUN: Would remove embedded inventory data from parts collection');
      return;
    }

    try {
      this.logger.info('Removing embedded inventory data from parts collection...');
      
      const result = await this.Part.updateMany(
        { 'inventory': { $exists: true } },
        { $unset: { 'inventory': 1 } }
      );

      this.logger.success('Embedded inventory data removed', {
        modifiedCount: result.modifiedCount
      });
    } catch (error) {
      this.logger.error('Failed to remove embedded inventory data', { error: error.message });
      throw error;
    }
  }

  async cleanup() {
    try {
      await mongoose.disconnect();
      this.logger.info('Disconnected from MongoDB');
    } catch (error) {
      this.logger.error('Error during cleanup', { error: error.message });
    }
  }
}

// Main execution function
async function main() {
  const logger = new ProductionLogger(CONFIG.LOG_FILE);
  const stats = new MigrationStats();
  const backupManager = new BackupManager(logger, CONFIG.BACKUP_DIR);
  const validator = new DataValidator(logger, stats);
  const migrationExecutor = new InventoryMigrationExecutor(logger, stats, backupManager, validator);

  try {
    logger.info('=== PRODUCTION INVENTORY MIGRATION V3 STARTED ===', CONFIG);

    // Initialize migration
    await migrationExecutor.initialize();

    // Pre-migration validation
    const preValidation = await validator.validatePreMigration(migrationExecutor.Part, migrationExecutor.Inventories);
    
    if (!preValidation.canProceed) {
      throw new Error('Pre-migration validation failed - cannot proceed safely');
    }

    // Create backup
    await backupManager.createBackup(migrationExecutor.Part);

    // Execute migration
    await migrationExecutor.executeMigration();

    // Post-migration validation
    await validator.validatePostMigration(migrationExecutor.Part, migrationExecutor.Inventories);

    // Remove embedded inventory data (only if validation passes)
    if (stats.errors.length === 0) {
      await migrationExecutor.removeEmbeddedInventory();
    } else {
      logger.warn('Skipping embedded inventory removal due to errors');
    }

    // Final report
    const report = stats.getReport();
    logger.success('=== MIGRATION COMPLETED SUCCESSFULLY ===', report);

    console.log('\n=== MIGRATION SUMMARY ===');
    console.log(`Parts Processed: ${report.summary.partsProcessed}`);
    console.log(`Inventory Records Created: ${report.summary.inventoryRecordsCreated}`);
    console.log(`Errors: ${report.summary.errors}`);
    console.log(`Warnings: ${report.summary.warnings}`);
    console.log(`Duration: ${report.summary.duration}`);
    console.log(`Log File: ${CONFIG.LOG_FILE}`);

    if (report.summary.errors > 0) {
      console.log('\nERRORS OCCURRED - Check log file for details');
      process.exit(1);
    }

  } catch (error) {
    logger.error('Migration failed with critical error', { 
      error: error.message, 
      stack: error.stack 
    });
    console.error('CRITICAL ERROR:', error.message);
    process.exit(1);
  } finally {
    await migrationExecutor.cleanup();
    logger.close();
  }
}

// Handle process signals
process.on('SIGINT', () => {
  console.log('\nMigration interrupted by user');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\nMigration terminated');
  process.exit(1);
});

// Run migration if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { InventoryMigrationExecutor };
