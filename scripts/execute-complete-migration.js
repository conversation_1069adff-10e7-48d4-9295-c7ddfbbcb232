#!/usr/bin/env node

/**
 * COMPLETE INVENTORY MIGRATION EXECUTION
 * 
 * This script executes the complete migration from embedded inventory
 * in parts collection to the new dedicated inventories collection.
 */

const mongoose = require('mongoose');

const CONFIG = {
  MONGODB_URI: process.env.MONGODB_URI || 'mongodb://localhost:27017/IMS',
  BATCH_SIZE: 10,
  DRY_RUN: process.env.DRY_RUN === 'true'
};

class CompleteMigrationExecutor {
  constructor() {
    this.db = null;
    this.migratedCount = 0;
    this.errorCount = 0;
    this.startTime = Date.now();
  }

  async connect() {
    try {
      await mongoose.connect(CONFIG.MONGODB_URI);
      this.db = mongoose.connection.db;
      console.log('✅ Connected to MongoDB');
    } catch (error) {
      console.error('❌ Failed to connect to MongoDB:', error.message);
      throw error;
    }
  }

  async disconnect() {
    try {
      await mongoose.disconnect();
      console.log('✅ Disconnected from MongoDB');
    } catch (error) {
      console.error('❌ Error disconnecting:', error.message);
    }
  }

  async executeMigration() {
    console.log('🚀 Starting complete inventory migration...');
    
    try {
      // Step 1: Validate pre-migration state
      await this.validatePreMigrationState();
      
      // Step 2: Clear existing inventories collection
      await this.clearInventoriesCollection();
      
      // Step 3: Execute migration
      await this.migrateAllParts();
      
      // Step 4: Validate post-migration state
      await this.validatePostMigrationState();
      
      // Step 5: Generate report
      this.generateMigrationReport();
      
    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      throw error;
    }
  }

  async validatePreMigrationState() {
    console.log('🔍 Validating pre-migration state...');
    
    const partsCount = await this.db.collection('parts').countDocuments();
    const partsWithInventory = await this.db.collection('parts').countDocuments({
      'inventory.stockLevels': { $exists: true }
    });
    const existingInventories = await this.db.collection('inventories').countDocuments();
    
    console.log(`📊 Pre-migration state:`);
    console.log(`   - Total parts: ${partsCount}`);
    console.log(`   - Parts with inventory: ${partsWithInventory}`);
    console.log(`   - Existing inventory records: ${existingInventories}`);
    
    if (partsWithInventory === 0) {
      throw new Error('No parts with embedded inventory found - migration may have already been completed');
    }
  }

  async clearInventoriesCollection() {
    console.log('🧹 Clearing existing inventories collection...');
    
    if (!CONFIG.DRY_RUN) {
      const result = await this.db.collection('inventories').deleteMany({});
      console.log(`   - Deleted ${result.deletedCount} existing inventory records`);
    } else {
      console.log('   - DRY RUN: Would clear inventories collection');
    }
  }

  async migrateAllParts() {
    console.log('📦 Migrating all parts...');
    
    // Get all parts with embedded inventory
    const parts = await this.db.collection('parts').find({
      'inventory.stockLevels': { $exists: true }
    }).toArray();
    
    console.log(`   - Found ${parts.length} parts to migrate`);
    
    // Process in batches
    for (let i = 0; i < parts.length; i += CONFIG.BATCH_SIZE) {
      const batch = parts.slice(i, i + CONFIG.BATCH_SIZE);
      await this.migrateBatch(batch, i + 1);
    }
  }

  async migrateBatch(parts, startIndex) {
    console.log(`   📦 Processing batch ${Math.ceil(startIndex / CONFIG.BATCH_SIZE)} (${parts.length} parts)...`);
    
    const inventoryRecords = [];
    
    for (const part of parts) {
      try {
        const records = this.createInventoryRecords(part);
        inventoryRecords.push(...records);
        this.migratedCount++;
      } catch (error) {
        console.error(`     ❌ Error processing part ${part.partNumber}:`, error.message);
        this.errorCount++;
      }
    }
    
    // Insert batch of inventory records
    if (inventoryRecords.length > 0 && !CONFIG.DRY_RUN) {
      try {
        await this.db.collection('inventories').insertMany(inventoryRecords);
        console.log(`     ✅ Inserted ${inventoryRecords.length} inventory records`);
      } catch (error) {
        console.error(`     ❌ Error inserting batch:`, error.message);
        this.errorCount += inventoryRecords.length / 5; // 5 records per part
      }
    } else if (CONFIG.DRY_RUN) {
      console.log(`     🔍 DRY RUN: Would insert ${inventoryRecords.length} inventory records`);
    }
  }

  createInventoryRecords(part) {
    const stockTypes = ['raw', 'hardening', 'grinding', 'finished', 'rejected'];
    const records = [];
    const now = new Date();
    
    for (const stockType of stockTypes) {
      const quantity = part.inventory.stockLevels[stockType] || 0;
      
      const record = {
        partId: part._id,
        warehouseId: part.inventory.warehouseId,
        stockType: stockType,
        quantity: quantity,
        lastUpdated: part.inventory.lastStockUpdate || now,
        safetyStockLevel: part.inventory.safetyStockLevel || 0,
        maximumStockLevel: part.inventory.maximumStockLevel || 1000,
        averageDailyUsage: part.inventory.averageDailyUsage || 0,
        abcClassification: part.inventory.abcClassification || 'C',
        createdAt: now,
        updatedAt: now
      };
      
      records.push(record);
    }
    
    return records;
  }

  async validatePostMigrationState() {
    console.log('🔍 Validating post-migration state...');
    
    const partsCount = await this.db.collection('parts').countDocuments();
    const partsWithInventory = await this.db.collection('parts').countDocuments({
      'inventory.stockLevels': { $exists: true }
    });
    const inventoryRecords = await this.db.collection('inventories').countDocuments();
    
    console.log(`📊 Post-migration state:`);
    console.log(`   - Total parts: ${partsCount}`);
    console.log(`   - Parts with embedded inventory: ${partsWithInventory}`);
    console.log(`   - Inventory records created: ${inventoryRecords}`);
    
    // Validate expected counts
    const expectedInventoryRecords = partsWithInventory * 5; // 5 stock types per part
    
    if (inventoryRecords !== expectedInventoryRecords) {
      console.warn(`⚠️  Expected ${expectedInventoryRecords} inventory records, but found ${inventoryRecords}`);
    } else {
      console.log('✅ Inventory record count matches expected value');
    }
    
    // Test aggregation pipeline
    await this.testAggregationPipeline();
  }

  async testAggregationPipeline() {
    console.log('🔧 Testing aggregation pipeline...');
    
    try {
      const result = await this.db.collection('parts').aggregate([
        { $limit: 1 },
        {
          $lookup: {
            from: 'inventories',
            localField: '_id',
            foreignField: 'partId',
            as: 'inventoryRecords'
          }
        },
        {
          $addFields: {
            reconstructedInventory: {
              stockLevels: {
                raw: {
                  $sum: {
                    $map: {
                      input: {
                        $filter: {
                          input: '$inventoryRecords',
                          cond: { $eq: ['$$this.stockType', 'raw'] }
                        }
                      },
                      as: 'record',
                      in: '$$record.quantity'
                    }
                  }
                },
                finished: {
                  $sum: {
                    $map: {
                      input: {
                        $filter: {
                          input: '$inventoryRecords',
                          cond: { $eq: ['$$this.stockType', 'finished'] }
                        }
                      },
                      as: 'record',
                      in: '$$record.quantity'
                    }
                  }
                }
              },
              currentStock: {
                $sum: {
                  $map: {
                    input: {
                      $filter: {
                        input: '$inventoryRecords',
                        cond: { $eq: ['$$this.stockType', 'finished'] }
                      }
                    },
                    as: 'record',
                    in: '$$record.quantity'
                  }
                }
              }
            }
          }
        }
      ]).toArray();
      
      if (result.length > 0 && result[0].reconstructedInventory) {
        console.log('✅ Aggregation pipeline working correctly');
        console.log(`   - Sample reconstructed stock: ${JSON.stringify(result[0].reconstructedInventory.stockLevels)}`);
      } else {
        console.warn('⚠️  Aggregation pipeline may have issues');
      }
      
    } catch (error) {
      console.error('❌ Aggregation pipeline test failed:', error.message);
    }
  }

  generateMigrationReport() {
    const duration = Date.now() - this.startTime;
    
    console.log('\n📋 MIGRATION REPORT');
    console.log('==================');
    console.log(`✅ Parts migrated: ${this.migratedCount}`);
    console.log(`❌ Errors: ${this.errorCount}`);
    console.log(`⏱️  Duration: ${duration}ms`);
    console.log(`🔄 Mode: ${CONFIG.DRY_RUN ? 'DRY RUN' : 'LIVE'}`);
    
    if (this.errorCount === 0) {
      console.log('\n🎉 MIGRATION COMPLETED SUCCESSFULLY!');
    } else {
      console.log('\n⚠️  MIGRATION COMPLETED WITH ERRORS');
    }
  }
}

// Main execution
async function main() {
  const executor = new CompleteMigrationExecutor();
  
  try {
    await executor.connect();
    await executor.executeMigration();
    process.exit(0);
  } catch (error) {
    console.error('💥 Migration failed:', error.message);
    process.exit(1);
  } finally {
    await executor.disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { CompleteMigrationExecutor };
