#!/usr/bin/env node

/**
 * INVENTORY SCHEMA MIGRATION SCRIPT V3
 * 
 * This script migrates inventory data from the embedded model in the parts collection
 * to the new dedicated inventories collection.
 * 
 * Migration Process:
 * 1. Read all parts with embedded inventory data
 * 2. For each part, extract stockLevels and create individual inventory records
 * 3. Create documents in the new inventories collection
 * 4. Verify data integrity
 * 5. Remove embedded inventory data from parts collection
 * 
 * SAFETY FEATURES:
 * - Dry run mode for testing
 * - Comprehensive logging
 * - Data validation
 * - Rollback capability
 * - Progress tracking
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  DRY_RUN: process.env.DRY_RUN === 'true' || process.argv.includes('--dry-run'),
  BATCH_SIZE: parseInt(process.env.BATCH_SIZE) || 50,
  LOG_FILE: process.env.LOG_FILE || `migration-v3-${new Date().toISOString().split('T')[0]}.log`,
  BACKUP_FILE: process.env.BACKUP_FILE || `parts-backup-${Date.now()}.json`,
  MONGODB_URI: process.env.MONGODB_URI || 'mongodb://localhost:27017/IMS'
};

// Logging utility
class Logger {
  constructor(logFile) {
    this.logFile = logFile;
    this.logStream = fs.createWriteStream(logFile, { flags: 'a' });
  }

  log(level, message, data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      data
    };
    
    const logLine = JSON.stringify(logEntry) + '\n';
    console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`);
    this.logStream.write(logLine);
    
    if (data) {
      console.log('Data:', JSON.stringify(data, null, 2));
    }
  }

  info(message, data) { this.log('info', message, data); }
  warn(message, data) { this.log('warn', message, data); }
  error(message, data) { this.log('error', message, data); }
  success(message, data) { this.log('success', message, data); }

  close() {
    this.logStream.end();
  }
}

// Migration statistics
class MigrationStats {
  constructor() {
    this.partsProcessed = 0;
    this.inventoryRecordsCreated = 0;
    this.errors = [];
    this.warnings = [];
    this.startTime = Date.now();
  }

  addError(error, context) {
    this.errors.push({ error: error.message, context, timestamp: new Date().toISOString() });
  }

  addWarning(warning, context) {
    this.warnings.push({ warning, context, timestamp: new Date().toISOString() });
  }

  getReport() {
    const duration = Date.now() - this.startTime;
    return {
      partsProcessed: this.partsProcessed,
      inventoryRecordsCreated: this.inventoryRecordsCreated,
      errors: this.errors.length,
      warnings: this.warnings.length,
      duration: `${Math.round(duration / 1000)}s`,
      errorDetails: this.errors,
      warningDetails: this.warnings
    };
  }
}

// Define schemas (simplified versions for migration)
const StockLevelsSchema = new mongoose.Schema({
  raw: { type: Number, default: 0 },
  hardening: { type: Number, default: 0 },
  grinding: { type: Number, default: 0 },
  finished: { type: Number, default: 0 },
  rejected: { type: Number, default: 0 }
}, { _id: false });

const InventorySchema = new mongoose.Schema({
  stockLevels: StockLevelsSchema,
  warehouseId: mongoose.Schema.Types.ObjectId,
  safetyStockLevel: Number,
  maximumStockLevel: Number,
  averageDailyUsage: Number,
  abcClassification: String,
  lastStockUpdate: Date
}, { _id: false });

const PartSchema = new mongoose.Schema({
  partNumber: String,
  name: String,
  inventory: InventorySchema
}, { timestamps: true });

const InventoriesSchema = new mongoose.Schema({
  partId: { type: mongoose.Schema.Types.ObjectId, ref: 'Part', required: true },
  warehouseId: { type: mongoose.Schema.Types.ObjectId, ref: 'Warehouse', required: true },
  stockType: { 
    type: String, 
    enum: ['raw', 'hardening', 'grinding', 'finished', 'rejected'],
    required: true 
  },
  quantity: { type: Number, required: true, min: 0, default: 0 },
  lastUpdated: { type: Date, default: Date.now },
  safetyStockLevel: Number,
  maximumStockLevel: Number,
  averageDailyUsage: Number,
  abcClassification: String
}, { timestamps: true });

// Compound unique index
InventoriesSchema.index({ partId: 1, warehouseId: 1, stockType: 1 }, { unique: true });

// Migration class
class InventoryMigration {
  constructor(logger, stats) {
    this.logger = logger;
    this.stats = stats;
    this.Part = null;
    this.Inventories = null;
  }

  async initialize() {
    try {
      // Connect to MongoDB
      await mongoose.connect(CONFIG.MONGODB_URI);
      this.logger.info('Connected to MongoDB', { uri: CONFIG.MONGODB_URI });

      // Initialize models
      this.Part = mongoose.model('Part', PartSchema);
      this.Inventories = mongoose.model('Inventories', InventoriesSchema, 'inventories');

      this.logger.info('Models initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize migration', { error: error.message });
      throw error;
    }
  }

  async createBackup() {
    try {
      this.logger.info('Creating backup of parts collection...');
      
      const parts = await this.Part.find({}).lean();
      const backupData = {
        timestamp: new Date().toISOString(),
        totalParts: parts.length,
        parts: parts
      };

      fs.writeFileSync(CONFIG.BACKUP_FILE, JSON.stringify(backupData, null, 2));
      this.logger.success('Backup created successfully', { 
        file: CONFIG.BACKUP_FILE, 
        totalParts: parts.length 
      });
    } catch (error) {
      this.logger.error('Failed to create backup', { error: error.message });
      throw error;
    }
  }

  async validatePreMigration() {
    try {
      this.logger.info('Running comprehensive pre-migration validation...');

      // Check if inventories collection already exists and has data
      const existingInventories = await this.Inventories.countDocuments();
      if (existingInventories > 0) {
        this.logger.warn('Inventories collection already contains data', {
          count: existingInventories
        });
        this.stats.addWarning('Inventories collection not empty', { count: existingInventories });
      }

      // Count parts with inventory data
      const partsWithInventory = await this.Part.countDocuments({
        'inventory.stockLevels': { $exists: true }
      });

      // Validate data structure consistency
      const sampleParts = await this.Part.find({
        'inventory.stockLevels': { $exists: true }
      }).limit(10);

      let structureIssues = 0;
      for (const part of sampleParts) {
        if (!part.inventory?.warehouseId) {
          this.stats.addWarning('Part missing warehouseId', {
            partId: part._id,
            partNumber: part.partNumber
          });
          structureIssues++;
        }

        if (!part.inventory?.stockLevels) {
          this.stats.addWarning('Part missing stockLevels', {
            partId: part._id,
            partNumber: part.partNumber
          });
          structureIssues++;
        }
      }

      // Check for required indexes
      const partIndexes = await this.Part.collection.indexes();
      const hasPartNumberIndex = partIndexes.some(idx => idx.key && idx.key.partNumber);

      if (!hasPartNumberIndex) {
        this.logger.warn('Parts collection missing partNumber index');
        this.stats.addWarning('Missing critical index', { collection: 'parts', index: 'partNumber' });
      }

      // Validate warehouse references
      const warehouseIds = await this.Part.distinct('inventory.warehouseId', {
        'inventory.warehouseId': { $exists: true }
      });

      this.logger.info('Pre-migration validation complete', {
        partsWithInventory,
        existingInventories,
        structureIssues,
        uniqueWarehouses: warehouseIds.length,
        sampleValidated: sampleParts.length
      });

      return {
        partsWithInventory,
        existingInventories,
        structureIssues,
        uniqueWarehouses: warehouseIds.length,
        isValid: structureIssues === 0
      };
    } catch (error) {
      this.logger.error('Pre-migration validation failed', { error: error.message });
      throw error;
    }
  }

  async migratePart(part) {
    const inventoryRecords = [];

    try {
      if (!part.inventory || !part.inventory.stockLevels) {
        this.stats.addWarning('Part has no inventory data', { 
          partId: part._id, 
          partNumber: part.partNumber 
        });
        return inventoryRecords;
      }

      const { stockLevels, warehouseId, ...inventoryMetadata } = part.inventory;
      
      // Validate warehouse ID
      if (!warehouseId) {
        this.stats.addWarning('Part has no warehouse ID', { 
          partId: part._id, 
          partNumber: part.partNumber 
        });
        return inventoryRecords;
      }

      // Create inventory records for each stock type with quantity > 0
      const stockTypes = ['raw', 'hardening', 'grinding', 'finished', 'rejected'];
      
      for (const stockType of stockTypes) {
        const quantity = stockLevels[stockType] || 0;
        
        // Only create records for non-zero quantities (optional: create all records)
        if (quantity > 0) {
          const inventoryRecord = {
            partId: part._id,
            warehouseId: warehouseId,
            stockType: stockType,
            quantity: quantity,
            lastUpdated: inventoryMetadata.lastStockUpdate || new Date(),
            safetyStockLevel: inventoryMetadata.safetyStockLevel || null,
            maximumStockLevel: inventoryMetadata.maximumStockLevel || null,
            averageDailyUsage: inventoryMetadata.averageDailyUsage || null,
            abcClassification: inventoryMetadata.abcClassification || null
          };

          inventoryRecords.push(inventoryRecord);
        }
      }

      this.logger.info(`Migrated part ${part.partNumber}`, {
        partId: part._id,
        recordsCreated: inventoryRecords.length,
        totalStock: Object.values(stockLevels).reduce((sum, qty) => sum + (qty || 0), 0)
      });

    } catch (error) {
      this.stats.addError(error, { 
        partId: part._id, 
        partNumber: part.partNumber 
      });
      this.logger.error(`Failed to migrate part ${part.partNumber}`, { 
        error: error.message,
        partId: part._id
      });
    }

    return inventoryRecords;
  }

  async executeMigration() {
    try {
      this.logger.info('Starting inventory migration...', { 
        dryRun: CONFIG.DRY_RUN,
        batchSize: CONFIG.BATCH_SIZE
      });

      // Get all parts with inventory data
      const totalParts = await this.Part.countDocuments({ 
        'inventory.stockLevels': { $exists: true } 
      });
      
      this.logger.info(`Found ${totalParts} parts to migrate`);

      let skip = 0;
      let allInventoryRecords = [];

      // Process parts in batches
      while (skip < totalParts) {
        const parts = await this.Part.find({ 
          'inventory.stockLevels': { $exists: true } 
        })
        .skip(skip)
        .limit(CONFIG.BATCH_SIZE)
        .lean();

        this.logger.info(`Processing batch: ${skip + 1}-${skip + parts.length} of ${totalParts}`);

        // Migrate each part in the batch
        for (const part of parts) {
          const inventoryRecords = await this.migratePart(part);
          allInventoryRecords.push(...inventoryRecords);
          this.stats.partsProcessed++;
        }

        skip += CONFIG.BATCH_SIZE;
      }

      // Insert inventory records
      if (allInventoryRecords.length > 0 && !CONFIG.DRY_RUN) {
        this.logger.info(`Inserting ${allInventoryRecords.length} inventory records...`);
        
        try {
          await this.Inventories.insertMany(allInventoryRecords, { ordered: false });
          this.stats.inventoryRecordsCreated = allInventoryRecords.length;
          this.logger.success('Inventory records inserted successfully');
        } catch (error) {
          // Handle duplicate key errors gracefully
          if (error.code === 11000) {
            this.logger.warn('Some inventory records already exist', { 
              duplicates: error.writeErrors?.length || 0 
            });
            this.stats.inventoryRecordsCreated = allInventoryRecords.length - (error.writeErrors?.length || 0);
          } else {
            throw error;
          }
        }
      } else if (CONFIG.DRY_RUN) {
        this.logger.info('DRY RUN: Would insert inventory records', { 
          count: allInventoryRecords.length 
        });
        this.stats.inventoryRecordsCreated = allInventoryRecords.length;
      }

    } catch (error) {
      this.logger.error('Migration execution failed', { error: error.message });
      throw error;
    }
  }

  async validatePostMigration() {
    try {
      this.logger.info('Running comprehensive post-migration validation...');

      // Count inventory records created
      const inventoryCount = await this.Inventories.countDocuments();

      // Validate data integrity by comparing totals
      const inventoryTotals = await this.Inventories.aggregate([
        {
          $group: {
            _id: '$partId',
            totalQuantity: { $sum: '$quantity' },
            recordCount: { $sum: 1 },
            stockTypes: { $addToSet: '$stockType' },
            warehouses: { $addToSet: '$warehouseId' }
          }
        }
      ]);

      // Cross-validate with original parts data
      const originalParts = await this.Part.find({
        'inventory.stockLevels': { $exists: true }
      }).select('_id inventory.stockLevels');

      let validationErrors = 0;
      let totalOriginalStock = 0;
      let totalMigratedStock = 0;

      for (const part of originalParts) {
        const stockLevels = part.inventory?.stockLevels;
        if (stockLevels) {
          const originalTotal = Object.values(stockLevels).reduce((sum, qty) => sum + (qty || 0), 0);
          totalOriginalStock += originalTotal;

          // Find corresponding migrated data
          const migratedData = inventoryTotals.find(inv => inv._id.toString() === part._id.toString());
          if (migratedData) {
            totalMigratedStock += migratedData.totalQuantity;

            // Validate stock levels match
            if (originalTotal !== migratedData.totalQuantity) {
              this.logger.error('Stock quantity mismatch', {
                partId: part._id,
                original: originalTotal,
                migrated: migratedData.totalQuantity
              });
              validationErrors++;
            }
          } else if (originalTotal > 0) {
            this.logger.error('Part with stock not migrated', {
              partId: part._id,
              originalStock: originalTotal
            });
            validationErrors++;
          }
        }
      }

      // Validate indexes were created
      const inventoryIndexes = await this.Inventories.collection.indexes();
      const hasCompoundIndex = inventoryIndexes.some(idx =>
        idx.key && idx.key.partId && idx.key.warehouseId && idx.key.stockType
      );

      if (!hasCompoundIndex) {
        this.logger.error('Critical compound index missing on inventories collection');
        validationErrors++;
      }

      // Check for orphaned records
      const orphanedRecords = await this.Inventories.aggregate([
        {
          $lookup: {
            from: 'parts',
            localField: 'partId',
            foreignField: '_id',
            as: 'part'
          }
        },
        {
          $match: { 'part.0': { $exists: false } }
        },
        {
          $count: 'orphaned'
        }
      ]);

      const orphanedCount = orphanedRecords.length > 0 ? orphanedRecords[0].orphaned : 0;

      this.logger.success('Post-migration validation complete', {
        inventoryRecords: inventoryCount,
        partsWithInventory: inventoryTotals.length,
        totalOriginalStock,
        totalMigratedStock,
        validationErrors,
        orphanedRecords: orphanedCount,
        hasRequiredIndexes: hasCompoundIndex
      });

      if (validationErrors > 0) {
        throw new Error(`Migration validation failed with ${validationErrors} errors`);
      }

      if (totalOriginalStock !== totalMigratedStock) {
        throw new Error(`Stock totals don't match: Original=${totalOriginalStock}, Migrated=${totalMigratedStock}`);
      }

      return {
        inventoryCount,
        inventoryTotals,
        validationErrors,
        stockIntegrityValid: totalOriginalStock === totalMigratedStock
      };
    } catch (error) {
      this.logger.error('Post-migration validation failed', { error: error.message });
      throw error;
    }
  }

  async removeEmbeddedInventory() {
    if (CONFIG.DRY_RUN) {
      this.logger.info('DRY RUN: Would remove embedded inventory data from parts collection');
      return;
    }

    try {
      this.logger.info('Removing embedded inventory data from parts collection...');
      
      const result = await this.Part.updateMany(
        { 'inventory': { $exists: true } },
        { $unset: { 'inventory': 1 } }
      );

      this.logger.success('Embedded inventory data removed', {
        modifiedCount: result.modifiedCount
      });
    } catch (error) {
      this.logger.error('Failed to remove embedded inventory data', { error: error.message });
      throw error;
    }
  }

  async cleanup() {
    try {
      await mongoose.disconnect();
      this.logger.info('Disconnected from MongoDB');
    } catch (error) {
      this.logger.error('Error during cleanup', { error: error.message });
    }
  }
}

// Main execution function
async function main() {
  const logger = new Logger(CONFIG.LOG_FILE);
  const stats = new MigrationStats();
  const migration = new InventoryMigration(logger, stats);

  try {
    logger.info('=== INVENTORY MIGRATION V3 STARTED ===', CONFIG);

    // Initialize migration
    await migration.initialize();

    // Create backup
    await migration.createBackup();

    // Pre-migration validation
    await migration.validatePreMigration();

    // Execute migration
    await migration.executeMigration();

    // Post-migration validation
    await migration.validatePostMigration();

    // Remove embedded inventory data (only if not dry run and no errors)
    if (stats.errors.length === 0) {
      await migration.removeEmbeddedInventory();
    } else {
      logger.warn('Skipping embedded inventory removal due to errors');
    }

    // Final report
    const report = stats.getReport();
    logger.success('=== MIGRATION COMPLETED ===', report);

    console.log('\n=== MIGRATION SUMMARY ===');
    console.log(`Parts Processed: ${report.partsProcessed}`);
    console.log(`Inventory Records Created: ${report.inventoryRecordsCreated}`);
    console.log(`Errors: ${report.errors}`);
    console.log(`Warnings: ${report.warnings}`);
    console.log(`Duration: ${report.duration}`);
    console.log(`Log File: ${CONFIG.LOG_FILE}`);
    console.log(`Backup File: ${CONFIG.BACKUP_FILE}`);

    if (report.errors > 0) {
      console.log('\nERRORS OCCURRED - Check log file for details');
      process.exit(1);
    }

  } catch (error) {
    logger.error('Migration failed with critical error', { error: error.message, stack: error.stack });
    console.error('CRITICAL ERROR:', error.message);
    process.exit(1);
  } finally {
    await migration.cleanup();
    logger.close();
  }
}

// Handle process signals
process.on('SIGINT', () => {
  console.log('\nMigration interrupted by user');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\nMigration terminated');
  process.exit(1);
});

// Run migration if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { InventoryMigration, Logger, MigrationStats };
