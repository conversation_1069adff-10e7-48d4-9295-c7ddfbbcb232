/**
 * Inventory Schema Migration Script
 * Migrates from legacy structure to new granular stock tracking
 */

import mongoose from 'mongoose';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

interface MigrationStats {
  partsProcessed: number;
  partsUpdated: number;
  transactionsProcessed: number;
  transactionsUpdated: number;
  warehousesCreated: number;
  errors: string[];
}

async function createDefaultWarehouses() {
  console.log('🏭 Creating default warehouses...');
  
  const warehouses = mongoose.connection.db.collection('warehouses');
  
  const defaultWarehouses = [
    {
      _id: new mongoose.Types.ObjectId('65f000000000000000000001'),
      name: 'Main Warehouse',
      location_id: 'MAIN',
      description: 'Primary warehouse location',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      _id: new mongoose.Types.ObjectId('65f000000000000000000002'),
      name: 'Trend Engineering',
      location_id: 'TEJ',
      description: 'Trend Engineering warehouse',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];
  
  let created = 0;
  for (const warehouse of defaultWarehouses) {
    const existing = await warehouses.findOne({ _id: warehouse._id });
    if (!existing) {
      await warehouses.insertOne(warehouse);
      created++;
      console.log(`✅ Created warehouse: ${warehouse.name}`);
    } else {
      console.log(`⚠️ Warehouse already exists: ${warehouse.name}`);
    }
  }
  
  return created;
}

async function migrateParts(): Promise<{ processed: number; updated: number; errors: string[] }> {
  console.log('📦 Migrating parts to stockLevels structure...');
  
  const stats = { processed: 0, updated: 0, errors: [] as string[] };
  const partsCollection = mongoose.connection.db.collection('parts');
  
  try {
    const cursor = partsCollection.find({});
    
    while (await cursor.hasNext()) {
      const part = await cursor.next();
      if (!part) continue;
      
      stats.processed++;
      
      try {
        // Check if already migrated
        if (part.inventory?.stockLevels) {
          console.log(`⚠️ Part ${part.partNumber} already migrated`);
          continue;
        }
        
        // Create new stockLevels structure
        const currentStock = part.inventory?.currentStock || 0;
        const stockLevels = {
          raw: 0,
          hardening: 0,
          grinding: 0,
          finished: currentStock, // Move current stock to finished
          rejected: 0
        };
        
        // Update the part with new structure
        const updateResult = await partsCollection.updateOne(
          { _id: part._id },
          {
            $set: {
              'inventory.stockLevels': stockLevels,
              'inventory.lastStockUpdate': new Date()
            },
            $unset: {
              'inventory.currentStock': ''
            }
          }
        );
        
        if (updateResult.modifiedCount > 0) {
          stats.updated++;
          console.log(`✅ Migrated part: ${part.partNumber} (${currentStock} → finished)`);
        }
        
      } catch (error: any) {
        const errorMsg = `Part ${part._id}: ${error.message}`;
        stats.errors.push(errorMsg);
        console.error(`❌ Error migrating part ${part._id}:`, error.message);
      }
    }
    
  } catch (error: any) {
    const errorMsg = `Parts migration error: ${error.message}`;
    stats.errors.push(errorMsg);
    console.error('❌ Error in parts migration:', error);
  }
  
  return stats;
}

async function migrateTransactions(): Promise<{ processed: number; updated: number; errors: string[] }> {
  console.log('📋 Migrating transactions to event-sourced structure...');
  
  const stats = { processed: 0, updated: 0, errors: [] as string[] };
  const legacyTransactions = mongoose.connection.db.collection('inventoryTransactions');
  const newTransactions = mongoose.connection.db.collection('transactions');
  
  try {
    const cursor = legacyTransactions.find({});
    
    while (await cursor.hasNext()) {
      const transaction = await cursor.next();
      if (!transaction) continue;
      
      stats.processed++;
      
      try {
        // Check if already migrated
        const existing = await newTransactions.findOne({ 
          legacyId: transaction._id 
        });
        
        if (existing) {
          console.log(`⚠️ Transaction ${transaction._id} already migrated`);
          continue;
        }
        
        // Map legacy transaction to new structure
        let from = null;
        let to = null;
        let newTransactionType = transaction.transactionType;
        
        // Default warehouse ID
        const defaultWarehouseId = new mongoose.Types.ObjectId('65f000000000000000000001');
        
        // Map transaction types and create from/to structure
        switch (transaction.transactionType) {
          case 'stock_in_purchase':
            newTransactionType = 'purchase_receipt';
            from = null; // External source
            to = {
              warehouseId: defaultWarehouseId,
              stockType: 'raw'
            };
            break;
            
          case 'sales_shipment':
            from = {
              warehouseId: defaultWarehouseId,
              stockType: 'finished'
            };
            to = null; // External destination
            break;
            
          case 'adjustment':
            // Keep as adjustment
            from = null;
            to = {
              warehouseId: defaultWarehouseId,
              stockType: 'finished'
            };
            break;
            
          default:
            // Default to internal transfer
            newTransactionType = 'internal_transfer';
            from = {
              warehouseId: defaultWarehouseId,
              stockType: 'finished'
            };
            to = {
              warehouseId: defaultWarehouseId,
              stockType: 'finished'
            };
        }
        
        // Create new transaction document
        const newTransaction = {
          _id: new mongoose.Types.ObjectId(),
          partId: transaction.partId, // Keep as string for now
          quantity: Math.abs(transaction.quantity || 0),
          transactionType: newTransactionType,
          from: from,
          to: to,
          transactionDate: transaction.transactionDate || new Date(),
          referenceNumber: transaction.referenceNumber || null,
          referenceType: transaction.referenceType || null,
          userId: transaction.userId || null,
          notes: transaction.notes || `Migrated from legacy transaction ${transaction._id}`,
          createdAt: new Date(),
          legacyId: transaction._id // Keep reference to original
        };
        
        await newTransactions.insertOne(newTransaction);
        stats.updated++;
        
        console.log(`✅ Migrated transaction: ${transaction._id} → ${newTransactionType}`);
        
      } catch (error: any) {
        const errorMsg = `Transaction ${transaction._id}: ${error.message}`;
        stats.errors.push(errorMsg);
        console.error(`❌ Error migrating transaction ${transaction._id}:`, error.message);
      }
    }
    
  } catch (error: any) {
    const errorMsg = `Transactions migration error: ${error.message}`;
    stats.errors.push(errorMsg);
    console.error('❌ Error in transactions migration:', error);
  }
  
  return stats;
}

async function runMigration() {
  console.log('🚀 Starting inventory schema migration...');
  
  const uri = process.env.MONGODB_URI!;
  const dbName = process.env.MONGODB_DB_NAME || 'IMS';
  
  try {
    await mongoose.connect(uri, {
      dbName: dbName,
      serverSelectionTimeoutMS: 10000,
      connectTimeoutMS: 10000,
    });
    
    console.log('✅ Connected to MongoDB');
    
    const stats: MigrationStats = {
      partsProcessed: 0,
      partsUpdated: 0,
      transactionsProcessed: 0,
      transactionsUpdated: 0,
      warehousesCreated: 0,
      errors: []
    };
    
    // Step 1: Create default warehouses
    stats.warehousesCreated = await createDefaultWarehouses();
    
    // Step 2: Migrate parts
    const partsResult = await migrateParts();
    stats.partsProcessed = partsResult.processed;
    stats.partsUpdated = partsResult.updated;
    stats.errors.push(...partsResult.errors);
    
    // Step 3: Migrate transactions
    const transactionsResult = await migrateTransactions();
    stats.transactionsProcessed = transactionsResult.processed;
    stats.transactionsUpdated = transactionsResult.updated;
    stats.errors.push(...transactionsResult.errors);
    
    // Print final results
    console.log('\n📊 MIGRATION RESULTS:');
    console.log(`✅ Warehouses created: ${stats.warehousesCreated}`);
    console.log(`✅ Parts processed: ${stats.partsProcessed}`);
    console.log(`✅ Parts updated: ${stats.partsUpdated}`);
    console.log(`✅ Transactions processed: ${stats.transactionsProcessed}`);
    console.log(`✅ Transactions updated: ${stats.transactionsUpdated}`);
    
    if (stats.errors.length > 0) {
      console.log(`\n⚠️ Errors encountered: ${stats.errors.length}`);
      stats.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    await mongoose.disconnect();
    console.log('\n🎉 Migration completed successfully!');
    
  } catch (error: any) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
}

runMigration().catch(console.error);
