#!/usr/bin/env node

/**
 * Table Functionality Test Script
 * 
 * This script performs automated testing of the migrated DataTable components
 * to ensure they meet the requirements and function correctly.
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Test results storage
const testResults = [];

// Helper function to log with colors
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Helper function to add test result
function addTestResult(component, test, status, message) {
  testResults.push({ component, test, status, message });
  const statusColor = status === 'PASS' ? 'green' : status === 'FAIL' ? 'red' : 'yellow';
  log(`  ${status}: ${test} - ${message}`, statusColor);
}

// Test file existence and structure
function testFileStructure() {
  log('\n📁 Testing File Structure...', 'cyan');
  
  const requiredFiles = [
    'app/components/data-display/data-table/DataTable.tsx',
    'app/components/data-display/data-table/column-definitions.tsx',
    'app/components/tables/ProductsTable/ProductsTableClient.tsx',
    'app/components/tables/AssembliesTable/AssembliesTableClient.tsx',
    'app/components/inventory/InventoryTable.tsx',
    'app/components/features/ProductTable.tsx',
  ];

  requiredFiles.forEach(filePath => {
    const fullPath = path.join(process.cwd(), filePath);
    if (fs.existsSync(fullPath)) {
      addTestResult('File Structure', `${filePath}`, 'PASS', 'File exists');
    } else {
      addTestResult('File Structure', `${filePath}`, 'FAIL', 'File missing');
    }
  });
}

// Test DataTable component implementation
function testDataTableImplementation() {
  log('\n🔧 Testing DataTable Implementation...', 'cyan');
  
  const dataTablePath = path.join(process.cwd(), 'app/components/data-display/data-table/DataTable.tsx');
  
  if (fs.existsSync(dataTablePath)) {
    const content = fs.readFileSync(dataTablePath, 'utf8');
    
    // Test for required imports
    const requiredImports = [
      '@tanstack/react-table',
      'useReactTable',
      'getCoreRowModel',
      'getSortedRowModel',
      'getFilteredRowModel',
      'getPaginationRowModel'
    ];
    
    requiredImports.forEach(importName => {
      if (content.includes(importName)) {
        addTestResult('DataTable', `Import: ${importName}`, 'PASS', 'Required import found');
      } else {
        addTestResult('DataTable', `Import: ${importName}`, 'FAIL', 'Required import missing');
      }
    });
    
    // Test for required features
    const requiredFeatures = [
      'enableSorting',
      'enableFiltering',
      'enableGlobalSearch',
      'enablePagination',
      'enableRowSelection',
      'mobileDisplayMode',
      'density'
    ];
    
    requiredFeatures.forEach(feature => {
      if (content.includes(feature)) {
        addTestResult('DataTable', `Feature: ${feature}`, 'PASS', 'Feature implemented');
      } else {
        addTestResult('DataTable', `Feature: ${feature}`, 'FAIL', 'Feature missing');
      }
    });
    
    // Test for accessibility features
    const accessibilityFeatures = [
      'aria-label',
      'role=',
      'caption',
      'TableHeader',
      'TableBody'
    ];
    
    accessibilityFeatures.forEach(feature => {
      if (content.includes(feature)) {
        addTestResult('DataTable', `Accessibility: ${feature}`, 'PASS', 'Accessibility feature found');
      } else {
        addTestResult('DataTable', `Accessibility: ${feature}`, 'WARN', 'Accessibility feature not found');
      }
    });
    
  } else {
    addTestResult('DataTable', 'File Check', 'FAIL', 'DataTable.tsx not found');
  }
}

// Test migrated table components
function testMigratedComponents() {
  log('\n🔄 Testing Migrated Components...', 'cyan');
  
  const components = [
    {
      name: 'ProductsTable',
      path: 'app/components/tables/ProductsTable/ProductsTableClient.tsx',
      expectedImports: ['DataTable', 'createProductsSimpleColumns', 'createProductsComplexColumns']
    },
    {
      name: 'AssembliesTable',
      path: 'app/components/tables/AssembliesTable/AssembliesTableClient.tsx',
      expectedImports: ['DataTable', 'createAssembliesColumns']
    },
    {
      name: 'InventoryTable',
      path: 'app/components/inventory/InventoryTable.tsx',
      expectedImports: ['DataTable', 'createInventoryColumns']
    },
    {
      name: 'ProductTable (Features)',
      path: 'app/components/features/ProductTable.tsx',
      expectedImports: ['DataTable', 'createFeatureProductColumns']
    }
  ];
  
  components.forEach(component => {
    const fullPath = path.join(process.cwd(), component.path);
    
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      // Check for DataTable usage
      if (content.includes('<DataTable')) {
        addTestResult(component.name, 'DataTable Usage', 'PASS', 'Component uses DataTable');
      } else {
        addTestResult(component.name, 'DataTable Usage', 'FAIL', 'Component does not use DataTable');
      }
      
      // Check for required imports
      component.expectedImports.forEach(importName => {
        if (content.includes(importName)) {
          addTestResult(component.name, `Import: ${importName}`, 'PASS', 'Required import found');
        } else {
          addTestResult(component.name, `Import: ${importName}`, 'FAIL', 'Required import missing');
        }
      });
      
      // Check for mobile responsiveness
      if (content.includes('mobileDisplayMode')) {
        addTestResult(component.name, 'Mobile Responsiveness', 'PASS', 'Mobile display mode configured');
      } else {
        addTestResult(component.name, 'Mobile Responsiveness', 'WARN', 'Mobile display mode not configured');
      }
      
      // Check for accessibility
      if (content.includes('caption')) {
        addTestResult(component.name, 'Accessibility', 'PASS', 'Table caption provided');
      } else {
        addTestResult(component.name, 'Accessibility', 'WARN', 'Table caption not provided');
      }
      
    } else {
      addTestResult(component.name, 'File Check', 'FAIL', 'Component file not found');
    }
  });
}

// Test column definitions
function testColumnDefinitions() {
  log('\n📊 Testing Column Definitions...', 'cyan');
  
  const columnDefsPath = path.join(process.cwd(), 'app/components/data-display/data-table/column-definitions.tsx');
  
  if (fs.existsSync(columnDefsPath)) {
    const content = fs.readFileSync(columnDefsPath, 'utf8');
    
    const expectedFunctions = [
      'createProductsSimpleColumns',
      'createProductsComplexColumns',
      'createAssembliesColumns',
      'createInventoryColumns',
      'createFeatureProductColumns'
    ];
    
    expectedFunctions.forEach(funcName => {
      if (content.includes(funcName)) {
        addTestResult('Column Definitions', `Function: ${funcName}`, 'PASS', 'Column function defined');
      } else {
        addTestResult('Column Definitions', `Function: ${funcName}`, 'FAIL', 'Column function missing');
      }
    });
    
    const expectedInterfaces = [
      'ProductColumnData',
      'AssemblyColumnData',
      'InventoryColumnData',
      'FeatureProductColumnData'
    ];
    
    expectedInterfaces.forEach(interfaceName => {
      if (content.includes(interfaceName)) {
        addTestResult('Column Definitions', `Interface: ${interfaceName}`, 'PASS', 'Interface defined');
      } else {
        addTestResult('Column Definitions', `Interface: ${interfaceName}`, 'FAIL', 'Interface missing');
      }
    });
    
  } else {
    addTestResult('Column Definitions', 'File Check', 'FAIL', 'column-definitions.tsx not found');
  }
}

// Test deprecated file cleanup
function testDeprecatedFiles() {
  log('\n🗑️  Testing Deprecated File Status...', 'cyan');
  
  const deprecatedFiles = [
    'app/components/tables/ProductsTable.tsx',
    'app/components/tables/AssembliesTable.tsx'
  ];
  
  deprecatedFiles.forEach(filePath => {
    const fullPath = path.join(process.cwd(), filePath);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      if (content.includes('@deprecated')) {
        addTestResult('Deprecated Files', filePath, 'PASS', 'File properly marked as deprecated');
      } else {
        addTestResult('Deprecated Files', filePath, 'WARN', 'File exists but not marked as deprecated');
      }
    } else {
      addTestResult('Deprecated Files', filePath, 'PASS', 'Deprecated file removed');
    }
  });
}

// Generate test report
function generateTestReport() {
  log('\n📋 Test Report Summary', 'bright');
  log('=' * 50, 'bright');
  
  const summary = testResults.reduce((acc, result) => {
    acc[result.status] = (acc[result.status] || 0) + 1;
    return acc;
  }, {});
  
  log(`Total Tests: ${testResults.length}`, 'bright');
  log(`✅ Passed: ${summary.PASS || 0}`, 'green');
  log(`❌ Failed: ${summary.FAIL || 0}`, 'red');
  log(`⚠️  Warnings: ${summary.WARN || 0}`, 'yellow');
  
  const passRate = ((summary.PASS || 0) / testResults.length * 100).toFixed(1);
  log(`\nPass Rate: ${passRate}%`, passRate >= 90 ? 'green' : passRate >= 70 ? 'yellow' : 'red');
  
  if (summary.FAIL > 0) {
    log('\n❌ Failed Tests:', 'red');
    testResults
      .filter(result => result.status === 'FAIL')
      .forEach(result => {
        log(`  • ${result.component}: ${result.test} - ${result.message}`, 'red');
      });
  }
  
  if (summary.WARN > 0) {
    log('\n⚠️  Warnings:', 'yellow');
    testResults
      .filter(result => result.status === 'WARN')
      .forEach(result => {
        log(`  • ${result.component}: ${result.test} - ${result.message}`, 'yellow');
      });
  }
  
  // Save detailed report to file
  const reportPath = path.join(process.cwd(), 'test-results.json');
  fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
  log(`\n📄 Detailed report saved to: ${reportPath}`, 'blue');
}

// Main test execution
function runTests() {
  log('🧪 Starting Table Functionality Tests...', 'bright');
  log('=' * 50, 'bright');
  
  testFileStructure();
  testDataTableImplementation();
  testMigratedComponents();
  testColumnDefinitions();
  testDeprecatedFiles();
  
  generateTestReport();
  
  log('\n✨ Testing Complete!', 'bright');
}

// Run tests if script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  testResults
};
