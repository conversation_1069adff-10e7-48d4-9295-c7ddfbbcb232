#!/usr/bin/env node

/**
 * FIX MIGRATION ISSUES - COMPLETE SOLUTION
 * 
 * This script fixes all identified issues with the inventory migration:
 * 1. Fixes database indexes
 * 2. Executes complete migration
 * 3. Validates data integrity
 * 4. Updates feature flags
 */

const mongoose = require('mongoose');

const CONFIG = {
  MONGODB_URI: process.env.MONGODB_URI || 'mongodb://localhost:27017/IMS'
};

class MigrationFixer {
  constructor() {
    this.db = null;
  }

  async connect() {
    await mongoose.connect(CONFIG.MONGODB_URI);
    this.db = mongoose.connection.db;
    console.log('✅ Connected to MongoDB');
  }

  async disconnect() {
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }

  async fixAllIssues() {
    console.log('🔧 FIXING ALL MIGRATION ISSUES...\n');

    try {
      // Step 1: Fix database indexes
      await this.fixDatabaseIndexes();
      
      // Step 2: Execute complete migration
      await this.executeCompleteMigration();
      
      // Step 3: Validate migration
      await this.validateMigration();
      
      // Step 4: Provide frontend fix instructions
      this.provideFrontendInstructions();
      
      console.log('\n🎉 ALL ISSUES FIXED SUCCESSFULLY!');
      
    } catch (error) {
      console.error('❌ Fix failed:', error.message);
      throw error;
    }
  }

  async fixDatabaseIndexes() {
    console.log('🔧 Step 1: Fixing database indexes...');
    
    try {
      // Drop problematic indexes
      const indexes = await this.db.collection('inventories').indexes();
      console.log(`   Found ${indexes.length} existing indexes`);
      
      // Drop the problematic partId_warehouseId index (without stockType)
      try {
        await this.db.collection('inventories').dropIndex('partId_1_warehouseId_1');
        console.log('   ✅ Dropped problematic partId_warehouseId index');
      } catch (error) {
        console.log('   ℹ️  partId_warehouseId index not found (already dropped)');
      }
      
      // Ensure the correct compound unique index exists
      try {
        await this.db.collection('inventories').createIndex(
          { partId: 1, warehouseId: 1, stockType: 1 },
          { unique: true, name: 'inventories_compound_unique' }
        );
        console.log('   ✅ Created correct compound unique index');
      } catch (error) {
        console.log('   ℹ️  Compound unique index already exists');
      }
      
    } catch (error) {
      console.error('   ❌ Error fixing indexes:', error.message);
      throw error;
    }
  }

  async executeCompleteMigration() {
    console.log('🔧 Step 2: Executing complete migration...');
    
    try {
      // Clear existing inventory records
      const deleteResult = await this.db.collection('inventories').deleteMany({});
      console.log(`   🧹 Cleared ${deleteResult.deletedCount} existing inventory records`);
      
      // Get all parts with embedded inventory
      const parts = await this.db.collection('parts').find({
        'inventory.stockLevels': { $exists: true }
      }).toArray();
      
      console.log(`   📦 Found ${parts.length} parts to migrate`);
      
      let totalRecords = 0;
      const batchSize = 50;
      
      // Process in batches
      for (let i = 0; i < parts.length; i += batchSize) {
        const batch = parts.slice(i, i + batchSize);
        const inventoryRecords = [];
        
        for (const part of batch) {
          const records = this.createInventoryRecords(part);
          inventoryRecords.push(...records);
        }
        
        if (inventoryRecords.length > 0) {
          await this.db.collection('inventories').insertMany(inventoryRecords);
          totalRecords += inventoryRecords.length;
          console.log(`   ✅ Migrated batch ${Math.ceil((i + 1) / batchSize)}: ${inventoryRecords.length} records`);
        }
      }
      
      console.log(`   🎉 Migration complete: ${totalRecords} inventory records created`);
      
    } catch (error) {
      console.error('   ❌ Migration failed:', error.message);
      throw error;
    }
  }

  createInventoryRecords(part) {
    const stockTypes = ['raw', 'hardening', 'grinding', 'finished', 'rejected'];
    const records = [];
    const now = new Date();
    
    for (const stockType of stockTypes) {
      const quantity = part.inventory.stockLevels[stockType] || 0;
      
      const record = {
        partId: part._id,
        warehouseId: part.inventory.warehouseId,
        stockType: stockType,
        quantity: quantity,
        lastUpdated: part.inventory.lastStockUpdate || now,
        safetyStockLevel: part.inventory.safetyStockLevel || 0,
        maximumStockLevel: part.inventory.maximumStockLevel || 1000,
        averageDailyUsage: part.inventory.averageDailyUsage || 0,
        abcClassification: part.inventory.abcClassification || 'C',
        createdAt: now,
        updatedAt: now
      };
      
      records.push(record);
    }
    
    return records;
  }

  async validateMigration() {
    console.log('🔧 Step 3: Validating migration...');
    
    try {
      const partsCount = await this.db.collection('parts').countDocuments();
      const partsWithInventory = await this.db.collection('parts').countDocuments({
        'inventory.stockLevels': { $exists: true }
      });
      const inventoryRecords = await this.db.collection('inventories').countDocuments();
      
      console.log(`   📊 Validation results:`);
      console.log(`      - Total parts: ${partsCount}`);
      console.log(`      - Parts with embedded inventory: ${partsWithInventory}`);
      console.log(`      - Inventory records created: ${inventoryRecords}`);
      
      const expectedRecords = partsWithInventory * 5;
      if (inventoryRecords === expectedRecords) {
        console.log(`   ✅ Perfect! Expected ${expectedRecords} records, got ${inventoryRecords}`);
      } else {
        console.log(`   ⚠️  Expected ${expectedRecords} records, got ${inventoryRecords}`);
      }
      
      // Test aggregation pipeline
      const testResult = await this.db.collection('parts').aggregate([
        { $limit: 1 },
        {
          $lookup: {
            from: 'inventories',
            localField: '_id',
            foreignField: 'partId',
            as: 'inventoryRecords'
          }
        },
        {
          $addFields: {
            reconstructedInventory: {
              currentStock: {
                $sum: {
                  $map: {
                    input: {
                      $filter: {
                        input: '$inventoryRecords',
                        cond: { $eq: ['$$this.stockType', 'finished'] }
                      }
                    },
                    as: 'record',
                    in: '$$record.quantity'
                  }
                }
              },
              totalStock: { $sum: '$inventoryRecords.quantity' }
            }
          }
        }
      ]).toArray();
      
      if (testResult.length > 0 && testResult[0].reconstructedInventory) {
        console.log(`   ✅ Aggregation pipeline working correctly`);
        console.log(`      - Sample current stock: ${testResult[0].reconstructedInventory.currentStock}`);
        console.log(`      - Sample total stock: ${testResult[0].reconstructedInventory.totalStock}`);
      }
      
    } catch (error) {
      console.error('   ❌ Validation failed:', error.message);
      throw error;
    }
  }

  provideFrontendInstructions() {
    console.log('🔧 Step 4: Frontend Configuration Instructions...');
    console.log(`
   📋 REQUIRED FRONTEND CONFIGURATION:
   
   1. Set Environment Variables:
      export USE_INVENTORIES_V3=true
      export MIGRATION_MODE=v3-only
      export ENABLE_MIGRATION_PERFORMANCE_MONITORING=true
   
   2. Restart Application:
      npm run build && npm run start
      # OR
      pm2 restart all
   
   3. Test API Endpoints:
      curl http://localhost:3000/api/parts-v3?limit=5
      curl http://localhost:3000/api/inventories?limit=5
   
   4. Verify Frontend:
      - Navigate to /inventory page
      - Check that data loads correctly
      - Verify no "Finished: 0 pcs" entries
   
   5. Monitor Performance:
      - Check /api/parts-v3 OPTIONS endpoint for health
      - Monitor response times
      - Watch for any errors in logs
    `);
  }
}

// MongoDB Queries for Manual Validation
const VALIDATION_QUERIES = {
  // Check migration status
  checkMigrationStatus: `
    // 1. Count parts with embedded inventory
    db.parts.countDocuments({"inventory.stockLevels": {$exists: true}})
    
    // 2. Count inventory records
    db.inventories.countDocuments({})
    
    // 3. Test aggregation pipeline
    db.parts.aggregate([
      {$limit: 1},
      {$lookup: {from: "inventories", localField: "_id", foreignField: "partId", as: "inv"}},
      {$addFields: {currentStock: {$sum: {$map: {input: {$filter: {input: "$inv", cond: {$eq: ["$$this.stockType", "finished"]}}}, as: "r", in: "$$r.quantity"}}}}}
    ])
  `,
  
  // Fix frontend data loading
  testFrontendAPIs: `
    // Test parts-v3 endpoint
    curl http://localhost:3000/api/parts-v3?limit=5
    
    // Test inventories endpoint  
    curl http://localhost:3000/api/inventories?limit=5
    
    // Test service health
    curl -X OPTIONS http://localhost:3000/api/parts-v3
  `
};

// Main execution
async function main() {
  const fixer = new MigrationFixer();
  
  try {
    await fixer.connect();
    await fixer.fixAllIssues();
    
    console.log('\n📋 VALIDATION QUERIES:');
    console.log(VALIDATION_QUERIES.checkMigrationStatus);
    
    console.log('\n📋 FRONTEND TEST COMMANDS:');
    console.log(VALIDATION_QUERIES.testFrontendAPIs);
    
  } catch (error) {
    console.error('💥 Fix failed:', error.message);
    process.exit(1);
  } finally {
    await fixer.disconnect();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { MigrationFixer };
