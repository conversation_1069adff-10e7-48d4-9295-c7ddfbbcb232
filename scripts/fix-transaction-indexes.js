const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config({ path: '.env.local' });

const MONGODB_URI = process.env.MONGODB_URI;
const DB_NAME = process.env.MONGODB_DB_NAME || 'Trend_IMS';

async function fixTransactionIndexes() {
  console.log('🔧 Starting transaction indexes fix...');
  
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('✅ Connected to MongoDB');
    
    const db = client.db(DB_NAME);
    const transactionsCollection = db.collection('transactions');
    
    // Check current indexes
    console.log('\n📊 Analyzing current indexes...');
    const indexes = await transactionsCollection.indexes();
    
    console.log('Current indexes:');
    indexes.forEach((index, i) => {
      console.log(`  ${i + 1}. ${index.name}: ${JSON.stringify(index.key)}`);
    });
    
    // Check for problematic indexes
    const problematicIndexes = indexes.filter(index => 
      index.name.includes('transaction_id') || 
      Object.keys(index.key).includes('transaction_id')
    );
    
    if (problematicIndexes.length > 0) {
      console.log('\n⚠️  Found problematic indexes:');
      problematicIndexes.forEach(index => {
        console.log(`  - ${index.name}: ${JSON.stringify(index.key)}`);
      });
      
      // Drop problematic indexes
      console.log('\n🗑️  Dropping problematic indexes...');
      for (const index of problematicIndexes) {
        if (index.name !== '_id_') { // Never drop the _id index
          try {
            await transactionsCollection.dropIndex(index.name);
            console.log(`  ✅ Dropped index: ${index.name}`);
          } catch (error) {
            console.log(`  ❌ Failed to drop index ${index.name}: ${error.message}`);
          }
        }
      }
    }
    
    // Create the correct index on transactionId (camelCase)
    console.log('\n🔧 Creating correct indexes...');
    
    try {
      // Create unique index on transactionId (camelCase)
      await transactionsCollection.createIndex(
        { transactionId: 1 }, 
        { 
          unique: true, 
          name: 'transactionId_1',
          partialFilterExpression: { transactionId: { $ne: null } }
        }
      );
      console.log('  ✅ Created unique index on transactionId');
    } catch (error) {
      if (error.code === 11000) {
        console.log('  ⚠️  Index already exists or duplicate values found');
      } else {
        console.log(`  ❌ Failed to create transactionId index: ${error.message}`);
      }
    }
    
    // Create other useful indexes
    try {
      await transactionsCollection.createIndex({ partId: 1 }, { name: 'partId_1' });
      console.log('  ✅ Created index on partId');
    } catch (error) {
      console.log(`  ⚠️  partId index: ${error.message}`);
    }
    
    try {
      await transactionsCollection.createIndex({ transactionDate: -1 }, { name: 'transactionDate_-1' });
      console.log('  ✅ Created index on transactionDate');
    } catch (error) {
      console.log(`  ⚠️  transactionDate index: ${error.message}`);
    }
    
    try {
      await transactionsCollection.createIndex({ userId: 1 }, { name: 'userId_1' });
      console.log('  ✅ Created index on userId');
    } catch (error) {
      console.log(`  ⚠️  userId index: ${error.message}`);
    }
    
    // Check final indexes
    console.log('\n📋 Final indexes:');
    const finalIndexes = await transactionsCollection.indexes();
    finalIndexes.forEach((index, i) => {
      console.log(`  ${i + 1}. ${index.name}: ${JSON.stringify(index.key)}`);
    });
    
    // Check for any documents with null transactionId
    console.log('\n🔍 Checking for null transactionId values...');
    const nullTransactionIds = await transactionsCollection.countDocuments({
      $or: [
        { transactionId: null },
        { transactionId: { $exists: false } }
      ]
    });
    
    if (nullTransactionIds > 0) {
      console.log(`⚠️  Found ${nullTransactionIds} documents with null transactionId`);
      console.log('   Run fix-transaction-ids.js to fix these first');
    } else {
      console.log('✅ All documents have valid transactionId values');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the fix
fixTransactionIndexes().catch(console.error);
