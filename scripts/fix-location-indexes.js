/**
 * <PERSON><PERSON><PERSON> to fix the location collection indexes
 * 
 * This script addresses the bug where a unique index on warehouseId prevents
 * adding multiple locations to the same warehouse.
 * 
 * Actions:
 * 1. Drop the incorrect unique index on warehouseId
 * 2. Ensure correct compound indexes are in place
 * 3. Verify the fix by testing location creation
 */

const { MongoClient } = require('mongodb');
require('dotenv').config({ path: '.env.local' });

// MongoDB connection configuration
const MONGODB_URI = process.env.MONGODB_URI || process.env.MONGODB_URI_DEV || 'mongodb://localhost:27017';
const DB_NAME = process.env.MONGODB_DB_NAME || 'IMS';

console.log('🔧 Using MongoDB URI:', MONGODB_URI.replace(/\/\/.*@/, '//<credentials>@'));
console.log('🔧 Using Database:', DB_NAME);

async function fixLocationIndexes() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    console.log('🔗 Connecting to MongoDB...');
    await client.connect();
    
    const db = client.db(DB_NAME);
    const collection = db.collection('locations');
    
    console.log('📋 Current indexes:');
    const currentIndexes = await collection.indexes();
    currentIndexes.forEach(index => {
      console.log(`  - ${index.name}: ${JSON.stringify(index.key)} ${index.unique ? '(UNIQUE)' : ''}`);
    });
    
    // Check if the problematic index exists
    const problematicIndex = currentIndexes.find(idx => idx.name === 'warehouseId_1');
    
    if (problematicIndex) {
      console.log('\n❌ Found problematic unique index on warehouseId');
      console.log('🔧 Dropping incorrect index: warehouseId_1');
      
      try {
        await collection.dropIndex('warehouseId_1');
        console.log('✅ Successfully dropped warehouseId_1 index');
      } catch (error) {
        console.error('❌ Error dropping index:', error.message);
        throw error;
      }
    } else {
      console.log('\n✅ No problematic warehouseId_1 index found');
    }
    
    // Ensure correct indexes are in place
    console.log('\n🔧 Ensuring correct indexes are in place...');
    
    // Check for the correct compound index
    const correctIndex = currentIndexes.find(idx => 
      idx.name === 'locations_warehouse_name_unique' || 
      (idx.key.warehouseId === 1 && idx.key.name === 1 && idx.unique)
    );
    
    if (!correctIndex) {
      console.log('📝 Creating compound unique index: { warehouseId: 1, name: 1 }');
      await collection.createIndex(
        { warehouseId: 1, name: 1 },
        { 
          unique: true,
          name: 'locations_warehouse_name_unique',
          background: true
        }
      );
      console.log('✅ Created compound unique index');
    } else {
      console.log('✅ Compound unique index already exists');
    }
    
    // Test the fix by attempting to create multiple locations for the same warehouse
    console.log('\n🧪 Testing the fix...');
    
    // Find a warehouse to test with
    const warehousesCollection = db.collection('warehouses');
    const testWarehouse = await warehousesCollection.findOne({});
    
    if (!testWarehouse) {
      console.log('⚠️  No warehouses found for testing');
      return;
    }
    
    console.log(`📍 Testing with warehouse: ${testWarehouse.name} (${testWarehouse._id})`);
    
    // Try to create two test locations
    const testLocation1 = {
      warehouseId: testWarehouse._id,
      name: 'TEST-LOCATION-1',
      description: 'Test location 1 for bug fix verification',
      locationType: 'Bin',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const testLocation2 = {
      warehouseId: testWarehouse._id,
      name: 'TEST-LOCATION-2',
      description: 'Test location 2 for bug fix verification',
      locationType: 'Shelf',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    try {
      // Clean up any existing test locations first
      await collection.deleteMany({ name: { $in: ['TEST-LOCATION-1', 'TEST-LOCATION-2'] } });
      
      console.log('📝 Creating first test location...');
      const result1 = await collection.insertOne(testLocation1);
      console.log(`✅ Created location 1: ${result1.insertedId}`);
      
      console.log('📝 Creating second test location...');
      const result2 = await collection.insertOne(testLocation2);
      console.log(`✅ Created location 2: ${result2.insertedId}`);
      
      console.log('🎉 SUCCESS! Multiple locations can now be added to the same warehouse');
      
      // Clean up test locations
      await collection.deleteMany({ name: { $in: ['TEST-LOCATION-1', 'TEST-LOCATION-2'] } });
      console.log('🧹 Cleaned up test locations');
      
    } catch (error) {
      console.error('❌ Test failed:', error.message);
      if (error.message.includes('E11000')) {
        console.error('💥 The unique constraint issue still exists!');
      }
      throw error;
    }
    
    console.log('\n📋 Final indexes:');
    const finalIndexes = await collection.indexes();
    finalIndexes.forEach(index => {
      console.log(`  - ${index.name}: ${JSON.stringify(index.key)} ${index.unique ? '(UNIQUE)' : ''}`);
    });
    
    console.log('\n✅ Location indexes have been successfully fixed!');
    
  } catch (error) {
    console.error('❌ Error fixing indexes:', error);
    throw error;
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  fixLocationIndexes()
    .then(() => {
      console.log('\n🎉 Script completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Script failed:', error);
      process.exit(1);
    });
}

module.exports = { fixLocationIndexes };
