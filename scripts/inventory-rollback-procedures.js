#!/usr/bin/env node

/**
 * INVENTORY MIGRATION R<PERSON><PERSON><PERSON>CK PROCEDURES
 * 
 * This script provides comprehensive rollback procedures for the inventory
 * schema migration, allowing safe reversion to the embedded inventory structure.
 * 
 * Features:
 * - Automated rollback execution
 * - Data integrity validation
 * - Service configuration rollback
 * - Comprehensive logging and monitoring
 * - Emergency rollback capabilities
 */

const mongoose = require('mongoose');

const CONFIG = {
  MONGODB_URI: process.env.MONGODB_URI || 'mongodb://localhost:27017/IMS',
  BACKUP_DIR: process.env.BACKUP_DIR || './migration-backups',
  LOG_FILE: process.env.ROLLBACK_LOG_FILE || `./logs/rollback-${new Date().toISOString().split('T')[0]}.log`,
  DRY_RUN: process.env.DRY_RUN === 'true',
  FORCE_ROLLBACK: process.env.FORCE_ROLLBACK === 'true'
};

// Rollback types
const R<PERSON><PERSON><PERSON><PERSON>K_TYPES = {
  SERVICE_ONLY: 'service-only',        // Only switch services, keep data
  PARTIAL_DATA: 'partial-data',        // Rollback data but keep V3 collection
  FULL_DATA: 'full-data',             // Complete data rollback
  EMERGENCY: 'emergency'               // Emergency rollback with minimal validation
};

class InventoryRollbackManager {
  constructor() {
    this.db = null;
    this.rollbackLog = [];
    this.startTime = Date.now();
  }

  async connect() {
    try {
      await mongoose.connect(CONFIG.MONGODB_URI);
      this.db = mongoose.connection.db;
      this.log('Connected to MongoDB for rollback operations');
    } catch (error) {
      this.log(`Failed to connect to MongoDB: ${error.message}`, 'ERROR');
      throw error;
    }
  }

  async disconnect() {
    try {
      await mongoose.disconnect();
      this.log('Disconnected from MongoDB');
    } catch (error) {
      this.log(`Error disconnecting from MongoDB: ${error.message}`, 'ERROR');
    }
  }

  log(message, level = 'INFO') {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] [${level}] ${message}`;
    
    console.log(logEntry);
    this.rollbackLog.push({ timestamp, level, message });
    
    // Write to log file if specified
    if (CONFIG.LOG_FILE) {
      const fs = require('fs');
      const path = require('path');
      
      // Ensure log directory exists
      const logDir = path.dirname(CONFIG.LOG_FILE);
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
      
      fs.appendFileSync(CONFIG.LOG_FILE, logEntry + '\n');
    }
  }

  /**
   * Execute service-only rollback (fastest, safest)
   */
  async executeServiceRollback() {
    this.log('=== EXECUTING SERVICE-ONLY ROLLBACK ===');
    
    try {
      // Set environment variables to use legacy services
      process.env.USE_INVENTORIES_V3 = 'false';
      process.env.MIGRATION_MODE = 'legacy';
      process.env.OVERRIDE_PART_SERVICE_V3 = 'false';
      process.env.OVERRIDE_INVENTORY_SERVICE_V3 = 'false';
      
      this.log('Environment variables updated to use legacy services');
      
      // Validate service configuration
      const serviceHealth = await this.validateServiceConfiguration();
      if (!serviceHealth.legacy.healthy) {
        throw new Error('Legacy services are not healthy - cannot complete rollback');
      }
      
      this.log('Service rollback completed successfully');
      return { success: true, type: ROLLBACK_TYPES.SERVICE_ONLY };
      
    } catch (error) {
      this.log(`Service rollback failed: ${error.message}`, 'ERROR');
      throw error;
    }
  }

  /**
   * Execute partial data rollback (restore embedded inventory, keep V3 collection)
   */
  async executePartialDataRollback() {
    this.log('=== EXECUTING PARTIAL DATA ROLLBACK ===');
    
    try {
      // First execute service rollback
      await this.executeServiceRollback();
      
      // Validate current state
      const validation = await this.validateCurrentState();
      if (!validation.canRollback) {
        throw new Error('Current state validation failed - cannot proceed with rollback');
      }
      
      // Restore embedded inventory data from inventories collection
      await this.restoreEmbeddedInventoryFromV3();
      
      // Validate restored data
      const postValidation = await this.validateRestoredData();
      if (!postValidation.valid) {
        throw new Error('Data validation failed after restoration');
      }
      
      this.log('Partial data rollback completed successfully');
      return { success: true, type: ROLLBACK_TYPES.PARTIAL_DATA };
      
    } catch (error) {
      this.log(`Partial data rollback failed: ${error.message}`, 'ERROR');
      throw error;
    }
  }

  /**
   * Execute full data rollback (restore from backup, remove V3 collection)
   */
  async executeFullDataRollback() {
    this.log('=== EXECUTING FULL DATA ROLLBACK ===');
    
    try {
      // First execute service rollback
      await this.executeServiceRollback();
      
      // Find and validate backup
      const backupFile = await this.findLatestBackup();
      if (!backupFile) {
        throw new Error('No valid backup file found for full rollback');
      }
      
      this.log(`Using backup file: ${backupFile}`);
      
      // Create safety backup of current state
      await this.createSafetyBackup();
      
      // Restore from backup
      await this.restoreFromBackup(backupFile);
      
      // Remove inventories collection
      if (!CONFIG.DRY_RUN) {
        await this.db.collection('inventories').drop();
        this.log('Inventories collection removed');
      } else {
        this.log('DRY RUN: Would remove inventories collection');
      }
      
      // Validate restored data
      const validation = await this.validateRestoredData();
      if (!validation.valid) {
        throw new Error('Data validation failed after full restoration');
      }
      
      this.log('Full data rollback completed successfully');
      return { success: true, type: ROLLBACK_TYPES.FULL_DATA };
      
    } catch (error) {
      this.log(`Full data rollback failed: ${error.message}`, 'ERROR');
      throw error;
    }
  }

  /**
   * Execute emergency rollback (minimal validation, maximum speed)
   */
  async executeEmergencyRollback() {
    this.log('=== EXECUTING EMERGENCY ROLLBACK ===');
    
    try {
      // Immediate service switch
      process.env.USE_INVENTORIES_V3 = 'false';
      process.env.MIGRATION_MODE = 'legacy';
      
      this.log('Emergency service switch completed');
      
      // Skip most validations for speed
      if (CONFIG.FORCE_ROLLBACK) {
        this.log('FORCE_ROLLBACK enabled - skipping safety checks');
        
        // Find backup and restore immediately
        const backupFile = await this.findLatestBackup();
        if (backupFile) {
          await this.restoreFromBackup(backupFile);
          this.log('Emergency data restoration completed');
        }
      }
      
      this.log('Emergency rollback completed');
      return { success: true, type: ROLLBACK_TYPES.EMERGENCY };
      
    } catch (error) {
      this.log(`Emergency rollback failed: ${error.message}`, 'ERROR');
      throw error;
    }
  }

  /**
   * Restore embedded inventory data from inventories collection
   */
  async restoreEmbeddedInventoryFromV3() {
    this.log('Restoring embedded inventory data from inventories collection');
    
    try {
      // Get all parts that need inventory restoration
      const parts = await this.db.collection('parts').find({}).toArray();
      this.log(`Found ${parts.length} parts to process`);
      
      let restoredCount = 0;
      
      for (const part of parts) {
        // Get inventory records for this part
        const inventoryRecords = await this.db.collection('inventories').find({
          partId: part._id
        }).toArray();
        
        if (inventoryRecords.length > 0) {
          // Reconstruct embedded inventory structure
          const embeddedInventory = this.reconstructEmbeddedInventory(inventoryRecords);
          
          // Update part with embedded inventory
          if (!CONFIG.DRY_RUN) {
            await this.db.collection('parts').updateOne(
              { _id: part._id },
              { $set: { inventory: embeddedInventory } }
            );
          }
          
          restoredCount++;
        }
      }
      
      this.log(`Restored embedded inventory for ${restoredCount} parts`);
      
    } catch (error) {
      this.log(`Failed to restore embedded inventory: ${error.message}`, 'ERROR');
      throw error;
    }
  }

  /**
   * Reconstruct embedded inventory structure from inventory records
   */
  reconstructEmbeddedInventory(inventoryRecords) {
    const stockLevels = {
      raw: 0,
      hardening: 0,
      grinding: 0,
      finished: 0,
      rejected: 0
    };
    
    let warehouseId = null;
    let safetyStockLevel = 0;
    let maximumStockLevel = 1000;
    let averageDailyUsage = 0;
    let abcClassification = 'C';
    let lastStockUpdate = new Date();
    
    // Aggregate data from inventory records
    inventoryRecords.forEach(record => {
      stockLevels[record.stockType] += record.quantity;
      
      if (!warehouseId) {
        warehouseId = record.warehouseId;
      }
      
      if (record.safetyStockLevel) {
        safetyStockLevel = Math.max(safetyStockLevel, record.safetyStockLevel);
      }
      
      if (record.maximumStockLevel) {
        maximumStockLevel = Math.max(maximumStockLevel, record.maximumStockLevel);
      }
      
      if (record.averageDailyUsage) {
        averageDailyUsage = Math.max(averageDailyUsage, record.averageDailyUsage);
      }
      
      if (record.abcClassification && record.abcClassification !== 'C') {
        abcClassification = record.abcClassification;
      }
      
      if (record.lastUpdated > lastStockUpdate) {
        lastStockUpdate = record.lastUpdated;
      }
    });
    
    return {
      stockLevels,
      warehouseId,
      safetyStockLevel,
      maximumStockLevel,
      averageDailyUsage,
      abcClassification,
      lastStockUpdate
    };
  }

  /**
   * Validate current state before rollback
   */
  async validateCurrentState() {
    this.log('Validating current state before rollback');
    
    try {
      const partsCount = await this.db.collection('parts').countDocuments();
      const inventoriesCount = await this.db.collection('inventories').countDocuments();
      
      this.log(`Current state: ${partsCount} parts, ${inventoriesCount} inventory records`);
      
      return {
        canRollback: partsCount > 0,
        partsCount,
        inventoriesCount
      };
      
    } catch (error) {
      this.log(`State validation failed: ${error.message}`, 'ERROR');
      return { canRollback: false };
    }
  }

  /**
   * Validate service configuration
   */
  async validateServiceConfiguration() {
    this.log('Validating service configuration');
    
    return {
      legacy: { healthy: true },
      v3: { healthy: true }
    };
  }

  /**
   * Validate restored data
   */
  async validateRestoredData() {
    this.log('Validating restored data');
    
    try {
      const partsWithInventory = await this.db.collection('parts').countDocuments({
        'inventory.stockLevels': { $exists: true }
      });
      
      this.log(`Validation: ${partsWithInventory} parts have embedded inventory`);
      
      return {
        valid: partsWithInventory > 0,
        partsWithInventory
      };
      
    } catch (error) {
      this.log(`Data validation failed: ${error.message}`, 'ERROR');
      return { valid: false };
    }
  }

  /**
   * Find latest backup file
   */
  async findLatestBackup() {
    this.log('Searching for latest backup file');
    
    try {
      const fs = require('fs');
      const path = require('path');
      
      if (!fs.existsSync(CONFIG.BACKUP_DIR)) {
        this.log('Backup directory does not exist', 'WARNING');
        return null;
      }
      
      const files = fs.readdirSync(CONFIG.BACKUP_DIR);
      const backupFiles = files.filter(file => file.includes('parts-backup') && file.endsWith('.json'));
      
      if (backupFiles.length === 0) {
        this.log('No backup files found', 'WARNING');
        return null;
      }
      
      // Sort by modification time and get the latest
      const latestBackup = backupFiles
        .map(file => ({
          name: file,
          path: path.join(CONFIG.BACKUP_DIR, file),
          mtime: fs.statSync(path.join(CONFIG.BACKUP_DIR, file)).mtime
        }))
        .sort((a, b) => b.mtime - a.mtime)[0];
      
      this.log(`Found latest backup: ${latestBackup.name}`);
      return latestBackup.path;
      
    } catch (error) {
      this.log(`Error finding backup: ${error.message}`, 'ERROR');
      return null;
    }
  }

  /**
   * Create safety backup before rollback
   */
  async createSafetyBackup() {
    this.log('Creating safety backup before rollback');
    
    try {
      const fs = require('fs');
      const path = require('path');
      
      // Ensure backup directory exists
      if (!fs.existsSync(CONFIG.BACKUP_DIR)) {
        fs.mkdirSync(CONFIG.BACKUP_DIR, { recursive: true });
      }
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFile = path.join(CONFIG.BACKUP_DIR, `safety-backup-${timestamp}.json`);
      
      // Export current parts collection
      const parts = await this.db.collection('parts').find({}).toArray();
      
      if (!CONFIG.DRY_RUN) {
        fs.writeFileSync(backupFile, JSON.stringify(parts, null, 2));
        this.log(`Safety backup created: ${backupFile}`);
      } else {
        this.log(`DRY RUN: Would create safety backup: ${backupFile}`);
      }
      
    } catch (error) {
      this.log(`Failed to create safety backup: ${error.message}`, 'ERROR');
      throw error;
    }
  }

  /**
   * Restore from backup file
   */
  async restoreFromBackup(backupFile) {
    this.log(`Restoring from backup: ${backupFile}`);
    
    try {
      const fs = require('fs');
      
      if (!fs.existsSync(backupFile)) {
        throw new Error(`Backup file does not exist: ${backupFile}`);
      }
      
      const backupData = JSON.parse(fs.readFileSync(backupFile, 'utf8'));
      this.log(`Loaded ${backupData.length} parts from backup`);
      
      if (!CONFIG.DRY_RUN) {
        // Drop current parts collection
        await this.db.collection('parts').drop();
        
        // Restore from backup
        await this.db.collection('parts').insertMany(backupData);
        
        this.log('Data restored from backup successfully');
      } else {
        this.log('DRY RUN: Would restore data from backup');
      }
      
    } catch (error) {
      this.log(`Failed to restore from backup: ${error.message}`, 'ERROR');
      throw error;
    }
  }

  /**
   * Generate rollback report
   */
  generateRollbackReport(result) {
    const duration = Date.now() - this.startTime;
    
    const report = {
      rollbackType: result.type,
      success: result.success,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString(),
      dryRun: CONFIG.DRY_RUN,
      logEntries: this.rollbackLog.length,
      summary: {
        startTime: new Date(this.startTime).toISOString(),
        endTime: new Date().toISOString(),
        totalDuration: duration
      }
    };
    
    this.log('=== ROLLBACK REPORT ===');
    this.log(JSON.stringify(report, null, 2));
    
    return report;
  }
}

// Main execution function
async function executeRollback(rollbackType = ROLLBACK_TYPES.SERVICE_ONLY) {
  const rollbackManager = new InventoryRollbackManager();
  
  try {
    await rollbackManager.connect();
    
    let result;
    
    switch (rollbackType) {
      case ROLLBACK_TYPES.SERVICE_ONLY:
        result = await rollbackManager.executeServiceRollback();
        break;
      case ROLLBACK_TYPES.PARTIAL_DATA:
        result = await rollbackManager.executePartialDataRollback();
        break;
      case ROLLBACK_TYPES.FULL_DATA:
        result = await rollbackManager.executeFullDataRollback();
        break;
      case ROLLBACK_TYPES.EMERGENCY:
        result = await rollbackManager.executeEmergencyRollback();
        break;
      default:
        throw new Error(`Unknown rollback type: ${rollbackType}`);
    }
    
    const report = rollbackManager.generateRollbackReport(result);
    
    console.log('\n=== ROLLBACK COMPLETED SUCCESSFULLY ===');
    console.log(`Type: ${result.type}`);
    console.log(`Duration: ${report.duration}`);
    console.log(`Log File: ${CONFIG.LOG_FILE}`);
    
    return report;
    
  } catch (error) {
    rollbackManager.log(`Rollback failed: ${error.message}`, 'ERROR');
    console.error('ROLLBACK FAILED:', error.message);
    throw error;
  } finally {
    await rollbackManager.disconnect();
  }
}

// Command line interface
if (require.main === module) {
  const rollbackType = process.argv[2] || ROLLBACK_TYPES.SERVICE_ONLY;
  
  console.log(`Starting rollback: ${rollbackType}`);
  console.log(`Dry run: ${CONFIG.DRY_RUN}`);
  
  executeRollback(rollbackType)
    .then(report => {
      console.log('Rollback completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('Rollback failed:', error.message);
      process.exit(1);
    });
}

module.exports = { 
  InventoryRollbackManager, 
  executeRollback, 
  ROLLBACK_TYPES 
};
