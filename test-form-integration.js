#!/usr/bin/env node

/**
 * Test Form Integration and Data Transformation
 * Tests the data flow from API → EditPartAction → Form → API
 */

const BASE_URL = 'http://localhost:3000/api/parts';

/**
 * Simulate the EditPartAction data transformation
 */
function transformApiDataToFormData(apiData) {
  // Helper function to extract ID from populated field or return the value as-is
  const extractId = (field) => {
    if (!field) return '';
    if (typeof field === 'string') return field;
    if (typeof field === 'object' && field._id) return field._id;
    if (typeof field === 'object' && field.id) return field.id;
    return '';
  };

  return {
    _id: apiData._id,
    name: apiData.name,
    businessName: apiData.businessName || null,
    partNumber: apiData.partNumber || '',
    description: apiData.description || '',
    technicalSpecs: apiData.technicalSpecs || '',
    isManufactured: apiData.isManufactured || false,
    reorderLevel: apiData.reorderLevel || null,
    status: (apiData.status || 'active'),
    categoryId: apiData.categoryId || '',
    inventory: {
      stockLevels: {
        raw: apiData.inventory?.stockLevels?.raw || 0,
        hardening: apiData.inventory?.stockLevels?.hardening || 0,
        grinding: apiData.inventory?.stockLevels?.grinding || 0,
        finished: apiData.inventory?.stockLevels?.finished || (apiData.inventory?.currentStock || 0),
        rejected: apiData.inventory?.stockLevels?.rejected || 0,
      },
      warehouseId: extractId(apiData.inventory?.warehouseId || apiData.warehouse),
      lastStockUpdate: apiData.inventory?.lastStockUpdate ? new Date(apiData.inventory.lastStockUpdate) : null,
      safetyStockLevel: apiData.inventory?.safetyStockLevel || undefined,
      maximumStockLevel: apiData.inventory?.maximumStockLevel || undefined,
      averageDailyUsage: apiData.inventory?.averageDailyUsage || undefined,
      abcClassification: apiData.inventory?.abcClassification || undefined,
    },
    isAssembly: apiData.isAssembly || false,
    subParts: apiData.subParts || [],
    schemaVersion: apiData.schemaVersion || 1,
    supplierId: extractId(apiData.supplierId || apiData.supplier),
    unitOfMeasure: apiData.unitOfMeasure || 'pcs',
    costPrice: apiData.costPrice || undefined,
  };
}

/**
 * Simulate the form submission data preparation
 */
function prepareFormDataForSubmission(formData) {
  // Remove immutable fields from the request body
  const {
    _id,
    partNumber,
    isAssembly,
    subParts,
    schemaVersion,
    ...updateData
  } = formData;

  // Also exclude system-managed inventory fields
  if (updateData.inventory?.lastStockUpdate) {
    const { lastStockUpdate, ...inventoryWithoutTimestamp } = updateData.inventory;
    updateData.inventory = inventoryWithoutTimestamp;
  }

  // Handle ObjectId fields - convert empty strings to null
  if (updateData.supplierId === '') {
    updateData.supplierId = null;
  }
  if (updateData.categoryId === '') {
    updateData.categoryId = null;
  }

  return updateData;
}

/**
 * Make HTTP request with error handling
 */
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });

    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${data.error || 'Request failed'}`);
    }

    return { success: true, data, status: response.status };
  } catch (error) {
    return { success: false, error: error.message, status: error.status || 500 };
  }
}

/**
 * Test the complete form integration workflow
 */
async function testFormIntegration() {
  console.log('🧪 Testing Form Integration Workflow');
  console.log('API → EditPartAction → Form → API');

  // Step 1: Get a part from the API (simulating EditPartAction fetch)
  console.log('\n📥 Step 1: Fetching part data from API...');
  const getResult = await makeRequest(`${BASE_URL}/688106fed0fbc36823bf7e6c`);
  
  if (!getResult.success) {
    console.error('❌ Failed to fetch part data:', getResult.error);
    return false;
  }

  const apiData = getResult.data.data;
  console.log('✅ API data fetched successfully');
  console.log(`   Part: ${apiData.name} (${apiData.partNumber})`);
  console.log(`   Business Name: ${apiData.businessName}`);
  console.log(`   Stock Levels:`, apiData.inventory.stockLevels);
  console.log(`   Warehouse: ${apiData.warehouse?.name} (${apiData.warehouse?._id})`);
  console.log(`   Supplier: ${apiData.supplier?.name} (${apiData.supplier?._id})`);

  // Step 2: Transform API data to form data (simulating EditPartAction transformation)
  console.log('\n🔄 Step 2: Transforming API data to form data...');
  const formData = transformApiDataToFormData(apiData);
  
  console.log('✅ Data transformation completed');
  console.log(`   Form Name: ${formData.name}`);
  console.log(`   Form Business Name: ${formData.businessName}`);
  console.log(`   Form Stock Levels:`, formData.inventory.stockLevels);
  console.log(`   Form Warehouse ID: ${formData.inventory.warehouseId}`);
  console.log(`   Form Supplier ID: ${formData.supplierId}`);

  // Validate transformation
  const transformationValid = 
    formData.name === apiData.name &&
    formData.businessName === apiData.businessName &&
    formData.inventory.stockLevels.finished === apiData.inventory.stockLevels.finished &&
    formData.inventory.warehouseId === apiData.warehouse._id &&
    formData.supplierId === apiData.supplier._id;

  if (!transformationValid) {
    console.error('❌ Data transformation validation failed');
    console.log('Expected warehouse ID:', apiData.warehouse._id);
    console.log('Actual warehouse ID:', formData.inventory.warehouseId);
    console.log('Expected supplier ID:', apiData.supplier._id);
    console.log('Actual supplier ID:', formData.supplierId);
    return false;
  }

  console.log('✅ Data transformation validation passed');

  // Step 3: Prepare form data for submission (simulating form submission)
  console.log('\n📤 Step 3: Preparing form data for submission...');
  
  // Simulate user making changes to the form
  const modifiedFormData = {
    ...formData,
    name: 'Form Integration Test - Updated',
    businessName: 'Updated via Form Integration Test',
    inventory: {
      ...formData.inventory,
      stockLevels: {
        ...formData.inventory.stockLevels,
        raw: 50,
        finished: 100
      },
      safetyStockLevel: 25
    }
  };

  const submissionData = prepareFormDataForSubmission(modifiedFormData);
  
  console.log('✅ Form data prepared for submission');
  console.log(`   Excluded immutable fields: _id, partNumber, isAssembly, subParts, schemaVersion`);
  console.log(`   Update data keys:`, Object.keys(submissionData));

  // Validate that immutable fields are excluded
  const immutableFieldsExcluded = 
    !submissionData.hasOwnProperty('_id') &&
    !submissionData.hasOwnProperty('partNumber') &&
    !submissionData.hasOwnProperty('isAssembly') &&
    !submissionData.hasOwnProperty('subParts') &&
    !submissionData.hasOwnProperty('schemaVersion');

  if (!immutableFieldsExcluded) {
    console.error('❌ Immutable fields not properly excluded');
    console.log('Submission data contains:', Object.keys(submissionData));
    return false;
  }

  console.log('✅ Immutable fields properly excluded');

  // Step 4: Submit the update (simulating form submission)
  console.log('\n💾 Step 4: Submitting update to API...');
  const updateResult = await makeRequest(`${BASE_URL}/${apiData._id}`, {
    method: 'PUT',
    body: JSON.stringify(submissionData)
  });

  if (!updateResult.success) {
    console.error('❌ Form submission failed:', updateResult.error);
    return false;
  }

  const updatedPart = updateResult.data.data;
  console.log('✅ Form submission successful');
  console.log(`   Updated Name: ${updatedPart.name}`);
  console.log(`   Updated Business Name: ${updatedPart.businessName}`);
  console.log(`   Updated Stock Levels:`, updatedPart.inventory.stockLevels);

  // Validate the update
  const updateValid = 
    updatedPart.name === modifiedFormData.name &&
    updatedPart.businessName === modifiedFormData.businessName &&
    updatedPart.inventory.stockLevels.raw === modifiedFormData.inventory.stockLevels.raw &&
    updatedPart.inventory.stockLevels.finished === modifiedFormData.inventory.stockLevels.finished &&
    updatedPart.inventory.safetyStockLevel === modifiedFormData.inventory.safetyStockLevel;

  if (!updateValid) {
    console.error('❌ Update validation failed');
    return false;
  }

  console.log('✅ Update validation passed');

  // Step 5: Verify the updated data can be fetched and transformed again
  console.log('\n🔄 Step 5: Verifying round-trip data integrity...');
  const verifyResult = await makeRequest(`${BASE_URL}/${apiData._id}`);
  
  if (!verifyResult.success) {
    console.error('❌ Failed to verify updated data:', verifyResult.error);
    return false;
  }

  const verifyData = verifyResult.data.data;
  const verifyFormData = transformApiDataToFormData(verifyData);

  const roundTripValid = 
    verifyFormData.name === modifiedFormData.name &&
    verifyFormData.businessName === modifiedFormData.businessName &&
    verifyFormData.inventory.stockLevels.raw === modifiedFormData.inventory.stockLevels.raw &&
    verifyFormData.inventory.stockLevels.finished === modifiedFormData.inventory.stockLevels.finished;

  if (!roundTripValid) {
    console.error('❌ Round-trip validation failed');
    return false;
  }

  console.log('✅ Round-trip validation passed');
  console.log('✅ Complete form integration workflow successful');

  return true;
}

/**
 * Test pre-selection data extraction
 */
async function testPreSelectionData() {
  console.log('\n🧪 Testing Pre-selection Data Extraction');
  
  // Get a part with populated warehouse and supplier
  const result = await makeRequest(`${BASE_URL}/688106fed0fbc36823bf7e6c`);
  
  if (!result.success) {
    console.error('❌ Failed to fetch part for pre-selection test');
    return false;
  }

  const apiData = result.data.data;
  const formData = transformApiDataToFormData(apiData);

  console.log('📋 Pre-selection Test Results:');
  console.log(`   API Warehouse: ${apiData.warehouse?.name} (${apiData.warehouse?._id})`);
  console.log(`   Form Warehouse ID: ${formData.inventory.warehouseId}`);
  console.log(`   API Supplier: ${apiData.supplier?.name} (${apiData.supplier?._id})`);
  console.log(`   Form Supplier ID: ${formData.supplierId}`);

  // Validate pre-selection extraction
  const preSelectionValid = 
    formData.inventory.warehouseId === apiData.warehouse?._id &&
    formData.supplierId === apiData.supplier?._id;

  if (!preSelectionValid) {
    console.error('❌ Pre-selection data extraction failed');
    return false;
  }

  console.log('✅ Pre-selection data extraction successful');
  return true;
}

/**
 * Run all integration tests
 */
async function runIntegrationTests() {
  console.log('🚀 Starting Form Integration Test Suite');
  console.log('Testing complete data flow: API → Form → API');
  
  const tests = [
    { name: 'Form Integration Workflow', fn: testFormIntegration },
    { name: 'Pre-selection Data Extraction', fn: testPreSelectionData }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      console.log(`\n${'='.repeat(60)}`);
      const result = await test.fn();
      if (result) {
        passed++;
        console.log(`✅ ${test.name} PASSED`);
      } else {
        failed++;
        console.log(`❌ ${test.name} FAILED`);
      }
    } catch (error) {
      console.error(`❌ Test ${test.name} threw an error:`, error.message);
      failed++;
    }
  }
  
  console.log(`\n${'='.repeat(60)}`);
  console.log('📊 Integration Test Results Summary');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All integration tests passed! Form workflow is working correctly.');
    process.exit(0);
  } else {
    console.log('\n💥 Some integration tests failed. Please check the errors above.');
    process.exit(1);
  }
}

// Run the integration tests
runIntegrationTests().catch(error => {
  console.error('💥 Integration test suite failed with error:', error.message);
  process.exit(1);
});
