const fs = require('fs');
const path = require('path');

// Files that need to be updated
const filesToUpdate = [
  'app/services/mongodb.ts',
  'app/services/part.service.ts',
  'app/services/analytics.ts'
];

function replaceInFile(filePath) {
  try {
    const fullPath = path.resolve(filePath);
    if (!fs.existsSync(fullPath)) {
      console.log(`File not found: ${filePath}`);
      return;
    }

    let content = fs.readFileSync(fullPath, 'utf8');
    const originalContent = content;
    
    // Replace all occurrences of connectToMongoose with connectToDatabase
    content = content.replace(/connectToMongoose/g, 'connectToDatabase');
    
    // Also fix any import statements
    content = content.replace(/import connectToMongoose from/g, 'import connectToDatabase from');
    
    if (content !== originalContent) {
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`Updated ${filePath}`);
      
      // Count replacements
      const matches = originalContent.match(/connectToMongoose/g);
      console.log(`  - Replaced ${matches ? matches.length : 0} occurrences`);
    } else {
      console.log(`No changes needed in ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

console.log('Fixing connectToMongoose references...');
filesToUpdate.forEach(replaceInFile);
console.log('Done!');
