# Trend IMS Environment Configuration Template
# Copy this file to .env and fill in the actual values

# Environment
NODE_ENV=development

# Server Configuration
PORT=5174
HOST=localhost

# MongoDB Connection
# For local development, use: mongodb://localhost:27017/trend_ims
# For production, use your MongoDB Atlas connection string
MONGODB_URI=mongodb://localhost:27017/trend_ims
MONGODB_DB_NAME=trend_ims

# Next Auth Configuration
# For local development, use: http://localhost:5174
# For production, use your actual domain
NEXTAUTH_URL=http://localhost:5174
# Generate a secure secret for production: https://generate-secret.vercel.app/32
NEXTAUTH_SECRET=your-nextauth-secret-key-change-this-in-production

# Sentry Configuration (Optional - for error monitoring)
# Sign up at https://sentry.io and create a project to get your DSN
# Comment out these lines if you don't want to use Sentry
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn-here
# SENTRY_AUTH_TOKEN=your-sentry-auth-token-for-source-maps
# Set to 'true' to enable Sentry in development (default: only enabled in production)
# NEXT_PUBLIC_ENABLE_SENTRY_DEV=false

# MCP Configuration (for development tools)
MCP_POSTMAN_PORT=3366

# API and Application URLs
# For local development, use: http://localhost:5174
# For production, use your actual domain
NEXT_PUBLIC_APP_URL=http://localhost:5174
NEXT_PUBLIC_API_BASE_URL=http://localhost:5174
API_BASE_URL=http://localhost:5174/api

# Database SSL Configuration
# For development only - set to 0 to bypass certificate validation
# For production, remove this line or set to 1
NODE_TLS_REJECT_UNAUTHORIZED=0

# Additional Notes:
# 1. Never commit your actual .env file to version control
# 2. For production deployment, ensure all URLs use HTTPS
# 3. Generate strong, unique secrets for production environments
# 4. Consider using environment-specific .env files (.env.local, .env.production, etc.)
