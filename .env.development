# =============================================================================
# TREND IMS - DEVELOPMENT ENVIRONMENT CONFIGURATION
# =============================================================================
# This file is specifically for development environment
# =============================================================================

# =============================================================================
# CORE ENVIRONMENT SETTINGS
# =============================================================================
NODE_ENV=development

# =============================================================================
# SERVER CONFIGURATION (Development)
# =============================================================================
PORT=3001
HOST=localhost

# =============================================================================
# API URLS (Development)
# =============================================================================
NEXT_PUBLIC_APP_URL=http://localhost:3001
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001

# =============================================================================
# AUTHENTICATION (Development)
# =============================================================================
NEXTAUTH_URL=http://localhost:3001
NEXTAUTH_SECRET=dev-secret-key-change-in-production

# =============================================================================
# MONGODB CONFIGURATION (Development)
# =============================================================================
# Use the same MongoDB connection as production for consistency
MONGODB_URI=mongodb+srv://test:<EMAIL>/?retryWrites=true&w=majority&appName=TEJ-IMS
MONGODB_DB_NAME=IMS

# Development-specific MongoDB settings
MONGODB_DEV_CONNECT_TIMEOUT=5000
MONGODB_DEV_SERVER_SELECTION_TIMEOUT=5000
MONGODB_DEV_SOCKET_TIMEOUT=8000
MONGODB_DEV_MAX_POOL_SIZE=5
MONGODB_DEV_MIN_POOL_SIZE=1

# =============================================================================
# DEVELOPMENT DEBUGGING
# =============================================================================
ENABLE_ENV_LOGGING=true
ENABLE_DB_CONNECTION_DEBUG=true
SKIP_MONGODB_CONNECTION=false
