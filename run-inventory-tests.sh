#!/bin/bash

# Multi-Warehouse Inventory Testing Script
# 
# This script runs the comprehensive test suite for the multi-warehouse
# inventory system, including setup, execution, and reporting.

set -e  # Exit on any error

echo "🚀 Multi-Warehouse Inventory Testing Suite"
echo "=========================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js and npm are installed
print_status "Checking prerequisites..."
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js first."
    exit 1
fi

if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

print_success "Prerequisites check passed"

# Check if MongoDB is running (optional check)
print_status "Checking MongoDB connection..."
if [ -n "$MONGODB_URI" ]; then
    print_status "Using MongoDB URI: $MONGODB_URI"
else
    print_warning "MONGODB_URI not set, using default: mongodb://localhost:27017"
    export MONGODB_URI="mongodb://localhost:27017/inventory_management_test"
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    print_status "Installing dependencies..."
    npm install
    print_success "Dependencies installed"
else
    print_status "Dependencies already installed"
fi

# Install Playwright browsers if needed
print_status "Ensuring Playwright browsers are installed..."
npx playwright install chromium
print_success "Playwright browsers ready"

# Create test results directory
mkdir -p test-results
print_status "Test results directory created"

# Set environment variables for testing
export NODE_ENV=test
export BASE_URL=${BASE_URL:-"http://localhost:3000"}

print_status "Environment configured:"
print_status "  NODE_ENV: $NODE_ENV"
print_status "  BASE_URL: $BASE_URL"
print_status "  MONGODB_URI: $MONGODB_URI"

echo ""
echo "🧪 Running Test Suite"
echo "===================="

# Function to run a specific test file
run_test_file() {
    local test_file=$1
    local test_name=$2
    
    print_status "Running $test_name..."
    
    if npx playwright test "$test_file" --reporter=list; then
        print_success "$test_name completed successfully"
        return 0
    else
        print_error "$test_name failed"
        return 1
    fi
}

# Track test results
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Test execution order (from most critical to least critical)
declare -a TEST_FILES=(
    "tests/multi-warehouse-display.spec.ts:Multi-Warehouse Display Tests"
    "tests/inventory-crud.spec.ts:CRUD Operations Tests"
    "tests/integration-workflow.spec.ts:Integration Workflow Tests"
)

# Run each test file
for test_entry in "${TEST_FILES[@]}"; do
    IFS=':' read -r test_file test_name <<< "$test_entry"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if run_test_file "$test_file" "$test_name"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
        
        # Continue with other tests even if one fails
        print_warning "Continuing with remaining tests..."
    fi
    
    echo ""
done

# Generate comprehensive report
echo "📊 Final Test Results"
echo "===================="
echo "Total Test Suites: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $FAILED_TESTS"

if [ $FAILED_TESTS -eq 0 ]; then
    print_success "All test suites passed! 🎉"
    
    echo ""
    echo "✅ Multi-Warehouse Inventory System Verification Complete"
    echo "========================================================="
    echo "The following functionality has been verified:"
    echo "  ✓ Multi-warehouse inventory display in Part View Modal"
    echo "  ✓ Aggregated totals match warehouse breakdown"
    echo "  ✓ CRUD operations work with new schema"
    echo "  ✓ Stock movements update inventory correctly"
    echo "  ✓ Integration workflows function end-to-end"
    echo "  ✓ Error handling and edge cases"
    echo ""
    
    EXIT_CODE=0
else
    print_error "Some test suites failed. Check the detailed reports for more information."
    EXIT_CODE=1
fi

# Generate detailed HTML report
print_status "Generating comprehensive test report..."
if npx playwright test --reporter=html; then
    print_success "HTML report generated: test-results/html-report/index.html"
else
    print_warning "Could not generate HTML report"
fi

echo ""
echo "📁 Test Artifacts"
echo "================"
echo "Test reports and artifacts are available in:"
echo "  - HTML Report: test-results/html-report/index.html"
echo "  - JSON Report: test-results/results.json"
echo "  - JUnit Report: test-results/results.xml"
echo "  - Screenshots: test-results/ (for failed tests)"
echo "  - Videos: test-results/ (for failed tests)"

echo ""
echo "🔍 Next Steps"
echo "============"
if [ $FAILED_TESTS -eq 0 ]; then
    echo "All tests passed! The multi-warehouse inventory system is working correctly."
    echo ""
    echo "Recommended actions:"
    echo "  1. Review the HTML report for detailed test results"
    echo "  2. Deploy the changes to staging environment"
    echo "  3. Perform user acceptance testing"
    echo "  4. Update documentation with new features"
else
    echo "Some tests failed. Recommended actions:"
    echo "  1. Review the HTML report to identify specific failures"
    echo "  2. Check the console output above for error details"
    echo "  3. Fix the identified issues"
    echo "  4. Re-run the tests to verify fixes"
    echo ""
    echo "Common issues to check:"
    echo "  - Database connectivity and schema"
    echo "  - API endpoint availability"
    echo "  - Frontend component rendering"
    echo "  - Network timeouts or performance issues"
fi

echo ""
echo "=========================================="
echo "Multi-Warehouse Inventory Testing Complete"
echo "=========================================="

exit $EXIT_CODE
