#!/usr/bin/env node

/**
 * Comprehensive CRUD Test Suite for Parts API
 * Tests all Create, Read, Update, Delete operations with the new stockLevels schema
 */

const BASE_URL = 'http://localhost:3000/api/parts';

// Test data for creating a new part
const testPartData = {
  partNumber: `TEST-${Date.now()}`,
  name: 'Automated Test Part',
  businessName: 'Test Business Name',
  description: 'Created by automated test suite',
  technicalSpecs: 'Test specifications',
  isManufactured: true,
  reorderLevel: 20,
  status: 'active',
  inventory: {
    stockLevels: {
      raw: 100,
      hardening: 50,
      grinding: 30,
      finished: 80,
      rejected: 5
    },
    warehouseId: '65f000000000000000000001',
    safetyStockLevel: 15,
    maximumStockLevel: 200,
    averageDailyUsage: 5,
    abcClassification: 'A'
  },
  unitOfMeasure: 'pcs',
  costPrice: 25.99
};

// Test data for updating the part
const updateData = {
  name: 'Updated Test Part',
  businessName: 'Updated Business Name',
  description: 'Updated by automated test suite',
  inventory: {
    stockLevels: {
      raw: 120,
      hardening: 60,
      grinding: 40,
      finished: 90,
      rejected: 3
    },
    safetyStockLevel: 20,
    maximumStockLevel: 250
  },
  costPrice: 29.99
};

let createdPartId = null;

/**
 * Make HTTP request with error handling
 */
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });

    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${data.error || 'Request failed'}`);
    }

    return { success: true, data, status: response.status };
  } catch (error) {
    return { success: false, error: error.message, status: error.status || 500 };
  }
}

/**
 * Test 1: CREATE - Create a new part
 */
async function testCreate() {
  console.log('\n🧪 Test 1: CREATE Operation');
  console.log('Creating new part with stockLevels...');
  
  const result = await makeRequest(BASE_URL, {
    method: 'POST',
    body: JSON.stringify(testPartData)
  });

  if (!result.success) {
    console.error('❌ CREATE failed:', result.error);
    return false;
  }

  const part = result.data.data;
  createdPartId = part._id;
  
  console.log('✅ CREATE successful');
  console.log(`   Part ID: ${part._id}`);
  console.log(`   Part Number: ${part.partNumber}`);
  console.log(`   Business Name: ${part.businessName}`);
  console.log(`   Stock Levels:`, part.inventory.stockLevels);
  console.log(`   Virtual currentStock: ${part.inventory.currentStock}`);
  
  // Validate the created data
  const isValid = 
    part.partNumber === testPartData.partNumber &&
    part.name === testPartData.name &&
    part.businessName === testPartData.businessName &&
    part.inventory.stockLevels.raw === testPartData.inventory.stockLevels.raw &&
    part.inventory.stockLevels.finished === testPartData.inventory.stockLevels.finished;
    
  if (!isValid) {
    console.error('❌ CREATE validation failed - data mismatch');
    return false;
  }
  
  console.log('✅ CREATE validation passed');
  return true;
}

/**
 * Test 2: READ - Get the created part
 */
async function testRead() {
  console.log('\n🧪 Test 2: READ Operation');
  console.log(`Reading part ${createdPartId}...`);
  
  const result = await makeRequest(`${BASE_URL}/${createdPartId}`);

  if (!result.success) {
    console.error('❌ READ failed:', result.error);
    return false;
  }

  const part = result.data.data;
  
  console.log('✅ READ successful');
  console.log(`   Name: ${part.name}`);
  console.log(`   Business Name: ${part.businessName}`);
  console.log(`   Stock Levels:`, part.inventory.stockLevels);
  console.log(`   Warehouse ID: ${part.inventory.warehouseId}`);
  
  // Validate the read data matches what we created
  const isValid = 
    part._id === createdPartId &&
    part.partNumber === testPartData.partNumber &&
    part.inventory.stockLevels.finished === testPartData.inventory.stockLevels.finished;
    
  if (!isValid) {
    console.error('❌ READ validation failed - data mismatch');
    return false;
  }
  
  console.log('✅ READ validation passed');
  return true;
}

/**
 * Test 3: UPDATE - Update the part
 */
async function testUpdate() {
  console.log('\n🧪 Test 3: UPDATE Operation');
  console.log(`Updating part ${createdPartId}...`);
  
  const result = await makeRequest(`${BASE_URL}/${createdPartId}`, {
    method: 'PUT',
    body: JSON.stringify(updateData)
  });

  if (!result.success) {
    console.error('❌ UPDATE failed:', result.error);
    return false;
  }

  const part = result.data.data;
  
  console.log('✅ UPDATE successful');
  console.log(`   Updated Name: ${part.name}`);
  console.log(`   Updated Business Name: ${part.businessName}`);
  console.log(`   Updated Stock Levels:`, part.inventory.stockLevels);
  console.log(`   Updated Cost Price: ${part.costPrice}`);
  
  // Validate the updated data
  const isValid = 
    part.name === updateData.name &&
    part.businessName === updateData.businessName &&
    part.inventory.stockLevels.raw === updateData.inventory.stockLevels.raw &&
    part.inventory.stockLevels.finished === updateData.inventory.stockLevels.finished &&
    part.costPrice === updateData.costPrice;
    
  if (!isValid) {
    console.error('❌ UPDATE validation failed - data mismatch');
    console.log('Expected:', updateData);
    console.log('Actual:', {
      name: part.name,
      businessName: part.businessName,
      stockLevels: part.inventory.stockLevels,
      costPrice: part.costPrice
    });
    return false;
  }
  
  console.log('✅ UPDATE validation passed');
  return true;
}

/**
 * Test 4: DELETE - Delete the part
 */
async function testDelete() {
  console.log('\n🧪 Test 4: DELETE Operation');
  console.log(`Deleting part ${createdPartId}...`);
  
  const result = await makeRequest(`${BASE_URL}/${createdPartId}`, {
    method: 'DELETE'
  });

  if (!result.success) {
    console.error('❌ DELETE failed:', result.error);
    return false;
  }
  
  console.log('✅ DELETE successful');
  
  // Verify the part is actually deleted by trying to read it
  console.log('Verifying part is deleted...');
  const readResult = await makeRequest(`${BASE_URL}/${createdPartId}`);
  
  if (readResult.success) {
    console.error('❌ DELETE validation failed - part still exists');
    return false;
  }
  
  console.log('✅ DELETE validation passed - part no longer exists');
  return true;
}

/**
 * Test 5: Immutable Fields Validation
 */
async function testImmutableFields() {
  console.log('\n🧪 Test 5: Immutable Fields Validation');
  console.log('Testing that immutable fields are rejected...');
  
  // Create a test part first
  const createResult = await makeRequest(BASE_URL, {
    method: 'POST',
    body: JSON.stringify({
      ...testPartData,
      partNumber: `IMMUTABLE-TEST-${Date.now()}`
    })
  });

  if (!createResult.success) {
    console.error('❌ Failed to create test part for immutable field test');
    return false;
  }

  const testPartId = createResult.data.data._id;
  
  // Try to update with immutable fields
  const updateResult = await makeRequest(`${BASE_URL}/${testPartId}`, {
    method: 'PUT',
    body: JSON.stringify({
      name: 'Updated Name',
      partNumber: 'SHOULD-BE-REJECTED', // This should be rejected
      _id: 'SHOULD-BE-REJECTED' // This should be rejected
    })
  });

  // Clean up the test part
  await makeRequest(`${BASE_URL}/${testPartId}`, { method: 'DELETE' });

  if (updateResult.success) {
    console.error('❌ Immutable fields validation failed - update should have been rejected');
    return false;
  }
  
  console.log('✅ Immutable fields correctly rejected');
  console.log(`   Error: ${updateResult.error}`);
  return true;
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting CRUD Test Suite for Parts API');
  console.log('Testing new stockLevels schema and immutable field validation');
  
  const tests = [
    { name: 'CREATE', fn: testCreate },
    { name: 'READ', fn: testRead },
    { name: 'UPDATE', fn: testUpdate },
    { name: 'DELETE', fn: testDelete },
    { name: 'IMMUTABLE_FIELDS', fn: testImmutableFields }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.error(`❌ Test ${test.name} threw an error:`, error.message);
      failed++;
    }
  }
  
  console.log('\n📊 Test Results Summary');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! CRUD operations are working correctly.');
    process.exit(0);
  } else {
    console.log('\n💥 Some tests failed. Please check the errors above.');
    process.exit(1);
  }
}

// Run the tests
runAllTests().catch(error => {
  console.error('💥 Test suite failed with error:', error.message);
  process.exit(1);
});
