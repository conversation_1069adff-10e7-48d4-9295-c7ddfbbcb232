# Trend_IMS API Documentation

## Table of Contents

1. [Introduction](#introduction)
2. [Resources](#resources)
   - [2.1. Assemblies](#21-assemblies)
   - [2.2. Parts](#22-parts)
   - [2.3. Products](#23-products)
   - [2.4. Inventory](#24-inventory)
   - [2.5. Inventory Transactions](#25-inventory-transactions)
   - [2.6. Categories](#26-categories)
   - [2.7. Suppliers](#27-suppliers)
   - [2.8. Warehouses](#28-warehouses)
   - [2.9. Work Orders](#29-work-orders)
   - [2.10. Batches](#210-batches)
   - [2.11. Purchase Orders](#211-purchase-orders)
   - [2.12. Analytics](#212-analytics)
   - [2.13. Reports](#213-reports)
   - [2.14. Users](#214-users)
   - [2.15. Settings](#215-settings)
   - [2.16. Utilities & Monitoring](#216-utilities--monitoring)
   - [2.17. Logistics](#217-logistics)
3. [Data Models](#data-models)

---

## 1. Introduction

This document provides comprehensive documentation for the Trend_IMS (Inventory Management System) API. The API is built using Next.js App Router with file-based routing and provides RESTful endpoints for managing inventory, assemblies, parts, products, and related business operations.

### Base URL

All API endpoints are relative to the application's base URL:

```
/api
```

### Authentication

**⚠️ Current Status: Authentication Not Implemented**

The application is currently under development and does not have an authentication system implemented. All endpoints are currently accessible without authentication headers.

**Future Implementation**: When authentication is implemented, all endpoints will require a valid Bearer token:

```http
Authorization: Bearer <your-token-here>
```

### Standard Response Format

All API endpoints return responses in a consistent JSON format:

#### Success Response Format

```json
{
  "success": true,
  "data": <response_data>,
  "message": "Optional success message",
  "metadata": {
    "duration": 150,
    "pagination": {
      "totalCount": 100,
      "currentPage": 1,
      "totalPages": 5,
      "limit": 20,
      "hasNextPage": true,
      "hasPreviousPage": false
    }
  }
}
```

#### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": [
      {
        "field": "fieldName",
        "message": "Field-specific error message"
      }
    ]
  }
}
```

#### Legacy Response Format

**Used by**: Purchase Orders, Users, Settings, Logistics, Work Orders

Some endpoints use an alternative format during the transition to the standard format:

```json
{
  "data": <response_data>,
  "pagination": {
    "totalCount": 100,
    "currentPage": 1,
    "totalPages": 5,
    "limit": 20,
    "hasNextPage": true,
    "hasPreviousPage": false
  },
  "error": null,
  "meta": {
    "duration": 150
  }
}
```

**Note**: The codebase is transitioning to the standard format. Legacy format endpoints will be updated in future releases.

### Standard HTTP Status Codes

- `200 OK` - Successful GET, PUT requests
- `201 Created` - Successful POST requests (resource creation)
- `400 Bad Request` - Invalid request data or parameters
- `404 Not Found` - Resource not found
- `422 Unprocessable Entity` - Validation errors
- `500 Internal Server Error` - Server-side errors

### Pagination

The API supports both offset-based and cursor-based pagination:

#### Offset-Based Pagination (Default)

**Query Parameters:**
- `page` (integer, default: 1) - Page number
- `limit` (integer, default: 20, max: 100-500 depending on endpoint) - Items per page

**Response:**
```json
{
  "pagination": {
    "totalCount": 100,
    "currentPage": 1,
    "totalPages": 5,
    "limit": 20,
    "hasNextPage": true,
    "hasPreviousPage": false
  }
}
```

#### Cursor-Based Pagination (For Large Datasets)

**Query Parameters:**
- `cursor` (string) - Cursor for pagination
- `limit` (integer, default: 20) - Items per page

**Response:**
```json
{
  "pagination": {
    "limit": 20,
    "hasNextPage": true,
    "hasPreviousPage": false,
    "nextCursor": "eyJfaWQiOiI2NjQwZjBhMGExYjJjM2Q0ZTVmNmEwMGEifQ==",
    "previousCursor": null
  }
}
```

### Common Query Parameters

Most list endpoints support the following query parameters:

- **Pagination**: `page`, `limit`, `cursor`
- **Sorting**: `sortBy` (field name), `sortOrder` (`asc` or `desc`)
- **Filtering**: `status`, `category`, `search` (varies by endpoint)
- **Search**: `q` or `search` - Full-text search across relevant fields

### Performance Considerations

- **Rate Limiting**:
  - List endpoints: 100-500 items per request (varies by endpoint)
  - Search endpoints: 100 items per request maximum
  - Analytics endpoints: 1000 items per request maximum
  - No global rate limiting currently implemented
- **Response Times**:
  - Target: <500ms for most endpoints
  - Analytics: <1000ms for complex aggregations
  - Search: <300ms for simple queries
- **Caching**: Some endpoints implement caching for improved performance
- **Compression**: Large responses are automatically compressed
- **N+1 Query Detection**: Monitoring in place to detect and prevent N+1 query issues

### Error Handling

All endpoints implement comprehensive error handling with:
- Detailed error messages
- Field-specific validation errors
- Performance timing information
- Proper HTTP status codes
- Consistent error response format

---

## 2. Resources

### 2.1. Assemblies

The Assemblies API manages Bills of Materials (BOMs) for manufactured items. Assemblies define the parts and sub-assemblies required to build products.

#### Base Endpoint
```
/api/assemblies
```

#### Available Endpoints

##### GET /api/assemblies
Retrieve a paginated list of assemblies with optional filtering and sorting.

**Query Parameters:**
- `page` (integer, default: 1) - Page number for pagination
- `limit` (integer, default: 20, max: 500) - Number of items per page
- `sortBy` (string) - Field to sort by (e.g., 'name', 'createdAt', 'assemblyCode')
- `sortOrder` (string) - Sort direction: 'asc' or 'desc'
- `status` (string) - Filter by status: 'active', 'pending_review', 'obsolete' (canonical values)
- `isTopLevel` (boolean) - Filter by top-level assemblies: 'true' or 'false'
- `includeParts` (boolean) - Include populated parts data: 'true' or 'false'

**Example Request:**
```http
GET /api/assemblies?page=1&limit=20&status=active&sortBy=name&sortOrder=asc&includeParts=true
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "681f796bd6a21248b8ec7640",
      "assemblyCode": "ASM-TA-100",
      "name": "Tamping Arm Assembly",
      "description": "Complete tamping arm assembly for railway maintenance",
      "status": "active",
      "version": 1,
      "isTopLevel": false,
      "parentId": null,
      "productId": null,
      "partsRequired": [
        {
          "partId": "6640f1000000000000000001",
          "quantityRequired": 2,
          "unitOfMeasure": "pcs"
        }
      ],
      "manufacturingInstructions": "Follow SOP-ASM-100 for assembly.",
      "estimatedBuildTime": "1.5 hours",
      "createdAt": "2025-05-10T16:06:03.385Z",
      "updatedAt": "2025-05-11T10:00:00.000Z"
    }
  ],
  "metadata": {
    "pagination": {
      "totalCount": 45,
      "currentPage": 1,
      "totalPages": 3,
      "limit": 20,
      "hasNextPage": true,
      "hasPreviousPage": false
    },
    "duration": 150
  }
}
```

##### POST /api/assemblies
Create a new assembly.

**Request Body:**
```json
{
  "assemblyCode": "ASM-NEW-001",
  "name": "New Assembly",
  "description": "Description of the new assembly",
  "version": "1.0",
  "status": "design",
  "parentId": null,
  "partsRequired": [
    {
      "partId": "6640f1000000000000000001",
      "quantityRequired": 2,
      "unitOfMeasure": "pcs"
    }
  ],
  "manufacturingLeadTime": "2 hours",
  "notes": "Assembly notes"
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "_id": "681f796bd6a21248b8ec7641",
    "assemblyCode": "ASM-NEW-001",
    "name": "New Assembly",
    "status": "design",
    "version": "1.0",
    "createdAt": "2025-06-16T10:00:00.000Z",
    "updatedAt": "2025-06-16T10:00:00.000Z"
  },
  "message": "Assembly created successfully"
}
```

##### GET /api/assemblies/[id]
Retrieve a specific assembly by ID or assembly code.

**Path Parameters:**
- `id` (string) - Assembly ObjectId or assemblyCode

**Query Parameters:**
- `includeParts` (boolean) - Include populated parts data: 'true' or 'false'

**Example Request:**
```http
GET /api/assemblies/ASM-TA-100?includeParts=true
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "_id": "681f796bd6a21248b8ec7640",
    "assemblyCode": "ASM-TA-100",
    "name": "Tamping Arm Assembly",
    "description": "Complete tamping arm assembly",
    "status": "active",
    "version": 1,
    "partsRequired": [
      {
        "partId": {
          "_id": "6640f1000000000000000001",
          "partNumber": "DL23.108",
          "name": "Spacer Ring",
          "currentStock": 169
        },
        "quantityRequired": 2,
        "unitOfMeasure": "pcs"
      }
    ],
    "createdAt": "2025-05-10T16:06:03.385Z",
    "updatedAt": "2025-05-11T10:00:00.000Z"
  },
  "metadata": {
    "duration": 85
  }
}
```

##### PUT /api/assemblies/[id]
Update an existing assembly by ID or assembly code.

**Path Parameters:**
- `id` (string) - Assembly ObjectId or assemblyCode

**Request Body:**
```json
{
  "name": "Updated Assembly Name",
  "description": "Updated description",
  "status": "active",
  "partsRequired": [
    {
      "partId": "6640f1000000000000000001",
      "quantityRequired": 3,
      "unitOfMeasure": "pcs"
    }
  ],
  "manufacturingLeadTime": "2.5 hours",
  "notes": "Updated notes"
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "_id": "681f796bd6a21248b8ec7640",
    "assemblyCode": "ASM-TA-100",
    "name": "Updated Assembly Name",
    "status": "active",
    "updatedAt": "2025-06-16T10:30:00.000Z"
  },
  "message": "Assembly updated successfully"
}
```

##### DELETE /api/assemblies/[id]
Delete an assembly by ID or assembly code.

**Path Parameters:**
- `id` (string) - Assembly ObjectId or assemblyCode

**Example Request:**
```http
DELETE /api/assemblies/ASM-TA-100
```

**Example Response:**
```json
{
  "success": true,
  "data": null,
  "message": "Assembly deleted successfully",
  "metadata": {
    "duration": 120
  }
}
```

##### POST /api/assemblies/[id]/duplicate
Create a duplicate of an existing assembly with a new assembly code.

**Path Parameters:**
- `id` (string) - Source assembly ObjectId or assemblyCode

**Request Body:**
```json
{
  "newAssemblyCode": "ASM-TA-100-COPY",
  "newName": "Tamping Arm Assembly (Copy)",
  "newDescription": "Duplicated assembly for testing"
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "_id": "681f796bd6a21248b8ec7642",
    "assemblyCode": "ASM-TA-100-COPY",
    "name": "Tamping Arm Assembly (Copy)",
    "status": "pending_review",
    "version": 1,
    "partsRequired": [
      {
        "partId": "6640f1000000000000000001",
        "quantityRequired": 2,
        "unitOfMeasure": "pcs"
      }
    ]
  },
  "message": "Assembly duplicated successfully"
}
```

##### GET /api/assemblies/search
Search assemblies with advanced filtering options.

**Query Parameters:**
- `q` (string) - Search query (searches across name, description, assemblyCode)
- `page` (integer, default: 1) - Page number
- `limit` (integer, default: 20, max: 100) - Items per page
- `sortField` (string, default: 'name') - Field to sort by
- `sortOrder` (string, default: 'asc') - Sort direction: 'asc' or 'desc'
- `status` (string) - Filter by status
- `type` (string) - Filter by type: 'standard', 'custom', 'kit'
- `assembly_id` (string) - Exact assembly ID match
- `createdAfter` (string) - ISO date string for created after filter
- `createdBefore` (string) - ISO date string for created before filter
- `hasComponents` (boolean) - Filter assemblies with/without components

**Example Request:**
```http
GET /api/assemblies/search?q=tamping&status=active&sortField=name&sortOrder=asc&limit=10
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "681f796bd6a21248b8ec7640",
      "assemblyCode": "ASM-TA-100",
      "name": "Tamping Arm Assembly",
      "status": "active",
      "createdAt": "2025-05-10T16:06:03.385Z"
    }
  ],
  "message": "Found 1 assemblies matching criteria",
  "metadata": {
    "duration": 95,
    "pagination": {
      "totalCount": 1,
      "currentPage": 1,
      "totalPages": 1,
      "limit": 10,
      "hasNextPage": false,
      "hasPreviousPage": false
    }
  }
}
```

##### GET /api/assemblies/status
Get assemblies filtered by status.

**Query Parameters:**
- `status` (string, required) - Status to filter by: 'active', 'pending_review', 'obsolete'
- `page` (integer, default: 1) - Page number
- `limit` (integer, default: 20, max: 100) - Items per page
- `sortField` (string, default: 'name') - Field to sort by
- `sortOrder` (string, default: 'asc') - Sort direction

**Example Request:**
```http
GET /api/assemblies/status?status=active&page=1&limit=20
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "681f796bd6a21248b8ec7640",
      "assemblyCode": "ASM-TA-100",
      "name": "Tamping Arm Assembly",
      "status": "active",
      "version": 1
    }
  ],
  "message": "Found 15 assemblies with status: active",
  "metadata": {
    "duration": 75,
    "pagination": {
      "totalCount": 15,
      "currentPage": 1,
      "totalPages": 1,
      "limit": 20,
      "hasNextPage": false,
      "hasPreviousPage": false
    }
  }
}
```

##### GET /api/assemblies/type
Get assemblies filtered by type.

**Query Parameters:**
- `type` (string, required) - Type to filter by: 'standard', 'custom', 'kit'
- `page` (integer, default: 1) - Page number
- `limit` (integer, default: 20, max: 100) - Items per page
- `sortField` (string, default: 'name') - Field to sort by
- `sortOrder` (string, default: 'asc') - Sort direction

**Example Request:**
```http
GET /api/assemblies/type?type=standard&page=1&limit=20
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "681f796bd6a21248b8ec7640",
      "assemblyCode": "ASM-TA-100",
      "name": "Tamping Arm Assembly",
      "type": "standard",
      "status": "active"
    }
  ],
  "message": "Found 8 assemblies with type: standard",
  "metadata": {
    "duration": 65,
    "pagination": {
      "totalCount": 8,
      "currentPage": 1,
      "totalPages": 1,
      "limit": 20,
      "hasNextPage": false,
      "hasPreviousPage": false
    }
  }
}
```

#### Data Validation

**Assembly Status Values:**
- `active` - Assembly is active and ready for production
- `pending_review` - Assembly is pending review/approval
- `obsolete` - Assembly is obsolete and no longer used

**Required Fields for Creation:**
- `assemblyCode` (string) - Unique business code
- `name` (string) - Assembly name
- `version` (string/number) - Version identifier
- `status` (string) - Assembly status

**Optional Fields:**
- `description` (string) - Assembly description
- `parentId` (string) - Parent assembly ObjectId
- `partsRequired` (array) - Array of required parts
- `manufacturingLeadTime` (string) - Estimated manufacturing time
- `notes` (string) - Additional notes

#### Error Responses

**400 Bad Request - Validation Error:**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed",
    "details": [
      {
        "field": "assemblyCode",
        "message": "Assembly code is required"
      }
    ]
  }
}
```

**404 Not Found:**
```json
{
  "success": false,
  "error": {
    "code": "NOT_FOUND",
    "message": "Assembly with ID ASM-TA-999 not found"
  }
}
```

**500 Internal Server Error:**
```json
{
  "success": false,
  "error": {
    "code": "INTERNAL_SERVER_ERROR",
    "message": "An unexpected error occurred"
  }
}
```

---

### 2.2. Parts

The Parts API manages individual parts and components used in assemblies and products. Parts include both manufactured and purchased items with comprehensive inventory tracking.

#### Base Endpoint
```
/api/parts
```

#### Available Endpoints

##### GET /api/parts
Retrieve a paginated list of parts with optional filtering and sorting.

**Query Parameters:**
- `page` (integer, default: 1) - Page number for pagination
- `limit` (integer, default: 20, max: 100) - Number of items per page
- `sortField` (string, default: 'updatedAt') - Field to sort by
- `sortOrder` (string, default: 'desc') - Sort direction: 'asc' or 'desc'
- `status` (string) - Filter by status: 'active', 'inactive', 'obsolete'
- `isManufactured` (boolean) - Filter by manufactured status: 'true' or 'false'
- `minStock` (integer) - Minimum current stock filter
- `maxStock` (integer) - Maximum current stock filter

**Example Request:**
```http
GET /api/parts?page=1&limit=20&status=active&sortField=name&sortOrder=asc&minStock=10
```

**Example Response:**
```json
{
  "data": [
    {
      "_id": "6640f0a0a1b2c3d4e5f6a00a",
      "partNumber": "DL23.108",
      "name": "Spacer Ring",
      "description": "High-precision spacer ring for tamping units",
      "technicalSpecs": "Material: Steel, Tolerance: ±0.1mm",
      "isManufactured": true,
      "reorderLevel": 20,
      "status": "active",
      "inventory": {
        "currentStock": 169,
        "warehouseId": "65f000000000000000000001",
        "safetyStockLevel": 10,
        "maximumStockLevel": 60,
        "averageDailyUsage": 0.5,
        "abcClassification": "A",
        "lastStockUpdate": "2025-02-28T21:20:28.000Z"
      },
      "supplierId": "681f796ad6a21248b8ec7600",
      "unitOfMeasure": "pcs",
      "costPrice": 8.9,
      "categoryId": "65f000020000000000000001",
      "createdAt": "2025-02-28T21:20:28.000Z",
      "updatedAt": "2025-02-28T21:20:28.000Z"
    }
  ],
  "pagination": {
    "totalCount": 245,
    "currentPage": 1,
    "totalPages": 13,
    "limit": 20,
    "hasNextPage": true,
    "hasPreviousPage": false
  },
  "error": null,
  "meta": {
    "duration": 125,
    "count": 20,
    "totalCount": 245,
    "page": 1,
    "limit": 20
  }
}
```

##### POST /api/parts
Create a new part.

**Request Body:**
```json
{
  "partNumber": "DL24.109",
  "name": "Bearing Assembly",
  "description": "High-load bearing assembly",
  "technicalSpecs": "Load capacity: 5000N",
  "isManufactured": false,
  "reorderLevel": 15,
  "status": "active",
  "inventory": {
    "currentStock": 50,
    "warehouseId": "65f000000000000000000001",
    "safetyStockLevel": 5,
    "maximumStockLevel": 100,
    "averageDailyUsage": 2.0,
    "abcClassification": "B"
  },
  "supplierId": "681f796ad6a21248b8ec7600",
  "unitOfMeasure": "pcs",
  "costPrice": 125.50,
  "categoryId": "65f000020000000000000001"
}
```

**Example Response:**
```json
{
  "data": {
    "_id": "6640f0a0a1b2c3d4e5f6a00b",
    "partNumber": "DL24.109",
    "name": "Bearing Assembly",
    "status": "active",
    "isManufactured": false,
    "inventory": {
      "currentStock": 50,
      "warehouseId": "65f000000000000000000001",
      "safetyStockLevel": 5,
      "maximumStockLevel": 100,
      "averageDailyUsage": 2.0,
      "abcClassification": "B",
      "lastStockUpdate": "2025-06-16T10:00:00.000Z"
    },
    "createdAt": "2025-06-16T10:00:00.000Z",
    "updatedAt": "2025-06-16T10:00:00.000Z"
  },
  "error": null,
  "meta": {
    "duration": 180
  }
}
```

##### GET /api/parts/[id]
Retrieve a specific part by ID or part number.

**Path Parameters:**
- `id` (string) - Part ObjectId or partNumber

**Example Request:**
```http
GET /api/parts/DL23.108
```

**Example Response:**
```json
{
  "data": {
    "_id": "6640f0a0a1b2c3d4e5f6a00a",
    "partNumber": "DL23.108",
    "name": "Spacer Ring",
    "description": "High-precision spacer ring",
    "technicalSpecs": "Material: Steel, Tolerance: ±0.1mm",
    "isManufactured": true,
    "reorderLevel": 20,
    "status": "active",
    "inventory": {
      "currentStock": 169,
      "warehouseId": "65f000000000000000000001",
      "safetyStockLevel": 10,
      "maximumStockLevel": 60,
      "averageDailyUsage": 0.5,
      "abcClassification": "A",
      "lastStockUpdate": "2025-02-28T21:20:28.000Z"
    },
    "supplierId": "681f796ad6a21248b8ec7600",
    "unitOfMeasure": "pcs",
    "costPrice": 8.9,
    "categoryId": "65f000020000000000000001",
    "createdAt": "2025-02-28T21:20:28.000Z",
    "updatedAt": "2025-02-28T21:20:28.000Z"
  },
  "error": null,
  "meta": {
    "duration": 95
  }
}
```

##### PUT /api/parts/[id]
Update an existing part by ID.

**Path Parameters:**
- `id` (string) - Part ObjectId

**Request Body:**
```json
{
  "name": "Updated Spacer Ring",
  "description": "Updated high-precision spacer ring",
  "status": "active",
  "reorderLevel": 25,
  "inventory": {
    "safetyStockLevel": 15,
    "maximumStockLevel": 80,
    "averageDailyUsage": 0.7,
    "abcClassification": "A"
  },
  "costPrice": 9.5
}
```

**Example Response:**
```json
{
  "data": {
    "_id": "6640f0a0a1b2c3d4e5f6a00a",
    "partNumber": "DL23.108",
    "name": "Updated Spacer Ring",
    "status": "active",
    "reorderLevel": 25,
    "costPrice": 9.5,
    "updatedAt": "2025-06-16T10:30:00.000Z"
  },
  "error": null,
  "meta": {
    "duration": 140
  }
}
```

##### DELETE /api/parts/[id]
Delete a part by ID.

**Path Parameters:**
- `id` (string) - Part ObjectId

**Example Request:**
```http
DELETE /api/parts/6640f0a0a1b2c3d4e5f6a00a
```

**Example Response:**
```json
{
  "success": true,
  "data": null,
  "message": "Part deleted successfully",
  "meta": {
    "duration": 110
  }
}
```

##### GET /api/parts/search
Search parts with advanced filtering options.

**Query Parameters:**
- `search` (string) - Search query (searches across name, description, partNumber, technicalSpecs)
- `page` (integer, default: 1) - Page number
- `limit` (integer, default: 20, max: 50) - Items per page
- `sortField` (string, default: 'updatedAt') - Field to sort by
- `sortOrder` (string, default: 'desc') - Sort direction: 'asc' or 'desc'
- `status` (string) - Filter by status: 'active', 'inactive', 'obsolete'
- `is_manufactured` (boolean) - Filter by manufactured status
- `minStock` (integer) - Minimum current stock filter
- `maxStock` (integer) - Maximum current stock filter

**Example Request:**
```http
GET /api/parts/search?search=spacer&status=active&sortField=name&sortOrder=asc&limit=10
```

**Example Response:**
```json
{
  "data": [
    {
      "_id": "6640f0a0a1b2c3d4e5f6a00a",
      "partNumber": "DL23.108",
      "name": "Spacer Ring",
      "status": "active",
      "inventory": {
        "currentStock": 169,
        "safetyStockLevel": 10
      },
      "createdAt": "2025-02-28T21:20:28.000Z"
    }
  ],
  "pagination": {
    "totalCount": 3,
    "currentPage": 1,
    "totalPages": 1,
    "limit": 10,
    "hasNextPage": false,
    "hasPreviousPage": false
  },
  "error": null,
  "meta": {
    "duration": 85,
    "query": "spacer",
    "filter": {
      "status": "active"
    },
    "sort": {
      "name": 1
    },
    "count": 3,
    "totalCount": 3
  }
}
```

##### POST /api/parts/seed
Seed the parts collection with sample data (development/testing endpoint).

**Request Body:**
```json
{}
```

**Example Response:**
```json
{
  "data": {
    "_id": "6640f0a0a1b2c3d4e5f6a00a",
    "partNumber": "DL23.108",
    "name": "Spacer Ring",
    "status": "active",
    "inventory": {
      "currentStock": 169,
      "warehouseId": "65f000000000000000000001",
      "safetyStockLevel": 10,
      "maximumStockLevel": 60,
      "averageDailyUsage": 0.5,
      "abcClassification": "A"
    },
    "createdAt": "2025-02-28T21:20:28.000Z",
    "updatedAt": "2025-02-28T21:20:28.000Z"
  },
  "message": "Part created successfully",
  "success": true
}
```

#### Data Validation

**Part Status Values:**
- `active` - Part is active and available for use
- `inactive` - Part is temporarily inactive
- `obsolete` - Part is obsolete and no longer used

**Required Fields for Creation:**
- `partNumber` (string) - Unique business identifier
- `name` (string) - Part name
- `isManufactured` (boolean) - Whether part is manufactured in-house
- `status` (string) - Part status
- `inventory` (object) - Inventory information
  - `currentStock` (number) - Current stock quantity
  - `warehouseId` (string) - Warehouse ObjectId
  - `safetyStockLevel` (number) - Safety stock level
  - `maximumStockLevel` (number) - Maximum stock level
  - `averageDailyUsage` (number) - Average daily usage
  - `abcClassification` (string) - ABC classification (A, B, C)
- `unitOfMeasure` (string) - Unit of measure
- `costPrice` (number) - Cost price per unit

**Optional Fields:**
- `description` (string) - Part description
- `technicalSpecs` (string) - Technical specifications
- `reorderLevel` (number) - Reorder point
- `supplierId` (string) - Supplier ObjectId
- `categoryId` (string) - Category ObjectId

#### Inventory Structure

The inventory object is embedded within each part and contains:

```json
{
  "currentStock": 169,
  "warehouseId": "65f000000000000000000001",
  "safetyStockLevel": 10,
  "maximumStockLevel": 60,
  "averageDailyUsage": 0.5,
  "abcClassification": "A",
  "lastStockUpdate": "2025-02-28T21:20:28.000Z"
}
```

#### Error Responses

**400 Bad Request - Validation Error:**
```json
{
  "data": null,
  "error": "Invalid part data provided.",
  "errorDetails": {
    "fieldErrors": {
      "partNumber": ["Part number is required."],
      "inventory.currentStock": ["Current stock cannot be negative."]
    }
  },
  "meta": {
    "duration": 45
  }
}
```

**404 Not Found:**
```json
{
  "data": null,
  "error": "Part with ID DL99.999 not found",
  "meta": {
    "duration": 65
  }
}
```

**409 Conflict - Duplicate Part Number:**
```json
{
  "data": null,
  "error": "Part with number DL23.108 already exists.",
  "meta": {
    "duration": 75
  }
}
```
```
```

---

### 2.3. Products

The Products API manages finished goods that can be sold. Products can be manufactured from assemblies or be direct resales of parts.

#### Base Endpoint
```
/api/products
```

#### Available Endpoints

##### GET /api/products
Retrieve a paginated list of products with optional filtering and sorting.

**Query Parameters:**
- `page` (integer, default: 1) - Page number for pagination
- `limit` (integer, default: 20, max: 500) - Number of items per page
- `sortField` (string, default: 'name') - Field to sort by
- `sortOrder` (string, default: 'asc') - Sort direction: 'asc' or 'desc'
- `status` (string) - Filter by status: 'active', 'discontinued', 'in_development'
- `categoryId` (string) - Filter by category ObjectId
- `query` (string) - Search query (if provided, uses search functionality)

**Example Request:**
```http
GET /api/products?page=1&limit=20&status=active&sortField=name&sortOrder=asc
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "6628c5f0a1b2c3d4e5f6a7b2",
      "productCode": "PROD-TAMP-UNIT-DUO",
      "name": "Complete Tamping Unit (Duomatic Model)",
      "description": "Fully assembled tamping unit for Duomatic machines.",
      "categoryId": "65f000020000000000000005",
      "status": "active",
      "sellingPrice": 62000.00,
      "assemblyId": null,
      "partId": null,
      "createdAt": "2024-02-11T11:00:00.000Z",
      "updatedAt": "2024-02-11T11:00:00.000Z"
    }
  ],
  "message": "Products retrieved successfully",
  "metadata": {
    "duration": 125,
    "pagination": {
      "totalCount": 45,
      "currentPage": 1,
      "totalPages": 3,
      "limit": 20,
      "hasNextPage": true,
      "hasPreviousPage": false
    }
  }
}
```

##### POST /api/products
Create a new product.

**Request Body:**
```json
{
  "productCode": "PROD-NEW-001",
  "name": "New Product",
  "description": "Description of the new product",
  "categoryId": "65f000020000000000000005",
  "status": "active",
  "sellingPrice": 1500.00,
  "assemblyId": "681f796bd6a21248b8ec7640",
  "partId": null
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "_id": "6628c5f0a1b2c3d4e5f6a7b3",
    "productCode": "PROD-NEW-001",
    "name": "New Product",
    "description": "Description of the new product",
    "categoryId": "65f000020000000000000005",
    "status": "active",
    "sellingPrice": 1500.00,
    "assemblyId": "681f796bd6a21248b8ec7640",
    "partId": null,
    "createdAt": "2025-06-16T10:00:00.000Z",
    "updatedAt": "2025-06-16T10:00:00.000Z"
  },
  "message": "Product created successfully",
  "metadata": {
    "duration": 180
  }
}
```

##### GET /api/products/[id]
Retrieve a specific product by ID or product code.

**Path Parameters:**
- `id` (string) - Product ObjectId or productCode

**Example Request:**
```http
GET /api/products/PROD-TAMP-UNIT-DUO
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "_id": "6628c5f0a1b2c3d4e5f6a7b2",
    "productCode": "PROD-TAMP-UNIT-DUO",
    "name": "Complete Tamping Unit (Duomatic Model)",
    "description": "Fully assembled tamping unit for Duomatic machines.",
    "categoryId": "65f000020000000000000005",
    "status": "active",
    "sellingPrice": 62000.00,
    "assemblyId": null,
    "partId": null,
    "createdAt": "2024-02-11T11:00:00.000Z",
    "updatedAt": "2024-02-11T11:00:00.000Z"
  },
  "message": "Product retrieved successfully"
}
```

##### PUT /api/products/[id]
Update an existing product by ID or product code.

**Path Parameters:**
- `id` (string) - Product ObjectId or productCode

**Request Body:**
```json
{
  "name": "Updated Product Name",
  "description": "Updated product description",
  "status": "active",
  "sellingPrice": 65000.00,
  "assemblyId": "681f796bd6a21248b8ec7640"
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "_id": "6628c5f0a1b2c3d4e5f6a7b2",
    "productCode": "PROD-TAMP-UNIT-DUO",
    "name": "Updated Product Name",
    "description": "Updated product description",
    "status": "active",
    "sellingPrice": 65000.00,
    "updatedAt": "2025-06-16T10:30:00.000Z"
  },
  "message": "Product updated successfully"
}
```

##### DELETE /api/products/[id]
Delete a product by ID or product code.

**Path Parameters:**
- `id` (string) - Product ObjectId or productCode

**Example Request:**
```http
DELETE /api/products/PROD-TAMP-UNIT-DUO
```

**Example Response:**
```json
{
  "success": true,
  "data": null,
  "message": "Product deleted successfully"
}
```

##### GET /api/products/search
Search products with advanced filtering options.

**Query Parameters:**
- `query` (string) - Search query (searches across name, description, productCode)
- `page` (integer, default: 1) - Page number
- `limit` (integer, default: 20, max: 500) - Items per page
- `sortField` (string, default: 'name') - Field to sort by
- `sortOrder` (string, default: 'asc') - Sort direction: 'asc' or 'desc'
- `categoryId` (string) - Filter by category ObjectId
- `status` (string) - Filter by status
- `minPrice` (number) - Minimum selling price filter
- `maxPrice` (number) - Maximum selling price filter

**Example Request:**
```http
GET /api/products/search?query=tamping&status=active&minPrice=1000&maxPrice=100000&sortField=sellingPrice&sortOrder=asc
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "6628c5f0a1b2c3d4e5f6a7b2",
      "productCode": "PROD-TAMP-UNIT-DUO",
      "name": "Complete Tamping Unit (Duomatic Model)",
      "status": "active",
      "sellingPrice": 62000.00,
      "createdAt": "2024-02-11T11:00:00.000Z"
    }
  ],
  "message": "Products search completed successfully",
  "metadata": {
    "pagination": {
      "totalCount": 1,
      "currentPage": 1,
      "totalPages": 1,
      "limit": 20,
      "hasNextPage": false,
      "hasPreviousPage": false
    },
    "query": "tamping",
    "duration": 95
  }
}
```

##### GET /api/products/status
Get products filtered by status.

**Query Parameters:**
- `status` (string, required) - Status to filter by: 'active', 'discontinued', 'in_development'
- `page` (integer, default: 1) - Page number
- `limit` (integer, default: 20, max: 500) - Items per page
- `sortField` (string, default: 'name') - Field to sort by
- `sortOrder` (string, default: 'asc') - Sort direction

**Example Request:**
```http
GET /api/products/status?status=active&page=1&limit=20
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "6628c5f0a1b2c3d4e5f6a7b2",
      "productCode": "PROD-TAMP-UNIT-DUO",
      "name": "Complete Tamping Unit (Duomatic Model)",
      "status": "active",
      "sellingPrice": 62000.00
    }
  ],
  "message": "Products by status retrieved successfully",
  "metadata": {
    "pagination": {
      "totalCount": 25,
      "currentPage": 1,
      "totalPages": 2,
      "limit": 20,
      "hasNextPage": true,
      "hasPreviousPage": false
    },
    "status": "active",
    "duration": 75
  }
}
```

##### GET /api/products/category
Get products filtered by category.

**Query Parameters:**
- `categoryId` (string, required) - Category ObjectId to filter by
- `page` (integer, default: 1) - Page number
- `limit` (integer, default: 20, max: 500) - Items per page
- `sortField` (string, default: 'name') - Field to sort by
- `sortOrder` (string, default: 'asc') - Sort direction

**Example Request:**
```http
GET /api/products/category?categoryId=65f000020000000000000005&page=1&limit=20
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "6628c5f0a1b2c3d4e5f6a7b2",
      "productCode": "PROD-TAMP-UNIT-DUO",
      "name": "Complete Tamping Unit (Duomatic Model)",
      "categoryId": "65f000020000000000000005",
      "status": "active",
      "sellingPrice": 62000.00
    }
  ],
  "message": "Products by category retrieved successfully",
  "metadata": {
    "pagination": {
      "totalCount": 8,
      "currentPage": 1,
      "totalPages": 1,
      "limit": 20,
      "hasNextPage": false,
      "hasPreviousPage": false
    },
    "categoryId": "65f000020000000000000005",
    "duration": 65
  }
}
```

#### Data Validation

**Product Status Values:**
- `active` - Product is active and available for sale
- `discontinued` - Product is discontinued and no longer sold
- `in_development` - Product is under development

**Required Fields for Creation:**
- `productCode` (string) - Unique business code
- `name` (string) - Product name
- `description` (string) - Product description
- `categoryId` (string) - Category ObjectId
- `sellingPrice` (number) - Selling price (must be >= 0)

**Optional Fields:**
- `status` (string, default: 'active') - Product status
- `assemblyId` (string) - Assembly ObjectId if product is manufactured from assembly
- `partId` (string) - Part ObjectId if product is direct resale of part

#### Product Relationships

Products can be related to:
- **Assemblies**: If the product is manufactured from an assembly (assemblyId)
- **Parts**: If the product is a direct resale of a part (partId)
- **Categories**: All products must belong to a category (categoryId)

#### Error Responses

**400 Bad Request - Missing Required Fields:**
```json
{
  "success": false,
  "error": {
    "code": "INVALID_PARAMETER",
    "message": "Missing required fields: productCode, name",
    "details": [
      {
        "field": "productCode",
        "message": "This field is required"
      },
      {
        "field": "name",
        "message": "This field is required"
      }
    ]
  }
}
```

**400 Bad Request - Invalid Status:**
```json
{
  "success": false,
  "error": {
    "code": "INVALID_PARAMETER",
    "message": "Invalid status value. Must be one of: active, discontinued, in_development"
  }
}
```

**404 Not Found:**
```json
{
  "success": false,
  "error": {
    "code": "NOT_FOUND",
    "message": "Product with ID PROD-999 not found"
  }
}
```
```

---

### 2.4. Inventory

The Inventory API manages stock levels across warehouses for parts, assemblies, and products.

#### Base Endpoint
```
/api/inventory
```

#### Available Endpoints

##### GET /api/inventory
Retrieve inventory records with pagination, filtering, and search capabilities.

**Query Parameters:**
- `page` (integer, default: 1) - Page number
- `limit` (integer, default: 20, max: 100) - Items per page
- `sortField` (string, default: 'last_stock_update') - Field to sort by
- `sortOrder` (string, default: 'desc') - Sort direction
- `itemType` (string) - Filter by item type: 'Part', 'Assembly', 'Product'
- `warehouseId` (string) - Filter by warehouse ObjectId
- `lowStockOnly` (boolean) - Show only low stock items: 'true'
- `query` (string) - Search query across item names

**Example Request:**
```http
GET /api/inventory?page=1&limit=20&itemType=Part&lowStockOnly=true
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "inventory_record_id",
      "item_id": "6640f0a0a1b2c3d4e5f6a00a",
      "item_type": "Part",
      "warehouse_id": "65f000000000000000000001",
      "quantity_on_hand": 15,
      "quantity_allocated": 5,
      "quantity_available": 10,
      "location_in_warehouse": "A-01-B",
      "reorder_level": 20,
      "safety_stock_level": 10,
      "last_stock_update": "2025-06-16T10:00:00.000Z"
    }
  ],
  "message": "Low stock inventory retrieved successfully",
  "metadata": {
    "duration": 125,
    "pagination": {
      "totalCount": 8,
      "currentPage": 1,
      "totalPages": 1,
      "limit": 20,
      "hasNextPage": false,
      "hasPreviousPage": false
    }
  }
}
```

##### POST /api/inventory
Create a new inventory record (for Assembly/Product items only).

**Request Body:**
```json
{
  "item_id": "681f796bd6a21248b8ec7640",
  "item_type": "Assembly",
  "warehouse_id": "65f000000000000000000001",
  "quantity_on_hand": 50,
  "location_in_warehouse": "B-02-A",
  "reorder_level": 10,
  "safety_stock_level": 5,
  "notes": "Initial stock for new assembly"
}
```

##### GET /api/inventory/low-stock
Get inventory items below reorder level.

**Query Parameters:**
- `page` (integer, default: 1) - Page number for pagination
- `limit` (integer, default: 20, max: 100) - Number of items per page
- `warehouse_id` (string) - Filter by specific warehouse

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "6640f0a0a1b2c3d4e5f6a00b",
      "item_id": "6640f0a0a1b2c3d4e5f6a00a",
      "item_type": "Part",
      "warehouse_id": "65f000000000000000000001",
      "quantity_on_hand": 5,
      "reorder_level": 20,
      "safety_stock_level": 10,
      "status": "low_stock"
    }
  ],
  "message": "Low stock items retrieved successfully",
  "metadata": {
    "duration": 95,
    "pagination": {
      "totalCount": 15,
      "currentPage": 1,
      "totalPages": 1,
      "limit": 20
    }
  }
}
```

##### POST /api/inventory/allocate
Allocate inventory for orders or production.

**Required Fields:**
- `id` (string) - Inventory record ObjectId
- `allocation_quantity` (number) - Quantity to allocate

**Optional Fields:**
- `reference_number` (string) - Reference order/work order number
- `notes` (string) - Additional notes

**Request Body:**
```json
{
  "id": "6640f0a0a1b2c3d4e5f6a00b",
  "allocation_quantity": 10,
  "reference_number": "WO-2025-001",
  "notes": "Allocated for assembly production"
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "_id": "6640f0a0a1b2c3d4e5f6a00b",
    "quantity_on_hand": 50,
    "quantity_allocated": 20,
    "quantity_available": 30,
    "last_allocation": {
      "quantity": 10,
      "reference": "WO-2025-001",
      "timestamp": "2025-06-16T10:00:00.000Z"
    }
  },
  "message": "Inventory allocated successfully",
  "metadata": {
    "duration": 120,
    "previous_allocated": 10,
    "new_allocated": 20,
    "allocation_amount": 10
  }
}
```

##### POST /api/inventory/adjust
Adjust inventory quantities for cycle counts and corrections.

**Required Fields:**
- `id` (string) - Inventory record ObjectId
- `adjustment_quantity` (number) - Quantity adjustment (positive or negative)
- `reason` (string) - Reason for adjustment

**Request Body:**
```json
{
  "id": "6640f0a0a1b2c3d4e5f6a00b",
  "adjustment_quantity": -5,
  "reason": "Cycle count correction",
  "reference_number": "CC-2025-001",
  "notes": "Found damaged items during cycle count"
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "_id": "6640f0a0a1b2c3d4e5f6a00b",
    "quantity_on_hand": 45,
    "quantity_allocated": 10,
    "quantity_available": 35,
    "last_adjustment": {
      "quantity": -5,
      "reason": "Cycle count correction",
      "reference": "CC-2025-001",
      "timestamp": "2025-06-16T10:00:00.000Z"
    }
  },
  "message": "Inventory quantity adjusted successfully",
  "metadata": {
    "duration": 110,
    "previous_quantity": 50,
    "new_quantity": 45,
    "adjustment": -5
  }
}
```

##### POST /api/inventory/update-stock
Update stock levels for receiving and shipping.

**Required Fields:**
- `itemId` (string) - Item ObjectId
- `itemType` (string) - Item type: 'Part', 'Assembly', 'Product'
- `warehouseId` (string) - Warehouse ObjectId
- `quantityChange` (number) - Quantity change (positive for receiving, negative for shipping)
- `userId` (string) - User performing the update

**Optional Fields:**
- `reference_number` (string) - Reference document number
- `notes` (string) - Additional notes

**Request Body:**
```json
{
  "itemId": "6640f0a0a1b2c3d4e5f6a00a",
  "itemType": "Part",
  "warehouseId": "65f000000000000000000001",
  "quantityChange": 25,
  "userId": "65f000010000000000000003",
  "reference_number": "PO-2025-001",
  "notes": "Received shipment from supplier"
}
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "_id": "6640f0a0a1b2c3d4e5f6a00b",
    "item_id": "6640f0a0a1b2c3d4e5f6a00a",
    "item_type": "Part",
    "warehouse_id": "65f000000000000000000001",
    "quantity_on_hand": 75,
    "quantity_allocated": 10,
    "quantity_available": 65,
    "last_stock_update": "2025-06-16T10:00:00.000Z"
  },
  "message": "Stock levels updated successfully",
  "metadata": {
    "duration": 130,
    "previous_quantity": 50,
    "new_quantity": 75,
    "quantity_change": 25
  }
}

##### GET /api/inventory/warehouse/[warehouseId]
Get inventory for a specific warehouse.

**Path Parameters:**
- `warehouseId` (string) - Warehouse ObjectId

**Query Parameters:**
- `page` (integer, default: 1) - Page number for pagination
- `limit` (integer, default: 20, max: 100) - Number of items per page
- `item_type` (string) - Filter by item type: 'Part', 'Assembly', 'Product'

##### GET /api/inventory/item/[itemType]/[itemId]
Get inventory records for a specific item across all warehouses.

**Path Parameters:**
- `itemType` (string) - Item type: 'Part', 'Assembly', 'Product'
- `itemId` (string) - Item ObjectId

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "6640f0a0a1b2c3d4e5f6a00b",
      "item_id": "6640f0a0a1b2c3d4e5f6a00a",
      "item_type": "Part",
      "warehouse_id": "65f000000000000000000001",
      "warehouse_name": "Main Warehouse",
      "quantity_on_hand": 50,
      "quantity_allocated": 10,
      "quantity_available": 40,
      "location_in_warehouse": "A-1-B-3"
    },
    {
      "_id": "6640f0a0a1b2c3d4e5f6a00c",
      "item_id": "6640f0a0a1b2c3d4e5f6a00a",
      "item_type": "Part",
      "warehouse_id": "65f000000000000000000002",
      "warehouse_name": "Secondary Warehouse",
      "quantity_on_hand": 25,
      "quantity_allocated": 5,
      "quantity_available": 20,
      "location_in_warehouse": "B-2-A-1"
    }
  ],
  "message": "Item inventory across warehouses retrieved successfully",
  "metadata": {
    "duration": 85,
    "total_quantity": 75,
    "total_allocated": 15,
    "total_available": 60,
    "warehouse_count": 2
  }
}
```

##### GET /api/inventory/test
Development/testing endpoint for inventory operations.

##### GET /api/inventory/test-stock
Development/testing endpoint for stock level testing.

---

### 2.5. Inventory Transactions

The Inventory Transactions API tracks all inventory movements and changes.

#### Base Endpoint
```
/api/inventory-transactions
```

#### Available Endpoints

##### GET /api/inventory-transactions
Retrieve inventory transaction history with filtering.

**Query Parameters:**
- `page` (integer, default: 1) - Page number
- `limit` (integer, default: 10, max: 500) - Items per page
- `partId` (string) - Filter by part ObjectId
- `itemType` (string) - Filter by item type: 'Part', 'Assembly', 'Product'
- `warehouseId` (string) - Filter by warehouse ObjectId
- `transactionType` (string) - Filter by transaction type
- `startDate` (string) - Filter transactions after date (ISO format)
- `endDate` (string) - Filter transactions before date (ISO format)

**Example Request:**
```http
GET /api/inventory-transactions?partId=6640f0a0a1b2c3d4e5f6a00a&transactionType=stock_in_purchase&limit=20
```

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "_id": "6809e615f6450d12271ec00f",
      "itemId": "6640f0a0a1b2c3d4e5f6a001",
      "itemType": "Part",
      "warehouseId": "65f000000000000000000001",
      "transactionType": "stock_in_purchase",
      "quantity": 50,
      "previousStock": 52,
      "newStock": 102,
      "transactionDate": "2025-03-01T10:00:00.000Z",
      "referenceNumber": "PO-001",
      "referenceType": "PurchaseOrder",
      "userId": "65f000010000000000000003",
      "notes": "Received from Reliable Bearings Co.",
      "createdAt": "2025-03-01T10:05:00.000Z"
    }
  ],
  "pagination": {
    "total": 156,
    "page": 1,
    "limit": 20,
    "totalPages": 8
  },
  "meta": {
    "duration": 95
  }
}
```

##### POST /api/inventory-transactions
Create a new inventory transaction and update stock levels.

**Request Body:**
```json
{
  "partId": "6640f0a0a1b2c3d4e5f6a00a",
  "itemType": "Part",
  "warehouseId": "65f000000000000000000001",
  "transactionType": "stock_in_purchase",
  "quantity": 25,
  "referenceNumber": "PO-002",
  "referenceType": "PurchaseOrder",
  "userId": "65f000010000000000000003",
  "notes": "Stock replenishment"
}
```

**Transaction Types:**
- `stock_in_purchase` - Stock received from purchase
- `stock_out_production` - Stock consumed in production
- `adjustment_cycle_count` - Inventory adjustment
- `stock_in_production` - Stock produced
- `transfer_out` - Stock transferred out
- `transfer_in` - Stock transferred in
- `sales_shipment` - Stock shipped for sales

---

### 2.6. Categories

The Categories API manages product and part categorization with hierarchical support.

#### Base Endpoint
```
/api/categories
```

#### Available Endpoints

##### GET /api/categories
Retrieve all categories with pagination and filtering.

**Query Parameters:**
- `page` (integer, default: 1) - Page number
- `limit` (integer, default: 20, max: 500) - Items per page
- `sortField` (string, default: 'name') - Field to sort by
- `sortOrder` (string, default: 'asc') - Sort direction

**Example Response:**
```json
{
  "data": [
    {
      "_id": "65f000020000000000000001",
      "name": "Railway Components",
      "description": "Components for railway maintenance equipment",
      "parentCategory": null,
      "createdAt": "2024-01-15T10:00:00.000Z"
    }
  ],
  "pagination": {
    "totalCount": 15,
    "currentPage": 1,
    "totalPages": 1,
    "limit": 20
  }
}
```

##### POST /api/categories
Create a new category.

##### GET /api/categories/[id]
Get a specific category by ID.

##### PUT /api/categories/[id]
Update a category.

##### DELETE /api/categories/[id]
Delete a category.

##### GET /api/categories/child
Get child categories or top-level categories.

---

### 2.7. Suppliers

The Suppliers API manages supplier information and relationships.

#### Base Endpoint
```
/api/suppliers
```

#### Available Endpoints

##### GET /api/suppliers
Retrieve all suppliers with pagination and filtering.

**Query Parameters:**
- `page` (integer, default: 1) - Page number
- `limit` (integer, default: 20, max: 500) - Items per page
- `sortField` (string, default: 'name') - Field to sort by
- `sortOrder` (string, default: 'asc') - Sort direction

**Example Response:**
```json
{
  "data": [
    {
      "_id": "681f796ad6a21248b8ec7600",
      "supplier_id": "SUP-001",
      "name": "Reliable Bearings Co.",
      "contact_info": {
        "email": "<EMAIL>",
        "phone": "******-0123",
        "address": "123 Industrial Ave, Manufacturing City, MC 12345"
      },
      "status": "active",
      "createdAt": "2024-01-10T09:00:00.000Z"
    }
  ],
  "pagination": {
    "totalCount": 25,
    "currentPage": 1,
    "totalPages": 2,
    "limit": 20
  }
}
```

##### POST /api/suppliers
Create a new supplier.

##### GET /api/suppliers/[id]
Get a specific supplier by supplier_id.

##### PUT /api/suppliers/[id]
Update a supplier.

##### DELETE /api/suppliers/[id]
Delete a supplier.

---

### 2.8. Warehouses

The Warehouses API manages warehouse locations and information.

#### Base Endpoint
```
/api/warehouses
```

#### Available Endpoints

##### GET /api/warehouses
Retrieve all warehouses with pagination and filtering.

**Query Parameters:**
- `page` (integer, default: 1) - Page number
- `limit` (integer, default: 20, max: 500) - Items per page
- `sortField` (string, default: 'name') - Field to sort by
- `sortOrder` (string, default: 'asc') - Sort direction

**Example Response:**
```json
{
  "data": [
    {
      "_id": "65f000000000000000000001",
      "location_id": "WH-MAIN-001",
      "name": "Main Warehouse",
      "address": "456 Storage Blvd, Warehouse District, WD 67890",
      "capacity": 10000,
      "current_utilization": 7500,
      "status": "active",
      "createdAt": "2024-01-05T08:00:00.000Z"
    }
  ],
  "pagination": {
    "totalCount": 5,
    "currentPage": 1,
    "totalPages": 1,
    "limit": 20
  }
}
```

##### POST /api/warehouses
Create a new warehouse.

##### GET /api/warehouses/[id]
Get a specific warehouse by location_id.

##### PUT /api/warehouses/[id]
Update a warehouse.

##### DELETE /api/warehouses/[id]
Delete a warehouse.

---

### 2.9. Work Orders

The Work Orders API manages production orders for manufacturing assemblies and products.

#### Base Endpoint
```
/api/work-orders
```

#### Available Endpoints

##### GET /api/work-orders
Retrieve all work orders with pagination and filtering.

**Query Parameters:**
- `page` (integer, default: 1) - Page number
- `limit` (integer, default: 20, max: 500) - Items per page
- `sortField` (string, default: 'createdAt') - Field to sort by
- `sortOrder` (string, default: 'desc') - Sort direction
- `status` (string) - Filter by status: 'pending', 'in_progress', 'completed', 'cancelled'
- `priority` (string) - Filter by priority: 'low', 'medium', 'high', 'urgent'

**Example Response:**
```json
{
  "data": [
    {
      "_id": "6809e615f6450d12271ec001",
      "woNumber": "WO-2025-001",
      "assemblyId": "681f796bd6a21248b8ec7640",
      "quantity": 10,
      "status": "in_progress",
      "priority": "high",
      "startDate": "2025-06-15T08:00:00.000Z",
      "dueDate": "2025-06-20T17:00:00.000Z",
      "createdAt": "2025-06-14T10:00:00.000Z"
    }
  ],
  "pagination": {
    "totalCount": 45,
    "currentPage": 1,
    "totalPages": 3,
    "limit": 20
  }
}
```

##### POST /api/work-orders
Create a new work order.

**Required Fields:**
- `quantity` (number) - Quantity to produce
- Additional fields based on work order schema

##### GET /api/work-orders/[id]
Get a specific work order by woNumber.

##### PUT /api/work-orders/[id]
Update a work order.

##### DELETE /api/work-orders/[id]
Delete a work order.

##### GET /api/work-orders/[id]/batches
Get batches associated with a specific work order.

---

### 2.10. Batches

The Batches API manages production batches for tracking manufacturing progress.

#### Base Endpoint
```
/api/batches
```

#### Available Endpoints

##### GET /api/batches
Retrieve all batches with pagination and filtering.

**Query Parameters:**
- `page` (integer, default: 1) - Page number
- `limit` (integer, default: 20, max: 500) - Items per page
- `sortField` (string, default: 'createdAt') - Field to sort by
- `sortOrder` (string, default: 'desc') - Sort direction
- `status` (string) - Filter by batch status
- `workOrderId` (string) - Filter by work order
- `startDate` (string) - Filter batches started after date
- `endDate` (string) - Filter batches started before date

**Example Response:**
```json
{
  "data": [
    {
      "_id": "6809e615f6450d12271ec002",
      "batchNumber": "BATCH-2025-001",
      "workOrderId": "WO-2025-001",
      "quantity": 5,
      "status": "in_progress",
      "startDate": "2025-06-15T09:00:00.000Z",
      "estimatedCompletionDate": "2025-06-18T17:00:00.000Z",
      "createdAt": "2025-06-15T08:30:00.000Z"
    }
  ],
  "pagination": {
    "totalCount": 28,
    "currentPage": 1,
    "totalPages": 2,
    "limit": 20
  }
}
```

##### POST /api/batches
Create a new batch.

**Required Fields:**
- `workOrderId` (string) - Associated work order
- `quantity` (number) - Batch quantity
- `userId` (string) - User creating the batch

##### GET /api/batches/[id]
Get a specific batch by ID.

##### PUT /api/batches/[id]
Update a batch.

##### DELETE /api/batches/[id]
Delete a batch.

##### GET /api/batches/[id]/inventory
Get inventory summary for a specific batch.

---

### 2.11. Purchase Orders

The Purchase Orders API manages procurement orders for purchasing parts and materials from suppliers.

#### Base Endpoint
```
/api/purchase-orders
```

#### Available Endpoints

##### GET /api/purchase-orders
Retrieve all purchase orders with pagination, sorting, and filtering.

**Query Parameters:**
- `page` (integer, default: 1) - Page number for pagination
- `limit` (integer, default: 20, max: 500) - Number of items per page
- `sortField` (string, default: 'order_date') - Field to sort by
- `sortOrder` (string, default: 'desc') - Sort direction: 'asc' or 'desc'
- `status` (string) - Filter by status: 'draft', 'pending_approval', 'approved', 'ordered', 'partially_received', 'fully_received', 'cancelled', 'closed'
- `supplier_id` (string) - Filter by supplier ObjectId

**Example Request:**
```http
GET /api/purchase-orders?page=1&limit=20&status=approved&sortField=order_date&sortOrder=desc
```

**Example Response (Legacy Format):**
```json
{
  "data": [
    {
      "_id": "6809e615f6450d12271ec003",
      "po_number": "PO-2025-001",
      "supplier_id": "681f796ad6a21248b8ec7600",
      "order_date": "2025-06-15T08:00:00.000Z",
      "expected_delivery_date": "2025-06-25T17:00:00.000Z",
      "actual_delivery_date": null,
      "items": [
        {
          "item_id": "6640f0a0a1b2c3d4e5f6a00a",
          "item_type": "Part",
          "description": "High-precision spacer ring",
          "quantity_ordered": 50,
          "quantity_received": 0,
          "unit_price": 8.90,
          "total_price": 445.00
        }
      ],
      "sub_total": 445.00,
      "tax_amount": 44.50,
      "shipping_cost": 25.00,
      "total_amount": 514.50,
      "status": "approved",
      "createdAt": "2025-06-14T10:00:00.000Z"
    }
  ],
  "pagination": {
    "totalCount": 28,
    "currentPage": 1,
    "totalPages": 2,
    "limit": 20,
    "hasNextPage": true,
    "hasPreviousPage": false
  },
  "error": null,
  "meta": {
    "duration": 145
  }
}
```

##### POST /api/purchase-orders
Create a new purchase order.

**Required Fields:**
- `supplier_id` (string) - Supplier ObjectId
- `items` (array) - Array of items to order
- `createdBy` (string) - User creating the order

**Request Body:**
```json
{
  "po_number": "PO-2025-002",
  "supplier_id": "681f796ad6a21248b8ec7600",
  "order_date": "2025-06-16T08:00:00.000Z",
  "expected_delivery_date": "2025-06-26T17:00:00.000Z",
  "items": [
    {
      "item_id": "6640f0a0a1b2c3d4e5f6a00a",
      "item_type": "Part",
      "description": "Bearing assembly",
      "quantity_ordered": 25,
      "unit_price": 125.50,
      "total_price": 3137.50
    }
  ],
  "sub_total": 3137.50,
  "tax_amount": 313.75,
  "shipping_cost": 50.00,
  "total_amount": 3501.25,
  "status": "draft",
  "createdBy": "65f000010000000000000003"
}
```

##### GET /api/purchase-orders/[id]
Get a specific purchase order by PO number or ObjectId.

**Path Parameters:**
- `id` (string) - Purchase Order ObjectId or po_number

##### PUT /api/purchase-orders/[id]
Update a purchase order by PO number or ObjectId.

##### DELETE /api/purchase-orders/[id]
Delete a purchase order by PO number or ObjectId.

---

### 2.12. Analytics

The Analytics API provides data aggregation and business intelligence endpoints for inventory management insights.

#### Base Endpoint
```
/api/analytics
```

#### Available Endpoints

##### GET /api/analytics
Get analytics data with flexible options for dashboard or specific types.

**Query Parameters:**
- `type` (string) - Specific analytics type: 'inventory-trends', 'stock-levels', 'category-distribution', 'inventory-value'
- `dashboard` (boolean) - Get combined dashboard data: 'true'
- `timeRange` (string, default: 'month') - Time range for trends: 'week', 'month', 'quarter', 'year'
- `category` (string) - Filter by category ObjectId
- `startDate` (string) - Start date for date range (ISO format)
- `endDate` (string) - End date for date range (ISO format)

**Example Request (Dashboard):**
```http
GET /api/analytics?dashboard=true&timeRange=month
```

**Example Response (Dashboard):**
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalValue": 125000.50,
      "totalItems": 1250,
      "lowStockItems": 15,
      "categories": 8
    },
    "stockLevels": [
      {
        "week": "2025-W24",
        "totalStock": 5000,
        "lowStockCount": 12,
        "averageValue": 25000
      }
    ],
    "inventoryTrends": [
      {
        "date": "2025-06-01",
        "totalValue": 120000,
        "itemCount": 1200,
        "movement": "increase"
      }
    ],
    "categoryDistribution": [
      {
        "name": "Railway Components",
        "value": 450
      },
      {
        "name": "Bearings",
        "value": 320
      }
    ],
    "inventoryValue": [
      {
        "category": "Railway Components",
        "totalValue": 75000.25,
        "itemCount": 450
      }
    ],
    "generatedAt": "2025-06-16T10:00:00.000Z"
  },
  "message": "Dashboard analytics data retrieved successfully",
  "metadata": {
    "duration": 285
  }
}
```

**Example Request (Specific Type):**
```http
GET /api/analytics?type=inventory-trends&timeRange=month&category=65f000020000000000000001
```

##### GET /api/analytics/category-distribution
Get distribution of items by category.

**Query Parameters:**
- `startDate` (string) - Start date filter (ISO format)
- `endDate` (string) - End date filter (ISO format)

**Example Response:**
```json
{
  "success": true,
  "data": {
    "distribution": [
      {
        "name": "Railway Components",
        "value": 450
      },
      {
        "name": "Bearings",
        "value": 320
      },
      {
        "name": "Hydraulic Parts",
        "value": 280
      }
    ],
    "generatedAt": "2025-06-16T10:00:00.000Z"
  },
  "message": "Category distribution data generated successfully",
  "metadata": {
    "duration": 95
  }
}
```

##### GET /api/analytics/inventory-trends
Get inventory trend analysis over time.

**Query Parameters:**
- `timeRange` (string, default: 'month') - Time range: 'week', 'month', 'quarter', 'year'
- `category` (string) - Filter by category ObjectId

##### GET /api/analytics/inventory-value
Get total inventory value calculations by category.

##### GET /api/analytics/stock-levels
Get stock level analytics across warehouses.

**Query Parameters:**
- `category` (string) - Filter by category ObjectId

---

### 2.13. Reports

The Reports API generates various business reports for inventory, production, procurement, and assembly management.

#### Base Endpoint
```
/api/reports
```

#### Available Endpoints

##### GET /api/reports
Get available report types and their descriptions.

**Example Response:**
```json
{
  "success": true,
  "data": [
    {
      "type": "inventory",
      "name": "Inventory Report",
      "description": "Comprehensive inventory status and stock levels",
      "parameters": ["lowStock", "category", "startDate", "endDate"]
    },
    {
      "type": "production",
      "name": "Production Report",
      "description": "Work orders and production status",
      "parameters": ["status", "startDate", "endDate"]
    },
    {
      "type": "procurement",
      "name": "Procurement Report",
      "description": "Purchase orders and supplier performance",
      "parameters": ["status", "startDate", "endDate"]
    },
    {
      "type": "assembly",
      "name": "Assembly Report",
      "description": "Assembly BOMs and component analysis",
      "parameters": ["product"]
    }
  ],
  "message": "Available report types retrieved successfully",
  "metadata": {
    "duration": 45
  }
}
```

##### GET /api/reports/[type]
Generate a specific report by type.

**Path Parameters:**
- `type` (string) - Report type: 'inventory', 'production', 'procurement', 'assembly'

**Common Query Parameters:**
- `startDate` (string) - Start date for date range (ISO format)
- `endDate` (string) - End date for date range (ISO format)

**Example Request (Inventory Report):**
```http
GET /api/reports/inventory?lowStock=true&category=65f000020000000000000001&startDate=2025-06-01&endDate=2025-06-16
```

**Example Response (Inventory Report):**
```json
{
  "success": true,
  "data": {
    "reportType": "inventory",
    "generatedAt": "2025-06-16T10:00:00.000Z",
    "dateRange": {
      "startDate": "2025-06-01T00:00:00.000Z",
      "endDate": "2025-06-16T23:59:59.999Z"
    },
    "summary": {
      "totalItems": 245,
      "lowStockItems": 15,
      "totalValue": 125000.50,
      "averageStockLevel": 85.2
    },
    "items": [
      {
        "_id": "6640f0a0a1b2c3d4e5f6a00a",
        "partNumber": "DL23.108",
        "name": "Spacer Ring",
        "currentStock": 15,
        "reorderLevel": 20,
        "safetyStockLevel": 10,
        "status": "low_stock",
        "value": 133.50,
        "category": "Railway Components"
      }
    ],
    "filters": {
      "lowStockOnly": true,
      "categoryFilter": "65f000020000000000000001"
    }
  },
  "message": "inventory report generated successfully",
  "metadata": {
    "duration": 185
  }
}
```

##### GET /api/reports/inventory
Generate inventory reports with stock levels and valuation.

**Query Parameters:**
- `lowStock` (boolean) - Show only low stock items: 'true'
- `category` (string) - Filter by category ObjectId
- `startDate` (string) - Start date for date range
- `endDate` (string) - End date for date range

##### GET /api/reports/production
Generate production reports with work orders and batch status.

**Query Parameters:**
- `status` (string) - Filter by production status
- `startDate` (string) - Start date for date range
- `endDate` (string) - End date for date range

##### GET /api/reports/procurement
Generate procurement reports with purchase order analysis.

**Query Parameters:**
- `status` (string) - Filter by procurement status
- `startDate` (string) - Start date for date range
- `endDate` (string) - End date for date range

##### GET /api/reports/assembly
Generate assembly reports with BOM analysis.

**Query Parameters:**
- `product` (string) - Filter by product ObjectId

---

### 2.14. Users

The Users API manages user accounts, roles, and authentication for the system.

#### Base Endpoint
```
/api/users
```

#### Available Endpoints

##### GET /api/users
Retrieve all users with pagination, sorting, and filtering.

**Query Parameters:**
- `page` (integer, default: 1) - Page number for pagination
- `limit` (integer, default: 20, max: 100) - Number of items per page
- `sortField` (string, default: 'username') - Field to sort by
- `sortOrder` (string, default: 'asc') - Sort direction: 'asc' or 'desc'
- `role` (string) - Filter by user role
- `status` (string) - Filter by user status: 'active', 'inactive'

**Example Request:**
```http
GET /api/users?page=1&limit=20&role=admin&sortField=username&sortOrder=asc
```

**Example Response (Legacy Format):**
```json
{
  "data": [
    {
      "_id": "65f000010000000000000003",
      "username": "admin_user",
      "email": "<EMAIL>",
      "firstName": "Admin",
      "lastName": "User",
      "role": "admin",
      "status": "active",
      "lastLogin": "2025-06-15T14:30:00.000Z",
      "createdAt": "2025-01-15T10:00:00.000Z",
      "updatedAt": "2025-06-15T14:30:00.000Z"
    }
  ],
  "pagination": {
    "totalCount": 15,
    "currentPage": 1,
    "totalPages": 1,
    "limit": 20,
    "hasNextPage": false,
    "hasPreviousPage": false
  },
  "error": null,
  "meta": {
    "duration": 95
  }
}
```

##### POST /api/users
Create a new user account.

**Required Fields:**
- `username` (string) - Unique username
- `email` (string) - User email address
- `firstName` (string) - User's first name
- `lastName` (string) - User's last name
- `role` (string) - User role: 'admin', 'manager', 'operator', 'viewer'

**Request Body:**
```json
{
  "username": "new_user",
  "email": "<EMAIL>",
  "firstName": "New",
  "lastName": "User",
  "role": "operator",
  "status": "active",
  "password": "securePassword123"
}
```

**Example Response:**
```json
{
  "data": {
    "_id": "65f000010000000000000004",
    "username": "new_user",
    "email": "<EMAIL>",
    "firstName": "New",
    "lastName": "User",
    "role": "operator",
    "status": "active",
    "createdAt": "2025-06-16T10:00:00.000Z",
    "updatedAt": "2025-06-16T10:00:00.000Z"
  },
  "error": null,
  "meta": {
    "duration": 180
  }
}
```

##### GET /api/users/[username]
Get a specific user by username.

**Path Parameters:**
- `username` (string) - Unique username

**Example Response:**
```json
{
  "data": {
    "_id": "65f000010000000000000003",
    "username": "admin_user",
    "email": "<EMAIL>",
    "firstName": "Admin",
    "lastName": "User",
    "role": "admin",
    "status": "active",
    "lastLogin": "2025-06-15T14:30:00.000Z",
    "permissions": ["read", "write", "delete", "admin"],
    "createdAt": "2025-01-15T10:00:00.000Z"
  },
  "error": null,
  "meta": {
    "duration": 65
  }
}
```

##### PUT /api/users/[username]
Update user information by username.

**Path Parameters:**
- `username` (string) - Unique username

**Request Body:**
```json
{
  "firstName": "Updated",
  "lastName": "Name",
  "email": "<EMAIL>",
  "role": "manager",
  "status": "active"
}
```

##### DELETE /api/users/[username]
Delete a user account by username.

**Path Parameters:**
- `username` (string) - Unique username

**Example Response:**
```json
{
  "data": {
    "deleted": true,
    "username": "user_to_delete"
  },
  "error": null,
  "meta": {
    "duration": 120
  }
}
```

#### User Roles and Permissions

**Available Roles:**
- `admin` - Full system access and user management
- `manager` - Inventory and production management
- `operator` - Day-to-day operations and data entry
- `viewer` - Read-only access to reports and data

---

### 2.15. Settings

The Settings API manages application configuration and system preferences.

#### Base Endpoint
```
/api/settings
```

#### Available Endpoints

##### GET /api/settings
Get all application settings with pagination and filtering.

**Query Parameters:**
- `page` (integer, default: 1) - Page number for pagination
- `limit` (integer, default: 20, max: 100) - Number of items per page
- `sortField` (string, default: 'key') - Field to sort by
- `sortOrder` (string, default: 'asc') - Sort direction: 'asc' or 'desc'
- `category` (string) - Filter by setting category

**Example Request:**
```http
GET /api/settings?page=1&limit=20&category=inventory&sortField=key&sortOrder=asc
```

**Example Response (Legacy Format):**
```json
{
  "data": [
    {
      "_id": "65f000020000000000000010",
      "key": "inventory.default_reorder_level",
      "value": 20,
      "description": "Default reorder level for new parts",
      "category": "inventory",
      "dataType": "number",
      "isEditable": true,
      "createdAt": "2025-01-15T10:00:00.000Z",
      "updatedAt": "2025-06-15T14:30:00.000Z"
    },
    {
      "_id": "65f000020000000000000011",
      "key": "inventory.low_stock_threshold",
      "value": 10,
      "description": "Threshold for low stock alerts",
      "category": "inventory",
      "dataType": "number",
      "isEditable": true,
      "createdAt": "2025-01-15T10:00:00.000Z"
    }
  ],
  "pagination": {
    "totalCount": 25,
    "currentPage": 1,
    "totalPages": 2,
    "limit": 20,
    "hasNextPage": true,
    "hasPreviousPage": false
  },
  "error": null,
  "meta": {
    "duration": 85
  }
}
```

##### POST /api/settings
Create a new setting or update an existing one.

**Required Fields:**
- `key` (string) - Unique setting key
- `value` (any) - Setting value
- `description` (string) - Setting description
- `category` (string) - Setting category

**Request Body:**
```json
{
  "key": "production.default_batch_size",
  "value": 50,
  "description": "Default batch size for production orders",
  "category": "production",
  "dataType": "number",
  "isEditable": true
}
```

**Example Response:**
```json
{
  "data": {
    "_id": "65f000020000000000000012",
    "key": "production.default_batch_size",
    "value": 50,
    "description": "Default batch size for production orders",
    "category": "production",
    "dataType": "number",
    "isEditable": true,
    "createdAt": "2025-06-16T10:00:00.000Z",
    "updatedAt": "2025-06-16T10:00:00.000Z"
  },
  "error": null,
  "meta": {
    "duration": 120
  }
}
```

##### GET /api/settings/[key]
Get a specific setting by key.

**Path Parameters:**
- `key` (string) - Setting key

**Example Request:**
```http
GET /api/settings/inventory.default_reorder_level
```

**Example Response:**
```json
{
  "data": {
    "_id": "65f000020000000000000010",
    "key": "inventory.default_reorder_level",
    "value": 20,
    "description": "Default reorder level for new parts",
    "category": "inventory",
    "dataType": "number",
    "isEditable": true,
    "validationRules": {
      "min": 1,
      "max": 1000
    },
    "createdAt": "2025-01-15T10:00:00.000Z",
    "updatedAt": "2025-06-15T14:30:00.000Z"
  },
  "error": null,
  "meta": {
    "duration": 45
  }
}
```

##### PUT /api/settings/[key]
Update a specific setting by key.

**Path Parameters:**
- `key` (string) - Setting key

**Request Body:**
```json
{
  "value": 25,
  "description": "Updated default reorder level for new parts"
}
```

##### DELETE /api/settings/[key]
Delete a specific setting by key.

**Path Parameters:**
- `key` (string) - Setting key

#### Setting Categories

**Available Categories:**
- `inventory` - Inventory management settings
- `production` - Production and manufacturing settings
- `procurement` - Purchase order and supplier settings
- `system` - System-wide configuration
- `notifications` - Alert and notification preferences
- `security` - Security and access control settings

---

### 2.16. Utilities & Monitoring

System utilities and monitoring endpoints for application health, diagnostics, and performance tracking.

#### Available Endpoints

##### GET /api/status
Get comprehensive application status and health check.

**Example Response:**
```json
{
  "status": "ok",
  "timestamp": "2025-06-16T10:00:00.000Z",
  "application": {
    "name": "trend-ims",
    "version": "1.0.0",
    "environment": "development",
    "uptime": 86400
  },
  "database": {
    "status": "connected",
    "message": "Database connection is healthy",
    "connectionState": "connected",
    "readyState": 1
  }
}
```

##### GET /api/db-status
Get detailed database connection status and health metrics.

**Query Parameters:**
- `metrics` (boolean) - Include performance metrics: 'true'
- `stats` (boolean) - Include database statistics: 'true'

**Example Request:**
```http
GET /api/db-status?metrics=true&stats=true
```

**Example Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-06-16T10:00:00.000Z",
  "connection": {
    "state": "connected",
    "readyState": 1,
    "host": "localhost:27017",
    "database": "trend_ims"
  },
  "metrics": {
    "responseTime": 15,
    "activeConnections": 5,
    "totalQueries": 1250,
    "averageQueryTime": 45
  },
  "stats": {
    "collections": 15,
    "documents": 12500,
    "dataSize": "25.6 MB",
    "indexSize": "5.2 MB"
  }
}
```

##### GET /api/diagnostic
Get comprehensive system diagnostic information including database health and collection statistics.

**Example Response:**
```json
{
  "success": true,
  "data": {
    "database": {
      "status": "connected",
      "connectionState": "connected",
      "readyState": 1,
      "host": "localhost:27017",
      "database": "trend_ims"
    },
    "collections": {
      "parts": {
        "count": 1250,
        "sampleData": {
          "_id": "6640f0a0a1b2c3d4e5f6a00a",
          "partNumber": "DL23.108",
          "name": "Spacer Ring"
        }
      },
      "assemblies": {
        "count": 85,
        "sampleData": {
          "_id": "6640f0a0a1b2c3d4e5f6a00b",
          "assemblyCode": "ASM-001",
          "name": "Main Assembly"
        }
      },
      "inventory": {
        "count": 2500,
        "sampleData": {
          "_id": "6640f0a0a1b2c3d4e5f6a00c",
          "item_type": "Part",
          "quantity_on_hand": 50
        }
      }
    },
    "system": {
      "nodeVersion": "v18.17.0",
      "platform": "darwin",
      "environment": "development",
      "uptime": 86400
    },
    "timestamp": "2025-06-16T10:00:00.000Z"
  },
  "message": "System diagnostic completed successfully",
  "metadata": {
    "duration": 250,
    "totalCollections": 15,
    "totalDocuments": 12500
  }
}
```

##### GET /api/monitoring/performance
Get detailed performance monitoring data and metrics.

**Query Parameters:**
- `metric` (string) - Specific metric: 'all', 'n1', 'cache', 'memory', 'health'

**Example Request:**
```http
GET /api/monitoring/performance?metric=all
```

**Example Response:**
```json
{
  "data": {
    "timestamp": "2025-06-16T10:00:00.000Z",
    "server": {
      "uptime": 86400,
      "memory": {
        "rss": 52428800,
        "heapTotal": 41943040,
        "heapUsed": 25165824,
        "external": 1048576
      },
      "nodeVersion": "v18.17.0"
    },
    "n1Detection": {
      "totalDetections": 5,
      "recentDetections": 2,
      "lastDetection": "2025-06-16T09:45:00.000Z",
      "affectedEndpoints": ["/api/assemblies", "/api/parts"]
    },
    "cache": {
      "hitRate": 0.85,
      "totalRequests": 1000,
      "hits": 850,
      "misses": 150,
      "size": "2.5 MB"
    },
    "health": {
      "status": "healthy",
      "checks": {
        "n1Detection": "healthy",
        "cache": "healthy",
        "memory": "healthy"
      }
    }
  },
  "error": null,
  "meta": {
    "metric": "all",
    "timestamp": "2025-06-16T10:00:00.000Z"
  }
}
```

##### GET /api/monitoring/sentry-issues
Get Sentry error monitoring data and recent issues.

**Query Parameters:**
- `projectId` (string, default: '4509213084418048') - Sentry project ID
- `query` (string, default: 'is:unresolved') - Sentry query filter
- `limit` (integer, default: 10, max: 100) - Number of issues to retrieve

**Example Request:**
```http
GET /api/monitoring/sentry-issues?query=is:unresolved&limit=20
```

**Example Response:**
```json
{
  "data": {
    "issues": [
      {
        "id": "4509213084418048",
        "title": "TypeError: Cannot read property 'length' of undefined",
        "level": "error",
        "status": "unresolved",
        "count": 15,
        "firstSeen": "2025-06-15T08:00:00.000Z",
        "lastSeen": "2025-06-16T09:30:00.000Z",
        "permalink": "https://sentry.io/issues/4509213084418048/"
      }
    ],
    "totalCount": 5,
    "projectId": "4509213084418048"
  },
  "error": null,
  "meta": {
    "query": "is:unresolved",
    "limit": 20,
    "timestamp": "2025-06-16T10:00:00.000Z"
  }
}
```

##### POST /api/monitoring/reset-n1
Reset N+1 query detection monitoring counters.

**Example Response:**
```json
{
  "data": {
    "reset": true,
    "previousStats": {
      "totalDetections": 5,
      "recentDetections": 2
    },
    "newStats": {
      "totalDetections": 0,
      "recentDetections": 0
    }
  },
  "message": "N+1 detection counters reset successfully",
  "meta": {
    "timestamp": "2025-06-16T10:00:00.000Z"
  }
}
```

#### Monitoring Features

**Performance Tracking:**
- N+1 query detection and prevention
- Cache hit/miss ratios and performance
- Memory usage and system resources
- API response times and throughput

**Health Checks:**
- Database connectivity and performance
- Application uptime and stability
- System resource utilization
- Error rates and exception tracking

**Integration:**
- Sentry error monitoring and alerting
- Performance metrics collection
- Real-time system diagnostics
- Automated health status reporting

---

### 2.17. Logistics

The Logistics API manages delivery tracking and logistics operations for purchase orders and shipments.

#### Base Endpoint
```
/api/logistics
```

#### Available Endpoints

##### GET /api/logistics
Retrieve all deliveries with pagination, sorting, and filtering.

**Query Parameters:**
- `page` (integer, default: 1) - Page number for pagination
- `limit` (integer, default: 20, max: 500) - Number of items per page
- `sortField` (string, default: 'createdAt') - Field to sort by
- `sortOrder` (string, default: 'desc') - Sort direction: 'asc' or 'desc'
- `status` (string) - Filter by delivery status
- `referenceType` (string) - Filter by reference type

**Example Request:**
```http
GET /api/logistics?page=1&limit=20&status=in_transit&sortField=createdAt&sortOrder=desc
```

**Example Response (Legacy Format):**
```json
{
  "data": [
    {
      "_id": "6809e615f6450d12271ec004",
      "deliveryId": "DEL-2025-001",
      "referenceType": "PurchaseOrder",
      "referenceId": "6809e615f6450d12271ec003",
      "supplierId": "681f796ad6a21248b8ec7600",
      "status": "in_transit",
      "scheduledDate": "2025-06-25T17:00:00.000Z",
      "actualDate": null,
      "trackingNumber": "TRK123456789",
      "notes": "Delivery scheduled for morning",
      "receivedBy": null,
      "createdAt": "2025-06-14T10:00:00.000Z",
      "updatedAt": "2025-06-15T14:30:00.000Z"
    }
  ],
  "pagination": {
    "totalCount": 15,
    "currentPage": 1,
    "totalPages": 1,
    "limit": 20,
    "hasNextPage": false,
    "hasPreviousPage": false
  },
  "error": null,
  "meta": {
    "duration": 125
  }
}
```

##### POST /api/logistics
Create a new delivery record.

**Required Fields:**
- `deliveryId` (string) - Unique delivery identifier
- `referenceType` (string) - Type of reference: 'PurchaseOrder', 'WorkOrder'
- `referenceId` (string) - ID of the referenced order
- `status` (string) - Delivery status
- `scheduledDate` (string) - Scheduled delivery date

**Request Body:**
```json
{
  "deliveryId": "DEL-2025-002",
  "referenceType": "PurchaseOrder",
  "referenceId": "6809e615f6450d12271ec003",
  "supplierId": "681f796ad6a21248b8ec7600",
  "status": "scheduled",
  "scheduledDate": "2025-06-26T17:00:00.000Z",
  "trackingNumber": "TRK987654321",
  "notes": "Handle with care - fragile items"
}
```

**Example Response (Legacy Format):**
```json
{
  "data": {
    "_id": "6809e615f6450d12271ec005",
    "deliveryId": "DEL-2025-002",
    "referenceType": "PurchaseOrder",
    "referenceId": "6809e615f6450d12271ec003",
    "supplierId": "681f796ad6a21248b8ec7600",
    "status": "scheduled",
    "scheduledDate": "2025-06-26T17:00:00.000Z",
    "actualDate": null,
    "trackingNumber": "TRK987654321",
    "notes": "Handle with care - fragile items",
    "receivedBy": null,
    "createdAt": "2025-06-16T10:00:00.000Z",
    "updatedAt": "2025-06-16T10:00:00.000Z"
  },
  "error": null,
  "meta": {
    "duration": 180
  }
}
```

##### GET /api/logistics/[id]
Get a specific delivery by ID.

**Path Parameters:**
- `id` (string) - Delivery ObjectId

##### PUT /api/logistics/[id]
Update a delivery record.

##### DELETE /api/logistics/[id]
Delete a delivery record.

#### Delivery Status Values
- `scheduled` - Delivery is scheduled
- `in_transit` - Package is in transit
- `delivered` - Successfully delivered
- `failed` - Delivery failed
- `cancelled` - Delivery cancelled

---

## 3. Data Models

This section defines the structure of key data models used throughout the Trend_IMS API. All models are based on MongoDB schemas with Mongoose ODM.

### 3.1. Part

The Part model represents individual components and materials.

```json
{
  "_id": "ObjectId",
  "partNumber": "string (required, unique)",
  "name": "string (required)",
  "description": "string",
  "technicalSpecs": "string",
  "isManufactured": "boolean (required)",
  "reorderLevel": "number",
  "status": "string (enum: active, inactive, obsolete)",
  "inventory": {
    "currentStock": "number (required)",
    "warehouseId": "ObjectId (required, ref: Warehouse)",
    "safetyStockLevel": "number (required)",
    "maximumStockLevel": "number (required)",
    "averageDailyUsage": "number (required)",
    "abcClassification": "string (enum: A, B, C)",
    "lastStockUpdate": "Date"
  },
  "supplierId": "ObjectId (ref: Supplier)",
  "unitOfMeasure": "string (required)",
  "costPrice": "number (required)",
  "categoryId": "ObjectId (ref: Category)",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

**Key Features:**
- Embedded inventory structure for stock tracking
- Unique partNumber as business identifier
- ABC classification for inventory management
- Manufacturing vs purchased part distinction

### 3.2. Assembly

The Assembly model represents Bills of Materials (BOMs).

```json
{
  "_id": "ObjectId",
  "assemblyCode": "string (required, unique)",
  "name": "string (required)",
  "description": "string",
  "version": "number (required, default: 1)",
  "status": "string (enum: active, pending_review, obsolete)",
  "isTopLevel": "boolean (required, default: true)",
  "parentId": "ObjectId (ref: Assembly)",
  "productId": "ObjectId (ref: Product)",
  "partsRequired": [
    {
      "partId": "ObjectId (ref: Part)",
      "quantityRequired": "number (required)",
      "unitOfMeasure": "string"
    }
  ],
  "manufacturingInstructions": "string",
  "estimatedBuildTime": "string",
  "manufacturingLeadTime": "string",
  "notes": "string",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

**Key Features:**
- Hierarchical assembly structure (parent/child) with isTopLevel flag
- Parts requirements with quantities and unit of measure
- Manufacturing instructions and timing estimates
- Version control support with numeric versioning (integer, default: 1)
- Status lifecycle management (active, pending_review, obsolete)
- Product and parent assembly relationships

### 3.3. Product

The Product model represents finished goods for sale.

```json
{
  "_id": "ObjectId",
  "productCode": "string (required, unique)",
  "name": "string (required)",
  "description": "string (required)",
  "categoryId": "ObjectId (required, ref: Category)",
  "status": "string (enum: active, discontinued, in_development)",
  "sellingPrice": "number (required, min: 0)",
  "assemblyId": "ObjectId (ref: Assembly)",
  "partId": "ObjectId (ref: Part)",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

**Key Features:**
- Can be manufactured from assembly or direct resale of part
- Category classification required
- Status lifecycle management
- Selling price tracking

### 3.4. Inventory Level

The Inventory Level model tracks stock across warehouses.

```json
{
  "_id": "ObjectId",
  "item_id": "ObjectId (required, refPath: item_type)",
  "item_type": "string (required, enum: Part, Assembly, Product)",
  "warehouse_id": "ObjectId (required, ref: Warehouse)",
  "quantity_on_hand": "number (required, min: 0)",
  "quantity_allocated": "number (default: 0, min: 0)",
  "quantity_available": "number (virtual: quantity_on_hand - quantity_allocated)",
  "location_in_warehouse": "string",
  "reorder_level": "number (min: 0)",
  "safety_stock_level": "number (min: 0)",
  "maximum_stock_level": "number (min: 0)",
  "average_daily_usage": "Decimal128",
  "abc_classification": "string",
  "last_stock_update": "Date (default: now)",
  "notes": "string",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

**Key Features:**
- Multi-item type support (Part, Assembly, Product)
- Stock allocation tracking
- Virtual quantity_available field
- Warehouse location tracking

### 3.5. Inventory Transaction

The Inventory Transaction model tracks all inventory movements.

```json
{
  "_id": "ObjectId",
  "itemId": "ObjectId (required)",
  "itemType": "string (required, enum: Part, Assembly, Product)",
  "warehouseId": "ObjectId (required, ref: Warehouse)",
  "transactionType": "string (required, enum: stock_in_purchase, stock_out_production, adjustment_cycle_count, stock_in_production, transfer_out, transfer_in, sales_shipment)",
  "quantity": "number (required)",
  "previousStock": "number",
  "newStock": "number",
  "transactionDate": "Date (default: now)",
  "referenceNumber": "string",
  "referenceType": "string (enum: PurchaseOrder, WorkOrder, SalesOrder, StockAdjustment)",
  "userId": "ObjectId (required, ref: User)",
  "notes": "string",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

### 3.6. Category

The Category model provides hierarchical classification.

```json
{
  "_id": "ObjectId",
  "name": "string (required)",
  "description": "string",
  "parentCategory": "ObjectId (ref: Category)",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

### 3.7. Supplier

The Supplier model manages vendor information.

```json
{
  "_id": "ObjectId",
  "supplier_id": "string (required, unique)",
  "name": "string (required)",
  "contact_info": {
    "email": "string",
    "phone": "string",
    "address": "string"
  },
  "status": "string (enum: active, inactive)",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

### 3.8. Warehouse

The Warehouse model manages storage locations.

```json
{
  "_id": "ObjectId",
  "location_id": "string (required, unique)",
  "name": "string (required)",
  "address": "string",
  "capacity": "number",
  "current_utilization": "number",
  "status": "string (enum: active, inactive)",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

### 3.9. Work Order

The Work Order model manages production orders.

```json
{
  "_id": "ObjectId",
  "woNumber": "string (required, unique)",
  "assemblyId": "ObjectId (ref: Assembly)",
  "quantity": "number (required)",
  "status": "string (enum: pending, in_progress, completed, cancelled)",
  "priority": "string (enum: low, medium, high, urgent)",
  "startDate": "Date",
  "dueDate": "Date",
  "completedDate": "Date",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

### 3.10. Batch

The Batch model tracks production batches.

```json
{
  "_id": "ObjectId",
  "batchNumber": "string (required, unique)",
  "workOrderId": "string (required, ref: WorkOrder)",
  "quantity": "number (required)",
  "status": "string (enum: pending, in_progress, completed, cancelled)",
  "startDate": "Date",
  "estimatedCompletionDate": "Date",
  "actualCompletionDate": "Date",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

### 3.11. Logistics

The Logistics model manages delivery tracking and shipment information.

```json
{
  "_id": "ObjectId",
  "deliveryId": "string (required, unique)",
  "referenceType": "string (required, enum: PurchaseOrder, WorkOrder)",
  "referenceId": "ObjectId (required)",
  "supplierId": "ObjectId (ref: Supplier)",
  "status": "string (required, enum: scheduled, in_transit, delivered, failed, cancelled)",
  "scheduledDate": "Date (required)",
  "actualDate": "Date",
  "trackingNumber": "string",
  "notes": "string",
  "receivedBy": "string",
  "createdAt": "Date",
  "updatedAt": "Date"
}
```

**Key Features:**
- Reference tracking to purchase orders and work orders
- Unique delivery identifier for tracking
- Scheduled vs actual delivery date tracking
- Delivery status lifecycle management
- Supplier relationship tracking
- Received by tracking for accountability

---

**Last Updated**: 2025-06-16
**API Version**: Development
**Documentation Version**: 1.2.0

**Recent Updates (v1.2.0)**:
- 🔧 **CRITICAL FIXES**: Corrected major field name discrepancies identified in comprehensive review
- ✅ **Logistics API**: Fixed field names (deliveryNumber → deliveryId, expectedDeliveryDate → scheduledDate, actualDeliveryDate → actualDate)
- ✅ **Logistics API**: Removed non-existent items array, added missing fields (trackingNumber, notes, receivedBy)
- ✅ **Inventory Sub-endpoints**: Fixed field names (inventory_id → id, quantity_to_allocate → allocation_quantity)
- ✅ **Inventory update-stock**: Complete rewrite with correct fields (itemId, itemType, warehouseId, quantityChange, userId)
- ✅ **Assembly Data Model**: Fixed version field type (string → number), removed non-existent fields (subAssemblies, costData, createdBy, updatedBy)
- ✅ **Diagnostic Endpoint**: Added missing GET /api/diagnostic documentation with database health checks
- ✅ **Logistics Data Model**: Updated to match actual implementation with correct field names

**Previous Updates (v1.1.0)**:
- ✅ Added complete Logistics API documentation (2.17)
- ✅ Added missing Inventory sub-endpoints (adjust, allocate, update-stock)
- ✅ Corrected response format inconsistencies (Legacy vs Standard formats)
- ✅ Enhanced performance and rate limiting documentation
