# Trend IMS - Inventory Management System

## Description

Trend IMS is a web-based application designed to manage inventory, track products, assemblies, purchase orders, and provide insights into stock levels and production status. It features a dashboard for quick overview and actions, detailed views for products and assemblies, and utilizes a MongoDB database for data persistence.

## Tech Stack

*   **Framework:** Next.js (v15+) with App Router
*   **Language:** TypeScript
*   **Database:** MongoDB with Mongoose ODM
*   **Styling:** Tailwind CSS
*   **UI Components:** Radix UI, Shadcn/ui (implied by structure/components like `components/ui`)
*   **State Management:** React Context API (`AppContext`, `ThemeContext`)
*   **Forms:** React Hook Form with Zod for validation
*   **Charting:** Recharts
*   **Animation:** Framer Motion
*   **Testing:** Jest, React Testing Library
*   **Linting/Formatting:** ESLint

## Features

*   **Dashboard:** Overview of key metrics (Total Items, Orders, Critical Reorders), quick actions (Add Stock, Create Order, Generate Report), production planning & capacity charts, assembly status, and high-demand item highlights.
*   **Product Management:** View, add, edit products (likely via `ProductsTable` and related forms/API).
*   **Assembly Management:** Track assembly status and components (e.g., `AssemblyStatus`, `AssembliesGrid`, `app/api/assemblies`).
*   **Part Management:** Hierarchical part entry and management (e.g., `HierarchicalPartsForm`).
*   **Purchase Orders:** Create and manage purchase orders (e.g., `app/api/purchase-orders`).
*   **Supplier Management:** (Likely based on `app/api/suppliers`)
*   **Database Status & Diagnostics:** API endpoints and scripts for checking DB connection and status.
*   **Theming:** Light/Dark mode support (`ThemeContext`, `ThemeToggle`).
*   **Real-time Updates:** Data refreshing capabilities (`RefreshDataButton`, `AppContext`).
*   **Alerts:** Notifications for low stock and out-of-stock items.

## Requirements and Dependencies

This section details the software, hardware, and service dependencies required to work with this project.

### Software Requirements

*   **Operating System:** Compatible with macOS, Windows (using WSL recommended), and Linux. (Inferred, typical for Node.js development)
*   **Node.js:** Version 20 or later is recommended (based on `@types/node": "^20"` in `devDependencies`). The specific version used during development can influence compatibility. Check your Node.js version using `node -v`.
    *   *Source:* `package.json` (`devDependencies`)
*   **Package Manager:** npm (usually bundled with Node.js) or Yarn. Check your npm version using `npm -v`.
    *   *Source:* Implied by `package.json` and standard Node.js workflows.
*   **Git:** Required for version control and cloning the repository.
    *   *Source:* Standard development practice.

### Hardware Requirements

*   Standard development hardware is sufficient (e.g., modern multi-core processor, >= 8GB RAM, SSD recommended). Specific requirements may vary based on the scale of data and concurrent usage during development or deployment.
    *   *Source:* General estimation.

### External Dependencies

*   **MongoDB:** A running MongoDB instance (v6.x or later recommended, based on `mongodb` driver version). This can be a local installation, a Docker container, or a cloud service like MongoDB Atlas. The connection URI must be configured in the `.env.local` file.
    *   *Source:* `package.json` (`dependencies`), environment setup scripts (`create-env.cjs`), database scripts (`init-mongodb.cjs`, etc.), `app/lib/mongodb.ts`.

### Project Dependencies (Libraries/Packages)

All Node.js package dependencies and their specific versions are listed in the `package.json` file. Key dependencies include:

*   **Core Framework:** `next`: ^15.2.4
*   **React:** `react`: ^19.1.0, `react-dom`: ^19.1.0
*   **Database:** `mongodb`: ^6.15.0, `mongoose`: ^8.13.1
*   **UI & Styling:** `tailwindcss`: ^3.4.1, `@radix-ui/*` (various), `lucide-react`: ^0.503.0, `clsx`: ^2.1.1, `tailwind-merge`: ^3.2.0
*   **State Management:** `react` (Context API)
*   **Forms & Validation:** `react-hook-form`: ^7.54.2, `@hookform/resolvers`: ^5.0.1, `zod`: ^3.24.2
*   **Charting:** `recharts`: ^2.12.2
*   **Animation:** `framer-motion`: ^11.0.8
*   **Utilities:** `lodash`: ^4.17.21, `date-fns`: ^3.3.1, `uuid`: ^11.1.0
*   **Development & Testing:** `typescript`: ^5, `eslint`: ^8, `jest`: ^29.7.0, `@testing-library/*` (various), `autoprefixer`: ^10.4.18, `postcss`: ^8.4.35

*   *Source:* `package.json` (`dependencies` and `devDependencies` sections). Run `npm install` or `yarn install` to install these exact versions based on `package-lock.json` or `yarn.lock`.

## Getting Started

### Setup

1.  **Clone the repository:**
    ```bash
    git clone <repository-url>
    cd Trend_IMS 
    ```
2.  **Install dependencies:**
    ```bash
    npm install
    # or
    yarn install
    ```
3.  **Configure Environment Variables:**
    *   Run the setup script to create a `.env.local` file based on `.env.example` (if it exists) or predefined defaults:
        ```bash
        npm run setup-env
        ```
    *   Manually edit the generated `.env.local` file with your specific configurations, especially the `MONGODB_URI`.
4.  **Initialize/Migrate Database (Optional but Recommended):**
    *   Run migration scripts if available (check `scripts` section in `package.json`):
        ```bash
        # Example: Initialize MongoDB data
        npm run migrate-data 
        # Example: Initialize assemblies data
        npm run init-assemblies
        ```

### Running the Development Server

1.  Ensure your environment variables are set up correctly (`.env.local`).
2.  Start the Next.js development server (includes environment check):
    ```bash
    npm run dev
    ```
3.  Open [http://localhost:5174](http://localhost:5174) (or the specified port) in your browser.

## Available Scripts

*   `npm run setup-env`: Creates the `.env.local` file.
*   `npm run dev`: Starts the development server with Turbopack on port 5174.
*   `npm run build`: Builds the application for production.
*   `npm run start`: Starts the production server.
*   `npm run lint`: Lints the codebase using ESLint.
*   `npm run test`: Runs tests using Jest.
*   `npm run test:watch`: Runs tests in watch mode.
*   `npm run test:coverage`: Generates a test coverage report.
*   `npm run test-db`: Tests the MongoDB connection.
*   `npm run test-ssl`: Tests SSL connection (likely for MongoDB Atlas).
*   `npm run diagnose-db`: Runs MongoDB diagnostics.
*   `npm run verify-db-data`: Verifies data in the MongoDB database.
*   `npm run migrate-data`: Initializes the MongoDB database with initial data.
*   `npm run migrate-assemblies`: Migrates assembly data.
*   `npm run init-assemblies`: Initializes assembly data (alternative script).
*   `npm run check-env`: Checks if required environment variables are set.

## Project Structure

*   `app/`: Main application code using Next.js App Router.
    *   `api/`: Backend API route handlers.
    *   `components/`: Reusable React components (UI, features, layout).
    *   `context/` / `contexts/`: React Context providers for state management.
    *   `dashboard/`: Contains the main dashboard page and potentially related components.
    *   `lib/`: Utility functions, database connection logic (`mongodb.ts`), API helpers.
    *   `models/`: Mongoose schema definitions for database models.
    *   `services/`: Business logic or external service integrations.
    *   `styles/`: Global and specific styles.
    *   `types/`: TypeScript type definitions.
    *   `utils/`: General helper functions.
*   `public/`: Static assets.
*   `scripts/`: Standalone scripts for database operations, migrations, etc.
*   `__tests__/`: Unit and integration tests.
*   Configuration Files: `next.config.mjs`, `tailwind.config.js`, `tsconfig.json`, `eslint.config.js`, `jest.config.js`, etc.

## Database

This project uses MongoDB as its primary database.

*   **Connection:** Managed via `app/lib/mongodb.ts` and configured using the `MONGODB_URI` environment variable.
*   **Models:** Defined using Mongoose schemas in `app/models/`.
*   **Management Scripts:** Several scripts are provided for testing the connection, diagnosing issues, verifying data, and running migrations (see "Available Scripts").