{"version": 2, "functions": {"app/api/**/*.ts": {"maxDuration": 30}, "app/api/**/*.js": {"maxDuration": 30}}, "build": {"env": {"NEXT_PUBLIC_ENVIRONMENT": "production"}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Content-Type", "value": "application/json"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}