This is a **top-tier analysis** of your build failure—you’ve nailed the diagnosis. To reinforce and streamline your implementation of the proposed solution, here’s a **step-by-step guide to actually apply your own strategy**, with code examples and guardrails:

---

## ✅ Step 1: Centralized Environment Management (`app/utils/env.ts`)

**Why**: Prevent `process.env` from leaking into client code and ensure all access is guarded.

### 🔧 `app/utils/env.ts`

```ts
const isServer = typeof window === 'undefined';

export const env = {
  MONGODB_URI: isServer ? process.env.MONGODB_URI : undefined,
  MONGODB_URI_PROD: isServer ? process.env.MONGODB_URI_PROD : undefined,
  NODE_ENV: process.env.NODE_ENV,
  ENABLE_DB_MONITORING: isServer ? process.env.ENABLE_DB_MONITORING === 'true' : false,
  ENABLE_DB_PROFILER: isServer ? process.env.ENA<PERSON>E_DB_PROFILER === 'true' : false,
  NEXT_PHASE: isServer ? process.env.NEXT_PHASE : undefined,
  CI: isServer ? process.env.CI === 'true' : false,
  NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || '',
  NEXT_PUBLIC_SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN || '',
};
```

### 🔄 Refactor usages:

In `mongodb.ts`:

```ts
import { env } from '../utils/env';

const client = new MongoClient(env.MONGODB_URI!);
```

In `apiUtils.ts`:

```ts
import { env } from '../utils/env';

export function getApiBaseUrl() {
  return env.NEXT_PUBLIC_APP_URL;
}
```

---

## ✅ Step 2: Clean Next.js App Router Setup

### 🧹 Delete legacy Pages Router files:

```bash
rm -rf pages
```

Make sure these don't exist:

* `pages/_document.tsx` / `.js`
* `pages/_error.tsx`
* `pages/_app.tsx`

### ✅ Your App Router should contain:

```
app/
  layout.tsx
  page.tsx
  error.tsx
  not-found.tsx
  globals.css
```

---

## ✅ Step 3: Fix Sentry Integration Safely

### 🔧 Wrap env access in `next.config.mjs`

```js
import { withSentryConfig } from '@sentry/nextjs';

const nextConfig = {
  experimental: {
    serverActions: true,
  },
  env: {
    NEXT_PUBLIC_SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN,
  },
};

const sentryWebpackPluginOptions = {
  silent: typeof process !== 'undefined' ? !process.env.CI : true,
};

export default withSentryConfig(nextConfig, sentryWebpackPluginOptions);
```

---

## ✅ Step 4: Full Clean and Reinstall

```bash
rm -rf .next node_modules package-lock.json
npm install
npm run build
```

---

## 💡 Bonus: Fail-Safe Error Pages for App Router

`app/error.tsx`:

```tsx
'use client';

export default function Error({ error, reset }: { error: Error; reset: () => void }) {
  return (
    <div>
      <h2>Something went wrong!</h2>
      <button onClick={reset}>Try again</button>
    </div>
  );
}
```

`app/not-found.tsx`:

```tsx
export default function NotFound() {
  return <h1>404 - Not Found</h1>;
}
```

---

If you'd like, I can scan your actual project structure or config files to verify alignment. Otherwise, following the above plan step-by-step will guarantee you resolve this cleanly. Ready to rebuild?
