// eslint.config.js
import globals from 'globals';
import tseslint from 'typescript-eslint';
import nextPlugin from '@next/eslint-plugin-next';
// eslint-plugin-react is a peer dependency of eslint-config-next and should be installed.
// If not, the build might fail asking for it.
import reactPlugin from 'eslint-plugin-react';
import reactHooksPlugin from 'eslint-plugin-react-hooks';
import jsxRuntime from 'eslint-plugin-react/configs/jsx-runtime.js';

export default tseslint.config(
  {
    ignores: [
      ".next/**",
      "dist/**",
      "node_modules/**",
      "public/**",
      "debug-relationships.js",
      "js_backup/**",
      "sql_backup/**",
      "split_sql/**",
      "instrumentation.ts",
      "instrumentation-client.ts",
      "sentry.*.config.ts",
      "postcss.config.js",
      "tailwind.config.js",
      "next.config.mjs",
      "jest.config.js",
      "jest.setup.js",
      "*.cjs" // CommonJS helper scripts
    ],
  },
  {
    files: ["**/*.{js,jsx,mjs,ts,tsx}"],
    plugins: {
      '@next/next': nextPlugin,
      'react': reactPlugin,
      'react-hooks': reactHooksPlugin,
    },
    languageOptions: {
      parser: tseslint.parser,
      parserOptions: {
        project: true, // Automatically find tsconfig.json
        tsconfigRootDir: process.cwd(), // Explicitly set root for tsconfig resolution
        ecmaFeatures: { jsx: true },
      },
      globals: {
        ...globals.browser,
        ...globals.node,
      },
    },
    rules: {
      // Base recommended rules from typescript-eslint
      ...tseslint.configs.recommended.rules,
      // React specific rules
      ...reactPlugin.configs.recommended.rules,
      ...jsxRuntime.rules, // Includes rules for the new JSX transform
      // React Hooks rules
      ...reactHooksPlugin.configs.recommended.rules,
      // Next.js specific rules
      ...nextPlugin.configs.recommended.rules,
      ...nextPlugin.configs['core-web-vitals'].rules,
      // Custom rule adjustments
      'react/prop-types': 'off',
      '@typescript-eslint/no-unused-vars': ['warn', { 'argsIgnorePattern': '^_' }],
      // Add other project-specific overrides here
    },
    settings: {
      react: {
        version: 'detect', // Automatically detect React version
      },
    },
  }
);
